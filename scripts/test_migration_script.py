#!/usr/bin/env python3
"""
测试迁移脚本

直接执行迁移SQL来测试迁移逻辑
"""

import asyncio
import os
import sys

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tortoise import Tortoise

from log import logger
from settings.config import settings


async def test_migration():
    """测试迁移脚本"""

    # 初始化数据库连接
    await Tortoise.init(config=settings.tortoise_orm)

    try:
        # 获取数据库连接
        conn = Tortoise.get_connection("default")

        logger.info("开始测试迁移脚本...")

        # 1. 创建测试表和数据
        logger.info("创建测试数据...")

        # 确保表存在 (MySQL 语法)
        await conn.execute_script(
            """
            CREATE TABLE IF NOT EXISTS `crawler_cookies_account` (
                `id` INT AUTO_INCREMENT PRIMARY KEY,
                `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                `is_deleted` INT NOT NULL DEFAULT 0,
                `account_name` VARCHAR(64) NOT NULL DEFAULT '',
                `platform_name` VARCHAR(64) NOT NULL DEFAULT '',
                `cookies` TEXT,
                `invalid_timestamp` BIGINT NOT NULL DEFAULT 0,
                `status` INT NOT NULL DEFAULT 0
            );
        """
        )

        # 插入测试数据 (MySQL 语法)
        await conn.execute_script(
            """
            INSERT INTO crawler_cookies_account 
            (id, account_name, platform_name, status, is_deleted) VALUES
            (1, 'test_available', 'douyin', 0, 0),
            (2, 'test_in_use_1', 'douyin', 1, 0),
            (3, 'test_in_use_2', 'trendinsight', 1, 0),
            (4, 'test_unavailable', 'douyin', -1, 0),
            (5, 'test_deleted', 'douyin', 1, 1)
            ON DUPLICATE KEY UPDATE 
            account_name = VALUES(account_name),
            platform_name = VALUES(platform_name),
            status = VALUES(status),
            is_deleted = VALUES(is_deleted);
        """
        )

        # 2. 检查迁移前的状态
        logger.info("迁移前的状态分布:")
        result = await conn.execute_query(
            "SELECT status, COUNT(*) as count FROM crawler_cookies_account WHERE is_deleted = 0 GROUP BY status ORDER BY status"
        )
        for row in result[1]:
            status, count = row
            status_text = {0: "可用", 1: "使用中", -1: "不可用"}.get(status, f"未知({status})")
            logger.info(f"  {status_text}: {count}")

        # 3. 执行迁移
        logger.info("执行迁移...")

        migration_sql = """
            -- 步骤1: 将所有"使用中"状态转为"可用"状态
            UPDATE crawler_cookies_account 
            SET status = 0, updated_at = CURRENT_TIMESTAMP
            WHERE status = 1;
            
            -- 步骤2: 清理测试数据（如果存在）
            DELETE FROM crawler_cookies_account 
            WHERE account_name LIKE 'test_%';
            
            -- 步骤3: 确保所有异常状态值都被修正
            UPDATE crawler_cookies_account 
            SET status = 0, updated_at = CURRENT_TIMESTAMP
            WHERE status NOT IN (0, -1);
            
            -- 步骤4: 添加状态字段约束检查 (MySQL 语法)
            ALTER TABLE crawler_cookies_account 
            ADD CONSTRAINT chk_account_status CHECK (status IN (0, -1));
        """

        await conn.execute_script(migration_sql)

        # 4. 检查迁移后的状态
        logger.info("迁移后的状态分布:")
        result = await conn.execute_query(
            "SELECT status, COUNT(*) as count FROM crawler_cookies_account WHERE is_deleted = 0 GROUP BY status ORDER BY status"
        )
        for row in result[1]:
            status, count = row
            status_text = {0: "可用", -1: "不可用"}.get(status, f"未知({status})")
            logger.info(f"  {status_text}: {count}")

        # 5. 测试约束
        logger.info("测试状态约束...")
        try:
            await conn.execute_query(
                "INSERT INTO crawler_cookies_account (account_name, platform_name, status) VALUES ('test_invalid', 'test', 999)"
            )
            logger.error("❌ 约束测试失败：允许插入无效状态")
        except Exception as e:
            logger.info(f"✅ 约束测试成功：{str(e)}")

        # 6. 清理测试数据
        logger.info("清理测试数据...")
        try:
            await conn.execute_query("ALTER TABLE crawler_cookies_account DROP CONSTRAINT chk_account_status")
        except:
            pass  # 约束可能不存在

        await conn.execute_query("DELETE FROM crawler_cookies_account WHERE account_name LIKE 'test_%'")

        logger.info("🎉 迁移测试完成！")

    except Exception as e:
        logger.error(f"❌ 测试过程中发生错误: {str(e)}")
        raise

    finally:
        await Tortoise.close_connections()


if __name__ == "__main__":
    asyncio.run(test_migration())
