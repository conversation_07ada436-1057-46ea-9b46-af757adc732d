#!/usr/bin/env python3
"""
多数据源配置验证脚本

用于验证 Tortoise-ORM 多数据源配置是否正确
"""

import asyncio
import logging
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), ".."))

from tortoise import Tortoise
from settings.config import settings


async def validate_config():
    """验证多数据源配置"""
    
    logging.info("🔍 验证多数据源配置")
    logging.info("=" * 40)
    
    # 1. 检查配置结构
    logging.info("\n📋 1. 检查配置结构")
    
    config = settings.tortoise_orm
    logging.info(f"✅ 配置类型: {type(config)}")
    
    # 检查连接配置
    if "connections" in config:
        connections = config["connections"]
        logging.info(f"✅ 连接配置: {list(connections.keys())}")
        
        for name, uri in connections.items():
            logging.info(f"   - {name}: {uri}")
    else:
        logging.error("❌ 缺少连接配置")
        return False
    
    # 检查应用配置
    if "apps" in config:
        apps = config["apps"]
        logging.info(f"✅ 应用配置: {list(apps.keys())}")
        
        for app_name, app_config in apps.items():
            models = app_config.get("models", [])
            connection = app_config.get("default_connection", "unknown")
            logging.info(f"   - {app_name}: {len(models)} 个模型, 连接: {connection}")
            for model in models:
                logging.info(f"     * {model}")
    else:
        logging.error("❌ 缺少应用配置")
        return False
    
    return True


async def test_connections():
    """测试数据库连接"""
    
    logging.info("\n🔌 2. 测试数据库连接")
    
    try:
        # 初始化 Tortoise
        await Tortoise.init(config=settings.tortoise_orm)
        logging.info("✅ Tortoise ORM 初始化成功")
        
        # 获取所有连接
        try:
            # 尝试新版本 API
            connection_names = list(Tortoise._connections.keys()) if hasattr(Tortoise, '_connections') else []
        except:
            # 如果失败，使用配置中的连接名称
            connection_names = list(settings.tortoise_orm["connections"].keys())

        logging.info(f"📋 可用连接: {connection_names}")

        # 测试每个连接
        for conn_name in connection_names:
            try:
                conn = Tortoise.get_connection(conn_name)
                
                # 执行简单查询测试连接
                result = await conn.execute_query("SELECT 1 as test")
                logging.info(f"✅ 连接 '{conn_name}' 测试成功: {result}")
                
            except Exception as e:
                logging.error(f"❌ 连接 '{conn_name}' 测试失败: {e}")
                return False
        
        return True
        
    except Exception as e:
        logging.error(f"❌ 初始化失败: {e}")
        return False


async def test_model_imports():
    """测试模型导入"""
    
    logging.info("\n📦 3. 测试模型导入")
    
    try:
        # 测试奇好助手模型导入
        from models.qihaozhushou import UserInboxSourceRelated, UserInboxVideoRelated
        logging.info("✅ 奇好助手模型导入成功")
        logging.info(f"   - UserInboxSourceRelated: {UserInboxSourceRelated}")
        logging.info(f"   - UserInboxVideoRelated: {UserInboxVideoRelated}")
        
        # 检查模型的数据库连接
        source_meta = UserInboxSourceRelated._meta
        video_meta = UserInboxVideoRelated._meta

        logging.info(f"   - UserInboxSourceRelated 表名: {getattr(source_meta, 'table', 'N/A')}")
        logging.info(f"   - UserInboxVideoRelated 表名: {getattr(video_meta, 'table', 'N/A')}")
        logging.info(f"   - UserInboxSourceRelated 应用: {getattr(source_meta, 'app', 'N/A')}")
        logging.info(f"   - UserInboxVideoRelated 应用: {getattr(video_meta, 'app', 'N/A')}")
        
        # 测试其他模型导入
        try:
            from models.douyin.models import DouyinVideo
            logging.info("✅ 抖音模型导入成功")
        except ImportError as e:
            logging.warning(f"⚠️  抖音模型导入失败（可能正常）: {e}")
        
        try:
            from models.system.crawler import CrawlerCookiesAccount
            logging.info("✅ 系统模型导入成功")
        except ImportError as e:
            logging.warning(f"⚠️  系统模型导入失败（可能正常）: {e}")
        
        return True
        
    except Exception as e:
        logging.error(f"❌ 模型导入失败: {e}")
        return False


async def test_schema_generation():
    """测试数据库表结构生成"""
    
    logging.info("\n🏗️  4. 测试数据库表结构生成")
    
    try:
        # 生成数据库表结构
        await Tortoise.generate_schemas()
        logging.info("✅ 数据库表结构生成成功")
        
        # 验证表是否存在
        try:
            connections = list(Tortoise._connections.keys()) if hasattr(Tortoise, '_connections') else []
        except:
            connections = list(settings.tortoise_orm["connections"].keys())

        for conn_name in connections:
            conn = Tortoise.get_connection(conn_name)
            
            if conn_name == "qihaozhushou":
                # 检查奇好助手相关表
                try:
                    result = await conn.execute_query(
                        "SELECT name FROM sqlite_master WHERE type='table' AND name LIKE 'user_inbox_%'"
                    )
                    tables = [row[0] for row in result[1]]
                    logging.info(f"✅ 连接 '{conn_name}' 中的用户收件箱表: {tables}")
                    
                except Exception as e:
                    logging.warning(f"⚠️  检查 '{conn_name}' 表结构时出错: {e}")
            
            elif conn_name == "default":
                # 检查默认数据库表
                try:
                    result = await conn.execute_query(
                        "SELECT name FROM sqlite_master WHERE type='table' LIMIT 5"
                    )
                    tables = [row[0] for row in result[1]]
                    logging.info(f"✅ 连接 '{conn_name}' 中的表（前5个）: {tables}")
                    
                except Exception as e:
                    logging.warning(f"⚠️  检查 '{conn_name}' 表结构时出错: {e}")
        
        return True
        
    except Exception as e:
        logging.error(f"❌ 表结构生成失败: {e}")
        return False


async def test_basic_operations():
    """测试基本数据库操作"""
    
    logging.info("\n🧪 5. 测试基本数据库操作")
    
    try:
        from models.qihaozhushou import UserInboxSourceRelated
        import uuid
        
        # 创建测试记录
        test_uuid = str(uuid.uuid4()).replace('-', '')
        test_record = await UserInboxSourceRelated.create(
            uuid=test_uuid,
            user_uuid=str(uuid.uuid4()).replace('-', ''),
            source_id=str(uuid.uuid4()).replace('-', ''),
            source_type="test"
        )
        
        logging.info(f"✅ 创建测试记录成功: {test_record.uuid}")
        
        # 查询测试记录
        found_record = await UserInboxSourceRelated.get(uuid=test_uuid)
        logging.info(f"✅ 查询测试记录成功: {found_record.source_type}")
        
        # 删除测试记录
        await found_record.delete()
        logging.info("✅ 删除测试记录成功")
        
        return True
        
    except Exception as e:
        logging.error(f"❌ 基本操作测试失败: {e}")
        return False


async def main():
    """主函数"""
    
    logging.info("🚀 多数据源配置验证")
    logging.info("=" * 50)
    
    success_count = 0
    total_tests = 5
    
    try:
        # 1. 验证配置
        if await validate_config():
            success_count += 1
        
        # 2. 测试连接
        if await test_connections():
            success_count += 1
        
        # 3. 测试模型导入
        if await test_model_imports():
            success_count += 1
        
        # 4. 测试表结构生成
        if await test_schema_generation():
            success_count += 1
        
        # 5. 测试基本操作
        if await test_basic_operations():
            success_count += 1
        
        # 总结
        logging.info(f"\n📊 验证结果: {success_count}/{total_tests} 项测试通过")
        
        if success_count == total_tests:
            logging.info("🎉 所有测试通过！多数据源配置正确。")
            return True
        else:
            logging.warning("⚠️  部分测试失败，请检查配置。")
            return False
        
    except Exception as e:
        logging.error(f"❌ 验证过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # 关闭连接
        try:
            await Tortoise.close_connections()
            logging.info("🔧 数据库连接已关闭")
        except:
            pass


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
