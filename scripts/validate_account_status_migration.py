#!/usr/bin/env python3
"""
验证账号状态简化迁移脚本

这个脚本用于验证数据库迁移的正确性：
1. 检查是否还有状态为1的账号
2. 验证状态约束是否生效
3. 测试账号管理器的功能
"""

import asyncio
import os
import sys

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tortoise import Tortoise

from log import logger
from models.system.crawler import CrawlerCookiesAccount
from pkg.crawler_account.account_manager import AccountManager
from settings.config import settings


async def validate_migration():
    """验证迁移结果"""

    # 初始化数据库连接
    await Tortoise.init(config=settings.tortoise_orm)

    try:
        logger.info("开始验证账号状态简化迁移...")

        # 1. 检查是否还有状态为1的账号
        in_use_count = await CrawlerCookiesAccount.filter(status=1, is_deleted=False).count()
        logger.info(f"状态为1(使用中)的账号数量: {in_use_count}")

        if in_use_count > 0:
            logger.error("❌ 迁移失败：仍有账号状态为1(使用中)")
            return False
        else:
            logger.info("✅ 迁移成功：没有状态为1的账号")

        # 2. 检查现有账号的状态分布
        total_count = await CrawlerCookiesAccount.filter(is_deleted=False).count()
        available_count = await CrawlerCookiesAccount.filter(status=0, is_deleted=False).count()
        unavailable_count = await CrawlerCookiesAccount.filter(status=-1, is_deleted=False).count()

        logger.info("账号状态分布:")
        logger.info(f"  总数: {total_count}")
        logger.info(f"  可用(0): {available_count}")
        logger.info(f"  不可用(-1): {unavailable_count}")
        logger.info(f"  其他状态: {total_count - available_count - unavailable_count}")

        # 3. 测试状态约束
        logger.info("测试状态约束...")
        try:
            # 尝试插入无效状态的账号
            test_account = CrawlerCookiesAccount(
                account_name="test_invalid_status", platform_name="test", status=999  # 无效状态
            )
            await test_account.save()
            logger.error("❌ 状态约束失败：允许插入无效状态")
            await test_account.delete()
            return False
        except Exception as e:
            logger.info(f"✅ 状态约束生效：{str(e)}")

        # 4. 测试账号管理器功能
        logger.info("测试账号管理器功能...")
        manager = AccountManager()

        # 如果有账号，测试获取功能
        if total_count > 0:
            # 获取第一个平台进行测试
            first_account = await CrawlerCookiesAccount.filter(is_deleted=False).first()
            if first_account:
                platform_name = first_account.platform_name

                try:
                    account = await manager.get_available_account(platform_name)
                    logger.info(f"✅ 成功获取账号: {account.account_name} (状态: {account.status})")

                    # 测试状态查询
                    status_info = await manager.get_account_status(account.id)
                    logger.info(f"✅ 账号状态查询成功: {status_info['status_text']}")

                    # 测试统计功能
                    stats = await manager.get_platform_account_stats(platform_name)
                    logger.info(f"✅ 平台统计查询成功: 可用 {stats['available_count']}/{stats['total_count']}")

                except Exception as e:
                    logger.error(f"❌ 账号管理器测试失败: {str(e)}")
                    return False

        logger.info("🎉 所有验证测试通过！")
        return True

    except Exception as e:
        logger.error(f"❌ 验证过程中发生错误: {str(e)}")
        return False

    finally:
        await Tortoise.close_connections()


async def create_test_data():
    """创建测试数据"""
    logger.info("创建测试数据...")

    # 创建一些测试账号
    test_accounts = [
        CrawlerCookiesAccount(
            account_name="test_available_1", platform_name="douyin", status=0, cookies="test_cookies_1"  # 可用
        ),
        CrawlerCookiesAccount(
            account_name="test_available_2", platform_name="douyin", status=0, cookies="test_cookies_2"  # 可用
        ),
        CrawlerCookiesAccount(
            account_name="test_unavailable_1", platform_name="douyin", status=-1, cookies="test_cookies_3"  # 不可用
        ),
        # 这个账号用于测试迁移 - 状态为1(使用中)
        CrawlerCookiesAccount(
            account_name="test_in_use_1",
            platform_name="trendinsight",
            status=1,  # 使用中 - 应该被迁移转换为0
            cookies="test_cookies_4",
        ),
    ]

    for account in test_accounts:
        await account.save()
        logger.info(f"创建测试账号: {account.account_name} (状态: {account.status})")


async def main():
    """主函数"""
    if len(sys.argv) > 1 and sys.argv[1] == "--create-test-data":
        # 初始化数据库连接
        await Tortoise.init(config=settings.tortoise_orm)
        try:
            await create_test_data()
            logger.info("测试数据创建完成")
        finally:
            await Tortoise.close_connections()
    else:
        # 验证迁移
        success = await validate_migration()
        if not success:
            sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
