#!/usr/bin/env python3
"""
简单的配置检查脚本
"""

import logging
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), ".."))

import json
from settings.config import settings

def main():
    logging.info("🔍 配置检查")
    logging.info("=" * 50)
    
    # 检查环境
    env = getattr(settings, 'current_env', 'unknown')
    logging.info(f"当前环境: {env}")
    
    # 检查 tortoise_orm 配置
    logging.info("\n📋 tortoise_orm 配置:")
    try:
        tortoise_config = settings.tortoise_orm
        logging.info(json.dumps(dict(tortoise_config), indent=2, default=str))
    except Exception as e:
        logging.error(f"❌ 获取配置失败: {e}")

    # 检查连接配置
    logging.info("\n🔗 连接配置:")
    try:
        connections = dict(settings.tortoise_orm.connections)
        for name, uri in connections.items():
            logging.info(f"  - {name}: {uri}")
    except Exception as e:
        logging.error(f"❌ 获取连接配置失败: {e}")
    
    # 检查应用配置
    logging.info("\n📦 应用配置:")
    try:
        apps = dict(settings.tortoise_orm.apps)
        for app_name, app_config in apps.items():
            models = app_config.get('models', [])
            default_conn = app_config.get('default_connection', 'N/A')
            logging.info(f"  - {app_name}: {len(models)} 个模型, 连接: {default_conn}")
            for model in models:
                logging.info(f"    * {model}")
    except Exception as e:
        logging.error(f"❌ 获取应用配置失败: {e}")

if __name__ == "__main__":
    main()