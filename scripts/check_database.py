#!/usr/bin/env python3
"""
检查数据库中账号状态的分布
"""

import asyncio
import os
import sys

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tortoise import Tortoise

from log import logger
from settings.config import settings


async def check_database():
    """检查数据库状态"""

    # 初始化数据库连接
    await Tortoise.init(config=settings.tortoise_orm)

    try:
        # 获取数据库连接
        conn = Tortoise.get_connection("default")

        logger.info("检查数据库中的账号状态分布...")

        # 检查表是否存在
        try:
            result = await conn.execute_query("SHOW TABLES LIKE 'crawler_cookies_account'")
            if not result[1]:
                logger.info("表 crawler_cookies_account 不存在")
                return
        except Exception as e:
            logger.error(f"检查表存在性失败: {e}")
            return

        # 检查所有状态分布
        logger.info("所有账号状态分布:")
        result = await conn.execute_query(
            "SELECT status, COUNT(*) as count FROM crawler_cookies_account GROUP BY status ORDER BY status"
        )
        if result and len(result) > 1 and result[1]:
            for row in result[1]:
                status, count = row
                status_text = {0: "可用", 1: "使用中", -1: "不可用"}.get(status, f"未知({status})")
                logger.info(f"  {status_text}: {count}")
        else:
            logger.info("  没有数据")

        # 检查非删除账号状态分布
        logger.info("非删除账号状态分布:")
        result = await conn.execute_query(
            "SELECT status, COUNT(*) as count FROM crawler_cookies_account WHERE is_deleted = 0 GROUP BY status ORDER BY status"
        )
        if result and len(result) > 1 and result[1]:
            for row in result[1]:
                status, count = row
                status_text = {0: "可用", 1: "使用中", -1: "不可用"}.get(status, f"未知({status})")
                logger.info(f"  {status_text}: {count}")
        else:
            logger.info("  没有数据")

        # 检查总数
        result = await conn.execute_query("SELECT COUNT(*) as total FROM crawler_cookies_account")
        if result and len(result) > 1 and result[1]:
            total = result[1][0][0]
            logger.info(f"总账号数: {total}")
        else:
            logger.info("总账号数: 0")

        result = await conn.execute_query("SELECT COUNT(*) as active FROM crawler_cookies_account WHERE is_deleted = 0")
        if result and len(result) > 1 and result[1]:
            active = result[1][0][0]
            logger.info(f"活跃账号数: {active}")
        else:
            logger.info("活跃账号数: 0")

        # 检查是否有异常状态值
        result = await conn.execute_query(
            "SELECT DISTINCT status FROM crawler_cookies_account WHERE status NOT IN (0, 1, -1)"
        )
        if result and len(result) > 1 and result[1]:
            logger.warning("发现异常状态值:")
            for row in result[1]:
                logger.warning(f"  状态值: {row[0]}")
        else:
            logger.info("✅ 所有状态值都在预期范围内 (0, 1, -1)")

    except Exception as e:
        logger.error(f"❌ 检查过程中发生错误: {str(e)}")
        raise

    finally:
        await Tortoise.close_connections()


if __name__ == "__main__":
    asyncio.run(check_database())
