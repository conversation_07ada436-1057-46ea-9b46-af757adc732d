#!/usr/bin/env python3
"""
直接应用账号状态简化迁移
"""

import asyncio
import os
import sys

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tortoise import Tortoise

from log import logger
from settings.config import settings

# 导入迁移函数将在函数内部处理


async def apply_migration():
    """应用迁移"""

    # 初始化数据库连接
    await Tortoise.init(config=settings.tortoise_orm)

    try:
        # 获取数据库连接
        conn = Tortoise.get_connection("default")

        logger.info("开始应用账号状态简化迁移...")

        # 导入迁移模块
        import importlib.util

        spec = importlib.util.spec_from_file_location(
            "migration",
            os.path.join(
                os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
                "migrations",
                "models",
                "1_20250717225000_simplify_account_status.py",
            ),
        )
        migration_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(migration_module)
        upgrade = migration_module.upgrade

        # 获取迁移SQL
        migration_sql = await upgrade(conn)

        logger.info("执行迁移SQL...")
        logger.debug(f"迁移SQL: {migration_sql}")

        # 执行迁移
        await conn.execute_script(migration_sql)

        logger.info("🎉 迁移应用成功！")

    except Exception as e:
        logger.error(f"❌ 迁移应用失败: {str(e)}")
        raise

    finally:
        await Tortoise.close_connections()


if __name__ == "__main__":
    asyncio.run(apply_migration())
