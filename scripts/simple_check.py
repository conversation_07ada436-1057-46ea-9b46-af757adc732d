#!/usr/bin/env python3
"""
简单检查数据库中账号状态的分布
"""

import asyncio
import os
import sys

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tortoise import Tortoise

from log import logger
from models.system.crawler import CrawlerCookiesAccount
from settings.config import settings


async def simple_check():
    """简单检查数据库状态"""

    # 初始化数据库连接
    await Tortoise.init(config=settings.tortoise_orm)

    try:
        logger.info("检查数据库中的账号状态分布...")

        # 检查总数
        total_count = await CrawlerCookiesAccount.all().count()
        logger.info(f"总账号数: {total_count}")

        if total_count == 0:
            logger.info("数据库中没有账号数据")
            return

        # 检查各状态分布
        status_0_count = await CrawlerCookiesAccount.filter(status=0).count()
        status_1_count = await CrawlerCookiesAccount.filter(status=1).count()
        status_neg1_count = await CrawlerCookiesAccount.filter(status=-1).count()

        logger.info("所有账号状态分布:")
        logger.info(f"  可用(0): {status_0_count}")
        logger.info(f"  使用中(1): {status_1_count}")
        logger.info(f"  不可用(-1): {status_neg1_count}")

        # 检查非删除账号
        active_total = await CrawlerCookiesAccount.filter(is_deleted=False).count()
        active_0 = await CrawlerCookiesAccount.filter(status=0, is_deleted=False).count()
        active_1 = await CrawlerCookiesAccount.filter(status=1, is_deleted=False).count()
        active_neg1 = await CrawlerCookiesAccount.filter(status=-1, is_deleted=False).count()

        logger.info("活跃账号状态分布:")
        logger.info(f"  总活跃账号: {active_total}")
        logger.info(f"  可用(0): {active_0}")
        logger.info(f"  使用中(1): {active_1}")
        logger.info(f"  不可用(-1): {active_neg1}")

        # 检查是否有其他状态值
        other_count = total_count - status_0_count - status_1_count - status_neg1_count
        if other_count > 0:
            logger.warning(f"发现 {other_count} 个账号有异常状态值")
            # 查找异常状态值
            all_accounts = await CrawlerCookiesAccount.all()
            unique_statuses = set()
            for account in all_accounts:
                unique_statuses.add(account.status)
            logger.warning(f"所有状态值: {sorted(unique_statuses)}")
        else:
            logger.info("✅ 所有状态值都在预期范围内")

    except Exception as e:
        logger.error(f"❌ 检查过程中发生错误: {str(e)}")
        raise

    finally:
        await Tortoise.close_connections()


if __name__ == "__main__":
    asyncio.run(simple_check())
