#!/usr/bin/env python3
"""
系统集成测试脚本

验证账号状态简化后的完整业务流程
"""

import asyncio
import logging
import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from tortoise import Tortoise

from models.system.crawler import CrawlerCookiesAccount
from pkg.crawler_account.account_manager import AccountManager
from settings.config import settings

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def setup_test_data():
    """创建测试数据"""
    logger.info("创建测试数据...")

    # 清理现有测试数据
    await CrawlerCookiesAccount.filter(account_name__startswith="integration_test_").delete()

    # 创建测试账号
    test_accounts = [
        {
            "account_name": "integration_test_available_1",
            "platform_name": "douyin",
            "status": 0,  # 可用
            "cookies": "test_cookies_1",
            "is_deleted": False,
        },
        {
            "account_name": "integration_test_available_2",
            "platform_name": "douyin",
            "status": 0,  # 可用
            "cookies": "test_cookies_2",
            "is_deleted": False,
        },
        {
            "account_name": "integration_test_unavailable",
            "platform_name": "douyin",
            "status": -1,  # 不可用
            "cookies": "test_cookies_unavailable",
            "is_deleted": False,
        },
        {
            "account_name": "integration_test_trendinsight",
            "platform_name": "trendinsight",
            "status": 0,  # 可用
            "cookies": "test_trendinsight_cookies",
            "is_deleted": False,
        },
    ]

    for account_data in test_accounts:
        await CrawlerCookiesAccount.create(**account_data)

    logger.info(f"创建了 {len(test_accounts)} 个测试账号")


async def test_account_manager_workflow():
    """测试账号管理器完整工作流程"""
    logger.info("测试账号管理器工作流程...")

    manager = AccountManager()

    # 1. 测试获取可用账号
    logger.info("1. 测试获取可用账号")
    account1 = await manager.get_available_account("douyin")
    assert account1 is not None
    assert account1.status == 0
    logger.info(f"✅ 成功获取账号: {account1.account_name}")

    # 2. 测试账号共享（多次获取相同账号）
    logger.info("2. 测试账号共享")
    account2 = await manager.get_available_account("douyin")
    assert account2 is not None
    # 可能获取到相同账号（共享使用）
    logger.info(f"✅ 再次获取账号: {account2.account_name}")

    # 3. 测试并发获取账号
    logger.info("3. 测试并发获取账号")
    tasks = [manager.get_available_account("douyin") for _ in range(5)]
    concurrent_accounts = await asyncio.gather(*tasks)
    assert all(acc is not None for acc in concurrent_accounts)
    logger.info(f"✅ 并发获取 {len(concurrent_accounts)} 个账号成功")

    # 4. 测试获取平台统计
    logger.info("4. 测试获取平台统计")
    stats = await manager.get_platform_account_stats("douyin")
    assert stats["platform_name"] == "douyin"
    assert stats["total_count"] >= 2
    assert stats["available_count"] >= 2
    assert stats["unavailable_count"] >= 1
    assert "availability_rate" in stats
    logger.info(f"✅ 平台统计: {stats}")

    # 5. 测试标记账号不可用
    logger.info("5. 测试标记账号不可用")
    await manager.mark_account_unavailable(account1.id, "集成测试")

    # 验证账号状态已更新
    updated_account = await CrawlerCookiesAccount.get(id=account1.id)
    assert updated_account.status == -1
    logger.info(f"✅ 账号已标记为不可用: {updated_account.account_name}")

    # 6. 测试获取账号状态
    logger.info("6. 测试获取账号状态")
    status_info = await manager.get_account_status(account1.id)
    assert status_info["status"] == -1
    assert status_info["status_text"] == "不可用"
    logger.info(f"✅ 账号状态: {status_info}")

    # 7. 测试不同平台
    logger.info("7. 测试不同平台")
    trendinsight_account = await manager.get_available_account("trendinsight")
    assert trendinsight_account is not None
    assert trendinsight_account.platform_name == "trendinsight"
    logger.info(f"✅ 获取TrendInsight账号: {trendinsight_account.account_name}")


async def test_business_logic_compatibility():
    """测试业务逻辑兼容性"""
    logger.info("测试业务逻辑兼容性...")

    # 1. 测试直接数据库查询（模拟控制器逻辑）
    logger.info("1. 测试直接数据库查询")
    available_accounts = await CrawlerCookiesAccount.filter(
        platform_name="douyin", status=0, is_deleted=False  # 只查询可用状态
    ).all()

    assert len(available_accounts) >= 1
    logger.info(f"✅ 查询到 {len(available_accounts)} 个可用账号")

    # 2. 测试状态过滤
    logger.info("2. 测试状态过滤")
    unavailable_accounts = await CrawlerCookiesAccount.filter(
        platform_name="douyin", status=-1, is_deleted=False  # 不可用状态
    ).all()

    assert len(unavailable_accounts) >= 1
    logger.info(f"✅ 查询到 {len(unavailable_accounts)} 个不可用账号")

    # 3. 验证不存在status=1的账号
    logger.info("3. 验证不存在使用中状态的账号")
    in_use_accounts = await CrawlerCookiesAccount.filter(status=1, is_deleted=False).count()  # 使用中状态（应该不存在）

    assert in_use_accounts == 0
    logger.info("✅ 确认没有使用中状态的账号")


async def test_error_handling():
    """测试错误处理"""
    logger.info("测试错误处理...")

    manager = AccountManager()

    # 1. 测试获取不存在平台的账号
    logger.info("1. 测试获取不存在平台的账号")
    try:
        await manager.get_available_account("nonexistent_platform")
        assert False, "应该抛出异常"
    except Exception as e:
        logger.info(f"✅ 正确抛出异常: {type(e).__name__}")

    # 2. 测试获取不存在账号的状态
    logger.info("2. 测试获取不存在账号的状态")
    try:
        await manager.get_account_status(99999)
        assert False, "应该抛出异常"
    except Exception as e:
        logger.info(f"✅ 正确抛出异常: {type(e).__name__}")

    # 3. 测试标记不存在的账号为不可用
    logger.info("3. 测试标记不存在的账号为不可用")
    try:
        await manager.mark_account_unavailable(99999, "测试")
        assert False, "应该抛出异常"
    except Exception as e:
        logger.info(f"✅ 正确抛出异常: {type(e).__name__}")


async def cleanup_test_data():
    """清理测试数据"""
    logger.info("清理测试数据...")
    deleted_count = await CrawlerCookiesAccount.filter(account_name__startswith="integration_test_").delete()
    logger.info(f"清理了 {deleted_count} 个测试账号")


async def run_integration_test():
    """运行完整的集成测试"""
    try:
        # 初始化数据库连接
        await Tortoise.init(config=settings.tortoise_orm)

        logger.info("🚀 开始系统集成测试")

        # 设置测试数据
        await setup_test_data()

        # 运行测试
        await test_account_manager_workflow()
        await test_business_logic_compatibility()
        await test_error_handling()

        logger.info("🎉 所有集成测试通过！")

    except Exception as e:
        logger.error(f"❌ 集成测试失败: {e}")
        raise
    finally:
        # 清理测试数据
        await cleanup_test_data()

        # 关闭数据库连接
        await Tortoise.close_connections()


if __name__ == "__main__":
    asyncio.run(run_integration_test())
