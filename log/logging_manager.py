"""
配置驱动的 Loguru 日志管理器

基于 dynaconf 配置自动设置 loguru 日志系统
"""
import sys
import logging
from pathlib import Path
from typing import Dict, Any, Optional, List
from loguru import logger as loguru_logger

from settings import settings


class LoggingManager:
    """基于配置的日志管理器"""
    
    def __init__(self):
        self._handlers_ids: List[int] = []
        self._sql_handler_configured = False
        self._is_initialized = False
    
    def setup_logger(self) -> Any:
        """根据配置设置 loguru 日志系统"""
        if self._is_initialized:
            return loguru_logger
        
        try:
            # 验证配置
            settings.validators.validate()
            
            # 移除默认处理器
            loguru_logger.remove()
            
            # 获取日志配置
            logging_config = settings.logging
            
            # 设置控制台日志
            if logging_config.console.enabled:
                self._setup_console_logger(logging_config.console, logging_config)
            
            # 设置文件日志
            if logging_config.file.enabled:
                self._setup_file_logger(logging_config.file, logging_config)
            
            # 设置 JSON 日志
            if logging_config.json.enabled:
                self._setup_json_logger(logging_config.json, logging_config)
            
            # 设置 SQL 日志
            if logging_config.sql.enabled:
                self._setup_sql_logger(logging_config.sql)
            
            self._is_initialized = True
            loguru_logger.info("日志系统初始化完成", extra={
                "console_enabled": logging_config.console.enabled,
                "file_enabled": logging_config.file.enabled,
                "json_enabled": logging_config.json.enabled,
                "sql_enabled": logging_config.sql.enabled,
                "handlers_count": len(self._handlers_ids)
            })
            
        except Exception as e:
            # 如果配置失败，使用基本配置
            loguru_logger.remove()
            loguru_logger.add(sys.stderr, level="INFO")
            loguru_logger.error(f"日志配置失败，使用默认配置: {e}")
        
        return loguru_logger
    
    def _get_format(self, handler_config: Any, global_config: Any) -> str:
        """获取日志格式"""
        return getattr(handler_config, 'format', None) or global_config.format
    
    def _setup_console_logger(self, config: Any, global_config: Any):
        """设置控制台日志处理器"""
        try:
            handler_id = loguru_logger.add(
                sink=sys.stderr,
                level=config.level,
                format=self._get_format(config, global_config),
                colorize=config.colorize,
                enqueue=False,  # 控制台不需要异步
                catch=True,
                backtrace=True,
                diagnose=True
            )
            self._handlers_ids.append(handler_id)
            loguru_logger.debug(f"控制台日志处理器已添加: ID={handler_id}")
        except Exception as e:
            loguru_logger.error(f"设置控制台日志失败: {e}")
    
    def _setup_file_logger(self, config: Any, global_config: Any):
        """设置文件日志处理器"""
        try:
            # 确保日志目录存在
            log_path = Path(config.path)
            log_path.parent.mkdir(parents=True, exist_ok=True)
            
            handler_id = loguru_logger.add(
                sink=str(log_path),
                level=config.level,
                format=self._get_format(config, global_config),
                rotation=config.rotation,
                retention=config.retention,
                compression=config.compression,
                enqueue=config.enqueue,
                encoding="utf-8",
                catch=True,
                backtrace=True,
                diagnose=True
            )
            self._handlers_ids.append(handler_id)
            loguru_logger.debug(f"文件日志处理器已添加: ID={handler_id}, 路径={log_path}")
        except Exception as e:
            loguru_logger.error(f"设置文件日志失败: {e}")
    
    def _setup_json_logger(self, config: Any, global_config: Any):
        """设置 JSON 结构化日志处理器"""
        try:
            # 确保日志目录存在
            log_path = Path(config.path)
            log_path.parent.mkdir(parents=True, exist_ok=True)
            
            handler_id = loguru_logger.add(
                sink=str(log_path),
                level=config.level,
                serialize=config.serialize,
                rotation=config.rotation,
                retention=config.retention,
                compression=config.compression,
                enqueue=config.enqueue,
                encoding="utf-8",
                catch=True,
                backtrace=True,
                diagnose=True
            )
            self._handlers_ids.append(handler_id)
            loguru_logger.debug(f"JSON 日志处理器已添加: ID={handler_id}, 路径={log_path}")
        except Exception as e:
            loguru_logger.error(f"设置 JSON 日志失败: {e}")
    
    def _setup_sql_logger(self, config: Any):
        """设置 SQL 日志处理器"""
        if self._sql_handler_configured:
            return
        
        try:
            # 集成现有的 SQL 日志功能
            from settings.sql_logging import enable_sql_logging
            enable_sql_logging(
                level=config.level,
                format_string=config.format
            )
            self._sql_handler_configured = True
            loguru_logger.debug("SQL 日志处理器已配置")
        except Exception as e:
            loguru_logger.error(f"设置 SQL 日志失败: {e}")
    
    def reload_configuration(self):
        """重新加载配置并重新设置日志系统"""
        try:
            # 禁用 SQL 日志
            if self._sql_handler_configured:
                from settings.sql_logging import disable_sql_logging
                disable_sql_logging()
                self._sql_handler_configured = False
            
            # 移除现有处理器
            for handler_id in self._handlers_ids:
                loguru_logger.remove(handler_id)
            self._handlers_ids.clear()
            
            # 重置初始化状态
            self._is_initialized = False
            
            # 重新设置
            return self.setup_logger()
        except Exception as e:
            loguru_logger.error(f"重新加载日志配置失败: {e}")
            return loguru_logger
    
    def bind_context(self, **context) -> Any:
        """绑定上下文信息到日志记录器"""
        return loguru_logger.bind(**context)
    
    def get_logger(self, name: Optional[str] = None) -> Any:
        """获取带名称的日志记录器"""
        if name:
            return loguru_logger.bind(logger_name=name)
        return loguru_logger
    
    def set_level(self, level: str):
        """动态设置日志级别"""
        try:
            # 临时更新配置
            old_level = settings.logging.level
            settings.set('logging.level', level.upper())
            
            # 重新加载配置
            self.reload_configuration()
            
            loguru_logger.info(f"日志级别已从 {old_level} 调整为 {level.upper()}")
        except Exception as e:
            loguru_logger.error(f"设置日志级别失败: {e}")
    
    def get_handlers_info(self) -> Dict[str, Any]:
        """获取当前处理器信息"""
        return {
            "handlers_count": len(self._handlers_ids),
            "handlers_ids": self._handlers_ids,
            "sql_configured": self._sql_handler_configured,
            "initialized": self._is_initialized
        }


# 全局日志管理器实例
_logging_manager = LoggingManager()


def get_logging_manager() -> LoggingManager:
    """获取日志管理器实例"""
    return _logging_manager


def setup_logging() -> Any:
    """初始化日志系统"""
    return _logging_manager.setup_logger()


def get_logger(name: Optional[str] = None) -> Any:
    """获取日志记录器"""
    return _logging_manager.get_logger(name)


def bind_context(**context) -> Any:
    """绑定上下文"""
    return _logging_manager.bind_context(**context)


def reload_logging() -> Any:
    """重新加载日志配置"""
    return _logging_manager.reload_configuration()


def set_log_level(level: str):
    """动态调整日志级别"""
    _logging_manager.set_level(level)


def get_handlers_info() -> Dict[str, Any]:
    """获取处理器信息"""
    return _logging_manager.get_handlers_info()