"""
日志模块 - 保持向后兼容性

这个文件保持现有的导入接口不变，内部使用新的配置驱动日志管理器
"""
from .logging_manager import (
    setup_logging,
    get_logger,
    bind_context,
    reload_logging,
    set_log_level,
    get_handlers_info,
    get_logging_manager
)

# 保持向后兼容的接口
class Loggin:
    """保持向后兼容的日志配置类"""
    
    def __init__(self):
        self.manager = get_logging_manager()
        # 保持向后兼容的属性
        try:
            from settings import settings
            debug = settings.debug
            if debug:
                self.level = "DEBUG"
            else:
                self.level = "INFO"
        except:
            self.level = "INFO"
    
    def setup_logger(self):
        """设置日志记录器 - 向后兼容接口"""
        return setup_logging()
    
    def _setup_tortoise_logging(self):
        """向后兼容 - Tortoise ORM 日志设置"""
        # 新版本中这个功能已经集成到配置中
        pass


# 创建实例并初始化
loggin = Loggin()
logger = loggin.setup_logger()

# 导出常用接口 (保持向后兼容)
__all__ = [
    'loggin',
    'logger',
    'get_logger',
    'bind_context',
    'reload_logging',
    'set_log_level',
    'get_handlers_info'
]