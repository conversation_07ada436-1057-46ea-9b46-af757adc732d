# TrendInsight 控制器示例代码

本目录包含TrendInsight控制器的示例调用代码，帮助开发者理解和使用各个控制器的功能。

## 示例列表

### 1. 关键词同步示例

- **文件**: `sync_keyword_videos_example.py`
- **功能**: 演示如何使用`KeywordSyncController`同步关键词相关的视频数据
- **运行方式**:

  ```bash
  python controllers/trendinsight/examples/sync_keyword_videos_example.py
  ```

### 2. 作者同步示例

#### 2.1 快速开始示例
- **文件**: `quick_start_sync_author.py`
- **功能**: 最简单的作者视频同步示例，适合快速测试和学习
- **特点**: 演示基本的作者同步流程，显示同步结果统计
- **运行方式**:

  ```bash
  python controllers/trendinsight/examples/quick_start_sync_author.py
  ```

#### 2.2 完整功能示例
- **文件**: `sync_author_videos_example.py`
- **功能**: 展示所有功能特性的详细示例
- **特点**:
  - 单个作者详细同步演示
  - 批量作者同步示例
  - API 调用方式演示
  - 详细的结果展示和错误处理
- **运行方式**:

  ```bash
  python controllers/trendinsight/examples/sync_author_videos_example.py
  ```

### 3. 数据库连接测试

- **文件**: `test_db_connection.py`
- **功能**: 测试数据库连接是否正常，验证环境配置
- **特点**: 简单的连接测试，显示数据库状态
- **运行方式**:

  ```bash
  python controllers/trendinsight/examples/test_db_connection.py
  ```

## 使用说明

1. 确保已在项目根目录下安装所有依赖:

   ```bash
   pip install -r requirements.txt
   ```

2. 确保数据库配置正确:

   - 检查`settings/config.py`中的数据库配置
   - 确保数据库服务正在运行
   - 确保已运行数据库迁移脚本

3. 运行示例代码:

   ```bash
   python controllers/trendinsight/examples/示例文件名.py
   ```

4. 查看输出结果，了解控制器的使用方法和返回数据结构

## 故障排除

### 常见错误及解决方案

#### 1. 数据库连接错误
```
❌ 同步失败: 500: 同步作者和视频失败: default_connection for the model <class 'models.trendinsight.models.TrendInsightAuthor'> cannot be None
```

**解决方案：**
1. 首先运行数据库连接测试：
   ```bash
   python controllers/trendinsight/examples/test_db_connection.py
   ```

2. 如果测试失败，检查：
   - 数据库文件是否存在（`db.sqlite3`）
   - 数据库配置是否正确（`settings/config.py`）
   - 是否已运行数据库迁移

3. 初始化数据库：
   ```bash
   make db-quick  # 快速初始化
   # 或
   make db-init   # 安全初始化
   ```

#### 2. 模块导入错误
```
❌ ModuleNotFoundError: No module named 'xxx'
```

**解决方案：**
- 确保在项目根目录运行示例
- 检查 Python 路径设置
- 安装所有依赖：`pip install -r requirements.txt`

#### 3. Cookies 无效错误
```
❌ 获取作者详情失败
```

**解决方案：**
- 检查数据库中的 TrendInsight cookies 是否有效
- 更新 cookies 数据

## 作者同步功能详细说明

### 核心功能
`sync_author_videos()` 方法提供以下功能：

1. **作者信息同步**
   - 检查作者是否已存在于 `trendinsight_author` 表
   - 不存在则通过 TrendInsight API 获取作者详情并创建记录
   - 存在则使用现有记录进行后续操作

2. **视频数据同步**
   - 通过 TrendInsight API 获取用户热门视频列表（最近30天）
   - 智能批量处理 `douyin_aweme` 表记录（创建和更新）

3. **关联关系管理**
   - 在 `trendinsight_video_related` 表中创建关联记录
   - 避免重复创建，支持增量同步

### 配置要求

在运行作者同步示例之前，请确保：

1. **数据库中有有效的 TrendInsight cookies**
2. **数据库中有有效的抖音平台 cookies**
3. **将示例中的 `user_id` 替换为真实的 TrendInsight 用户ID**

### HTTP API 调用示例

```bash
curl -X POST "http://localhost:8000/api/v1/trendinsight/author/sync" \
     -H "Content-Type: application/json" \
     -d '{"user_id": "heicgcbajggjdjjaefj"}'
```

### 返回数据结构

```python
{
    "author_action": "created",  # 或 "existing"
    "author_data": {
        "user_id": "heicgcbajggjdjjaefj",
        "user_name": "科技博主",
        "fans_count": "10000",
        "item_count": "500"
    },
    "videos_synced": 15,
    "videos_failed": 0,
    "relations_created": 15,
    "relations_existing": 0,
    "aweme_ids": ["7123456789012345678", "7123456789012345679"],
    "errors": []
}
```

## 注意事项

- 示例代码会自动初始化数据库连接，但需要确保数据库配置正确
- 需要确保TrendInsight API配置正确，包括账号信息等
- 某些示例可能需要网络连接来调用外部API
- 运行示例前请确保数据库已正确初始化并应用了所有迁移
- 作者同步示例中的 `user_id` 需要替换为真实的 TrendInsight 用户ID
- 系统会自动处理重复数据，不会创建重复记录
