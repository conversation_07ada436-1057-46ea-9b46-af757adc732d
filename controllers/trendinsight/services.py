# -*- coding: utf-8 -*-
"""
TrendInsight 控制器服务模块 - 向后兼容包装器

这个文件提供向后兼容性，将导入重定向到正确的服务层模块。
推荐直接使用 services.trendinsight 模块。
"""

import warnings

# 导入正确的服务类
from services.trendinsight import DouyinAwemeService as _DouyinAwemeService
from services.trendinsight import VideoTrendService as _VideoTrendService


class DouyinAwemeService(_DouyinAwemeService):
    """
    DouyinAweme 数据服务 - 向后兼容包装器
    
    这个类提供向后兼容性。推荐直接使用 services.trendinsight.DouyinAwemeService。
    """
    
    def __init__(self, *args, **kwargs):
        warnings.warn(
            "controllers.trendinsight.services.DouyinAwemeService 已废弃，"
            "请使用 services.trendinsight.DouyinAwemeService",
            DeprecationWarning,
            stacklevel=2
        )
        super().__init__(*args, **kwargs)


class VideoTrendService(_VideoTrendService):
    """
    视频趋势服务 - 向后兼容包装器
    
    这个类提供向后兼容性。推荐直接使用 services.trendinsight.VideoTrendService。
    """
    
    def __init__(self, *args, **kwargs):
        warnings.warn(
            "controllers.trendinsight.services.VideoTrendService 已废弃，"
            "请使用 services.trendinsight.VideoTrendService",
            DeprecationWarning,
            stacklevel=2
        )
        super().__init__(*args, **kwargs)


__all__ = [
    "DouyinAwemeService",
    "VideoTrendService",
]
