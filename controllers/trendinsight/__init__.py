"""
TrendInsight 控制器模块
"""

from .author_sync_controller import AuthorSync<PERSON>ontroller
from .keyword_sync_controller import KeywordSyncController
from ..douyin.video.controller import DouyinVideoController

# 创建控制器实例，保持向后兼容
author_sync_controller = AuthorSyncController()
keyword_sync_controller = KeywordSyncController()
video_process_controller = DouyinVideoController()  # 使用新的统一控制器

__all__ = [
    "AuthorSyncController",
    "KeywordSyncController",
    "DouyinVideoController",  # 更新为新的控制器类名
    "author_sync_controller",
    "keyword_sync_controller",
    "video_process_controller",  # 保持实例名不变，确保向后兼容
]
