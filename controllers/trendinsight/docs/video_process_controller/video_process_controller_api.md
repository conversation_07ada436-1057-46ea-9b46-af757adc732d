# VideoProcessController 接口说明

> 状态：草案（Draft）

## 1. 概述
- 负责视频拉取、解析、抽帧、特征提取、指标统计与洞察产出。
- 用途：趋势洞察、热度追踪、情绪/标签分析、素材库沉淀。

## 2. 术语
- Task：一次视频处理任务，包含来源、参数与处理流水线。
- Job：Task 下的子阶段（下载、转码、抽帧、分析）。

## 3. 鉴权与通用规范
- 鉴权：Bearer Token 或内部网关签名（待定）。
- Header：`Content-Type: application/json; charset=utf-8`
- 返回格式：统一包裹 `{ code, message, data, traceId }`

## 4. 接口列表
### 4.1 创建处理任务
- 方法：POST `/api/trendinsight/video/tasks`
- 请求：
```json
{
  "sourceUrl": "string",
  "priority": 0,
  "analysis": {"scene": true, "asr": true, "ocr": false, "faces": true},
  "callbacks": {"done": "https://example.com/hook", "progress": null},
  "ext": {"bizId": "optional"}
}
```
- 响应：
```json
{"code":0,"message":"ok","data":{"taskId":"vid_123"},"traceId":"..."}
```

### 4.2 查询任务详情
- 方法：GET `/api/trendinsight/video/tasks/{taskId}`
- 响应（示例）：
```json
{"code":0,"message":"ok","data":{"taskId":"vid_123","status":"running","progress":42,"stages":[{"name":"download","status":"done"},{"name":"transcode","status":"done"},{"name":"analyze","status":"running"}]},"traceId":"..."}
```

### 4.3 取消任务
- 方法：POST `/api/trendinsight/video/tasks/{taskId}:cancel`
- 响应：`{"code":0,"message":"ok","data":null,"traceId":"..."}`

### 4.4 获取分析结果
- 方法：GET `/api/trendinsight/video/tasks/{taskId}/result`
- 响应（示例片段）：
```json
{
  "code":0,
  "message":"ok",
  "data":{
    "summary":{"duration":123.4,"fps":25,"scenes":9},
    "tags":["sports","outdoor"],
    "sentiment":{"pos":0.62,"neg":0.15},
    "faces":[{"id":"p1","appear":[[1.2,4.8],[30.0,36.2]]}],
    "asr":[{"time":12.3,"text":"..."}],
    "ocr":[]
  },
  "traceId":"..."
}
```

## 5. 错误码
- 0：成功
- 1001：参数非法
- 2001：下载失败
- 2002：转码失败
- 3001：分析失败
- 5000：内部错误

## 6. 性能与配额
- 单任务视频时长上限：30min（可配）
- 并发上限：按租户/Token 配置

## 7. 变更记录
- v0.1 初始化草案
