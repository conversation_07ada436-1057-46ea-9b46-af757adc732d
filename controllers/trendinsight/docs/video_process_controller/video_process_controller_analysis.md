# VideoProcessController 处理逻辑分析

本文档总结 `controllers/trendinsight/video_process_controller.py` 的核心职责、方法流程、数据流和交互关系，并给出关键设计要点与优化建议。

## 核心职责
- 负责与抖音相关视频数据在“趋势洞察”场景下的处理与入库。
- 提供任务级批处理入口，支撑按关键词、按视频 ID/链接等多种方式处理。
- 调用 models 层函数完成趋势评分计算与更新。

## 主要类与方法
- 类：`VideoProcessController`
  - `fetch_video_trend_scores(...)`：按视频列表批量拉取并计算趋势评分，更新存储。
  - `process_keyword_video_index_data(...)`：按照关键词拉取视频索引并处理，更新趋势洞察视频数据。
  - `_convert_video_info_to_aweme_data(...)`：将视频原始信息转换为 aweme_data 格式（含时间解析与字段映射）。
  - 其余辅助/编排方法（如批处理入口、日志封装等）。

注：根据最新代码，已将以下局部导入上移至文件顶部，避免函数内重复导入：
- `from utils.time_utils import safe_convert_to_datetime`
- `from models.trendinsight.models import fetch_and_store_video_trend_score, update_trendinsight_video`

## 处理流程概览
1) 批量趋势评分：
   - 输入：视频 ID 列表或视频详情集合
   - 处理：遍历调用 `fetch_and_store_video_trend_score` 计算并更新
   - 输出：成功/失败计数与日志

2) 关键词索引处理：
   - 输入：关键词、时间范围、分页/并发配置
   - 处理：
     - 分页拉取索引数据
     - 转换视频结构 `_convert_video_info_to_aweme_data`
     - 调用 `update_trendinsight_video` 更新入库
   - 输出：处理条目数、失败重试数、日志

3) 结构转换 `_convert_video_info_to_aweme_data`：
   - 将原始视频 JSON 字段映射到 aweme_data 统一结构
   - 使用 `safe_convert_to_datetime` 进行时间字段安全解析

## 数据流
- 输入源：RPC/服务层获取的视频列表或关键词检索得到的索引数据
- 核心处理：
  - 评分：`fetch_and_store_video_trend_score(aweme_id, extra_meta)`
  - 更新：`update_trendinsight_video(aweme_data, extra_meta)`
- 持久化：models 层统一完成（数据库或缓存）

## 日志与错误处理
- 统一使用控制器 logger 记录 info/error
- 关键位置 try/except 包裹，出现异常时不中断批处理，累计失败并继续
- 示例：第 214 行处记录批量任务失败日志：`"批量获取趋势数据任务执行失败: {e}"`

## 关键设计要点
- 将局部导入上移，减少重复与冷启动开销。
- 分页/并发可配置，兼顾吞吐与稳定性。
- 辅助转换函数集中处理数据质量与时间解析。

## 改进建议
- 增加重试与退避策略，区分可恢复与不可恢复错误。
- 完善指标监控（处理耗时、成功率、外部依赖错误率）。
- 引入批处理任务 ID，贯穿全链路日志便于排障。
- 增加幂等保证（以 aweme_id+时间窗口去重）。
