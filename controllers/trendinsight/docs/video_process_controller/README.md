# VideoProcessController 使用说明

本指南帮助你在本项目中快速落地 `VideoProcessController` 的使用与集成。更详细的接口定义见同目录的 `video_process_controller_api.md`。

## 目录结构
```
controllers/
  trendinsight/
    video_process_controller.py
    docs/
      video_process_controller/
        README.md
        video_process_controller_api.md
```

## 快速开始

### 1. 导入与初始化
```python
from controllers.douyin.video.video_process_controller import VideoProcessController

controller = VideoProcessController()
```

### 2. 批量计算并落库视频趋势分数
```python
videos = [
    {"aweme_id": "1234567890", "title": "示例视频1"},
    {"aweme_id": "9876543210", "title": "示例视频2"},
    # 也可以仅传 aweme_id 字符串："1234567890"
]

config = {"concurrency": 8, "batch_size": 50, "ignore_errors": True}

result = controller.fetch_video_trend_scores(videos, config=config)
print(result)  # {"success": 120, "failed": 3, "errors": [ ...可选... ]}
```

### 3. 基于关键词拉取索引并落库趋势洞察数据
```python
summary = controller.process_keyword_video_index_data(
    keyword="直播带货",
    time_range=("2024-01-01", "2024-12-31"),
    paging={"page_size": 50, "limit": 1000},
    concurrency=8,
)
print(summary)  # {"success": 200, "failed": 5, "skipped": 12}
```

## 常见参数说明
- `concurrency`：并发度，结合外部 API 限流合理设置，推荐 4~16。
- `batch_size`：批处理大小，平衡吞吐与内存占用，推荐 50~200。
- `ignore_errors`：是否在单条失败时继续执行并汇总失败项。
- `time_range`：时间过滤区间，例如 `(start_dt, end_dt)`，建议传入 `datetime` 或 ISO 字符串。
- `paging.limit`：总处理条数上限，便于演示或分批跑任务。

## 日志与监控
- 建议在调用层为任务生成 `task_id`，并在日志上下文中携带 `keyword`、`aweme_id` 等字段。
- 监控指标：成功率、P95/P99 耗时、外部依赖失败率、重试次数、限流命中率等。

## 错误处理建议
- 网络/限流类错误：指数退避（如 0.5s, 1s, 2s, 4s...）+ 最大重试次数。
- 数据校验错误：跳过并记录到 `failed` 与错误摘要，避免阻塞大批量任务。
- 不可恢复错误：记录 `error` 日志并上报监控。

## 与模型层的契约
- `fetch_and_store_video_trend_score(aweme_id, extra)`：要求幂等；多次调用不会产生脏数据。
- `update_trendinsight_video(aweme_data, meta)`：以 `aweme_id` 为幂等键（可结合时间窗口）；缺失关键字段应显式报错。

## 版本与变更
- 若后续代码实现有调整，请同步更新本 README 与 `video_process_controller_api.md`，并在变更记录中标注版本号与日期。
