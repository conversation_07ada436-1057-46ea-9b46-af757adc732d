# VideoProcessController 流程图（Mermaid 版）

以下使用 Mermaid 流程图描述两条核心流程：

## A. 批量趋势评分（fetch_video_trend_scores）

```mermaid
flowchart TD
    A0([开始]) --> A1[接收输入：视频ID列表/视频详情集合、配置]
    A1 --> A2{校验输入是否为空？}
    A2 -- 是 --> A3[记录警告并结束]
    A2 -- 否 --> A4[遍历视频项]
    A4 --> A5[提取 aweme_id 与必要元数据]
    A5 --> A6[调用 models.trendinsight.fetch_and_store_video_trend_score]
    A6 --> A7{成功？}
    A7 -- 是 --> A8[success += 1]
    A7 -- 否 --> A9[记录错误，failed += 1]
    A8 --> A10{是否还有待处理项？}
    A9 --> A10
    A10 -- 是 --> A4
    A10 -- 否 --> A11[汇总结果（成功/失败数）并结束]
```

## B. 关键词索引处理（process_keyword_video_index_data）

```mermaid
flowchart TD
    B0([开始]) --> B1[接收输入：关键词、时间范围、分页/并发配置]
    B1 --> B2[初始化分页游标/偏移]
    B2 --> B3[调用检索服务拉取一页索引数据]
    B3 --> B4{数据为空？}
    B4 -- 是 --> B5[结束]
    B4 -- 否 --> B6[遍历索引项]
    B6 --> B7[转换为 aweme_data：调用 _convert_video_info_to_aweme_data]
    B7 --> B8{aweme_data 是否有效？}
    B8 -- 否 --> B9[记录跳过并继续]
    B8 -- 是 --> B10[调用 models.trendinsight.update_trendinsight_video]
    B10 --> B11{成功？}
    B11 -- 是 --> B12[success += 1]
    B11 -- 否 --> B13[记录错误，failed += 1]
    B9 --> B14{是否还有索引项？}
    B12 --> B14
    B13 --> B14
    B14 -- 是 --> B6
    B14 -- 否 --> B15[更新分页游标，继续下一页]
    B15 --> B16{无更多分页？}
    B16 -- 否 --> B3
    B16 -- 是 --> B17[汇总成功/失败数并结束]
```

## C. 转换函数（_convert_video_info_to_aweme_data）

```mermaid
flowchart TD
    C0[输入：原始视频 JSON] --> C1[字段映射：aweme_id、title、author、stats、publish_time 等]
    C1 --> C2[时间解析：safe_convert_to_datetime]
    C2 --> C3[补充默认值与冗余校验]
    C3 --> C4[输出：标准化 aweme_data 结构]
```

## 关键控制点
- 输入校验与空值短路
- 分页/并发与节流控制
- 失败不中断策略与日志落盘
- 统一异常捕获，保证批处理鲁棒性
- 幂等更新：以 aweme_id 作为主键+窗口约束
