# VideoProcessController UML（类图与时序图）

以下为类结构关系与核心交互的 UML 描述，采用 Mermaid 语法，便于在 Markdown 渲染器中直接预览。

## 类图
```mermaid
classDiagram
    class VideoProcessController {
      +fetch_video_trend_scores(videos, config)
      +process_keyword_video_index_data(keyword, time_range, paging, concurrency)
      -_convert_video_info_to_aweme_data(video_info) aweme_data
    }

    class TrendInsightModels {
      +fetch_and_store_video_trend_score(aweme_id, extra)
      +update_trendinsight_video(aweme_data, meta)
    }

    class DouyinModels {
      +safe_convert_to_datetime(raw_ts) DateTime
    }

    VideoProcessController --> TrendInsightModels : uses
    VideoProcessController --> DouyinModels : uses
```

说明：
- `VideoProcessController` 为编排控制器，聚合业务流程；
- `TrendInsightModels` 抽象表示 `models.trendinsight.models` 中涉及的方法（文件中通过顶部导入使用：`fetch_and_store_video_trend_score`, `update_trendinsight_video`）；
- `DouyinModels` 抽象表示 `models.douyin.models` 中的 `safe_convert_to_datetime`；
- 代码中已将上述方法的局部导入提升至文件顶部，以减少重复与冷启动成本。

## 时序图 A：批量趋势评分（fetch_video_trend_scores）
```mermaid
sequenceDiagram
    participant Client
    participant Controller as VideoProcessController
    participant TI as TrendInsightModels

    Client->>Controller: fetch_video_trend_scores(videos, config)
    alt 空输入
        Controller-->>Client: return (0, 0) / 记录警告
    else 非空
        loop 遍历视频项
            Controller->>Controller: 提取 aweme_id / meta
            Controller->>TI: fetch_and_store_video_trend_score(aweme_id, extra)
            alt 成功
                TI-->>Controller: ok
                Controller->>Controller: success += 1
            else 失败
                TI-->>Controller: error
                Controller->>Controller: failed += 1, 记录错误
            end
        end
        Controller-->>Client: 汇总结果(success, failed)
    end
```

## 时序图 B：关键词索引处理（process_keyword_video_index_data）
```mermaid
sequenceDiagram
    participant Client
    participant Controller as VideoProcessController
    participant TI as TrendInsightModels
    participant DY as DouyinModels

    Client->>Controller: process_keyword_video_index_data(keyword, time_range, paging, concurrency)
    Controller->>Controller: 初始化分页游标/偏移
    loop 分页
        Controller->>Controller: 拉取一页索引数据
        alt 数据为空
            Controller-->>Client: 结束
        else 有数据
            loop 遍历索引项
                Controller->>Controller: _convert_video_info_to_aweme_data(item)
                Controller->>DY: safe_convert_to_datetime(raw_ts)
                DY-->>Controller: datetime
                alt aweme_data 有效
                    Controller->>TI: update_trendinsight_video(aweme_data, meta)
                    alt 成功
                        TI-->>Controller: ok
                        Controller->>Controller: success += 1
                    else 失败
                        TI-->>Controller: error
                        Controller->>Controller: failed += 1, 记录错误
                    end
                else 无效
                    Controller->>Controller: 跳过并记录
                end
            end
        end
    end
    Controller-->>Client: 汇总(success, failed)
```

## 备注
- Mermaid 语法可在多数文档平台直接渲染；若在本地预览，请使用支持 Mermaid 的 Markdown 预览插件。
- 类图采用抽象依赖，实际实现通过顶部导入 `from models.trendinsight.models import fetch_and_store_video_trend_score, update_trendinsight_video` 与 `from utils.time_utils import safe_convert_to_datetime` 完成调用。
