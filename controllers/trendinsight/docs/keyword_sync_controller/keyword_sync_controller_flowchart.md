# KeywordSyncController 流程图

## 主要业务流程图

### sync_keyword_videos 方法流程图

```mermaid
flowchart TD
    A[开始: sync_keyword_videos] --> B[初始化 KeywordSyncResponse]
    B --> C[计算关键词哈希值]
    C --> D[查询关键词是否存在]
    D --> E{关键词是否存在?}
    
    E -->|是| F[设置为 EXISTING]
    E -->|否| G[创建新关键词记录]
    
    F --> H[获取现有关键词数据]
    G --> I{关键词创建成功?}
    
    I -->|是| J[设置为 CREATED]
    I -->|否| K[记录错误信息]
    
    H --> L[使用 TrendInsight API 搜索视频]
    J --> L
    K --> L
    
    L --> M{搜索成功且有结果?}
    
    M -->|否| N[记录搜索错误]
    M -->|是| O[转换视频数据格式]
    
    O --> P[获取aweme_id列表]
    P --> Q[批量查询DouyinAweme表]
    Q --> R[分类已存在和未存在的记录]
    R --> S{是否有新记录需要创建?}
    
    S -->|是| T[批量创建DouyinAweme记录]
    S -->|否| U[跳过创建步骤]
    
    T --> V{创建是否成功?}
    V -->|否| W[记录创建错误]
    V -->|是| X[检查是否有记录需要更新]
    
    U --> X
    W --> X
    
    X --> Y{是否有记录需要更新?}
    Y -->|是| Z[批量更新DouyinAweme记录]
    Y -->|否| AA[跳过更新步骤]
    
    Z --> BB{更新是否成功?}
    BB -->|否| CC[记录更新错误]
    BB -->|是| DD[查询已存在的关联记录]
    
    AA --> DD
    CC --> DD
    
    DD --> EE[计算需要创建的新关联]
    EE --> FF{是否有新关联需要创建?}
    
    FF -->|是| GG[批量创建关联记录]
    FF -->|否| HH[跳过创建步骤]
    
    GG --> II{批量创建成功?}
    II -->|否| JJ[记录创建错误]
    II -->|是| KK[更新统计信息]
    
    HH --> KK
    JJ --> KK
    
    KK --> LL[检查视频数量是否变化]
    LL --> MM{视频数量是否变化?}
    
    MM -->|是| NN[更新关键词视频总数]
    MM -->|否| OO[保持现有数量]
    
    NN --> PP[更新响应数据中的视频数量]
    OO --> PP
    
    N --> PP
    PP --> QQ[返回 KeywordSyncResponse]
    QQ --> RR[结束]

    style A fill:#e1f5fe
    style RR fill:#e8f5e8
    style K fill:#ffebee
    style N fill:#ffebee
    style W fill:#ffebee
    style CC fill:#ffebee
    style JJ fill:#ffebee
```

### 方法调用关系图

```mermaid
flowchart TD
    A[sync_keyword_videos] --> B[_calculate_keyword_hash]
    A --> C[TrendInsightKeyword.filter]
    A --> D[TrendInsightKeyword.create]
    A --> E[search_info_by_keyword]
    
    E --> F[_get_trendinsight_client]
    F --> G[client_manager.create_async_client]
    F --> H[AsyncTrendInsightAPI]
    
    E --> I[AsyncTrendInsightAPI.search_info_by_keyword]
    
    A --> J[TrendInsightVideoMapper.videos_to_video_items]
    A --> K[TrendInsightVideoMapper.ensure_douyin_aweme_records]
    A --> L[TrendInsightVideoRelated.filter]
    A --> M[TrendInsightVideoRelated.bulk_create]
    A --> N[keyword_record.save]

    style A fill:#e3f2fd
    style B fill:#f3e5f5
    style E fill:#f3e5f5
    style F fill:#f3e5f5
```

### DouyinAweme 数据处理详细流程图

```mermaid
flowchart TD
    A[获取video_items和aweme_ids] --> B[批量查询DouyinAweme表]
    B --> C[使用aweme_id IN查询]
    C --> D[获取已存在的记录]
    
    D --> E[计算集合差异]
    E --> F{是否有新记录?}
    E --> G{是否有需要更新的记录?}
    
    F -->|是| H[准备新记录数据]
    F -->|否| I[跳过创建]
    
    H --> J[批量创建DouyinAweme记录]
    J --> K{创建成功?}
    K -->|是| L[记录创建统计]
    K -->|否| M[记录创建错误]
    
    G -->|是| N[准备更新数据]
    G -->|否| O[跳过更新]
    
    N --> P[批量更新DouyinAweme记录]
    P --> Q{更新成功?}
    Q -->|是| R[记录更新统计]
    Q -->|否| S[记录更新错误]
    
    I --> T[返回处理结果]
    L --> T
    M --> T
    O --> T
    R --> T
    S --> T
    
    T --> U[继续关联记录处理]

    style A fill:#e1f5fe
    style U fill:#e8f5e8
    style M fill:#ffcdd2
    style S fill:#ffcdd2
```

### 数据流图

```mermaid
flowchart LR
    A[输入: keyword] --> B[MD5 Hash]
    B --> C[Database Query]
    C --> D{Keyword Exists?}
    
    D -->|No| E[Create Keyword Record]
    D -->|Yes| F[Use Existing Record]
    
    E --> G[Keyword Record]
    F --> G
    
    G --> H[TrendInsight API Call]
    H --> I[Video Search Results]
    
    I --> J[Data Transformation]
    J --> K[Video Items & IDs]
    
    K --> L[DouyinAweme Processing]
    L --> M[Relation Check]
    
    M --> N[Batch Relation Creation]
    N --> O[Statistics Update]
    
    O --> P[KeywordSyncResponse]

    style A fill:#e8f5e8
    style P fill:#e3f2fd
```

### 错误处理流程图

```mermaid
flowchart TD
    A[开始处理] --> B[try-catch 包装]
    
    B --> C[关键词创建步骤]
    C --> D{创建成功?}
    D -->|否| E[添加错误到 errors 列表]
    D -->|是| F[继续下一步]
    E --> F
    
    F --> G[视频搜索步骤]
    G --> H{搜索成功?}
    H -->|否| I[添加错误到 errors 列表]
    H -->|是| J[继续下一步]
    I --> J
    
    J --> K[DouyinAweme 处理步骤]
    K --> L{处理成功?}
    L -->|否| M[添加错误到 errors 列表]
    L -->|是| N[继续下一步]
    M --> N
    
    N --> O[关联记录创建步骤]
    O --> P{创建成功?}
    P -->|否| Q[添加错误到 errors 列表]
    P -->|是| R[完成处理]
    Q --> R
    
    R --> S[返回结果包含所有错误信息]
    
    B --> T{发生致命异常?}
    T -->|是| U[抛出 HTTPException]
    T -->|否| S

    style E fill:#ffcdd2
    style I fill:#ffcdd2
    style M fill:#ffcdd2
    style Q fill:#ffcdd2
    style U fill:#f44336,color:#fff
```

### 状态转换图

```mermaid
stateDiagram-v2
    [*] --> Initializing: 开始同步
    
    Initializing --> KeywordChecking: 初始化完成
    
    KeywordChecking --> KeywordExists: 关键词存在
    KeywordChecking --> KeywordCreating: 关键词不存在
    
    KeywordCreating --> KeywordCreated: 创建成功
    KeywordCreating --> KeywordCreateFailed: 创建失败
    
    KeywordExists --> VideoSearching: 继续处理
    KeywordCreated --> VideoSearching: 继续处理
    KeywordCreateFailed --> VideoSearching: 继续处理(记录错误)
    
    VideoSearching --> VideoFound: 搜索到视频
    VideoSearching --> VideoNotFound: 未找到视频
    
    VideoFound --> RelationProcessing: 开始处理关联
    VideoNotFound --> Completed: 完成(记录错误)
    
    RelationProcessing --> RelationCreated: 关联创建成功
    RelationProcessing --> RelationFailed: 关联创建失败
    RelationProcessing --> RelationSkipped: 所有关联已存在
    
    RelationCreated --> StatsUpdating: 更新统计
    RelationFailed --> StatsUpdating: 更新统计(记录错误)
    RelationSkipped --> StatsUpdating: 更新统计
    
    StatsUpdating --> Completed: 同步完成
    
    Completed --> [*]: 返回结果
```

### 时序图

```mermaid
sequenceDiagram
    participant Client
    participant Controller as KeywordSyncController
    participant DB as Database
    participant API as TrendInsight API
    participant Mapper as TrendInsightVideoMapper

    Client->>Controller: sync_keyword_videos(keyword)
    
    Controller->>Controller: _calculate_keyword_hash(keyword)
    Controller->>DB: TrendInsightKeyword.filter(keyword_hash)
    DB-->>Controller: existing_keyword or None
    
    alt keyword not exists
        Controller->>DB: TrendInsightKeyword.create(...)
        DB-->>Controller: new_keyword
    end
    
    Controller->>Controller: _get_trendinsight_client()
    Controller->>API: search_info_by_keyword(keyword, ...)
    API-->>Controller: VideoSearchResponse
    
    Controller->>Mapper: keyword_videos_to_douyin_aweme_data_list(videos, keyword)
    Mapper-->>Controller: video_data_list, video_ids

    Controller->>Service: DouyinAwemeService.ensure_douyin_aweme_records(video_data_list, video_ids)
    Service-->>Controller: aweme_created, aweme_existing
    
    Controller->>DB: TrendInsightVideoRelated.filter(source_id, video_id__in)
    DB-->>Controller: existing_relations
    
    Controller->>DB: TrendInsightVideoRelated.bulk_create(new_relations)
    DB-->>Controller: created_relations
    
    Controller->>DB: keyword_record.save() [update video_count]
    DB-->>Controller: updated_keyword
    
    Controller-->>Client: KeywordSyncResponse
```

## 流程图说明

### 主流程特点
1. **容错性**: 每个步骤都有错误处理，单个步骤失败不会中断整个流程
2. **幂等性**: 可以重复执行，不会产生重复数据
3. **批量处理**: 使用批量操作提高性能
4. **状态跟踪**: 详细记录每个步骤的执行状态

### 关键决策点
1. **关键词存在性检查**: 决定是创建新记录还是使用现有记录
2. **搜索结果验证**: 检查API返回结果的有效性
3. **关联记录去重**: 避免创建重复的关联记录
4. **统计信息更新**: 只在数据变化时更新统计

### 性能优化点
1. **哈希查询**: 使用MD5哈希快速查找关键词
2. **批量操作**: 减少数据库往返次数
3. **条件更新**: 避免不必要的数据库写操作
4. **集合运算**: 使用集合操作快速计算差集