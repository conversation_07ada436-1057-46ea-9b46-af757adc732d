# KeywordSyncController UML 图

## 类图 (Class Diagram)

### 主要类结构

```mermaid
classDiagram
    class KeywordSyncController {
        -logger: logging.Logger
        -config: TrendInsightConfig
        +__init__()
        -_calculate_keyword_hash(keyword: str) str
        -_get_trendinsight_client() AsyncTrendInsightAPI
        +search_info_by_keyword(keyword: str, author_ids: List[str], category_id: str, date_type: int, label_type: int, duration_type: int) VideoSearchResponse
        +sync_keyword_videos(keyword: str) KeywordSyncResponse
    }

    class InboxService {
        +process_source_videos(user_inbox_source_related, video_list)
        +get_user_inbox_summary(user_uuid)
    }

    KeywordSyncController --> InboxService : 收件箱视频关联

    class TrendInsightConfig {
        +config_data: dict
        +get_config() dict
    }

    class AsyncTrendInsightAPI {
        -async_client: httpx.AsyncClient
        +__init__(async_client)
        +search_info_by_keyword(...) VideoSearchResponse
    }

    class TrendInsightKeyword {
        +id: int
        +keyword: str
        +keyword_hash: str
        +video_count: int
        +created_at: datetime
        +updated_at: datetime
        +filter(keyword_hash: str) QuerySet
        +create(...) TrendInsightKeyword
        +save()
    }

    class TrendInsightVideoRelated {
        +id: int
        +source_type: SourceType
        +source_id: int
        +video_id: str
        +platform: Platform
        +created_at: datetime
        +filter(...) QuerySet
        +bulk_create(relations: List) None
    }

    class TrendInsightVideoMapper {
        +keyword_videos_to_douyin_aweme_data_list(videos: List, source_keyword: str) Tuple[List[DouyinAwemeData], List[str]]
        +douyin_rpc_aweme_list_to_douyin_aweme_data_list(aweme_list: List, source_keyword: str) Tuple[List[DouyinAwemeData], List[str]]
    }

    class KeywordSyncResponse {
        +keyword_action: KeywordActionType
        +keyword_data: KeywordData
        +videos_synced: int
        +videos_failed: int
        +relations_created: int
        +relations_existing: int
        +aweme_ids: List[str]
        +video_items: List[DouyinAwemeData]
        +errors: List[str]
    }

    class KeywordData {
        +id: int
        +keyword: str
        +keyword_hash: str
        +video_count: int
        +created_at: datetime
        +updated_at: datetime
    }

    class DouyinAwemeData {
        +aweme_id: str
        +title: Optional[str]
        +desc: Optional[str]
        +create_time: Optional[int]
        +nickname: Optional[str]
        +user_id: Optional[str]
        +liked_count: str
        +comment_count: str
        +share_count: str
        +collected_count: str
        +source_keyword: str
    }

    class VideoSearchResponse {
        +is_success: bool
        +video_items: List[dict]
        +total_count: int
        +error_msg: str
    }

    class client_manager {
        +create_async_client() httpx.AsyncClient
    }

    %% 关联关系
    KeywordSyncController --> TrendInsightConfig
    KeywordSyncController --> AsyncTrendInsightAPI
    KeywordSyncController --> TrendInsightKeyword
    KeywordSyncController --> TrendInsightVideoRelated
    KeywordSyncController --> TrendInsightVideoMapper
    KeywordSyncController --> KeywordSyncResponse
    KeywordSyncController --> client_manager

    AsyncTrendInsightAPI --> VideoSearchResponse
    KeywordSyncResponse --> KeywordData
    KeywordSyncResponse --> VideoItem
    TrendInsightVideoMapper --> VideoItem

    %% 枚举类
    class KeywordActionType {
        <<enumeration>>
        EXISTING
        CREATED
    }

    class SourceType {
        <<enumeration>>
        KEYWORD
        AUTHOR
    }

    class Platform {
        <<enumeration>>
        DOUYIN
        TRENDINSIGHT
        XIAOHONGSHU
        BILIBILI
        KUAISHOU
        WEIBO
        ZHIHU
        TIEBA
    }

    KeywordSyncResponse --> KeywordActionType
    TrendInsightVideoRelated --> SourceType
    TrendInsightVideoRelated --> Platform
```

## 组件图 (Component Diagram)

```mermaid
graph TB
    subgraph "Controller Layer"
        KSC[KeywordSyncController]
    end

    subgraph "Service Layer"
        API[AsyncTrendInsightAPI]
        CM[client_manager]
        Mapper[TrendInsightVideoMapper]
    end

    subgraph "Model Layer"
        TIK[TrendInsightKeyword]
        TIVR[TrendInsightVideoRelated]
        DA[DouyinAweme]
    end

    subgraph "Schema Layer"
        KSR[KeywordSyncResponse]
        KD[KeywordData]
        VI[VideoItem]
        VSR[VideoSearchResponse]
    end

    subgraph "Config Layer"
        TIC[TrendInsightConfig]
    end

    subgraph "External APIs"
        TAPI[TrendInsight API]
    end

    subgraph "Database"
        DB[(Database)]
    end

    %% 依赖关系
    KSC --> API
    KSC --> CM
    KSC --> Mapper
    KSC --> TIK
    KSC --> TIVR
    KSC --> TIC
    KSC --> KSR

    API --> TAPI
    API --> VSR
    CM --> API

    Mapper --> DA
    Mapper --> VI

    TIK --> DB
    TIVR --> DB
    DA --> DB

    KSR --> KD
    KSR --> VI

    style KSC fill:#e3f2fd
    style API fill:#f3e5f5
    style DB fill:#e8f5e8
```

## 序列图 (Sequence Diagram)

### 完整的同步流程序列图

```mermaid
sequenceDiagram
    participant C as Client
    participant KSC as KeywordSyncController
    participant H as _calculate_keyword_hash
    participant TIK as TrendInsightKeyword
    participant GTC as _get_trendinsight_client
    participant CM as client_manager
    participant API as AsyncTrendInsightAPI
    participant TAPI as TrendInsight API
    participant Mapper as TrendInsightVideoMapper
    participant TIVR as TrendInsightVideoRelated
    participant DA as DouyinAweme

    C->>KSC: sync_keyword_videos(keyword)
    
    note over KSC: 初始化 KeywordSyncResponse
    
    KSC->>H: _calculate_keyword_hash(keyword)
    H-->>KSC: keyword_hash
    
    KSC->>TIK: filter(keyword_hash=keyword_hash).first()
    TIK-->>KSC: existing_keyword or None
    
    alt keyword not exists
        KSC->>TIK: create(keyword, keyword_hash, video_count=0)
        TIK-->>KSC: new_keyword
        note over KSC: set keyword_action = CREATED
    else keyword exists
        note over KSC: set keyword_action = EXISTING
    end
    
    KSC->>GTC: _get_trendinsight_client()
    GTC->>CM: create_async_client()
    CM-->>GTC: async_client
    GTC->>API: AsyncTrendInsightAPI(async_client)
    API-->>GTC: api_instance
    GTC-->>KSC: api_instance
    
    KSC->>API: search_info_by_keyword(keyword, ...)
    API->>TAPI: HTTP Request
    TAPI-->>API: Video Data
    API-->>KSC: VideoSearchResponse
    
    alt search successful and has results
        KSC->>Mapper: videos_to_video_items(videos)
        Mapper-->>KSC: video_items, video_ids
        
        note over KSC: DouyinAweme 批量处理开始
        KSC->>DA: filter(aweme_id__in=video_ids)
        DA-->>KSC: existing_aweme_records
        
        note over KSC: 分类已存在和未存在的记录
        KSC->>KSC: calculate_new_and_existing_sets()
        
        alt has records to create
            KSC->>DA: bulk_create(new_aweme_records)
            DA-->>KSC: created_count
        end
        
        alt has records to update
            KSC->>DA: bulk_update(existing_aweme_records, fields)
            DA-->>KSC: updated_count
        end
        
        note over KSC: DouyinAweme 批量处理完成
        Mapper-->>KSC: aweme_created, aweme_existing
        
        KSC->>TIVR: filter(source_id=keyword.id, video_id__in=video_ids)
        TIVR-->>KSC: existing_relations
        
        note over KSC: Calculate new relations needed
        
        alt has new relations
            KSC->>TIVR: bulk_create(new_relations)
            TIVR-->>KSC: created_relations
        end
        
        alt video_count changed
            KSC->>TIK: save() [update video_count]
            TIK-->>KSC: updated_keyword
        end
        
    else search failed or no results
        note over KSC: Add error to errors list
    end
    
    KSC-->>C: KeywordSyncResponse
```

## 状态图 (State Diagram)

### KeywordSyncController 内部状态转换

```mermaid
stateDiagram-v2
    [*] --> Idle: 创建实例
    
    Idle --> Processing: sync_keyword_videos()
    
    state Processing {
        [*] --> Initializing
        
        Initializing --> HashCalculating: 计算哈希值
        HashCalculating --> KeywordQuerying: 查询关键词
        
        KeywordQuerying --> KeywordExists: 关键词存在
        KeywordQuerying --> KeywordCreating: 关键词不存在
        
        KeywordExists --> VideoSearching: 继续搜索
        KeywordCreating --> CreationSuccess: 创建成功
        KeywordCreating --> CreationFailed: 创建失败
        
        CreationSuccess --> VideoSearching: 继续搜索
        CreationFailed --> VideoSearching: 记录错误后继续
        
        VideoSearching --> SearchSuccess: 搜索成功
        VideoSearching --> SearchFailed: 搜索失败
        
        SearchSuccess --> DataProcessing: 处理数据
        SearchFailed --> Finalizing: 记录错误并完成
        
        DataProcessing --> AwemeProcessing: 处理DouyinAweme
        AwemeProcessing --> RelationChecking: 检查关联
        RelationChecking --> RelationCreating: 创建新关联
        RelationCreating --> StatsUpdating: 更新统计
        
        StatsUpdating --> Finalizing: 完成处理
        Finalizing --> [*]
    }
    
    Processing --> Idle: 返回结果
    Processing --> Error: 致命错误
    Error --> Idle: 抛出异常
```

## 活动图 (Activity Diagram)

### sync_keyword_videos 方法活动图

```mermaid
flowchart TD
    Start([开始]) --> Init[初始化响应对象]
    Init --> CalcHash[计算关键词哈希]
    CalcHash --> QueryKeyword[查询关键词是否存在]
    
    QueryKeyword --> Decision1{关键词是否存在?}
    Decision1 -->|是| SetExisting[设置 action = EXISTING]
    Decision1 -->|否| CreateKeyword[创建新关键词]
    
    CreateKeyword --> Decision2{创建是否成功?}
    Decision2 -->|是| SetCreated[设置 action = CREATED]
    Decision2 -->|否| RecordError1[记录创建错误]
    
    SetExisting --> SearchVideo[搜索视频]
    SetCreated --> SearchVideo
    RecordError1 --> SearchVideo
    
    SearchVideo --> Decision3{搜索是否成功?}
    Decision3 -->|否| RecordError2[记录搜索错误]
    Decision3 -->|是| ProcessData[处理视频数据]
    
    ProcessData --> EnsureAweme[确保DouyinAweme记录存在]
    EnsureAweme --> Decision4{处理是否成功?}
    Decision4 -->|否| RecordError3[记录处理错误]
    Decision4 -->|是| CheckRelations[检查现有关联]
    
    RecordError3 --> CheckRelations
    CheckRelations --> CalcNewRelations[计算需要创建的新关联]
    CalcNewRelations --> Decision5{是否有新关联?}
    
    Decision5 -->|否| UpdateStats[更新统计信息]
    Decision5 -->|是| CreateRelations[批量创建关联]
    
    CreateRelations --> Decision6{创建是否成功?}
    Decision6 -->|否| RecordError4[记录创建错误]
    Decision6 -->|是| UpdateStats
    
    RecordError4 --> UpdateStats
    UpdateStats --> Decision7{视频数量是否变化?}
    
    Decision7 -->|是| UpdateKeyword[更新关键词记录]
    Decision7 -->|否| FinalizeResponse[最终化响应]
    
    UpdateKeyword --> FinalizeResponse
    RecordError2 --> FinalizeResponse
    FinalizeResponse --> End([结束])

    style Start fill:#e8f5e8
    style End fill:#e8f5e8
    style RecordError1 fill:#ffcdd2
    style RecordError2 fill:#ffcdd2
    style RecordError3 fill:#ffcdd2
    style RecordError4 fill:#ffcdd2
```

## 对象图 (Object Diagram)

### 运行时对象实例关系

```mermaid
classDiagram
    class keywordSyncController {
        <<instance of KeywordSyncController>>
        logger: Logger("__main__.KeywordSyncController")
        config: TrendInsightConfig
    }

    class syncResult {
        <<instance of KeywordSyncResponse>>
        keyword_action: CREATED
        videos_synced: 25
        relations_created: 20
        errors: []
    }

    class keywordRecord {
        <<instance of TrendInsightKeyword>>
        id: 123
        keyword: "人工智能"
        keyword_hash: "abc123def456"
        video_count: 25
    }

    class apiClient {
        <<instance of AsyncTrendInsightAPI>>
        async_client: httpx.AsyncClient
    }

    class videoRelations {
        <<list of TrendInsightVideoRelated>>
        [relation1, relation2, relation3, ...]
    }

    keywordSyncController --> syncResult
    keywordSyncController --> keywordRecord
    keywordSyncController --> apiClient
    syncResult --> keywordRecord
    keywordRecord --> videoRelations
```

## 包图 (Package Diagram)

### 模块依赖关系

```mermaid
graph TB
    subgraph "controllers.trendinsight"
        KSC[KeywordSyncController]
    end

    subgraph "models.trendinsight"
        TIK[TrendInsightKeyword]
        TIVR[TrendInsightVideoRelated]
    end

    subgraph "models.enums"
        KAT[KeywordActionType]
        ST[SourceType]
        PT[Platform]
    end

    subgraph "schemas.trendinsight"
        KSR[KeywordSyncResponse]
        KD[KeywordData]
        VI[VideoItem]
    end

    subgraph "mapper.trendinsight"
        TIVM[TrendInsightVideoMapper]
    end

    subgraph "rpc.trendinsight"
        ATAPI[AsyncTrendInsightAPI]
        CM[client_manager]
        TIC[TrendInsightConfig]
        VSR[VideoSearchResponse]
    end

    subgraph "external"
        FASTAPI[FastAPI/HTTPException]
        LOGGING[logging]
        HASHLIB[hashlib]
    end

    KSC --> TIK
    KSC --> TIVR
    KSC --> KSR
    KSC --> TIVM
    KSC --> ATAPI
    KSC --> CM
    KSC --> TIC
    KSC --> KAT
    KSC --> ST
    KSC --> PT
    KSC --> FASTAPI
    KSC --> LOGGING
    KSC --> HASHLIB

    KSR --> KD
    KSR --> VI
    KSR --> KAT

    ATAPI --> VSR

    style KSC fill:#e3f2fd
```

## UML图说明

### 类图特点
1. **单一职责**: 每个类都有明确的职责
2. **依赖注入**: 通过构造函数注入依赖
3. **类型安全**: 使用了完整的类型注解
4. **封装性**: 私有方法用于内部逻辑

### 组件图特点
1. **分层架构**: 清晰的分层结构
2. **松耦合**: 组件间通过接口通信
3. **可扩展性**: 易于添加新的功能组件
4. **可测试性**: 各组件可独立测试

### 序列图特点
1. **异步处理**: 显示异步调用流程
2. **错误处理**: 包含错误处理分支
3. **批量操作**: 显示批量操作优化
4. **资源管理**: 显示资源的创建和释放

### 状态图特点
1. **状态清晰**: 每个状态都有明确定义
2. **转换条件**: 状态转换有明确触发条件
3. **错误恢复**: 包含错误状态和恢复机制
4. **最终状态**: 有明确的结束状态

### 设计模式应用
1. **单例模式**: client_manager 作为客户端管理器
2. **工厂模式**: 通过 create_async_client 创建客户端
3. **策略模式**: 不同的搜索参数策略
4. **模板方法模式**: 同步流程的标准化处理