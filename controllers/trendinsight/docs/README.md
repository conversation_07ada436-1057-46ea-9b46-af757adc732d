# TrendInsight 文档索引

> 路径：`controllers/trendinsight/docs`

本目录用于沉淀趋势洞察相关控制器/服务的接口与设计文档，统一风格与产出质量，便于团队协同与对外对齐。

## 文档结构
- video_process_controller/
  - video_process_controller_api.md：视频处理与分析接口说明（已创建）
- keyword_insight_controller/
  - keyword_insight_controller_api.md：关键词趋势洞察接口（待补充）
- hotrank_controller/
  - hotrank_controller_api.md：热榜聚合与查询接口（待补充）
- common/
  - error_code.md：通用错误码与返回结构（待补充）

## 统一规范
- 返回结构：`{ code, message, data, traceId }`
- 鉴权：Bearer Token 或网关签名（按环境配置）
- 内容编码：`application/json; charset=utf-8`

## 快速开始
1. 新增控制器时，复制已有模板文件，按模块实际字段与示例响应完善。
2. 补充典型调用样例（curl、HTTPie、SDK 调用片段）。
3. 增加边界与错误码对照，确保可定位问题。

## 变更记录
- v0.1 初始化索引
