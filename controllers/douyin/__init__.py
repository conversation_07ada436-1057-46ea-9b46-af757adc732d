"""
controllers/douyin 包

包含抖音平台相关的所有控制器业务逻辑
"""

from .collection import (
    DouyinCollectionController,
    collection_controller as douyin_collection_controller,
)
from .html.html import DouyinHTMLController
from .video.fetcher import VideoFetcherController
from .video.url_parser import VideoUrlParseController
from .video.rpc_client import DouyinRPCVideoController

# 为了向后兼容，保持原有的导入方式
try:
    # 使用新的统一控制器
    from .video.controller import DouyinVideoController
except ImportError:
    # 如果 video_controller 被删除，使用RPC视频控制器作为替代
    DouyinVideoController = DouyinRPCVideoController

# 为了向后兼容，提供 DouyinController 别名
DouyinController = DouyinRPCVideoController

# 对外暴露的主要接口
__all__ = [
    "DouyinController",  # 主控制器（向后兼容，指向RPC视频控制器）
    "DouyinRPCVideoController",  # RPC视频控制器
    "DouyinCollectionController",  # 收藏夹控制器
    "douyin_collection_controller",  # 收藏夹控制器实例
    "DouyinHTMLController",  # HTML控制器
    "DouyinVideoController",  # 视频控制器（向后兼容）
    "VideoFetcherController",  # 统一视频获取控制器
    "VideoUrlParseController",  # 视频URL解析控制器
]
