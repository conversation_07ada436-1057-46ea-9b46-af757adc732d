"""
快速测试 - controllers/douyin 新结构

简单快速的测试脚本，用于验证新的控制器结构是否正常工作
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
PROJECT_ROOT = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(PROJECT_ROOT))

from log import logger

from controllers.douyin import DouyinHTMLController
from controllers.douyin.video.fetcher import VideoFetcherController


async def quick_test():
    """快速测试主要功能"""
    logger.info("🔧 快速测试 - controllers/douyin 新结构")
    logger.info("=" * 50)

    # 测试视频ID（请替换为真实的抖音视频ID）
    test_aweme_id = "7123743056825716009"

    try:
        logger.info(f"📋 测试视频ID: {test_aweme_id}")
        logger.info("⏳ 开始测试...")

        # 测试精选页面方法
        logger.info("\n1️⃣ 测试精选页面方法...")
        html_controller = DouyinHTMLController()
        result = await html_controller.fetch_jingxuan_video_data(
            aweme_id=test_aweme_id, use_proxy=False, save_to_db=False  # 快速测试不使用代理  # 快速测试不保存数据库
        )

        if result["success"]:
            logger.info("✅ 精选页面方法测试通过")
            logger.info(f"   数据源: {result['source']}")
            logger.info(f"   响应时间: {result['html_response']['response_time']:.2f}秒")
            data = result["data"]
            if data:
                logger.info(f"   视频标题: {data.get('title', 'N/A')[:30]}...")
                logger.info(f"   作者昵称: {data.get('nickname', 'N/A')}")
        else:
            logger.error(f"❌ 精选页面方法测试失败: {result.get('error', '未知错误')}")

        # 测试智能自动方法
        from models.douyin import DouyinFetchMethod

        logger.info("\n2️⃣ 测试智能自动方法...")
        video_fetcher = VideoFetcherController()
        auto_result = await video_fetcher.fetch_video_data_auto(
            aweme_id=test_aweme_id,
            preferred_method=DouyinFetchMethod.JINGXUAN.value,
            fallback_methods=[DouyinFetchMethod.MOBILE.value],
            use_proxy=False,
            save_to_db=False,
        )

        if auto_result["success"]:
            logger.info("✅ 智能自动方法测试通过")
            logger.info(f"   使用方法: {auto_result['method']}")
            logger.info(f"   数据源: {auto_result['source']}")
        else:
            logger.error(f"❌ 智能自动方法测试失败: {auto_result.get('error', '未知错误')}")
            logger.error(f"   尝试的方法: {auto_result.get('methods_tried', [])}")

        # 测试控制器访问
        logger.info("\n3️⃣ 测试控制器组件访问...")
        from controllers.douyin.video.rpc_client import DouyinRPCVideoController
        rpc_video_controller = DouyinRPCVideoController()

        logger.info("✅ 控制器组件访问测试通过")
        logger.info(f"   HTML控制器: {type(html_controller).__name__}")
        logger.info(f"   RPC视频控制器: {type(rpc_video_controller).__name__}")
        logger.info(f"   视频获取控制器: {type(video_fetcher).__name__}")

        logger.info("\n" + "=" * 50)
        logger.info("🎉 快速测试完成!")

    except Exception as e:
        logger.error(f"\n❌ 测试过程中发生异常: {e}")
        import traceback

        logger.error(traceback.format_exc())


async def minimal_test():
    """最小化测试 - 仅测试导入和基本实例化"""
    logger.info("\n🔬 最小化测试...")

    try:
        # 测试导入
        from controllers.douyin import (
            DouyinHTMLController,
            DouyinVideoController,
        )
        from controllers.douyin.video.rpc_client import DouyinRPCVideoController

        logger.info("✅ 导入测试通过")

        # 测试实例化
        html_ctrl = DouyinHTMLController()
        video_ctrl = DouyinVideoController()
        rpc_ctrl = DouyinRPCVideoController()
        logger.info("✅ 实例化测试通过")

        # 测试视频获取控制器
        from controllers.douyin.video.fetcher import VideoFetcherController
        video_fetcher = VideoFetcherController()
        logger.info("✅ 视频获取控制器测试通过")

        logger.info("🎯 最小化测试完成 - 所有基本功能正常")

    except Exception as e:
        logger.error(f"❌ 最小化测试失败: {e}")
        import traceback

        logger.error(traceback.format_exc())


if __name__ == "__main__":
    logger.warning("⚠️  注意事项：")
    logger.warning("1. 请将 test_aweme_id 替换为真实的抖音视频ID")
    logger.warning("2. 快速测试默认不使用代理、不保存数据库")
    logger.warning("3. 如需完整测试，请运行 usage_examples.py")
    logger.warning("4. 如果遇到依赖包缺失，请先运行最小化测试")
    logger.info("")

    # 运行测试
    try:
        asyncio.run(minimal_test())
        asyncio.run(quick_test())
    except Exception as e:
        logger.error(f"\n❌ 运行测试时遇到问题: {e}")
        logger.error("这通常是由于缺少必要的依赖包导致的")
        logger.error("建议先安装依赖包或检查项目环境配置")