#!/usr/bin/env python3
"""
简单文件结构验证脚本
验证 controllers/douyin/ 新架构的文件是否正确创建
"""

import os
import sys
from pathlib import Path

from log import logger


def check_file_exists(filepath):
    """检查文件是否存在"""
    if os.path.exists(filepath):
        logger.info(f"✅ {filepath}")
        return True
    else:
        logger.error(f"❌ {filepath} - 文件不存在")
        return False


def check_file_content(filepath, expected_content=None):
    """检查文件内容"""
    if not os.path.exists(filepath):
        logger.error(f"❌ {filepath} - 文件不存在")
        return False

    try:
        with open(filepath, "r", encoding="utf-8") as f:
            content = f.read()

        file_size = len(content)
        line_count = content.count("\n")

        logger.info(f"✅ {filepath} - {file_size} 字符, {line_count} 行")

        if expected_content:
            if expected_content in content:
                logger.info(f"   ✅ 包含预期内容: {expected_content[:50]}...")
            else:
                logger.warning(f"   ⚠️ 未找到预期内容: {expected_content[:50]}...")

        return True
    except Exception as e:
        logger.error(f"❌ {filepath} - 读取错误: {e}")
        return False


def main():
    """主测试函数"""
    logger.info("🔧 检查 controllers/douyin/ 新架构文件结构")
    logger.info("=" * 60)

    # 项目根目录
    project_root = Path(__file__).parent.parent.parent.parent
    controllers_douyin = project_root / "controllers" / "douyin"

    logger.info(f"项目根目录: {project_root}")
    logger.info(f"控制器目录: {controllers_douyin}")
    logger.info("")

    # 检查目录存在
    if not controllers_douyin.exists():
        logger.error(f"❌ 目录不存在: {controllers_douyin}")
        return False
    else:
        logger.info(f"✅ 目录存在: {controllers_douyin}")

    logger.info("")

    # 检查核心文件
    core_files = [
        ("__init__.py", "DouyinController"),
        ("html_controller.py", "DouyinHTMLController"),
        ("video_controller.py", "DouyinVideoController"),
        ("main_controller.py", "douyin_controller"),
    ]

    logger.info("核心文件检查:")
    logger.info("-" * 30)
    all_core_files_ok = True
    for filename, expected_content in core_files:
        filepath = controllers_douyin / filename
        if not check_file_content(str(filepath), expected_content):
            all_core_files_ok = False

    logger.info("")

    # 检查示例文件
    examples_dir = controllers_douyin / "examples"
    example_files = ["quick_test.py", "usage_examples.py", "README.md"]

    logger.info("示例文件检查:")
    logger.info("-" * 30)
    all_example_files_ok = True
    for filename in example_files:
        filepath = examples_dir / filename
        if not check_file_exists(str(filepath)):
            all_example_files_ok = False

    logger.info("")

    # 检查原始示例文件
    rpc_examples_dir = project_root / "rpc" / "douyin" / "html_handler" / "examples"
    rpc_example_files = ["fetch_jingxuan_example.py", "quick_test.py"]

    logger.info("RPC示例文件检查:")
    logger.info("-" * 30)
    all_rpc_files_ok = True
    for filename in rpc_example_files:
        filepath = rpc_examples_dir / filename
        if not check_file_exists(str(filepath)):
            all_rpc_files_ok = False

    logger.info("")

    # 总结
    logger.info("总结:")
    logger.info("=" * 60)

    if all_core_files_ok:
        logger.info("✅ 核心文件结构完整")
    else:
        logger.error("❌ 核心文件结构有问题")

    if all_example_files_ok:
        logger.info("✅ 控制器示例文件完整")
    else:
        logger.error("❌ 控制器示例文件有问题")

    if all_rpc_files_ok:
        logger.info("✅ RPC示例文件完整")
    else:
        logger.error("❌ RPC示例文件有问题")

    if all_core_files_ok and all_example_files_ok and all_rpc_files_ok:
        logger.info("")
        logger.info("🎉 所有文件结构检查通过!")
        logger.info("")
        logger.info("📋 新架构说明:")
        logger.info("  - controllers/douyin/__init__.py: 模块导出")
        logger.info("  - controllers/douyin/html_controller.py: HTML方式数据获取")
        logger.info("  - controllers/douyin/video_controller.py: RPC方式数据获取")
        logger.info("  - controllers/douyin/main_controller.py: 统一主控制器")
        logger.info("  - controllers/douyin/examples/: 使用示例和文档")
        logger.info("  - rpc/douyin/html_handler/examples/: fetch_jingxuan_page 专用示例")
        logger.info("")
        return True
    else:
        logger.info("")
        logger.error("❌ 文件结构检查失败")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)