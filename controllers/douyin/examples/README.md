# Controllers/Douyin 新架构说明

## 概述

抖音控制器采用模块化架构，每个控制器专注于特定的功能，提供清晰的职责分离和更好的可维护性。

## 文件结构

```
controllers/douyin/
├── __init__.py              # 模块导出
├── html/                    # HTML方式数据获取
│   └── html.py             # HTML控制器
├── video/                   # 视频相关控制器
│   ├── video_fetcher.py    # 统一视频获取控制器
│   ├── rpc_video_controller.py  # RPC视频控制器
│   └── video_url_parse.py  # 视频URL解析控制器
├── collection/              # 收藏夹控制器
│   └── collection.py       # 收藏夹控制器
└── examples/               # 使用示例
    ├── usage_examples.py   # 详细使用示例
    ├── quick_test.py       # 快速测试脚本
    └── README.md          # 本文件
```

## 控制器说明

### 1. DouyinHTMLController (html_controller.py)
- **职责**: 基于HTML页面的数据获取
- **主要方法**:
  - `fetch_jingxuan_video_data()` - 精选页面数据获取
  - `fetch_mobile_video_data()` - 移动端分享页面数据获取
  - `fetch_pc_video_data()` - PC端页面数据获取

### 2. DouyinController (已移除)
- **状态**: 已移除，功能已迁移到专门的控制器
- **替代方案**:
  - 视频相关功能 → `DouyinRPCVideoController`
  - 收藏夹功能 → `DouyinCollectionController`
  - HTML获取功能 → `DouyinHTMLController`

### 3. VideoFetcherController (video/video_fetcher.py)
- **职责**: 专门的视频数据获取
- **主要功能**:
  - 统一的视频获取接口
  - 智能回退机制
  - 类型安全的数据映射
  - 详细的统计和日志

### 4. DouyinRPCVideoController (video/rpc_video_controller.py)
- **职责**: 专门的RPC视频数据获取
- **主要方法**:
  - `get_video_rpc_auto_cookies()` - 自动获取cookies并获取视频详情(RPC格式)
  - `get_video_db_auto_cookies()` - 自动获取cookies并获取视频详情(数据库格式)
  - `validate_cookies()` - 验证cookies有效性
  - `get_user_info_with_cookies()` - 获取用户信息

### 5. DouyinCollectionController (collection/collection.py)
- **职责**: 收藏夹数据管理
- **主要功能**:
  - 收藏夹数据获取
  - 收藏夹同步
  - 收藏夹管理

## 使用方法

### 基础使用

```python
from controllers.douyin import DouyinHTMLController

# 创建HTML控制器
html_controller = DouyinHTMLController()

# 精选页面方式获取
result = await html_controller.fetch_jingxuan_video_data(
    aweme_id="7123456789012345678",
    use_proxy=True,
    save_to_db=True
)

# 移动端方式获取
result = await html_controller.fetch_mobile_share_video_data(
    aweme_id="7123456789012345678",
    use_proxy=True
)
```

### 智能自动获取

```python
# 智能选择最佳方法，自动故障转移
from models.douyin import DouyinFetchMethod
from controllers.douyin.video.video_fetcher import VideoFetcherController

video_fetcher = VideoFetcherController()
result = await video_fetcher.fetch_video_data_auto(
    aweme_id="7123456789012345678",
    preferred_method=DouyinFetchMethod.JINGXUAN.value,      # 首选方法
    fallback_methods=[DouyinFetchMethod.MOBILE.value, DouyinFetchMethod.RPC.value], # 备用方法
    use_proxy=True
)
```

### 直接使用子控制器

```python
from controllers.douyin import DouyinHTMLController

# 直接创建HTML控制器
html_controller = DouyinHTMLController()
result = await html_controller.fetch_jingxuan_video_data(
    aweme_id="7123456789012345678"
)

# 使用专门的视频获取控制器（推荐用于新代码）
from controllers.douyin.video.video_fetcher import VideoFetcherController
video_fetcher = VideoFetcherController()
result = await video_fetcher.fetch_video_data_auto("7123456789012345678")
```

## 配置选项

### HTML控制器配置

```python
from rpc.douyin.html_handler.config import DouyinHTMLConfig

config = DouyinHTMLConfig(
    request_timeout=30,
    max_retries=5,
    enable_proxy=True,
    enable_cookie_rotation=True,
    enable_anti_crawler_detection=True
)

html_controller = DouyinHTMLController(config=config)
```

### 请求级别配置

```python
result = await douyin_controller.fetch_jingxuan_data(
    aweme_id="7123456789012345678",
    use_proxy=False,
    custom_headers={
        "User-Agent": "custom-agent",
        "Accept-Language": "zh-CN,zh;q=0.9"
    },
    timeout=25,
    save_to_db=False
)
```

## 错误处理

```python
result = await douyin_controller.fetch_video_data_auto(
    aweme_id="7123456789012345678"
)

if result["success"]:
    # 成功处理
    data = result["data"]
    method = result["method"]  # 使用的方法
    source = result["source"]  # 数据来源
else:
    # 错误处理
    error = result["error"]
    methods_tried = result.get("methods_tried", [])
    print(f"获取失败: {error}, 尝试过的方法: {methods_tried}")
```

## 兼容性说明

- **向后兼容**: 新架构与现有代码完全兼容
- **渐进迁移**: 可以逐步迁移现有代码到新架构
- **现有功能**: 原有 `controllers/douyin.py` 中的所有功能都保留

## 性能优化

1. **智能缓存**: 自动缓存有效的cookies和配置
2. **并发控制**: 批量处理时的并发数量控制
3. **故障转移**: 自动在不同方法间切换以提高成功率
4. **资源管理**: 自动管理HTTP连接和代理资源

## 测试和调试

### 快速测试
```bash
cd controllers/douyin/examples
python quick_test.py
```

### 详细示例
```bash
cd controllers/douyin/examples  
python usage_examples.py
```

### 单元测试
确保在项目根目录运行：
```bash
pytest tests/ -k douyin
```

## 最佳实践

1. **使用全局实例**: 优先使用 `douyin_controller` 全局实例
2. **智能获取**: 使用 `fetch_video_data_auto()` 获得最佳成功率
3. **错误处理**: 总是检查返回结果的 `success` 字段
4. **代理使用**: 生产环境建议启用代理以提高稳定性

## 故障排查

### 常见问题

1. **导入错误**: 确保项目根目录在Python路径中
2. **网络超时**: 调整timeout参数或启用代理
3. **数据解析失败**: 检查返回的HTML结构是否发生变化
4. **代理连接失败**: 验证代理服务器设置

### 调试模式

```python
# 启用详细日志
import logging
logging.getLogger("controllers.douyin").setLevel(logging.DEBUG)

# 查看详细响应信息
result = await douyin_controller.fetch_jingxuan_data(aweme_id, debug=True)
print(result["html_response"])  # 详细的HTTP响应信息
```

## 开发指南

如需扩展新功能：

1. **新的获取方法**: 在对应控制器中添加方法
2. **新的数据源**: 可以创建新的专用控制器
3. **新的配置**: 在config中添加相应选项
4. **新的错误处理**: 在异常处理中添加对应逻辑

## 更新日志

- **v1.0**: 初始模块化架构
- **TODO**: 
  - 添加更多数据源支持
  - 增强错误重试机制
  - 优化缓存策略