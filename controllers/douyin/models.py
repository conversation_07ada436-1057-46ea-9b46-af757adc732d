"""
控制器层的数据模型定义
每个步骤都有明确的输入输出类型
"""

from dataclasses import dataclass
from typing import Any, Dict, Optional, TypeVar

from mappers.douyin.pydantic_models import DouyinVideoData

T = TypeVar("T")


@dataclass
class HTMLFetchResult:
    """HTML获取结果"""

    success: bool
    content: Optional[str] = None
    error_message: Optional[str] = None
    status_code: Optional[int] = None
    response_time: Optional[float] = None


@dataclass
class JSONParseResult:
    """JSON解析结果"""

    success: bool
    data: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None
    raw_content: Optional[str] = None


@dataclass
class ModelConversionResult:
    """模型转换结果"""

    success: bool
    model: Optional[DouyinVideoData] = None
    error_message: Optional[str] = None
    validation_errors: Optional[Dict[str, str]] = None


@dataclass
class MobileDataFetchResult:
    """移动端数据获取完整结果"""

    success: bool
    aweme_id: str

    # 各步骤的结果
    html_result: Optional[HTMLFetchResult] = None
    json_result: Optional[JSONParseResult] = None
    model_result: Optional[ModelConversionResult] = None

    # 最终结果
    final_model: Optional[DouyinVideoData] = None
    error_message: Optional[str] = None
    failed_step: Optional[str] = None  # "html_fetch", "json_parse", "model_conversion"


@dataclass
class JingxuanDataFetchResult:
    """精选页面数据获取完整结果"""

    success: bool
    aweme_id: str

    # 各步骤的结果
    html_result: Optional[HTMLFetchResult] = None
    pace_f_extraction: Optional[str] = None
    uri_decode_result: Optional[str] = None
    json_result: Optional[JSONParseResult] = None
    model_result: Optional[ModelConversionResult] = None

    # 最终结果
    final_model: Optional[DouyinVideoData] = None
    error_message: Optional[str] = None
    failed_step: Optional[str] = None


@dataclass
class RPCDataFetchResult:
    """RPC数据获取完整结果"""

    success: bool
    aweme_id: str

    # RPC调用结果
    rpc_response: Optional[Any] = None
    model_result: Optional[ModelConversionResult] = None

    # 最终结果
    final_model: Optional[DouyinVideoData] = None
    error_message: Optional[str] = None
    failed_step: Optional[str] = None
