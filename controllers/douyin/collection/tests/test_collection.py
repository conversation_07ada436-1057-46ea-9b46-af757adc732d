"""
抖音收藏夹控制器单元测试
"""

import pytest
from unittest.mock import AsyncM<PERSON>, Mock, patch
from controllers.douyin.collection import DouyinCollectionController


class TestDouyinCollectionController:
    """DouyinCollectionController 单元测试"""
    
    @pytest.fixture
    def controller(self):
        """创建控制器实例"""
        return DouyinCollectionController()
    
    @pytest.fixture
    def mock_cookies(self):
        """模拟Cookie"""
        return "test_cookies"
    
    def test_controller_initialization(self, controller):
        """测试控制器初始化"""
        assert controller is not None
        assert hasattr(controller, '_get_douyin_client')
        assert hasattr(controller, 'get_self_aweme_collection_rpc_with_cookies')
        assert hasattr(controller, 'get_collect_video_list_rpc_with_cookies')
        assert hasattr(controller, 'sync_and_save_single_collection_with_cookies')
    
    @pytest.mark.asyncio
    async def test_get_douyin_client(self, controller):
        """测试获取抖音客户端"""
        client = await controller._get_douyin_client()
        assert client is not None
    
    @pytest.mark.asyncio
    @patch('controllers.douyin.collection.collection.async_douyin_collection_api')
    async def test_get_self_aweme_collection_success(self, mock_client, controller, mock_cookies):
        """测试成功获取收藏夹列表"""
        # 设置模拟数据
        mock_response = Mock()
        mock_response.collects_list = [Mock(collects_id="123", collects_name="测试收藏夹")]
        mock_response.cursor = 0
        mock_response.has_more = False
        
        mock_client.get_self_aweme_collection = AsyncMock(return_value=mock_response)
        
        # 执行测试
        result = await controller.get_self_aweme_collection_rpc_with_cookies(
            cursor=0, count=10, cookies=mock_cookies
        )
        
        # 验证结果
        assert result == mock_response
        mock_client.get_self_aweme_collection.assert_called_once()
    
    @pytest.mark.asyncio
    @patch('controllers.douyin.collection.collection.async_douyin_collection_api')
    async def test_get_self_aweme_collection_error(self, mock_client, controller, mock_cookies):
        """测试获取收藏夹列表失败"""
        # 设置模拟异常
        mock_client.get_self_aweme_collection = AsyncMock(side_effect=Exception("API Error"))
        
        # 执行测试并验证异常
        with pytest.raises(Exception) as exc_info:
            await controller.get_self_aweme_collection_rpc_with_cookies(
                cursor=0, count=10, cookies=mock_cookies
            )
        
        assert "获取收藏夹列表失败" in str(exc_info.value)
    
    @pytest.mark.asyncio
    @patch('controllers.douyin.collection.collection.async_douyin_collection_api')
    async def test_get_collect_video_list_success(self, mock_client, controller, mock_cookies):
        """测试成功获取收藏视频列表"""
        # 设置模拟数据
        mock_response = Mock()
        mock_response.aweme_list = [Mock(aweme_id="video123", desc="测试视频")]
        mock_response.cursor = 0
        mock_response.has_more = False
        
        mock_client.get_collect_video_list = AsyncMock(return_value=mock_response)
        
        # 执行测试
        result = await controller.get_collect_video_list_rpc_with_cookies(
            collects_id="collection123", cursor=0, count=10, cookies=mock_cookies
        )
        
        # 验证结果
        assert result == mock_response
        mock_client.get_collect_video_list.assert_called_once()
    
    @pytest.mark.asyncio
    @patch('controllers.douyin.collection.collection.async_douyin_collection_api')
    async def test_get_collect_video_list_error(self, mock_client, controller, mock_cookies):
        """测试获取收藏视频列表失败"""
        # 设置模拟异常
        mock_client.get_collect_video_list = AsyncMock(side_effect=Exception("API Error"))
        
        # 执行测试并验证异常
        with pytest.raises(Exception) as exc_info:
            await controller.get_collect_video_list_rpc_with_cookies(
                collects_id="collection123", cursor=0, count=10, cookies=mock_cookies
            )
        
        assert "获取收藏视频列表失败" in str(exc_info.value)
    
    @pytest.mark.asyncio
    @patch('controllers.douyin.collection.collection.DouyinAweme')
    @patch('mappers.douyin.collection_mapper.CollectionDataMapper')
    @patch('controllers.douyin.collection.collection.async_douyin_collection_api')
    async def test_sync_collection_success(self, mock_client, mock_mapper, mock_model, controller, mock_cookies):
        """测试成功同步收藏夹"""
        # 设置模拟视频数据
        mock_video = Mock()
        mock_video.aweme_id = "video123"
        mock_video.desc = "测试视频"
        mock_video.create_time = 1234567890
        
        # 设置模拟API响应
        mock_response = Mock()
        mock_response.aweme_list = [mock_video]
        mock_response.has_more = False
        mock_response.cursor = 0
        
        mock_client.get_collect_video_list = AsyncMock(return_value=mock_response)
        
        # 设置模拟数据库查询 - 没有已存在的视频
        mock_model.filter.return_value.all = AsyncMock(return_value=[])
        
        # 设置模拟数据映射
        mock_aweme_data = Mock()
        mock_aweme_data.model_dump.return_value = {'aweme_id': 'video123'}
        mock_mapper.map_video_info_to_douyin_aweme.return_value = mock_aweme_data
        
        # 设置模拟批量创建
        mock_model.bulk_create = AsyncMock()
        
        # 执行测试
        result = await controller.sync_and_save_single_collection_with_cookies(
            collection_id="collection123", cookies=mock_cookies
        )
        
        # 验证结果
        assert result['collections_synced'] == 1
        assert result['videos_synced'] == 1
        assert len(result['aweme_ids']) == 1
        assert result['aweme_ids'][0] == "video123"
        assert len(result['errors']) == 0
        
        # 验证方法调用
        mock_client.get_collect_video_list.assert_called()
        mock_model.filter.return_value.all.assert_called_once()
        mock_mapper.map_video_info_to_douyin_aweme.assert_called_once()
        mock_model.bulk_create.assert_called_once()
    
    @pytest.mark.asyncio
    @patch('controllers.douyin.collection.collection.async_douyin_collection_api')
    async def test_sync_collection_empty_response(self, mock_client, controller, mock_cookies):
        """测试同步空收藏夹"""
        # 设置模拟空响应
        mock_client.get_collect_video_list = AsyncMock(return_value=None)
        
        # 执行测试
        result = await controller.sync_and_save_single_collection_with_cookies(
            collection_id="collection123", cookies=mock_cookies
        )
        
        # 验证结果
        assert result['collections_synced'] == 0
        assert result['videos_synced'] == 0
        assert len(result['aweme_ids']) == 0
        assert len(result['errors']) == 0
    
    @pytest.mark.asyncio
    @patch('controllers.douyin.collection.collection.async_douyin_collection_api')
    async def test_sync_collection_api_error(self, mock_client, controller, mock_cookies):
        """测试同步收藏夹API错误"""
        # 设置模拟异常
        mock_client.get_collect_video_list = AsyncMock(side_effect=Exception("API Error"))
        
        # 执行测试并验证异常
        with pytest.raises(Exception) as exc_info:
            await controller.sync_and_save_single_collection_with_cookies(
                collection_id="collection123", cookies=mock_cookies
            )
        
        assert "同步收藏夹失败" in str(exc_info.value)
    
    @pytest.mark.asyncio
    @patch('controllers.douyin.collection.collection.DouyinAweme')
    @patch('controllers.douyin.collection.collection.async_douyin_collection_api')
    async def test_sync_collection_with_existing_videos(self, mock_client, mock_model, controller, mock_cookies):
        """测试同步收藏夹包含已存在视频"""
        # 设置模拟视频数据
        mock_video1 = Mock()
        mock_video1.aweme_id = "video123"  # 已存在
        mock_video2 = Mock()
        mock_video2.aweme_id = "video456"  # 新视频
        
        # 设置模拟API响应
        mock_response = Mock()
        mock_response.aweme_list = [mock_video1, mock_video2]
        mock_response.has_more = False
        mock_response.cursor = 0
        
        mock_client.get_collect_video_list = AsyncMock(return_value=mock_response)
        
        # 设置模拟数据库查询 - video123已存在
        existing_video = Mock()
        existing_video.aweme_id = "video123"
        mock_model.filter.return_value.all = AsyncMock(return_value=[existing_video])
        
        # 执行测试
        result = await controller.sync_and_save_single_collection_with_cookies(
            collection_id="collection123", cookies=mock_cookies
        )
        
        # 验证结果
        assert len(result['aweme_ids']) == 2  # 总共处理2个视频
        assert "video123" in result['aweme_ids']
        assert "video456" in result['aweme_ids']
        # videos_synced应该只计算新增的视频数量
        # 具体数量取决于mock_mapper是否被调用等实现细节