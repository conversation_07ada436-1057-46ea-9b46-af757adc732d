# DouyinCollectionController 时序图

## 1. 获取收藏夹列表时序图

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Controller as DouyinCollectionController
    participant R<PERSON> as async_douyin_collection_api
    participant Douy<PERSON> as 抖音服务器

    Client->>Controller: get_self_aweme_collection_rpc_with_cookies(cursor, count, cookies)
    Controller->>Controller: _get_douyin_client()
    Controller->>Controller: create SelfAwemeCollectionRequest
    Controller->>RPC: get_self_aweme_collection(request, cookies)
    RPC->>Douyin: HTTP请求 (带Cookie认证)
    Douyin-->>RPC: 返回收藏夹列表数据
    RPC-->>Controller: SelfAwemeCollectionResponse
    Controller-->>Client: 返回响应数据

    Note over Client,Douyin: 异常情况
    RPC-->>Controller: 抛出异常
    Controller-->>Client: HTTPException(500, "获取收藏夹列表失败")
```

## 2. 获取收藏视频列表时序图

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Controller as DouyinCollectionController
    participant <PERSON><PERSON> as async_douyin_collection_api
    participant Douyin as 抖音服务器

    Client->>Controller: get_collect_video_list_rpc_with_cookies(collects_id, cursor, count, cookies)
    Controller->>Controller: _get_douyin_client()
    Controller->>Controller: create CollectVideoListRequest
    Controller->>RPC: get_collect_video_list(request, cookies)
    RPC->>Douyin: HTTP请求 (带Cookie认证)
    Douyin-->>RPC: 返回视频列表数据
    RPC-->>Controller: CollectVideoListResponse
    Controller-->>Client: 返回响应数据

    Note over Client,Douyin: 异常情况
    RPC-->>Controller: 抛出异常
    Controller-->>Client: HTTPException(500, "获取收藏视频列表失败")
```

## 3. 同步收藏夹数据时序图（核心业务流程）

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Controller as DouyinCollectionController
    participant RPC as async_douyin_collection_api
    participant Douyin as 抖音服务器
    participant Mapper as CollectionDataMapper
    participant DB as 数据库(DouyinAweme)

    Client->>Controller: sync_and_save_single_collection_with_cookies(collection_id, cookies)
    Controller->>Controller: 初始化统计变量
    Controller->>Controller: _get_douyin_client()

    Note over Controller,Douyin: 分页获取所有视频数据
    loop 分页循环
        Controller->>Controller: create CollectVideoListRequest(cursor)
        Controller->>RPC: get_collect_video_list(request, cookies)
        RPC->>Douyin: HTTP请求获取视频列表
        Douyin-->>RPC: 返回分页视频数据
        RPC-->>Controller: CollectVideoListResponse
        Controller->>Controller: 收集视频到 all_videos[]
        
        alt has_more = true
            Controller->>Controller: 更新cursor，继续循环
        else has_more = false
            Controller->>Controller: 跳出循环
        end
    end

    Note over Controller,DB: 数据处理与保存
    Controller->>Controller: 提取所有 aweme_ids
    Controller->>DB: filter(aweme_id__in=aweme_ids).all()
    DB-->>Controller: 返回已存在的视频记录
    Controller->>Controller: 计算需要创建的新视频

    loop 数据转换
        Controller->>Mapper: map_video_info_to_douyin_aweme(video)
        Mapper-->>Controller: DouyinAwemeData
        Controller->>Controller: 转换为 DouyinAweme 模型
    end

    alt 有新视频需要保存
        Controller->>DB: bulk_create(videos_to_create)
        DB-->>Controller: 批量创建成功
        Controller->>Controller: 更新统计计数器
    end

    Controller->>Controller: 构建返回结果
    Controller-->>Client: 返回同步统计结果

    Note over Client,DB: 异常处理
    alt 发生异常
        Controller->>Controller: 记录错误到 errors[]
        Controller-->>Client: HTTPException(500, "同步收藏夹失败")
    end
```

## 4. 数据映射时序图

```mermaid
sequenceDiagram
    participant Controller as DouyinCollectionController
    participant Mapper as CollectionDataMapper
    participant VideoInfo as VideoInfo (RPC数据)
    participant DouyinAwemeData as DouyinAwemeData (Pydantic)
    participant DouyinAweme as DouyinAweme (ORM模型)

    Controller->>VideoInfo: 获取原始视频数据
    VideoInfo-->>Controller: 视频基础信息
    Controller->>Mapper: map_video_info_to_douyin_aweme(video)
    Mapper->>VideoInfo: 读取各个字段
    Mapper->>DouyinAwemeData: 创建标准化数据模型
    DouyinAwemeData-->>Mapper: 验证后的数据模型
    Mapper-->>Controller: DouyinAwemeData 实例
    Controller->>DouyinAwemeData: model_dump()
    DouyinAwemeData-->>Controller: 字典格式数据
    Controller->>DouyinAweme: **aweme_dict 创建ORM实例
    DouyinAweme-->>Controller: 准备写入数据库的模型
```

## 5. 错误处理时序图

```mermaid
sequenceDiagram
    participant Controller as DouyinCollectionController
    participant Operation as 业务操作
    participant ErrorHandler as 错误处理器
    participant Client as 客户端

    Controller->>Operation: 执行业务操作
    
    alt 操作成功
        Operation-->>Controller: 返回正常结果
        Controller->>Controller: 更新成功计数器
    else 操作失败
        Operation-->>Controller: 抛出异常
        Controller->>ErrorHandler: 捕获异常
        ErrorHandler->>ErrorHandler: 记录错误信息到 errors[]
        ErrorHandler-->>Controller: 继续执行后续操作
    end
    
    Controller->>Controller: 构建包含错误信息的结果
    Controller-->>Client: 返回结果(包含errors字段)
    
    Note over Controller,Client: 对于严重错误
    alt 致命错误
        Controller-->>Client: HTTPException(500, 具体错误信息)
    end
```

## 6. 性能优化时序图

```mermaid
sequenceDiagram
    participant Controller as DouyinCollectionController
    participant DB as 数据库
    participant BatchOp as 批量操作

    Note over Controller,BatchOp: 批量数据查询优化
    Controller->>DB: 单次查询所有需要的aweme_ids
    DB-->>Controller: 返回所有存在的记录
    Controller->>Controller: 使用集合操作快速过滤

    Note over Controller,BatchOp: 批量数据写入优化
    Controller->>BatchOp: 准备所有需要创建的记录
    BatchOp->>DB: bulk_create(所有新记录)
    DB-->>BatchOp: 一次性写入完成
    BatchOp-->>Controller: 批量操作结果

    Note over Controller,BatchOp: 内存优化
    Controller->>Controller: 及时清理临时数据
    Controller->>Controller: 使用生成器避免大列表
```

## 关键交互点

1. **认证机制**: 所有RPC调用都需要传递cookies进行身份验证
2. **分页处理**: 自动处理分页逻辑，确保获取完整数据
3. **数据一致性**: 通过数据库查询避免重复数据
4. **异常隔离**: 单个操作失败不影响整体流程
5. **批量优化**: 使用批量操作提高性能
6. **状态追踪**: 详细的统计信息帮助监控处理结果