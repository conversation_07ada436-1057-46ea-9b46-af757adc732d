# DouyinCollectionController 流程图

## 主要处理流程

### 1. 获取收藏夹列表流程

```mermaid
flowchart TD
    A[开始: get_self_aweme_collection_rpc_with_cookies] --> B[解析参数: cursor, count, cookies]
    B --> C[获取抖音客户端实例]
    C --> D[创建 SelfAwemeCollectionRequest]
    D --> E[调用 RPC 接口获取收藏夹列表]
    E --> F{请求成功?}
    F -->|是| G[返回 SelfAwemeCollectionResponse]
    F -->|否| H[抛出 HTTPException]
    G --> I[结束]
    H --> I
```

### 2. 获取收藏视频列表流程

```mermaid
flowchart TD
    A[开始: get_collect_video_list_rpc_with_cookies] --> B[解析参数: collects_id, cursor, count, cookies]
    B --> C[获取抖音客户端实例]
    C --> D[创建 CollectVideoListRequest]
    D --> E[调用 RPC 接口获取视频列表]
    E --> F{请求成功?}
    F -->|是| G[返回 CollectVideoListResponse]
    F -->|否| H[抛出 HTTPException]
    G --> I[结束]
    H --> I
```

### 3. 同步收藏夹数据流程（核心业务逻辑）

```mermaid
flowchart TD
    A[开始: sync_and_save_single_collection_with_cookies] --> B[初始化统计变量]
    B --> C[获取抖音客户端实例]
    C --> D[初始化视频游标 cursor=0]
    D --> E[调用获取视频列表 API]
    E --> F{有视频数据?}
    F -->|否| G[跳出循环]
    F -->|是| H[收集所有视频到 all_videos]
    H --> I{has_more?}
    I -->|是| J[更新 cursor]
    J --> E
    I -->|否| G
    G --> K{视频列表为空?}
    K -->|是| L[返回空结果统计]
    K -->|否| M[提取所有 aweme_id]
    M --> N[批量查询数据库中已存在的视频]
    N --> O[过滤出需要创建的新视频]
    O --> P{有新视频?}
    P -->|否| Q[跳过创建步骤]
    P -->|是| R[使用 CollectionDataMapper 转换数据]
    R --> S[批量创建 DouyinAweme 记录]
    S --> T[更新统计计数器]
    T --> Q
    Q --> U[构建返回结果]
    U --> V[结束]
    
    E --> |异常| W[记录错误信息]
    W --> X[继续处理或返回错误结果]
    X --> V
```

## 数据处理流程详解

### 数据转换流程

```mermaid
flowchart LR
    A[VideoInfo 原始数据] --> B[CollectionDataMapper.map_video_info_to_douyin_aweme]
    B --> C[DouyinAwemeData Pydantic模型]
    C --> D[model_dump() 转换为字典]
    D --> E[DouyinAweme Tortoise ORM模型]
    E --> F[bulk_create 批量保存到数据库]
```

### 错误处理流程

```mermaid
flowchart TD
    A[任意业务操作] --> B{发生异常?}
    B -->|是| C[捕获异常]
    C --> D[记录错误信息到 errors 列表]
    D --> E[继续处理其他数据]
    E --> F[在最终结果中包含错误信息]
    B -->|否| G[正常处理]
    G --> H[更新成功计数器]
    H --> I[继续下一步处理]
```

## 关键决策点

1. **空数据检查**: 在每个API调用后检查返回数据是否为空
2. **去重逻辑**: 通过数据库查询避免重复插入已存在的视频
3. **批量处理**: 使用批量操作提高数据库操作效率
4. **错误容错**: 单个视频处理失败不影响整体流程
5. **分页处理**: 自动处理API分页，获取所有视频数据

## 性能优化点

- 批量数据库操作 (bulk_create)
- 预先查询存在的记录避免重复
- 使用集合操作进行快速查找
- 异常隔离确保部分失败不影响整体