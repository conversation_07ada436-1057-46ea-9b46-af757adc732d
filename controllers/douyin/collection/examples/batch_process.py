"""
批量同步收藏夹示例

演示如何批量同步多个收藏夹的数据
"""

import asyncio
import time
from typing import List, Dict
from controllers.douyin.collection import collection_controller


async def sync_all_collections_example():
    """同步所有收藏夹的示例"""
    
    cookies = "your_douyin_cookies_here"
    
    print("🔄 批量同步收藏夹示例")
    print("=" * 50)
    
    try:
        # 1. 获取所有收藏夹
        print("\n📋 获取收藏夹列表...")
        collections_response = await collection_controller.get_self_aweme_collection_rpc_with_cookies(
            cursor=0,
            count=50,  # 获取更多收藏夹
            cookies=cookies
        )
        
        if not collections_response or not collections_response.collects_list:
            print("❌ 未获取到收藏夹数据")
            return
        
        collections = collections_response.collects_list
        print(f"✅ 找到 {len(collections)} 个收藏夹")
        
        # 2. 批量同步
        sync_results: List[Dict] = []
        total_videos = 0
        total_new_videos = 0
        
        for i, collection in enumerate(collections, 1):
            print(f"\n🔄 [{i}/{len(collections)}] 同步收藏夹: {collection.collects_name}")
            print(f"   收藏夹ID: {collection.collects_id}")
            
            start_time = time.time()
            
            try:
                result = await collection_controller.sync_and_save_single_collection_with_cookies(
                    collection_id=collection.collects_id,
                    cookies=cookies
                )
                
                end_time = time.time()
                duration = end_time - start_time
                
                result['collection_name'] = collection.collects_name
                result['duration'] = duration
                sync_results.append(result)
                
                video_count = len(result['aweme_ids'])
                new_videos = result['videos_synced']
                
                total_videos += video_count
                total_new_videos += new_videos
                
                print(f"   ✅ 完成 - 处理: {video_count} 个, 新增: {new_videos} 个, 耗时: {duration:.2f}s")
                
                if result['errors']:
                    print(f"   ⚠️  错误: {result['errors']}")
                
                # 避免请求过于频繁
                await asyncio.sleep(1)
                
            except Exception as e:
                print(f"   ❌ 同步失败: {str(e)}")
                sync_results.append({
                    'collection_name': collection.collects_name,
                    'error': str(e),
                    'videos_synced': 0,
                    'aweme_ids': []
                })
        
        # 3. 生成同步报告
        print("\n" + "=" * 50)
        print("📊 同步报告")
        print("=" * 50)
        
        print(f"总收藏夹数: {len(collections)}")
        print(f"总视频数: {total_videos}")
        print(f"新增视频数: {total_new_videos}")
        
        # 成功率统计
        successful_syncs = len([r for r in sync_results if 'error' not in r])
        success_rate = (successful_syncs / len(collections)) * 100
        print(f"同步成功率: {success_rate:.1f}% ({successful_syncs}/{len(collections)})")
        
        # 详细统计
        print(f"\n📋 详细统计:")
        for result in sync_results:
            name = result['collection_name']
            if 'error' in result:
                print(f"❌ {name}: 失败 - {result['error'][:50]}...")
            else:
                videos = len(result['aweme_ids'])
                new_videos = result['videos_synced']
                duration = result.get('duration', 0)
                print(f"✅ {name}: {videos} 个视频, {new_videos} 个新增, {duration:.1f}s")
        
        # 错误汇总
        errors = [r for r in sync_results if 'error' in r]
        if errors:
            print(f"\n⚠️  失败的收藏夹 ({len(errors)} 个):")
            for error_result in errors:
                print(f"   - {error_result['collection_name']}: {error_result['error']}")
        
    except Exception as e:
        print(f"❌ 批量同步失败: {str(e)}")


async def sync_specific_collections_example():
    """同步指定收藏夹的示例"""
    
    # 指定要同步的收藏夹ID列表
    target_collection_ids = [
        "7495633625980131112",  # 替换为实际的收藏夹ID
        # "collection_id_2",
        # "collection_id_3",
    ]
    
    cookies = "your_douyin_cookies_here"
    
    print("🎯 指定收藏夹同步示例")
    print("=" * 50)
    
    for i, collection_id in enumerate(target_collection_ids, 1):
        print(f"\n🔄 [{i}/{len(target_collection_ids)}] 同步收藏夹: {collection_id}")
        
        try:
            result = await collection_controller.sync_and_save_single_collection_with_cookies(
                collection_id=collection_id,
                cookies=cookies
            )
            
            video_count = len(result['aweme_ids'])
            new_videos = result['videos_synced']
            
            print(f"   ✅ 完成 - 处理: {video_count} 个, 新增: {new_videos} 个")
            
            if result['errors']:
                print(f"   ⚠️  错误: {result['errors']}")
                
        except Exception as e:
            print(f"   ❌ 同步失败: {str(e)}")


if __name__ == "__main__":
    print("选择同步模式:")
    print("1. 同步所有收藏夹")
    print("2. 同步指定收藏夹")
    print("请输入选择 (1/2): ", end="")
    
    choice = input().strip()
    
    if choice == "1":
        print("注意：同步所有收藏夹可能需要较长时间")
        asyncio.run(sync_all_collections_example())
    elif choice == "2":
        asyncio.run(sync_specific_collections_example())
    else:
        print("❌ 无效选择")