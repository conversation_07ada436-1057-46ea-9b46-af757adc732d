"""
收藏夹同步完整示例

演示完整的收藏夹操作流程，包括获取、筛选和同步
"""

import asyncio
import json
from datetime import datetime
from typing import List, Dict, Optional
from controllers.douyin.collection import collection_controller


class CollectionSyncManager:
    """收藏夹同步管理器"""
    
    def __init__(self, cookies: str):
        self.cookies = cookies
        self.controller = collection_controller
    
    async def get_all_collections(self) -> List[Dict]:
        """获取所有收藏夹信息"""
        collections = []
        cursor = 0
        count = 20
        
        while True:
            response = await self.controller.get_self_aweme_collection_rpc_with_cookies(
                cursor=cursor,
                count=count,
                cookies=self.cookies
            )
            
            if not response or not response.collects_list:
                break
            
            for collection in response.collects_list:
                collections.append({
                    'id': collection.collects_id,
                    'name': collection.collects_name,
                    'count': collection.collects_count,
                    'cover': collection.cover,
                    'create_time': collection.create_time,
                    'update_time': collection.update_time
                })
            
            if not response.has_more:
                break
            
            cursor = response.cursor
        
        return collections
    
    async def get_collection_videos_preview(self, collection_id: str, limit: int = 5) -> List[Dict]:
        """获取收藏夹视频预览"""
        response = await self.controller.get_collect_video_list_rpc_with_cookies(
            collects_id=collection_id,
            cursor=0,
            count=limit,
            cookies=self.cookies
        )
        
        if not response or not response.aweme_list:
            return []
        
        videos = []
        for video in response.aweme_list:
            videos.append({
                'aweme_id': video.aweme_id,
                'desc': video.desc[:50] + '...' if len(video.desc) > 50 else video.desc,
                'author': video.author.nickname if video.author else 'Unknown',
                'create_time': video.create_time,
                'stats': {
                    'like_count': video.statistics.digg_count if video.statistics else 0,
                    'comment_count': video.statistics.comment_count if video.statistics else 0,
                    'share_count': video.statistics.share_count if video.statistics else 0
                }
            })
        
        return videos
    
    async def sync_collection_with_details(self, collection_id: str, collection_name: str) -> Dict:
        """详细同步单个收藏夹"""
        print(f"🔄 开始同步收藏夹: {collection_name}")
        
        start_time = datetime.now()
        
        try:
            result = await self.controller.sync_and_save_single_collection_with_cookies(
                collection_id=collection_id,
                cookies=self.cookies
            )
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            # 添加额外信息
            result.update({
                'collection_name': collection_name,
                'start_time': start_time.isoformat(),
                'end_time': end_time.isoformat(),
                'duration_seconds': duration,
                'success': True
            })
            
            print(f"✅ 同步完成: {len(result['aweme_ids'])} 个视频, {result['videos_synced']} 个新增")
            
            return result
            
        except Exception as e:
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            error_result = {
                'collection_name': collection_name,
                'start_time': start_time.isoformat(),
                'end_time': end_time.isoformat(),
                'duration_seconds': duration,
                'success': False,
                'error': str(e),
                'videos_synced': 0,
                'aweme_ids': [],
                'errors': [str(e)]
            }
            
            print(f"❌ 同步失败: {str(e)}")
            return error_result


async def interactive_sync_example():
    """交互式同步示例"""
    
    cookies = "your_douyin_cookies_here"
    manager = CollectionSyncManager(cookies)
    
    print("🎭 交互式收藏夹同步示例")
    print("=" * 50)
    
    try:
        # 1. 获取所有收藏夹
        print("\n📋 获取收藏夹列表...")
        collections = await manager.get_all_collections()
        
        if not collections:
            print("❌ 未找到收藏夹")
            return
        
        print(f"✅ 找到 {len(collections)} 个收藏夹:")
        
        # 2. 显示收藏夹列表
        for i, collection in enumerate(collections):
            create_time = datetime.fromtimestamp(collection['create_time']).strftime('%Y-%m-%d')
            print(f"   {i+1:2d}. {collection['name']} ({collection['count']} 个视频, 创建于 {create_time})")
        
        # 3. 用户选择
        print(f"\n请选择要同步的收藏夹 (1-{len(collections)}) 或输入 'all' 同步全部: ", end="")
        choice = input().strip().lower()
        
        if choice == 'all':
            # 同步所有收藏夹
            selected_collections = collections
        else:
            try:
                index = int(choice) - 1
                if 0 <= index < len(collections):
                    selected_collections = [collections[index]]
                else:
                    print("❌ 无效选择")
                    return
            except ValueError:
                print("❌ 无效输入")
                return
        
        # 4. 显示选中收藏夹的预览
        for collection in selected_collections:
            print(f"\n🔍 收藏夹预览: {collection['name']}")
            videos = await manager.get_collection_videos_preview(collection['id'], 3)
            
            if videos:
                for i, video in enumerate(videos, 1):
                    print(f"   {i}. {video['desc']} - {video['author']}")
            else:
                print("   (空收藏夹)")
        
        # 5. 确认同步
        print(f"\n将同步 {len(selected_collections)} 个收藏夹，继续吗？(y/n): ", end="")
        confirm = input().strip().lower()
        
        if confirm != 'y':
            print("❌ 同步已取消")
            return
        
        # 6. 执行同步
        sync_results = []
        
        for i, collection in enumerate(selected_collections, 1):
            print(f"\n[{i}/{len(selected_collections)}] ", end="")
            
            result = await manager.sync_collection_with_details(
                collection['id'], 
                collection['name']
            )
            sync_results.append(result)
            
            # 避免请求过于频繁
            if i < len(selected_collections):
                await asyncio.sleep(2)
        
        # 7. 生成报告
        print("\n" + "=" * 50)
        print("📊 同步报告")
        print("=" * 50)
        
        total_videos = sum(len(r.get('aweme_ids', [])) for r in sync_results)
        total_new_videos = sum(r.get('videos_synced', 0) for r in sync_results)
        successful_syncs = sum(1 for r in sync_results if r.get('success', False))
        
        print(f"同步收藏夹: {len(selected_collections)} 个")
        print(f"成功同步: {successful_syncs} 个")
        print(f"处理视频: {total_videos} 个")
        print(f"新增视频: {total_new_videos} 个")
        
        # 详细结果
        print(f"\n📋 详细结果:")
        for result in sync_results:
            name = result['collection_name']
            duration = result.get('duration_seconds', 0)
            
            if result.get('success', False):
                videos = len(result.get('aweme_ids', []))
                new_videos = result.get('videos_synced', 0)
                print(f"✅ {name}: {videos} 个视频, {new_videos} 个新增, {duration:.1f}s")
            else:
                error = result.get('error', 'Unknown error')
                print(f"❌ {name}: 失败 - {error[:50]}...")
        
        # 保存报告
        report_filename = f"sync_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_filename, 'w', encoding='utf-8') as f:
            json.dump(sync_results, f, ensure_ascii=False, indent=2, default=str)
        
        print(f"\n💾 同步报告已保存到: {report_filename}")
        
    except Exception as e:
        print(f"❌ 示例执行失败: {str(e)}")


async def scheduled_sync_example():
    """定期同步示例"""
    
    cookies = "your_douyin_cookies_here"
    manager = CollectionSyncManager(cookies)
    
    print("⏰ 定期同步示例")
    print("=" * 50)
    
    # 模拟定期同步逻辑
    sync_interval = 60  # 60秒间隔（实际使用中可能是几小时或几天）
    max_iterations = 3  # 演示用，只执行3次
    
    for iteration in range(1, max_iterations + 1):
        print(f"\n🔄 第 {iteration} 次同步 (模拟定期任务)")
        print(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        try:
            collections = await manager.get_all_collections()
            
            if collections:
                # 只同步前2个收藏夹作为演示
                for collection in collections[:2]:
                    await manager.sync_collection_with_details(
                        collection['id'], 
                        collection['name']
                    )
                    await asyncio.sleep(1)
            
            print(f"✅ 第 {iteration} 次同步完成")
            
            if iteration < max_iterations:
                print(f"⏳ 等待 {sync_interval} 秒...")
                await asyncio.sleep(sync_interval)
            
        except Exception as e:
            print(f"❌ 第 {iteration} 次同步失败: {str(e)}")


if __name__ == "__main__":
    print("选择示例模式:")
    print("1. 交互式同步")
    print("2. 定期同步演示")
    print("请输入选择 (1/2): ", end="")
    
    choice = input().strip()
    
    if choice == "1":
        asyncio.run(interactive_sync_example())
    elif choice == "2":
        asyncio.run(scheduled_sync_example())
    else:
        print("❌ 无效选择")