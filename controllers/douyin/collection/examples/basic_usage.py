"""
抖音收藏夹控制器基础用法示例

演示如何使用收藏夹控制器的基本功能
"""

import asyncio
from controllers.douyin.collection import collection_controller


async def basic_usage_example():
    """基础用法示例"""
    
    # 注意：实际使用时需要提供有效的Cookie
    cookies = "your_douyin_cookies_here"
    
    print("🚀 抖音收藏夹控制器基础用法示例")
    print("=" * 50)
    
    try:
        # 1. 获取收藏夹列表
        print("\n📋 获取收藏夹列表...")
        collections_response = await collection_controller.get_self_aweme_collection_rpc_with_cookies(
            cursor=0,
            count=10,
            cookies=cookies
        )
        
        if collections_response and collections_response.collects_list:
            print(f"✅ 成功获取 {len(collections_response.collects_list)} 个收藏夹")
            for i, collection in enumerate(collections_response.collects_list[:3]):  # 只显示前3个
                print(f"   {i+1}. {collection.collects_name} (ID: {collection.collects_id})")
        else:
            print("❌ 未获取到收藏夹数据")
            return
        
        # 2. 获取第一个收藏夹的视频列表
        first_collection = collections_response.collects_list[0]
        print(f"\n🎬 获取收藏夹 '{first_collection.collects_name}' 的视频...")
        
        videos_response = await collection_controller.get_collect_video_list_rpc_with_cookies(
            collects_id=first_collection.collects_id,
            cursor=0,
            count=5,
            cookies=cookies
        )
        
        if videos_response and videos_response.aweme_list:
            print(f"✅ 成功获取 {len(videos_response.aweme_list)} 个视频")
            for i, video in enumerate(videos_response.aweme_list):
                print(f"   {i+1}. {video.desc[:30]}... (ID: {video.aweme_id})")
        else:
            print("❌ 收藏夹中没有视频")
        
        # 3. 同步收藏夹数据到数据库
        print(f"\n💾 同步收藏夹数据到数据库...")
        sync_result = await collection_controller.sync_and_save_single_collection_with_cookies(
            collection_id=first_collection.collects_id,
            cookies=cookies
        )
        
        print(f"✅ 同步完成:")
        print(f"   - 处理视频: {len(sync_result['aweme_ids'])} 个")
        print(f"   - 新增视频: {sync_result['videos_synced']} 个")
        print(f"   - 收藏夹同步: {sync_result['collections_synced']} 个")
        
        if sync_result['errors']:
            print(f"⚠️  处理过程中的错误: {sync_result['errors']}")
        
    except Exception as e:
        print(f"❌ 示例执行失败: {str(e)}")
        print("💡 请确保提供了有效的Cookie和网络连接")


if __name__ == "__main__":
    print("请注意：此示例需要有效的抖音Cookie才能正常运行")
    print("如果没有Cookie，示例将会失败")
    print("继续执行吗？(y/n): ", end="")
    
    response = input().lower()
    if response == 'y':
        asyncio.run(basic_usage_example())
    else:
        print("示例已取消")