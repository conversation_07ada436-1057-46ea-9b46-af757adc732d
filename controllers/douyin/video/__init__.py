"""
抖音视频功能模块

提供抖音视频相关的所有功能，包括视频控制器、数据获取器、URL解析器、RPC客户端等。
重构后的模块结构更加清晰，职责分明：
- controller.py: 主要的视频控制器，统一处理所有视频业务逻辑
- fetcher.py: 视频数据获取器，整合多种获取方法
- url_parser.py: URL解析器，处理各种抖音URL格式
- rpc_client.py: RPC客户端，专门处理RPC接口调用
"""

from .controller import DouyinVideoController, douyin_video_controller
from .fetcher import VideoFetcherController
from .url_parser import VideoUrlParseController
from .rpc_client import DouyinRPCVideoController, douyin_rpc_video_controller

__all__ = [
    # 主控制器
    "DouyinVideoController",
    "douyin_video_controller",
    # 数据获取器
    "VideoFetcherController", 
    # URL解析器
    "VideoUrlParseController",
    # RPC客户端
    "DouyinRPCVideoController",
    "douyin_rpc_video_controller",
]