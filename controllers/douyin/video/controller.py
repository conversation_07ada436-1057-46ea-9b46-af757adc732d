"""
抖音视频控制器

统一处理抖音视频相关的所有业务逻辑，包括：
- 视频ID处理和数据提取
- 多种提取方法的管道处理（数据库 → 移动端URL → 精选 → RPC API）
- TrendInsight 相关的视频处理功能
- 批量视频数据获取和处理
"""

import asyncio
import logging
from typing import Any, Dict, List, Optional, TYPE_CHECKING

if TYPE_CHECKING:
    from controllers.douyin.video.rpc_client import DouyinRPCVideoController

from fastapi import HTTPException
from loguru import logger

from models.douyin.models import DouyinAweme, update_douyin_aweme
from utils.douyin.extract.douyin_data_extractor import (
    AwemeItem,
    DouyinDataExtractor,
    ProcessUrlResult,
)
from utils.douyin.extract.jingxuan_exceptions import (
    JingxuanNetworkError,
    JingxuanTimeoutError,
    Jing<PERSON>uanParsingError,
    Jing<PERSON>uanDataError,
    <PERSON><PERSON><PERSON><PERSON>onversionError,
    Jing<PERSON><PERSON><PERSON><PERSON>dationError,
    JingxuanExtractionError,
)
from mappers.douyin.rpc_mapper import RPCDataMapper
from rpc.douyin import async_douyin_api
from rpc.douyin.schemas import VideoDetailRequest
from rpc.trendinsight.config import TrendInsightConfig

from .fetcher import VideoFetcherController


class DouyinVideoController:
    """抖音视频控制器 - 统一处理视频相关的所有业务逻辑"""

    def __init__(self, douyin_controller: Optional["DouyinController"] = None):
        """
        初始化视频控制器

        Args:
            douyin_controller: 抖音主控制器实例，用于数据转换等功能
        """
        self.douyin_controller = douyin_controller
        self.video_fetcher = VideoFetcherController()
        self.logger = logging.getLogger(__name__)
        self.config = TrendInsightConfig()

    async def process_video_id(self, aweme_id: str) -> Dict:
        """
        处理视频ID并提取相关数据

        提取管道优先级顺序：数据库 → 移动端URL → 精选 → RPC API
        整合了原 DouyinVideoController 和 VideoProcessController 的逻辑

        Args:
            aweme_id: 抖音视频ID

        Returns:
            Dict: 处理后的视频数据
        """
        logger.info(f"开始处理视频ID: {aweme_id}")

        try:
            # 验证视频ID格式
            if not aweme_id.isdigit():
                logger.error(f"无效的视频ID格式: {aweme_id}")
                raise HTTPException(status_code=400, detail="无效的视频ID格式")

            # 步骤1: 先尝试从数据库获取
            logger.info(f"步骤1: 尝试从数据库获取视频数据, aweme_id: {aweme_id}")
            try:
                db_video = await DouyinAweme.filter(aweme_id=aweme_id).first()
                if db_video:
                    logger.info(f"数据库查询成功，找到视频数据, aweme_id: {aweme_id}")
                    return self._build_response(aweme_id, await db_video.to_dict(), "database")
                else:
                    logger.info(f"数据库中未找到视频数据，继续下一步提取, aweme_id: {aweme_id}")
            except Exception as e:
                logger.error(f"数据库查询失败, aweme_id: {aweme_id}, 错误: {str(e)}")

            # 步骤2: 通过移动端URL处理
            logger.info(f"步骤2: 尝试移动端URL提取, aweme_id: {aweme_id}")
            try:
                result = await self._process_by_mobile_url(aweme_id)
                if result:
                    logger.info(f"移动端URL提取成功, aweme_id: {aweme_id}")
                    return result
                else:
                    logger.warning(f"移动端URL提取失败，继续精选提取, aweme_id: {aweme_id}")
            except Exception as e:
                logger.error(f"移动端URL提取异常, aweme_id: {aweme_id}, 错误: {str(e)}")

            # 步骤3: 如果移动端处理失败，尝试精选提取
            logger.info(f"步骤3: 尝试精选提取, aweme_id: {aweme_id}")
            try:
                result = await self._process_by_jingxuan_url(aweme_id)
                if result:
                    logger.info(f"精选提取成功, aweme_id: {aweme_id}")
                    return result
                else:
                    logger.warning(f"精选提取失败，继续RPC API提取, aweme_id: {aweme_id}")
            except Exception as e:
                logger.error(f"精选提取异常, aweme_id: {aweme_id}, 错误: {str(e)}")

            # 步骤4: 如果精选提取也失败，则通过RPC接口获取
            logger.info(f"步骤4: 尝试RPC API提取, aweme_id: {aweme_id}")
            try:
                result = await self._process_by_rpc_api(aweme_id)
                logger.info(f"RPC API提取成功, aweme_id: {aweme_id}")
                return result
            except Exception as e:
                logger.error(f"RPC API提取失败, aweme_id: {aweme_id}, 错误: {str(e)}")
                raise HTTPException(status_code=500, detail=f"所有提取方法均失败，最后错误: {str(e)}")

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"处理视频ID时发生未预期错误, aweme_id: {aweme_id}, 错误: {str(e)}")
            raise HTTPException(status_code=500, detail=f"处理视频ID失败: {str(e)}")

    async def fetch_missing_video_details(self, aweme_ids: List[str]) -> Dict[str, Any]:
        """
        批量获取缺失的视频详情
        
        来自原 VideoProcessController 的功能

        Args:
            aweme_ids: 视频ID列表

        Returns:
            Dict: 批量处理结果
        """
        logger.info(f"开始批量获取缺失视频详情，数量: {len(aweme_ids)}")
        
        results = {
            "total": len(aweme_ids),
            "success": 0,
            "failed": 0,
            "errors": [],
            "processed_videos": []
        }

        # 并发处理，但限制并发数量
        semaphore = asyncio.Semaphore(5)  # 最多同时处理5个
        
        async def fetch_single(aweme_id: str) -> Optional[Dict]:
            async with semaphore:
                try:
                    return await self._fetch_single_video_detail(aweme_id)
                except Exception as e:
                    logger.error(f"获取视频详情失败, aweme_id: {aweme_id}, 错误: {str(e)}")
                    results["errors"].append({"aweme_id": aweme_id, "error": str(e)})
                    results["failed"] += 1
                    return None

        # 并发执行所有任务
        tasks = [fetch_single(aweme_id) for aweme_id in aweme_ids]
        video_results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 统计结果
        for result in video_results:
            if result and not isinstance(result, Exception):
                results["processed_videos"].append(result)
                results["success"] += 1
        
        logger.info(f"批量获取完成，成功: {results['success']}, 失败: {results['failed']}")
        return results

    async def fetch_video_trend_scores(self, aweme_ids: List[str]) -> Dict[str, Any]:
        """
        获取视频趋势分数
        
        来自原 VideoProcessController 的功能

        Args:
            aweme_ids: 视频ID列表

        Returns:
            Dict: 趋势分数结果
        """
        logger.info(f"开始获取视频趋势分数，数量: {len(aweme_ids)}")
        
        # 这里应该调用 TrendInsight 相关的 API
        # 目前返回模拟结果
        results = {
            "total": len(aweme_ids),
            "trend_scores": {}
        }
        
        for aweme_id in aweme_ids:
            # 模拟趋势分数计算
            results["trend_scores"][aweme_id] = {
                "trend_score": 0.0,
                "engagement_rate": 0.0,
                "growth_rate": 0.0,
                "calculated_at": "2025-01-01T00:00:00Z"
            }
        
        logger.info(f"趋势分数计算完成，处理数量: {len(results['trend_scores'])}")
        return results

    async def process_keyword_video_index_data(self, keyword: str, video_data: List[Dict]) -> Dict[str, Any]:
        """
        处理关键词视频索引数据
        
        来自原 VideoProcessController 的功能

        Args:
            keyword: 关键词
            video_data: 视频数据列表

        Returns:
            Dict: 处理结果
        """
        logger.info(f"开始处理关键词视频索引数据，关键词: {keyword}, 视频数量: {len(video_data)}")
        
        results = {
            "keyword": keyword,
            "total_videos": len(video_data),
            "processed": 0,
            "errors": []
        }
        
        for video in video_data:
            try:
                # 处理单个视频的索引数据
                await self._process_single_video_index(keyword, video)
                results["processed"] += 1
            except Exception as e:
                logger.error(f"处理视频索引失败, video_id: {video.get('aweme_id')}, 错误: {str(e)}")
                results["errors"].append({
                    "video_id": video.get("aweme_id"),
                    "error": str(e)
                })
        
        logger.info(f"关键词视频索引处理完成，成功: {results['processed']}, 失败: {len(results['errors'])}")
        return results

    # === 私有方法 ===

    async def _process_by_mobile_url(self, aweme_id: str) -> Optional[Dict]:
        """
        通过移动端URL处理视频ID

        Args:
            aweme_id: 抖音视频ID

        Returns:
            Optional[Dict]: 处理成功返回数据，失败返回None
        """
        from rpc.douyin.html_handler.url_manager import DouyinURLManager

        mobile_url = DouyinURLManager.build_mobile_share_url(aweme_id)
        logger.info(f"开始移动端URL提取, URL: {mobile_url}")

        try:
            with DouyinDataExtractor() as extractor:
                result: ProcessUrlResult = extractor.process_url(mobile_url)

                if not (result.success and result.video_info):
                    error_msg = result.error or "未知错误"
                    logger.warning(f"移动端URL HTML解析失败, aweme_id: {aweme_id}, 错误: {error_msg}")
                    return None

                aweme_item: AwemeItem = result.video_info
                logger.info(f"移动端URL HTML解析成功, aweme_id: {aweme_id}")

                # 验证下载链接有效性
                logger.info(f"验证移动端URL提取的下载链接有效性, aweme_id: {aweme_id}")
                if not await self._validate_download_url(aweme_item):
                    logger.warning(f"移动端URL提取的下载链接无效, aweme_id: {aweme_id}")
                    return None

                logger.info(f"移动端URL提取的下载链接验证通过, aweme_id: {aweme_id}")

                # 转换并保存数据
                logger.info(f"开始转换和保存移动端URL提取的数据, aweme_id: {aweme_id}")
                aweme_data = self.douyin_controller._convert_html_aweme_to_db_model(aweme_item, by_mobile=True)
                saved_data = await self._save_and_return_data(aweme_id, aweme_data, aweme_item, "mobile_extraction")

                logger.info(f"移动端URL提取完成, aweme_id: {aweme_id}")
                return saved_data

        except Exception as e:
            logger.error(f"移动端URL提取过程中发生异常, aweme_id: {aweme_id}, 错误: {str(e)}")
            return None

    async def _process_by_jingxuan_url(self, aweme_id: str) -> Optional[Dict]:
        """
        通过精选URL处理视频ID，使用 VideoFetcherController

        Args:
            aweme_id: 抖音视频ID

        Returns:
            Optional[Dict]: 处理成功返回数据，失败返回None
        """
        logger.info(f"[精选提取] 开始精选提取方法获取视频信息, aweme_id: {aweme_id}")

        try:
            # 使用 VideoFetcherController 获取精选数据
            fetch_result = await self.video_fetcher.fetch_video_data(aweme_id, "jingxuan")

            if not fetch_result.success:
                error_msg = fetch_result.error or "未知错误"
                logger.warning(f"[精选提取] 精选提取失败, aweme_id: {aweme_id}, 错误: {error_msg}")
                return None

            if not fetch_result.data:
                logger.warning(f"[精选提取] 精选提取成功但未获得视频信息, aweme_id: {aweme_id}")
                return None

            logger.info(f"[精选提取] 精选提取成功, aweme_id: {aweme_id}")

            # 验证数据质量
            logger.info(f"[精选提取] 开始验证精选提取数据质量, aweme_id: {aweme_id}")
            if not await self._validate_jingxuan_data_quality(fetch_result.data, aweme_id):
                logger.warning(f"[精选提取] 精选提取数据质量验证失败, aweme_id: {aweme_id}")
                return None

            logger.info(f"[精选提取] 精选提取数据质量验证通过, aweme_id: {aweme_id}")
            logger.info(f"[精选提取] 精选提取完成, aweme_id: {aweme_id}")

            return self._build_response(aweme_id, fetch_result.data, "jingxuan_extraction")

        except JingxuanNetworkError as e:
            logger.error(
                f"[精选提取] 网络错误, aweme_id: {aweme_id}, 状态码: {getattr(e, 'status_code', 'unknown')}, 错误: {str(e)}"
            )
            return None
        except JingxuanTimeoutError as e:
            logger.error(
                f"[精选提取] 请求超时, aweme_id: {aweme_id}, 超时时间: {getattr(e, 'timeout_seconds', 'unknown')}秒, 错误: {str(e)}"
            )
            return None
        except JingxuanParsingError as e:
            logger.error(
                f"[精选提取] HTML解析错误, aweme_id: {aweme_id}, 解析阶段: {getattr(e, 'parsing_stage', 'unknown')}, 错误: {str(e)}"
            )
            return None
        except JingxuanDataError as e:
            logger.error(
                f"[精选提取] 数据错误, aweme_id: {aweme_id}, 数据类型: {getattr(e, 'data_type', 'unknown')}, 错误: {str(e)}"
            )
            return None
        except JingxuanConversionError as e:
            logger.error(
                f"[精选提取] 数据转换错误, aweme_id: {aweme_id}, 转换阶段: {getattr(e, 'conversion_stage', 'unknown')}, 错误: {str(e)}"
            )
            return None
        except JingxuanValidationError as e:
            logger.error(
                f"[精选提取] 数据验证错误, aweme_id: {aweme_id}, 验证类型: {getattr(e, 'validation_type', 'unknown')}, 错误: {str(e)}"
            )
            return None
        except JingxuanExtractionError as e:
            logger.error(
                f"[精选提取] 精选提取基础错误, aweme_id: {aweme_id}, 错误代码: {getattr(e, 'error_code', 'unknown')}, 错误: {str(e)}"
            )
            return None
        except Exception as e:
            logger.error(
                f"[精选提取] 精选提取过程中发生未知异常, aweme_id: {aweme_id}, 异常类型: {type(e).__name__}, 错误: {str(e)}"
            )
            return None

    async def _process_by_rpc_api(self, aweme_id: str) -> Dict:
        """
        通过RPC接口处理视频ID，使用 VideoFetcherController

        Args:
            aweme_id: 抖音视频ID

        Returns:
            Dict: 处理后的视频数据
        """
        logger.info(f"开始RPC接口获取视频信息, aweme_id: {aweme_id}")

        try:
            # 使用 VideoFetcherController 获取RPC数据
            fetch_result = await self.video_fetcher.fetch_video_data(aweme_id, "rpc")

            if not fetch_result.success:
                error_msg = fetch_result.error or "RPC获取失败"
                logger.error(f"RPC API提取失败, aweme_id: {aweme_id}, 错误: {error_msg}")
                raise HTTPException(status_code=500, detail=f"RPC获取视频数据失败: {error_msg}")

            if not fetch_result.data:
                logger.error(f"RPC API提取成功但未获得视频信息, aweme_id: {aweme_id}")
                raise HTTPException(status_code=500, detail="RPC获取视频数据为空")

            logger.info(f"RPC接口获取视频数据成功, aweme_id: {aweme_id}")
            logger.info(f"RPC API提取完成, aweme_id: {aweme_id}")

            return self._build_response(aweme_id, fetch_result.data, "rpc_api")

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"RPC API提取过程中发生异常, aweme_id: {aweme_id}, 错误: {str(e)}")
            raise HTTPException(status_code=500, detail=f"RPC获取视频数据失败: {str(e)}")

    async def _fetch_single_video_detail(self, aweme_id: str) -> Optional[Dict]:
        """
        获取单个视频详情
        
        来自原 VideoProcessController 的私有方法

        Args:
            aweme_id: 视频ID

        Returns:
            Optional[Dict]: 视频详情数据
        """
        try:
            # 通过 douyin RPC API 获取视频详情
            request = VideoDetailRequest(aweme_id=aweme_id)
            video_detail = await async_douyin_api.get_video_detail(request)

            # 转换为数据库模型格式
            mapper = RPCDataMapper()
            processed_data = mapper.convert_video_info_to_aweme_data(video_detail.aweme_detail)

            # 将 AwemeDataItem 转换为 DouyinAweme 实例
            aweme_dict = processed_data.model_dump()
            aweme_instance = DouyinAweme(**aweme_dict)

            # 保存到数据库
            success = await update_douyin_aweme(aweme_instance)

            if success:
                logger.info(f"视频详情获取并保存成功, aweme_id: {aweme_id}")
                return await aweme_instance.to_dict()
            else:
                logger.warning(f"视频详情获取成功但保存失败, aweme_id: {aweme_id}")
                return processed_data.model_dump()

        except Exception as e:
            logger.error(f"获取视频详情失败, aweme_id: {aweme_id}, 错误: {str(e)}")
            return None

    async def _process_single_video_index(self, keyword: str, video_data: Dict) -> None:
        """
        处理单个视频的索引数据

        Args:
            keyword: 关键词
            video_data: 视频数据
        """
        # 这里应该实现视频索引的具体逻辑
        # 目前是空实现
        pass

    async def _validate_jingxuan_data_quality(self, data: Dict[str, Any], aweme_id: str) -> bool:
        """
        验证精选提取数据质量

        Args:
            data: 从精选提取的视频数据
            aweme_id: 视频ID

        Returns:
            bool: 数据质量是否符合要求
        """
        logger.info(f"[精选提取] 开始验证精选数据质量, aweme_id: {aweme_id}")

        try:
            # 1. 验证aweme_id一致性
            extracted_id = str(data.get("aweme_id", ""))
            if extracted_id != aweme_id:
                logger.warning(f"[精选提取] aweme_id不匹配，期望: {aweme_id}, 实际: {extracted_id}")
                return False

            # 2. 验证必要字段存在
            required_fields = ["title", "desc", "author_name"]
            for field in required_fields:
                if not data.get(field):
                    logger.warning(f"[精选提取] 缺少必要字段: {field}, aweme_id: {aweme_id}")
                    return False

            # 3. 验证数据合理性
            if isinstance(data.get("statistics"), dict):
                stats = data["statistics"]
                # 检查统计数据是否合理（不能为负数）
                for stat_key in ["digg_count", "comment_count", "share_count", "play_count"]:
                    if stat_key in stats and stats[stat_key] < 0:
                        logger.warning(f"[精选提取] 统计数据异常: {stat_key}={stats[stat_key]}, aweme_id: {aweme_id}")
                        return False

            logger.info(f"[精选提取] 精选数据质量验证通过, aweme_id: {aweme_id}")
            return True

        except Exception as e:
            logger.error(f"[精选提取] 数据质量验证过程中发生异常, aweme_id: {aweme_id}, 错误: {str(e)}")
            return False

    async def _validate_download_url(self, aweme_item: AwemeItem) -> bool:
        """
        验证下载链接有效性

        Args:
            aweme_item: 视频数据项

        Returns:
            bool: 链接是否有效
        """
        # 这里应该实现下载链接验证的具体逻辑
        # 目前返回 True 表示总是通过验证
        return True

    async def _save_and_return_data(
        self, aweme_id: str, aweme_data: Dict, aweme_item: AwemeItem, source: str
    ) -> Dict:
        """
        保存数据并返回响应

        Args:
            aweme_id: 视频ID
            aweme_data: 转换后的数据库模型数据
            aweme_item: 原始视频数据项
            source: 数据来源

        Returns:
            Dict: 响应数据
        """
        try:
            # 保存到数据库
            aweme_instance = DouyinAweme(**aweme_data)
            success = await update_douyin_aweme(aweme_instance)
            
            if success:
                # 重新从数据库获取完整数据
                db_video = await DouyinAweme.filter(aweme_id=aweme_id).first()
                if db_video:
                    return self._build_response(aweme_id, await db_video.to_dict(), source)
            
            # 保存失败或数据库查询失败，返回原始数据
            return self._build_response(aweme_id, aweme_data, source)
            
        except Exception as e:
            logger.error(f"保存数据失败, aweme_id: {aweme_id}, 错误: {str(e)}")
            # 即使保存失败，也返回处理的数据
            return self._build_response(aweme_id, aweme_data, source)

    def _build_response(self, aweme_id: str, data: Dict, source: str) -> Dict:
        """
        构建标准响应格式

        Args:
            aweme_id: 视频ID
            data: 视频数据
            source: 数据来源

        Returns:
            Dict: 标准响应格式
        """
        return {
            "video_id": aweme_id,
            "input_type": "aweme_id",
            "original_input": aweme_id,
            "processed": True,
            "data": data,
            "source": source,
        }


# 创建全局实例
douyin_video_controller = DouyinVideoController()