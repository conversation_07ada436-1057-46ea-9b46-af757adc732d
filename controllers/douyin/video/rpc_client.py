"""
抖音RPC视频控制器

专门处理基于RPC接口的视频数据获取功能
"""

from typing import Dict

from fastapi import HTTPException

from core.crud import CRUDBase
from models.douyin.models import DouyinAweme
from models.enums import Platform
from rpc.douyin import async_douyin_api
from rpc.douyin.schemas import (
    VideoDetailRequest,
    VideoDetailResponse,
    UserInfoRequest,
)
from schemas.douyin import DouyinAwemeCreate, DouyinAwemeUpdate


class DouyinRPCVideoController(CRUDBase[DouyinAweme, DouyinAwemeCreate, DouyinAwemeUpdate]):
    """抖音RPC视频控制器 - 专门处理RPC相关的视频功能"""

    def __init__(self):
        super().__init__(model=DouyinAweme)

    async def _get_douyin_client(self):
        """获取抖音客户端实例"""
        return async_douyin_api



    # RPC + 传入cookies，返回RPC类型
    async def get_video_rpc_with_cookies(self, video_id: str, cookies: str) -> VideoDetailResponse:
        """
        传入cookies获取视频详情，返回RPC原响应格式

        Args:
            video_id: 视频ID
            cookies: 抖音网站的Cookie字符串

        Returns:
            VideoDetailResponse: RPC原响应格式
        """
        try:
            client = await self._get_douyin_client()
            request = VideoDetailRequest(aweme_id=video_id)

            # 传递cookies给RPC客户端
            response = await client.get_video_detail(request, cookies)
            return response
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"获取视频详情失败: {str(e)}")

    # RPC + 自动获取cookies，返回RPC类型
    async def get_video_rpc_auto_cookies(self, video_id: str) -> VideoDetailResponse:
        """
        自动获取cookies并获取视频详情，返回RPC原响应格式

        Args:
            video_id: 视频ID

        Returns:
            VideoDetailResponse: RPC原响应格式
        """
        try:
            # 从数据库获取cookies
            cookies = await self._get_cookies_from_db(Platform.DOUYIN)

            client = await self._get_douyin_client()
            request = VideoDetailRequest(aweme_id=video_id)

            # 传递cookies给RPC客户端
            response = await client.get_video_detail(request, cookies)
            return response
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"获取视频详情失败: {str(e)}")

    # 数据库 + 传入cookies，返回dict
    async def get_video_db_with_cookies(self, video_id: str, cookies: str) -> Dict:
        """
        传入cookies获取视频详情，返回数据库模型格式

        Args:
            video_id: 视频ID
            cookies: 抖音网站的Cookie字符串

        Returns:
            Dict: 数据库模型字典格式
        """
        try:
            # 先从数据库查询
            db_video = await DouyinAweme.filter(aweme_id=video_id).first()
            if db_video:
                return await db_video.to_dict()

            # 数据库没有则调用RPC获取并保存
            client = await self._get_douyin_client()
            request = VideoDetailRequest(aweme_id=video_id)

            # 传递cookies给RPC客户端
            rpc_response = await client.get_video_detail(request, cookies)

            # 转换数据格式
            from mappers.douyin.rpc_mapper import RPCDataMapper
            mapper = RPCDataMapper()
            aweme_data_dict = mapper.convert_video_detail_response_to_db_model(rpc_response)

            # 创建 DouyinAweme 实例
            aweme_instance = DouyinAweme(**aweme_data_dict)

            # 使用参考文件的方式保存到数据库
            from models.douyin.models import update_douyin_aweme

            success = await update_douyin_aweme(aweme_instance)

            if not success:
                raise HTTPException(status_code=500, detail="保存视频数据到数据库失败")

            # 重新查询已保存的数据并返回
            db_video = await DouyinAweme.filter(aweme_id=video_id).first()
            if db_video:
                return await db_video.to_dict()
            else:
                raise HTTPException(status_code=500, detail="保存后查询视频数据失败")

        except Exception as e:
            raise HTTPException(status_code=500, detail=f"获取视频详情失败: {str(e)}")

    # 数据库 + 自动获取cookies，返回dict
    async def get_video_db_auto_cookies(self, video_id: str) -> Dict:
        """
        自动获取cookies并获取视频详情，返回数据库模型格式

        Args:
            video_id: 视频ID

        Returns:
            Dict: 数据库模型字典格式
        """
        try:
            # 先从数据库查询
            db_video = await DouyinAweme.filter(aweme_id=video_id).first()
            if db_video:
                return await db_video.to_dict()

            # 数据库没有则获取cookies并调用RPC
            cookies = await self._get_cookies_from_db(Platform.DOUYIN)

            client = await self._get_douyin_client()
            request = VideoDetailRequest(aweme_id=video_id)

            # 传递cookies给RPC客户端
            rpc_response = await client.get_video_detail(request, cookies)

            # 转换数据格式
            from mappers.douyin.rpc_mapper import RPCDataMapper
            mapper = RPCDataMapper()
            aweme_data_dict = mapper.convert_video_detail_response_to_db_model(rpc_response)

            # 创建 DouyinAweme 实例
            aweme_instance = DouyinAweme(**aweme_data_dict)

            # 使用参考文件的方式保存到数据库
            from models.douyin.models import update_douyin_aweme

            success = await update_douyin_aweme(aweme_instance)

            if not success:
                raise HTTPException(status_code=500, detail="保存视频数据到数据库失败")

            # 重新查询已保存的数据并返回
            db_video = await DouyinAweme.filter(aweme_id=video_id).first()
            if db_video:
                return await db_video.to_dict()
            else:
                raise HTTPException(status_code=500, detail="保存后查询视频数据失败")

        except Exception as e:
            raise HTTPException(status_code=500, detail=f"获取视频详情失败: {str(e)}")

    async def validate_cookies(self, cookies: str) -> Dict:
        """
        验证传入的Cookies是否可用

        Args:
            cookies: Cookie 字符串

        Returns:
            Dict: 验证结果
        """
        try:
            # 创建抖音客户端
            client = await self._get_douyin_client()

            # 使用pong方法验证cookies
            is_valid = await client.pong(cookies)

            if is_valid:
                return {"valid": True, "message": "Cookies验证成功"}
            else:
                return {"valid": False, "message": "Cookies验证失败"}

        except Exception as e:
            return {"valid": False, "message": f"Cookies验证失败: {str(e)}"}

    async def get_user_info_with_cookies(self, sec_user_id: str, cookies: str) -> Dict:
        """
        传入cookies获取用户信息，返回RPC原响应格式

        Args:
            sec_user_id: 用户的加密ID
            cookies: 抖音网站的Cookie字符串

        Returns:
            Dict: RPC原响应格式的用户信息
        """
        try:
            # 创建抖音客户端
            client = await self._get_douyin_client()

            # 创建用户信息请求
            request = UserInfoRequest(sec_user_id=sec_user_id)

            # 调用RPC获取用户信息
            response = await client.get_user_info(request, cookies)

            # 将 Pydantic 模型转换为字典格式返回
            return response.model_dump()

        except Exception as e:
            raise HTTPException(status_code=500, detail=f"获取用户信息失败: {str(e)}")


# 创建控制器实例
douyin_rpc_video_controller = DouyinRPCVideoController()
