"""
Unified video fetcher controller that integrates all three fetch methods.
"""

import asyncio
from typing import Any, Dict, List, Optional

from loguru import logger

from models.douyin import DouyinFetchMethod
from mappers.douyin.exceptions import (
    ConfigurationException,
    FallbackExhaustedException,
    NetworkException,
)
from mappers.douyin.jingxuan_mapper import JingxuanDataMapper
from mappers.douyin.mobile_mapper import MobileDataMapper
from mappers.douyin.models import BatchFetchResult, FetcherConfig, FetchResult
from mappers.douyin.pydantic_models import DouyinVideoData
from mappers.douyin.rpc_mapper import RPCDataMapper


class VideoFetcherController:
    """
    统一的视频获取控制器，整合三种获取方法
    """

    def __init__(self, config: Optional[FetcherConfig] = None):
        """
        初始化视频获取控制器

        Args:
            config: 获取器配置
        """
        self.config = config or FetcherConfig()

        if not self.config.validate():
            raise ConfigurationException("Invalid fetcher configuration")

        # 初始化映射器
        self.mobile_mapper = MobileDataMapper()
        self.jingxuan_mapper = JingxuanDataMapper()
        self.rpc_mapper = RPCDataMapper()

        # 映射器字典
        self._mappers = {
            DouyinFetchMethod.MOBILE.value: self.mobile_mapper,
            DouyinFetchMethod.JINGXUAN.value: self.jingxuan_mapper,
            DouyinFetchMethod.RPC.value: self.rpc_mapper,
        }

        # 并发控制
        self._semaphore = asyncio.Semaphore(self.config.max_concurrent)

        # 性能统计
        self._stats = {
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "method_usage": {method: 0 for method in self._mappers.keys()},
            "method_success": {method: 0 for method in self._mappers.keys()},
        }

    async def fetch_video_data(
        self, aweme_id: str, method: str, options: Optional[Dict[str, Any]] = None
    ) -> FetchResult[DouyinVideoData]:
        """
        使用指定方法获取视频数据

        Args:
            aweme_id: 视频ID
            method: 获取方法，使用 DouyinFetchMethod 枚举值
            options: 额外选项

        Returns:
            FetchResult: 获取结果
        """
        if method not in self._mappers:
            raise ValueError(f"Unsupported method: {method}. Supported: {list(self._mappers.keys())}")

        result = FetchResult(success=False, aweme_id=aweme_id, method=method, source=method)

        self._stats["total_requests"] += 1
        self._stats["method_usage"][method] += 1

        try:
            # 使用信号量控制并发
            async with self._semaphore:
                raw_data = await self._fetch_raw_data(aweme_id, method, options)
                mapper = self._mappers[method]

                # 映射数据到标准格式
                standard_data: DouyinVideoData = mapper.map_to_standard_format(raw_data)

                # 保存到数据库（如果配置允许）
                if self.config.save_to_db:
                    await self._save_to_database(standard_data.model_dump())

                result.data = standard_data
                result.mark_completed(success=True)

                self._stats["successful_requests"] += 1
                self._stats["method_success"][method] += 1

                if self.config.enable_detailed_logging:
                    logger.info(f"Successfully fetched video data: {result.get_summary()}")

        except Exception as e:
            error_msg = f"Failed to fetch video data using {method}: {str(e)}"
            result.mark_completed(success=False, error=error_msg)
            result.exception_type = type(e).__name__

            self._stats["failed_requests"] += 1

            if self.config.enable_detailed_logging:
                logger.error(f"Failed to fetch video data: {result.get_summary()}")

            if self.config.fail_fast:
                raise

        return result

    async def fetch_with_fallback(
        self, aweme_id: str, methods: Optional[List[str]] = None, options: Optional[Dict[str, Any]] = None
    ) -> FetchResult[DouyinVideoData]:
        """
        使用回退策略获取视频数据

        Args:
            aweme_id: 视频ID
            methods: 要尝试的方法列表，如果为None则使用配置中的回退方法
            options: 额外选项

        Returns:
            FetchResult: 获取结果
        """
        if not self.config.enable_fallback:
            raise ConfigurationException("Fallback is disabled in configuration")

        if methods is None:
            methods = self.config.fallback_methods.copy()

        # 验证方法列表
        for method in methods:
            if method not in self._mappers:
                raise ValueError(f"Unsupported method: {method}")

        result: FetchResult[DouyinVideoData] = FetchResult(
            success=False, aweme_id=aweme_id, method="fallback", source="fallback"
        )

        method_errors = {}

        for method in methods:
            result.add_attempted_method(method)

            try:
                if self.config.enable_detailed_logging:
                    logger.info(f"Trying method {method} for aweme_id {aweme_id}")

                method_result = await self.fetch_video_data(aweme_id, method, options)

                if method_result.success:
                    # 成功获取数据，更新结果
                    result.data = method_result.data
                    result.method = method
                    result.source = method
                    result.response_time = method_result.response_time
                    result.mark_completed(success=True)

                    if self.config.enable_detailed_logging:
                        logger.info(f"Fallback succeeded with method {method}: {result.get_summary()}")

                    return result
                else:
                    method_errors[method] = method_result.error

            except Exception as e:
                error_msg = f"Method {method} failed: {str(e)}"
                method_errors[method] = error_msg

                if self.config.enable_detailed_logging:
                    logger.warning(f"Method {method} failed for aweme_id {aweme_id}: {error_msg}")

        # 所有方法都失败了
        error_msg = f"All fallback methods failed for aweme_id {aweme_id}"
        result.mark_completed(success=False, error=error_msg)
        result.metadata["method_errors"] = method_errors

        if self.config.enable_detailed_logging:
            logger.error(f"All fallback methods failed: {result.get_summary()}")

        if self.config.raise_on_all_failed:
            raise FallbackExhaustedException(
                error_msg, attempted_methods=result.attempted_methods, method_errors=method_errors
            )

        return result

    async def batch_fetch(
        self, aweme_ids: List[str], method: Optional[str] = None, options: Optional[Dict[str, Any]] = None
    ) -> BatchFetchResult[DouyinVideoData]:
        """
        批量获取视频数据

        Args:
            aweme_ids: 视频ID列表
            method: 获取方法，如果为None则使用回退策略
            options: 额外选项

        Returns:
            BatchFetchResult: 批量获取结果
        """
        batch_result: BatchFetchResult[DouyinVideoData] = BatchFetchResult(total_count=len(aweme_ids))

        if self.config.enable_detailed_logging:
            logger.info(f"Starting batch fetch for {len(aweme_ids)} videos")

        # 创建任务列表
        tasks = []
        for aweme_id in aweme_ids:
            if method:
                task = self.fetch_video_data(aweme_id, method, options)
            else:
                task = self.fetch_with_fallback(aweme_id, None, options)
            tasks.append(task)

        # 并发执行所有任务
        try:
            results = await asyncio.gather(*tasks, return_exceptions=True)

            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    # 创建失败结果
                    failed_result = FetchResult(
                        success=False,
                        aweme_id=aweme_ids[i],
                        method=method or "fallback",
                        source=method or "fallback",
                        error=str(result),
                        exception_type=type(result).__name__,
                    )
                    failed_result.mark_completed(success=False, error=str(result))
                    batch_result.add_result(failed_result)
                else:
                    batch_result.add_result(result)

        except Exception as e:
            logger.error(f"Batch fetch failed: {str(e)}")
            raise

        batch_result.mark_completed()

        if self.config.enable_detailed_logging:
            logger.info(f"Batch fetch completed: {batch_result.get_summary()}")

        return batch_result

    async def _fetch_raw_data(
        self, aweme_id: str, method: str, options: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        获取原始数据（具体的获取逻辑需要根据现有控制器实现）

        Args:
            aweme_id: 视频ID
            method: 获取方法
            options: 额外选项

        Returns:
            Dict[str, Any]: 原始数据
        """
        options = options or {}

        if method == DouyinFetchMethod.MOBILE.value:
            return await self._fetch_mobile_data(aweme_id, options)
        elif method == DouyinFetchMethod.JINGXUAN.value:
            return await self._fetch_jingxuan_data(aweme_id, options)
        elif method == DouyinFetchMethod.RPC.value:
            return await self._fetch_rpc_data(aweme_id, options)
        else:
            raise ValueError(f"Unknown method: {method}")

    async def _fetch_mobile_data(self, aweme_id: str, options: Dict[str, Any]) -> Dict[str, Any]:
        """
        获取移动端数据 - 使用强类型服务层

        Args:
            aweme_id: 视频ID
            options: 额外选项

        Returns:
            Dict[str, Any]: 移动端原始数据
        """
        # 使用HTML控制器获取移动端数据
        from ..html.html import DouyinHTMLController

        html_controller = DouyinHTMLController()
        result = await html_controller.fetch_mobile_share_video_data(
            aweme_id=aweme_id,
            use_proxy=options.get("use_proxy", True) if options else True,
            custom_headers=options.get("custom_headers") if options else None,
            timeout=options.get("timeout", 30) if options else 30,
            save_to_db=options.get("save_to_db", True) if options else True,
        )

        if result.get("success", False):
            # 返回HTML控制器的原始数据
            return result.get("data", {})
        else:
            error_msg = f"Mobile data fetch failed for {aweme_id}: {result.get('error', 'Unknown error')}"
            raise NetworkException(error_msg, context={"result": result})

    async def _fetch_jingxuan_data(self, aweme_id: str, options: Dict[str, Any]) -> Dict[str, Any]:
        """
        获取精选页面数据

        Args:
            aweme_id: 视频ID
            options: 额外选项

        Returns:
            Dict[str, Any]: 精选页面原始数据
        """
        # 使用HTML控制器获取精选页面数据
        from ..html.html import DouyinHTMLController

        html_controller = DouyinHTMLController()
        result = await html_controller.fetch_jingxuan_video_data(
            aweme_id=aweme_id,
            use_proxy=options.get("use_proxy", True),
            custom_headers=options.get("custom_headers"),
            timeout=options.get("timeout", 30),
            save_to_db=options.get("save_to_db", True),
        )

        if result.get("success", False):
            # 返回HTML控制器的原始数据
            return result.get("data", {})
        else:
            error_msg = f"Jingxuan data fetch failed for {aweme_id}: {result.get('error', 'Unknown error')}"
            raise NetworkException(error_msg, context={"result": result})

    async def _fetch_rpc_data(self, aweme_id: str, options: Dict[str, Any]) -> Dict[str, Any]:
        """
        获取RPC数据

        Args:
            aweme_id: 视频ID
            options: 额外选项

        Returns:
            Dict[str, Any]: RPC原始数据
        """
        # 使用RPC视频控制器获取RPC数据
        from .rpc_client import DouyinRPCVideoController

        video_controller = DouyinRPCVideoController()
        result = await video_controller.get_video_db_auto_cookies(aweme_id)

        if isinstance(result, dict):
            # RPC控制器返回字典格式的数据
            return result
        else:
            error_msg = f"RPC data fetch failed for {aweme_id}: Invalid result type"
            raise NetworkException(error_msg, context={"result": result})

    async def _save_to_database(self, data: Dict[str, Any]) -> None:
        """
        保存数据到数据库

        Args:
            data: 标准格式的数据
        """
        try:
            from models.douyin.models import update_douyin_aweme

            await update_douyin_aweme(data)
        except Exception as e:
            logger.error(f"Failed to save data to database: {str(e)}")
            # 不抛出异常，避免影响主要流程

    def get_stats(self) -> Dict[str, Any]:
        """
        获取统计信息

        Returns:
            Dict[str, Any]: 统计信息
        """
        stats = self._stats.copy()

        # 计算成功率
        if stats["total_requests"] > 0:
            stats["success_rate"] = stats["successful_requests"] / stats["total_requests"]
        else:
            stats["success_rate"] = 0.0

        # 计算各方法的成功率
        for method in self._mappers.keys():
            usage = stats["method_usage"][method]
            success = stats["method_success"][method]
            if usage > 0:
                stats[f"{method}_success_rate"] = success / usage
            else:
                stats[f"{method}_success_rate"] = 0.0

        return stats

    def reset_stats(self) -> None:
        """
        重置统计信息
        """
        self._stats = {
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "method_usage": {method: 0 for method in self._mappers.keys()},
            "method_success": {method: 0 for method in self._mappers.keys()},
        }

    async def fetch_video_data_auto(
        self,
        aweme_id: str,
        preferred_method: str = DouyinFetchMethod.JINGXUAN.value,
        fallback_methods: Optional[List[str]] = None,
        use_proxy: bool = True,
        save_to_db: bool = True,
    ) -> Dict:
        """
        智能获取视频数据，支持多种方法和回退机制

        这是一个高级接口，提供与主控制器兼容的返回格式。
        对于新代码，建议使用 fetch_with_fallback() 方法。

        Args:
            aweme_id: 抖音视频ID
            preferred_method: 首选方法，使用 DouyinFetchMethod 枚举值
            fallback_methods: 回退方法列表，如果首选方法失败则依次尝试
            use_proxy: 是否使用代理
            save_to_db: 是否保存到数据库

        Returns:
            Dict: 包含success、data、source、method等字段的结果
        """
        if fallback_methods is None:
            fallback_methods = [DouyinFetchMethod.MOBILE.value, DouyinFetchMethod.RPC.value]

        logger.info(f"智能获取视频数据: {aweme_id}, 首选方法: {preferred_method}")

        # 先从数据库查询
        try:
            from models.douyin.models import DouyinAweme
            db_video = await DouyinAweme.filter(aweme_id=aweme_id).first()
            if db_video:
                logger.info(f"从数据库获取到视频数据: {aweme_id}")
                return {
                    "success": True,
                    "data": await db_video.to_dict(),
                    "source": "database",
                    "method": "database",
                    "aweme_id": aweme_id,
                }
        except Exception as e:
            logger.warning(f"查询数据库失败: {e}")

        # 构建方法列表
        methods = [preferred_method] + [m for m in fallback_methods if m != preferred_method]

        last_error = None

        for method in methods:
            try:
                logger.info(f"尝试使用方法: {method}")

                # 使用 fetch_with_fallback 获取数据
                config = FetcherConfig(
                    preferred_methods=[method],
                    enable_fallback=False,  # 我们自己处理回退
                    use_proxy=use_proxy,
                    save_to_db=save_to_db,
                )

                result = await self.fetch_with_fallback(aweme_id, config)

                if result.success:
                    # 转换为兼容格式
                    return {
                        "success": True,
                        "data": result.data.model_dump() if result.data else None,
                        "source": result.source,
                        "method": method,
                        "aweme_id": aweme_id,
                    }
                else:
                    last_error = result.get_error_summary()
                    logger.warning(f"方法 {method} 失败: {last_error}")

            except Exception as e:
                last_error = str(e)
                logger.error(f"方法 {method} 异常: {e}")
                continue

        # 所有方法都失败
        logger.error(f"所有方法都失败，无法获取视频数据: {aweme_id}")
        return {
            "success": False,
            "aweme_id": aweme_id,
            "error": f"所有获取方法都失败: {last_error}",
            "methods_tried": methods,
        }
