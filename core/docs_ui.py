"""
自定义 OpenAPI 文档 UI 配置
提供 Swagger UI 和 ReDoc 的自定义样式和功能
"""

from fastapi import FastAPI
from fastapi.responses import HTMLResponse
from starlette.requests import Request


def get_custom_swagger_ui_html(
    *,
    openapi_url: str,
    title: str,
    swagger_js_url: str = "https://cdn.jsdelivr.net/npm/swagger-ui-dist@5.9.0/swagger-ui-bundle.js",
    swagger_css_url: str = "https://cdn.jsdelivr.net/npm/swagger-ui-dist@5.9.0/swagger-ui.css",
    swagger_favicon_url: str = "https://fastapi.tiangolo.com/img/favicon.png",
    oauth2_redirect_url: str = None,
    init_oauth: dict = None,
    swagger_ui_parameters: dict = None,
) -> HTMLResponse:
    """
    自定义 Swagger UI HTML
    """
    current_swagger_ui_parameters = {
        "dom_id": "#swagger-ui",
        "layout": "BaseLayout",
        "deepLinking": True,
        "showExtensions": True,
        "showCommonExtensions": True,
        "tryItOutEnabled": True,
        "requestInterceptor": """
        (request) => {
            // 自动添加 Bearer 前缀
            if (request.headers.Authorization && !request.headers.Authorization.startsWith('Bearer ')) {
                request.headers.Authorization = 'Bearer ' + request.headers.Authorization;
            }
            return request;
        }
        """,
        "responseInterceptor": """
        (response) => {
            // 可以在这里处理响应
            return response;
        }
        """,
        "docExpansion": "list",  # none, list, full
        "defaultModelsExpandDepth": 2,
        "defaultModelExpandDepth": 2,
        "displayOperationId": False,
        "displayRequestDuration": True,
        "filter": True,
        "showMutatedRequest": True,
        "supportedSubmitMethods": ["get", "post", "put", "delete", "patch"],
        "validatorUrl": None,
    }

    if swagger_ui_parameters:
        current_swagger_ui_parameters.update(swagger_ui_parameters)

    html = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <link type="text/css" rel="stylesheet" href="{swagger_css_url}">
        <link rel="shortcut icon" href="{swagger_favicon_url}">
        <title>{title}</title>
        <style>
            /* 自定义样式 */
            .swagger-ui .topbar {{
                background-color: #1f2937;
                border-bottom: 1px solid #374151;
            }}
            .swagger-ui .topbar .download-url-wrapper .select-label {{
                color: #f9fafb;
            }}
            .swagger-ui .topbar .download-url-wrapper input[type=text] {{
                background-color: #374151;
                border: 1px solid #6b7280;
                color: #f9fafb;
            }}
            .swagger-ui .info .title {{
                color: #1f2937;
            }}
            .swagger-ui .scheme-container {{
                background: #f8fafc;
                border: 1px solid #e2e8f0;
                border-radius: 4px;
                padding: 10px;
                margin: 10px 0;
            }}
            .swagger-ui .auth-wrapper {{
                background: #f0f9ff;
                border: 1px solid #0ea5e9;
                border-radius: 4px;
                padding: 10px;
                margin: 10px 0;
            }}
            .swagger-ui .btn.authorize {{
                background-color: #0ea5e9;
                border-color: #0ea5e9;
            }}
            .swagger-ui .btn.authorize:hover {{
                background-color: #0284c7;
                border-color: #0284c7;
            }}
            /* 操作按钮样式 */
            .swagger-ui .opblock.opblock-get .opblock-summary-method {{
                background: #10b981;
            }}
            .swagger-ui .opblock.opblock-post .opblock-summary-method {{
                background: #3b82f6;
            }}
            .swagger-ui .opblock.opblock-put .opblock-summary-method {{
                background: #f59e0b;
            }}
            .swagger-ui .opblock.opblock-delete .opblock-summary-method {{
                background: #ef4444;
            }}
            /* 响应状态码样式 */
            .swagger-ui .responses-inner h4 {{
                font-size: 14px;
                margin: 10px 0 5px 0;
            }}
            .swagger-ui .response-col_status {{
                font-weight: bold;
            }}
            /* 模型样式 */
            .swagger-ui .model-box {{
                background: #f8fafc;
                border: 1px solid #e2e8f0;
                border-radius: 4px;
            }}
        </style>
    </head>
    <body>
        <div id="swagger-ui">
        </div>
        <script src="{swagger_js_url}"></script>
        <script>
        const ui = SwaggerUIBundle({{
            url: '{openapi_url}',
    """

    for key, value in current_swagger_ui_parameters.items():
        if isinstance(value, str) and not value.startswith("(") and not value.startswith("function"):
            html += f'        {key}: "{value}",\n'
        else:
            html += f"        {key}: {value},\n"

    if oauth2_redirect_url:
        html += f'        oauth2RedirectUrl: "{oauth2_redirect_url}",\n'

    html += """        presets: [
            SwaggerUIBundle.presets.apis,
            SwaggerUIBundle.presets.standalone
        ],
        plugins: [
            SwaggerUIBundle.plugins.DownloadUrl
        ],
    })"""

    if init_oauth:
        html += f"""
        ui.initOAuth({init_oauth})
        """

    html += """
        </script>
    </body>
    </html>
    """

    return HTMLResponse(html)


def get_custom_redoc_html(
    *,
    openapi_url: str,
    title: str,
    redoc_js_url: str = "https://cdn.jsdelivr.net/npm/redoc@2.1.3/bundles/redoc.standalone.js",
    redoc_favicon_url: str = "https://fastapi.tiangolo.com/img/favicon.png",
    with_google_fonts: bool = True,
) -> HTMLResponse:
    """
    自定义 ReDoc HTML
    """
    html = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>{title}</title>
        <meta charset="utf-8"/>
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <link rel="shortcut icon" href="{redoc_favicon_url}">
        """
    if with_google_fonts:
        html += """
        <link href="https://fonts.googleapis.com/css?family=Montserrat:300,400,700|Roboto:300,400,700" rel="stylesheet">
        """
    html += f"""
        <style>
            body {{
                margin: 0;
                padding: 0;
                font-family: 'Roboto', sans-serif;
            }}
            redoc {{
                --redoc-color-primary: #1f2937;
                --redoc-color-primary-light: #374151;
                --redoc-color-success: #10b981;
                --redoc-color-warning: #f59e0b;
                --redoc-color-error: #ef4444;
                --redoc-color-info: #3b82f6;
                --redoc-font-family: 'Roboto', sans-serif;
                --redoc-font-size: 14px;
                --redoc-code-font-family: 'Monaco', 'Consolas', 'Lucida Console', monospace;
            }}
        </style>
    </head>
    <body>
        <redoc spec-url='{openapi_url}'></redoc>
        <script src="{redoc_js_url}"> </script>
    </body>
    </html>
    """
    return HTMLResponse(html)


def setup_custom_docs_ui(app: FastAPI) -> None:
    """
    设置自定义文档 UI
    """

    @app.get("/docs", include_in_schema=False)
    async def custom_swagger_ui_html(req: Request):
        root_path = req.scope.get("root_path", "").rstrip("/")
        openapi_url = root_path + app.openapi_url
        return get_custom_swagger_ui_html(
            openapi_url=openapi_url,
            title=f"{app.title} - Swagger UI",
            swagger_ui_parameters={
                "persistAuthorization": True,  # 持久化认证信息
                "displayRequestDuration": True,  # 显示请求耗时
                "filter": True,  # 启用搜索过滤
                "tryItOutEnabled": True,  # 启用试用功能
                "syntaxHighlight.theme": "agate",  # 语法高亮主题
            },
        )

    @app.get("/redoc", include_in_schema=False)
    async def redoc_html(req: Request):
        root_path = req.scope.get("root_path", "").rstrip("/")
        openapi_url = root_path + app.openapi_url
        return get_custom_redoc_html(
            openapi_url=openapi_url,
            title=f"{app.title} - ReDoc",
            with_google_fonts=True,
        )
