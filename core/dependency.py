from fastapi import Depends
from fastapi.security import HTT<PERSON><PERSON><PERSON><PERSON>

# from models import Role, User  # Disabled - admin models removed

# 创建 HTTPBearer 安全方案，用于 OpenAPI 文档
security = HTTPBearer(
    scheme_name="BearerAuth", description="JWT Bearer Token 认证，请在 Value 字段中输入 token（不需要 'Bearer ' 前缀）"
)


# Authentication disabled - admin models removed
# class AuthControl:
#     @classmethod
#     async def is_authed_header(cls, token: str = Header(..., description="token验证")) -> Optional["User"]:
#         """通过 Header 方式进行认证（兼容旧版本）"""
#         return await cls._authenticate_token(token)

#     @classmethod
#     async def is_authed_bearer(cls, credentials: HTTPAuthorizationCredentials = Depends(security)) -> Optional["User"]:
#         """通过 HTTPBearer 方式进行认证（推荐，支持 OpenAPI 文档）"""
#         return await cls._authenticate_token(credentials.credentials)

#     @classmethod
#     async def _authenticate_token(cls, token: str) -> Optional["User"]:
#         """通用的 token 认证逻辑"""
#         try:
#             if token == "dev":
#                 user = await User.filter().first()
#                 user_id = user.id
#             else:
#                 decode_data = jwt.decode(token, settings.SECRET_KEY, algorithms=settings.JWT_ALGORITHM)
#                 user_id = decode_data.get("user_id")
#             user = await User.filter(id=user_id).first()
#             if not user:
#                 raise HTTPException(status_code=401, detail="Authentication failed")
#             CTX_USER_ID.set(int(user_id))
#             return user
#         except jwt.DecodeError:
#             raise HTTPException(status_code=401, detail="无效的Token")
#         except jwt.ExpiredSignatureError:
#             raise HTTPException(status_code=401, detail="登录已过期")
#         except Exception as e:
#             raise HTTPException(status_code=500, detail=f"{repr(e)}")

#     # 为了兼容性，保留原方法名
#     @classmethod
#     async def is_authed(cls, token: str = Header(..., description="token验证")) -> Optional["User"]:
#         """兼容旧版本的认证方法"""
#         return await cls.is_authed_header(token)


# Permission control disabled - admin models removed
# class PermissionControl:
#     @classmethod
#     async def has_permission_header(
#         cls, request: Request, current_user: User = Depends(AuthControl.is_authed_header)
#     ) -> None:
#         """通过 Header 方式进行权限检查（兼容旧版本）"""
#         await cls._check_permission(request, current_user)

#     @classmethod
#     async def has_permission_bearer(
#         cls, request: Request, current_user: User = Depends(AuthControl.is_authed_bearer)
#     ) -> None:
#         """通过 HTTPBearer 方式进行权限检查（推荐，支持 OpenAPI 文档）"""
#         await cls._check_permission(request, current_user)

#     @classmethod
#     async def _check_permission(cls, request: Request, current_user: User) -> None:
#         """通用的权限检查逻辑"""
#         if current_user.is_superuser:
#             return
#         method = request.method
#         path = request.url.path
#         roles: list[Role] = await current_user.roles
#         if not roles:
#             raise HTTPException(status_code=403, detail="The user is not bound to a role")
#         apis = [await role.apis for role in roles]
#         permission_apis = list(set((api.method, api.path) for api in sum(apis, [])))
#         # path = "/api/v1/auth/userinfo"
#         # method = "GET"
#         if (method, path) not in permission_apis:
#             raise HTTPException(status_code=403, detail=f"Permission denied method:{method} path:{path}")

#     # 为了兼容性，保留原方法名
#     @classmethod
#     async def has_permission(cls, request: Request, current_user: User = Depends(AuthControl.is_authed)) -> None:
#         """兼容旧版本的权限检查方法"""
#         await cls.has_permission_header(request, current_user)


# Dependencies disabled - admin models removed
# DependAuth = Depends(AuthControl.is_authed)
# DependPermission = Depends(PermissionControl.has_permission)
# DependAuthBearer = Depends(AuthControl.is_authed_bearer)
# DependPermissionBearer = Depends(PermissionControl.has_permission_bearer)


# Placeholder dependencies for compatibility
def no_auth():
    """Placeholder for disabled authentication"""
    return None


def no_permission():
    """Placeholder for disabled permission check"""
    return None


DependAuth = Depends(no_auth)
DependPermission = Depends(no_permission)
DependAuthBearer = Depends(no_auth)
DependPermissionBearer = Depends(no_permission)
