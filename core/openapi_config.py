"""
OpenAPI 配置模块
提供 FastAPI 应用的 OpenAPI 文档配置
"""

from typing import Any, Dict, List

from fastapi import FastAPI
from fastapi.openapi.utils import get_openapi

from settings.config import settings


def get_openapi_tags() -> List[Dict[str, Any]]:
    """
    定义 OpenAPI 标签和分组
    """
    return [
        {
            "name": "抖音 API",
            "description": "抖音平台数据接口，包括视频详情获取、用户信息查询等功能。所有接口都需要传入有效的抖音cookies进行身份验证。",
            "externalDocs": {"description": "抖音开发者文档", "url": "https://developer.douyin.com/"},
        },
        {
            "name": "TrendInsight API",
            "description": "TrendInsight（巨量引擎）平台数据接口，包括达人搜索、视频搜索、作者详情、视频指数等功能。所有接口都需要传入有效的TrendInsight cookies进行身份验证。",
            "externalDocs": {"description": "巨量引擎开发者文档", "url": "https://ads.tiktok.com/marketing_api/"},
        },
    ]


def get_openapi_servers() -> List[Dict[str, Any]]:
    """
    定义 OpenAPI 服务器信息
    """
    servers = [
        {
            "url": "http://localhost:8000",
            "description": "开发环境服务器",
        },
    ]

    # 根据环境添加不同的服务器配置
    if not settings.DEBUG:
        servers.extend(
            [
                {
                    "url": "https://api.example.com",
                    "description": "生产环境服务器",
                },
                {
                    "url": "https://staging-api.example.com",
                    "description": "测试环境服务器",
                },
            ]
        )

    return servers


def get_openapi_security_schemes() -> Dict[str, Any]:
    """
    定义 OpenAPI 安全认证方案
    """
    return {
        "BearerAuth": {
            "type": "http",
            "scheme": "bearer",
            "bearerFormat": "JWT",
            "description": "JWT Bearer Token 认证，格式：Bearer <token>",
        }
    }


def get_openapi_info() -> Dict[str, Any]:
    """
    获取 OpenAPI 基本信息
    """
    return {
        "title": settings.APP_TITLE,
        "description": f"""
## {settings.APP_DESCRIPTION}

这是一个基于 FastAPI 和 Vue.js 构建的现代化管理系统。

### 主要功能

- 🔐 **用户认证与授权**: 基于 JWT 的安全认证机制
- 👥 **用户管理**: 完整的用户生命周期管理
- 🛡️ **角色权限**: 灵活的 RBAC 权限控制系统
- 📋 **菜单管理**: 动态菜单配置与权限控制
- 🔌 **API管理**: 接口权限分配与监控
- 🏢 **部门管理**: 组织架构层级管理
- 📊 **审计日志**: 完整的操作日志记录

### 技术栈

- **后端**: FastAPI + Tortoise ORM + SQLite/MySQL/PostgreSQL
- **认证**: JWT + Argon2 密码加密
- **文档**: 自动生成的 OpenAPI 3.0 文档
- **日志**: Loguru 结构化日志记录

### 认证说明

大部分 API 需要 JWT Token 认证，请先通过 `/api/v1/base/access_token` 接口获取 token，
然后在请求头中添加：`Authorization: Bearer <your_token>`

### 联系信息

- **开发者**: mizhexiaoxiao
- **邮箱**: <EMAIL>
- **版本**: {settings.VERSION}
        """,
        "version": settings.VERSION,
        "contact": {
            "name": "mizhexiaoxiao",
            "email": "<EMAIL>",
        },
        "license": {
            "name": "MIT License",
            "url": "https://opensource.org/licenses/MIT",
        },
    }


def custom_openapi(app: FastAPI) -> Dict[str, Any]:
    """
    自定义 OpenAPI 配置生成函数
    """
    if app.openapi_schema:
        return app.openapi_schema

    # 过滤路由，只包含 douyin 和 trendinsight 相关的路由
    filtered_routes = []
    for route in app.routes:
        if hasattr(route, "path"):
            # 只包含 douyin 和 trendinsight 的 API 路由
            if "/douyin/" in route.path or "/trendinsight/" in route.path:
                filtered_routes.append(route)

    openapi_schema = get_openapi(
        title=settings.APP_TITLE,
        version=settings.VERSION,
        description=get_openapi_info()["description"],
        routes=filtered_routes,
        servers=get_openapi_servers(),
        tags=get_openapi_tags(),
    )

    # 添加安全认证配置
    openapi_schema["components"]["securitySchemes"] = get_openapi_security_schemes()

    # 由于我们只显示平台API，不需要JWT认证配置
    # 平台API使用cookies认证，无需添加全局安全要求

    # 添加通用响应模型
    if "components" not in openapi_schema:
        openapi_schema["components"] = {}
    if "schemas" not in openapi_schema["components"]:
        openapi_schema["components"]["schemas"] = {}

    # 添加通用响应模型定义
    openapi_schema["components"]["schemas"].update(
        {
            "SuccessResponse": {
                "type": "object",
                "properties": {
                    "code": {"type": "integer", "example": 200, "description": "响应状态码"},
                    "message": {"type": "string", "example": "Success", "description": "响应消息"},
                    "data": {"description": "响应数据"},
                },
                "required": ["code", "message"],
            },
            "ErrorResponse": {
                "type": "object",
                "properties": {
                    "code": {"type": "integer", "example": 400, "description": "错误状态码"},
                    "message": {"type": "string", "example": "Error message", "description": "错误消息"},
                    "detail": {"type": "string", "description": "详细错误信息"},
                },
                "required": ["code", "message"],
            },
            "PaginatedResponse": {
                "type": "object",
                "properties": {
                    "code": {"type": "integer", "example": 200, "description": "响应状态码"},
                    "message": {"type": "string", "example": "Success", "description": "响应消息"},
                    "data": {"type": "array", "items": {}, "description": "数据列表"},
                    "total": {"type": "integer", "description": "总记录数"},
                    "page": {"type": "integer", "description": "当前页码"},
                    "page_size": {"type": "integer", "description": "每页记录数"},
                },
                "required": ["code", "message", "data", "total", "page", "page_size"],
            },
        }
    )

    app.openapi_schema = openapi_schema
    return app.openapi_schema


def setup_openapi_docs(app: FastAPI) -> None:
    """
    设置 OpenAPI 文档配置
    """
    # 设置自定义 OpenAPI 生成函数
    app.openapi = lambda: custom_openapi(app)
