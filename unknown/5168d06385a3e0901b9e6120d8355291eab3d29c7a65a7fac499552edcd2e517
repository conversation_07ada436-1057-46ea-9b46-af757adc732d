#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
抖音URL处理工具
用于处理抖音URL的解析、转换和验证
使用RPC/douyin接口而非直接HTTP请求
"""

import re
from typing import Dict, List, Optional
from urllib.parse import parse_qs, urlparse
import logging

# 添加日志记录器
logger = logging.getLogger(__name__)


class DouyinUrlProcessor:
    """抖音URL处理器"""

    def __init__(self, timeout: int = 10):
        """
        初始化URL处理器

        Args:
            timeout: 请求超时时间（秒）
        """
        self.timeout = timeout
        self.url_pattern = re.compile(r"(https?:\/\/\w+[^\s]+\.[^\s]+){1,}")

    def extract_all_urls(self, input_text: str) -> List[str]:
        """
        从文本中提取所有URL

        Args:
            input_text: 输入文本

        Returns:
            URL列表
        """
        return self.url_pattern.findall(input_text)

    async def convert_short_url_to_full(self, url: str) -> str:
        """
        将短链接转换为完整链接，使用RPC接口

        Args:
            url: 短链接URL

        Returns:
            完整链接URL

        Raises:
            Exception: 请求失败时抛出
        """
        try:
            from rpc.douyin import async_douyin_api
            from rpc.douyin.schemas import UrlResolveRequest

            request = UrlResolveRequest(url=url)
            response = await async_douyin_api.resolve_url(request)

            if response and response.resolved_url:
                return response.resolved_url
            else:
                raise Exception("URL解析失败：未获取到解析结果")
        except Exception as e:
            raise Exception(f"转换URL失败: {e}")

    def extract_video_id(self, url: str) -> Optional[str]:
        """
        从URL中提取视频ID

        Args:
            url: 抖音视频URL

        Returns:
            视频ID，如果提取失败返回None
        """
        # 首先尝试从URL路径中提取video ID
        pattern = r"/video/(\d+)/?"
        match = re.search(pattern, url)
        if match:
            return match.group(1)

        # 如果路径中没有找到，尝试从query参数中提取modal_id
        parsed_url = urlparse(url)
        query_params = parse_qs(parsed_url.query)

        if "modal_id" in query_params:
            modal_id = query_params["modal_id"][0]  # parse_qs返回列表，取第一个值
            return modal_id

        return None

    async def verify_video_download_url(self, download_url: str) -> bool:
        """
        验证抖音视频下载链接是否有效，使用RPC接口

        Args:
            download_url: 视频下载链接

        Returns:
            布尔值，表示链接是否有效
        """
        if not download_url:
            return False

        try:
            from rpc.douyin import async_douyin_api
            from rpc.douyin.schemas import UrlValidationRequest

            request = UrlValidationRequest(url=download_url)
            response = await async_douyin_api.validate_url(request)

            if response and response.is_valid:
                return True
            else:
                logging.error(f"下载链接验证失败: {response.message if response else '未知错误'}")
                return False

        except Exception as e:
            logging.error(f"验证下载链接时发生错误: {str(e)}")
            return False

    async def process_douyin_url(self, input_url: str) -> Dict[str, str]:
        """
        处理抖音URL，返回处理后的各种URL

        Args:
            input_url: 输入的URL（可能是短链接）

        Returns:
            包含各种URL的字典
        """
        result = {
            "original_url": input_url,
            "target_url": None,
            "mobile_url": None,
            "target_mobile_url": None,
            "video_id": None,
            "error": None,
        }

        try:
            # 提取URL
            urls = self.extract_all_urls(input_url)
            if not urls:
                result["error"] = "未找到有效的URL"
                return result

            # 转换为完整URL
            target_url = await self.convert_short_url_to_full(urls[0])
            result["target_url"] = target_url

            # 提取视频ID
            video_id = self.extract_video_id(target_url)
            if not video_id:
                result["error"] = "无法提取视频ID"
                return result

            result["video_id"] = video_id

            # 生成移动端URL
            from rpc.douyin.html_handler.url_manager import DouyinURLManager

            mobile_url = DouyinURLManager.build_mobile_share_url(video_id)
            result["mobile_url"] = mobile_url

            # 转换移动端URL
            target_mobile_url = await self.convert_short_url_to_full(mobile_url)
            result["target_mobile_url"] = target_mobile_url

        except Exception as e:
            result["error"] = str(e)

        return result


def extract_douyin_user_id(user_aweme_url: str) -> str:
    """
    从抖音用户URL中提取用户ID

    Args:
        user_aweme_url: 抖音用户链接，格式如 https://www.douyin.com/user/{user_id}

    Returns:
        str: 提取的用户ID，如果提取失败返回空字符串

    Examples:
        >>> extract_douyin_user_id("https://www.douyin.com/user/MS4wLjABAAAA123")
        "MS4wLjABAAAA123"
        >>> extract_douyin_user_id("https://www.douyin.com/user/123456789")
        "123456789"
        >>> extract_douyin_user_id("invalid_url")
        ""
    """
    if not user_aweme_url or not isinstance(user_aweme_url, str):
        return ""

    try:
        # 使用正则表达式匹配抖音用户URL模式
        # 支持多种可能的URL格式
        patterns = [
            r"https?://(?:www\.)?douyin\.com/user/([^/?#&]+)",  # 标准格式
            r"https?://(?:www\.)?douyin\.com/user/([^/?#&]+)/?",  # 带尾部斜杠
            r"douyin\.com/user/([^/?#&]+)",  # 不带协议
        ]

        for pattern in patterns:
            match = re.search(pattern, user_aweme_url)
            if match:
                user_id = match.group(1)
                # 验证提取的用户ID不为空且不包含特殊字符
                if user_id and len(user_id.strip()) > 0:
                    return user_id.strip()

        # 如果正则匹配失败，尝试使用URL解析
        parsed_url = urlparse(user_aweme_url)
        if "douyin.com" in parsed_url.netloc and "/user/" in parsed_url.path:
            path_parts = parsed_url.path.strip("/").split("/")
            if len(path_parts) >= 2 and path_parts[0] == "user":
                user_id = path_parts[1]
                if user_id and len(user_id.strip()) > 0:
                    return user_id.strip()

        logger.warning(f"extract_douyin_user_id: 无法从URL中提取用户ID - {user_aweme_url}")
        return ""

    except Exception as e:
        logger.error(f"extract_douyin_user_id: 提取用户ID时发生错误 - {e}, URL: {user_aweme_url}")
        return ""
