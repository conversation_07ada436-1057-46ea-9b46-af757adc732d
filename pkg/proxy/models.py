"""
代理模块的数据类型定义
"""

from dataclasses import dataclass
from typing import Optional


@dataclass
class IpInfoModel:
    """代理 IP 信息模型"""

    ip: str
    port: int
    protocol: str = "http"
    user: Optional[str] = None
    password: Optional[str] = None

    def to_proxy_url(self) -> str:
        """转换为代理 URL 格式"""
        if self.user and self.password:
            return f"{self.protocol}://{self.user}:{self.password}@{self.ip}:{self.port}"
        return f"{self.protocol}://{self.ip}:{self.port}"

    def to_dict(self) -> dict:
        """转换为字典格式，用于 requests 等库"""
        proxy_url = self.to_proxy_url()
        return {"http": proxy_url, "https": proxy_url}

    def __str__(self) -> str:
        return f"{self.ip}:{self.port}"

    @classmethod
    def from_string(cls, proxy_str: str, protocol: str = "http") -> "IpInfoModel":
        """
        从字符串格式创建代理信息

        Args:
            proxy_str: 代理字符串，格式为 "IP:端口"
            protocol: 协议类型，默认为 "http"

        Returns:
            IpInfoModel 实例

        Raises:
            ValueError: 字符串格式不正确时抛出异常
        """
        try:
            ip, port = proxy_str.split(":")
            return cls(ip=ip.strip(), port=int(port.strip()), protocol=protocol)
        except ValueError as e:
            raise ValueError(f"无效的代理字符串格式: {proxy_str}") from e
