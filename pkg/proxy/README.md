# 代理 IP 池模块

一个功能强大、易于使用的代理 IP 池模块，支持多种代理提供商，具备智能缓存、代理验证和故障转移等特性。

## 主要特性

- 🚀 **高性能**: 异步操作，支持并发请求
- 🔄 **智能缓存**: 减少API调用，提高响应速度
- ✅ **代理验证**: 自动验证代理有效性
- 🛡️ **故障转移**: 自动处理无效代理，确保服务稳定
- 🔌 **可扩展**: 支持多种代理提供商
- 📊 **监控友好**: 提供详细的状态信息和日志

## 支持的代理提供商

- [快代理 (KuaiDaiLi)](https://www.kuaidaili.com/) - 高质量的代理服务

## 安装依赖

```bash
pip install aiohttp aiocache
```

## 快速开始

### 基本使用

```python
import asyncio
from pkg.proxy import create_ip_pool

async def main():
    # 创建代理池
    pool = create_ip_pool(
        provider_type="kuaidaili",
        api_key="your_api_key",
        api_secret="your_api_secret"
    )
    
    try:
        # 加载代理
        await pool.load_proxies(count=10)
        
        # 获取代理
        proxy = await pool.get_proxy()
        print(f"代理: {proxy.to_proxy_url()}")
        
        # 获取池状态
        status = await pool.get_pool_status()
        print(f"状态: {status}")
        
    finally:
        await pool.close()

asyncio.run(main())
```

### 高级配置

```python
from pkg.proxy import create_kuaidaili_pool

# 使用便捷工厂函数
pool = create_kuaidaili_pool(
    api_key="your_api_key",
    api_secret="your_api_secret",
    cache_ttl=7200  # 缓存2小时
)
```

## API 文档

### 工厂函数

#### `create_ip_pool()`

创建代理池的主要工厂函数。

```python
def create_ip_pool(
    provider_type: str = "kuaidaili",
    api_key: Optional[str] = None,
    api_secret: Optional[str] = None,
    cache: Optional[BaseCache] = None,
    cache_ttl: int = 3600,
    **kwargs
) -> ProxyIpPool
```

**参数:**
- `provider_type`: 代理提供商类型 ("kuaidaili")
- `api_key`: API 密钥
- `api_secret`: API 密码
- `cache`: 自定义缓存实例
- `cache_ttl`: 缓存时间（秒）

#### `create_kuaidaili_pool()`

快代理专用的便捷工厂函数。

```python
def create_kuaidaili_pool(
    api_key: str,
    api_secret: Optional[str] = None,
    **kwargs
) -> ProxyIpPool
```

### ProxyIpPool 类

代理池的核心类。

#### 核心方法

- `load_proxies()`: 从代理提供商加载代理列表
- `get_proxy()`: 获取一个可用的代理（不会从缓存中删除，支持复用）
- `mark_ip_invalid(ip)`: 标记代理为无效并从缓存中删除
- `get_proxy_count()`: 获取当前可用代理数量
- `refresh_proxies()`: 刷新代理列表

#### 主要方法

```python
# 加载代理
async def load_proxies(self, count: int = 10) -> List[IpInfoModel]

# 获取单个代理（不会从缓存中删除，支持复用）
async def get_proxy(self) -> Optional[IpInfoModel]

# 批量获取代理
async def get_proxies(self, count: int) -> List[IpInfoModel]

# 标记代理为无效并从缓存中删除
async def mark_ip_invalid(self, ip: str) -> None

# 获取当前可用代理数量
async def get_proxy_count(self) -> int

# 刷新代理池
async def refresh_proxies(self, count: int = 10) -> List[IpInfoModel]

# 清除缓存
async def clear_cache(self) -> None

# 获取池状态
async def get_pool_status(self) -> dict

# 关闭资源
async def close(self) -> None
```

### IpInfoModel 类

代理信息模型。

```python
@dataclass
class IpInfoModel:
    ip: str
    port: int
    protocol: str = "http"
    username: Optional[str] = None
    password: Optional[str] = None
    location: Optional[str] = None
    
    def to_proxy_url(self) -> str:
        """转换为代理URL格式"""
        
    def to_dict(self) -> dict:
        """转换为字典格式"""
        
    @classmethod
    def from_string(cls, proxy_str: str) -> 'IpInfoModel':
        """从字符串创建实例"""
```

## 配置选项

### 缓存配置

```python
from aiocache import Cache

# 使用自定义缓存
custom_cache = Cache(Cache.REDIS, endpoint="localhost", port=6379)

pool = create_ip_pool(
    provider_type="kuaidaili",
    api_key="your_api_key",
    cache=custom_cache,
    cache_ttl=3600
)
```

## 错误处理

```python
try:
    pool = create_ip_pool(
        provider_type="kuaidaili",
        api_key="your_api_key"
    )
    
    proxies = await pool.load_proxies(count=10)
    
except ValueError as e:
    print(f"配置错误: {e}")
except Exception as e:
    print(f"运行时错误: {e}")
finally:
    await pool.close()
```

## 性能优化建议

1. **合理设置缓存时间**: 根据使用频率调整 `cache_ttl`
2. **批量获取代理**: 使用 `get_proxies()` 而不是多次调用 `get_proxy()`
3. **预加载代理**: 在应用启动时预先加载足够的代理

## 监控和调试

```python
# 获取详细状态
status = await pool.get_pool_status()
print(f"可用代理: {status['available_count']}")
print(f"总代理数: {status['total_count']}")
print(f"缓存状态: {status['cache_info']}")

# 启用详细日志
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 示例代码

查看 `example.py` 文件获取更多使用示例，包括：

- 基本使用
- 高级配置
- 错误处理
- 性能优化
- 自定义验证

## 许可证

本项目采用 MIT 许可证。

## 贡献

欢迎提交 Issue 和 Pull Request！

## 更新日志

### v1.0.0
- 重构代理池架构
- 添加智能缓存机制
- 改进错误处理
- 增强代理验证
- 添加批量操作支持
- 完善文档和示例
