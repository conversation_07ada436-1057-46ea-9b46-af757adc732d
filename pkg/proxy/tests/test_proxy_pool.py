"""
代理池模块的单元测试
"""

from unittest.mock import AsyncMock

import pytest

from pkg.proxy.factory import create_ip_pool, create_kuaidaili_pool
from pkg.proxy.models import IpInfoModel
from pkg.proxy.providers.kuai_daili_proxy import KuaiDaiLiProxy
from pkg.proxy.proxy_ip_pool import ProxyIpPool


class TestIpInfoModel:
    """测试 IpInfoModel 数据类"""

    def test_to_proxy_url_without_auth(self):
        """测试不带认证的代理 URL 生成"""
        proxy = IpInfoModel(ip="127.0.0.1", port=8080, protocol="http")
        assert proxy.to_proxy_url() == "http://127.0.0.1:8080"

    def test_to_proxy_url_with_auth(self):
        """测试带认证的代理 URL 生成"""
        proxy = IpInfoModel(ip="127.0.0.1", port=8080, protocol="http", user="username", password="password")
        assert proxy.to_proxy_url() == "***************************************"

    def test_to_dict(self):
        """测试转换为字典格式"""
        proxy = IpInfoModel(ip="127.0.0.1", port=8080, protocol="http")
        result = proxy.to_dict()
        expected = {"http": "http://127.0.0.1:8080", "https": "http://127.0.0.1:8080"}
        assert result == expected

    def test_str_representation(self):
        """测试字符串表示"""
        proxy = IpInfoModel(ip="127.0.0.1", port=8080)
        assert str(proxy) == "127.0.0.1:8080"

    def test_from_string(self):
        """测试从字符串创建代理信息"""
        proxy = IpInfoModel.from_string("***********:8080")
        assert proxy.ip == "***********"
        assert proxy.port == 8080
        assert proxy.protocol == "http"

    def test_from_string_with_protocol(self):
        """测试从字符串创建代理信息（指定协议）"""
        proxy = IpInfoModel.from_string("***********:8080", protocol="https")
        assert proxy.ip == "***********"
        assert proxy.port == 8080
        assert proxy.protocol == "https"

    def test_from_string_invalid_format(self):
        """测试无效格式的字符串"""
        with pytest.raises(ValueError, match="无效的代理字符串格式"):
            IpInfoModel.from_string("invalid")

        with pytest.raises(ValueError, match="无效的代理字符串格式"):
            IpInfoModel.from_string("***********")

        with pytest.raises(ValueError, match="无效的代理字符串格式"):
            IpInfoModel.from_string("***********:abc")


class TestProxyIpPool:
    """测试 ProxyIpPool 类"""

    @pytest.fixture
    def mock_provider(self):
        """创建模拟的代理提供商"""
        provider = AsyncMock(spec=KuaiDaiLiProxy)
        provider.get_proxies.return_value = [
            IpInfoModel(ip="***********", port=8080),
            IpInfoModel(ip="***********", port=8080),
            IpInfoModel(ip="***********", port=8080),
        ]
        provider.close.return_value = None
        return provider

    @pytest.fixture
    def proxy_pool(self, mock_provider):
        """创建代理池实例"""
        return ProxyIpPool(ip_provider=mock_provider)

    @pytest.mark.asyncio
    async def test_load_proxies(self, proxy_pool, mock_provider):
        """测试加载代理列表"""
        await proxy_pool.load_proxies(count=3)

        # 验证调用了提供商的方法
        mock_provider.get_proxies.assert_called_once_with(3)

        # 验证加载了正确的代理数量
        assert proxy_pool.get_proxy_count() == 3
        assert all(isinstance(p, IpInfoModel) for p in proxy_pool.proxy_list)

    @pytest.mark.asyncio
    async def test_get_proxy(self, proxy_pool):
        """测试获取代理"""
        await proxy_pool.load_proxies()

        proxy = await proxy_pool.get_proxy()

        # 验证返回了代理对象
        assert isinstance(proxy, IpInfoModel)

    @pytest.mark.asyncio
    async def test_mark_ip_invalid(self, proxy_pool):
        """测试标记代理为无效"""
        await proxy_pool.load_proxies(count=3)
        initial_count = proxy_pool.get_proxy_count()

        # 获取一个代理（现在不会从列表中删除）
        proxy = await proxy_pool.get_proxy()
        current_count = proxy_pool.get_proxy_count()

        # 验证获取代理后数量没有变化
        assert current_count == initial_count

        # 标记代理为无效
        await proxy_pool.mark_ip_invalid(proxy)

        # 验证代理数量减少了1
        assert proxy_pool.get_proxy_count() == initial_count - 1

    @pytest.mark.asyncio
    async def test_refresh_proxies(self, proxy_pool, mock_provider):
        """测试刷新代理池"""
        # 设置新的返回值
        mock_provider.get_proxies.return_value = [
            IpInfoModel(ip="********", port=8080),
            IpInfoModel(ip="********", port=8080),
        ]

        await proxy_pool.refresh_proxies(count=2)

        # 验证调用了提供商的方法
        mock_provider.get_proxies.assert_called_with(2)
        assert proxy_pool.get_proxy_count() == 2

    @pytest.mark.asyncio
    async def test_clear_cache(self, proxy_pool):
        """测试清除缓存"""
        # 这个测试主要验证方法不会抛出异常
        await proxy_pool.clear_cache()


class TestCreateIpPool:
    """测试工厂函数"""

    def test_create_kuaidaili_pool(self):
        """测试创建快代理池"""
        pool = create_ip_pool(provider_type="kuaidaili", api_key="test_key", api_secret="test_secret")

        assert isinstance(pool, ProxyIpPool)
        assert isinstance(pool.ip_provider, KuaiDaiLiProxy)

    def test_create_kuaidaili_pool_convenience_function(self):
        """测试便捷工厂函数"""
        pool = create_kuaidaili_pool(api_key="test_key", api_secret="test_secret")

        assert isinstance(pool, ProxyIpPool)
        assert isinstance(pool.ip_provider, KuaiDaiLiProxy)

    def test_create_pool_missing_api_key(self):
        """测试缺少 API 密钥时抛出异常"""
        with pytest.raises(ValueError, match="快代理需要提供 api_key 参数"):
            create_ip_pool(provider_type="kuaidaili")

    def test_create_pool_unsupported_provider(self):
        """测试不支持的提供商类型"""
        with pytest.raises(ValueError, match="不支持的代理提供商类型"):
            create_ip_pool(provider_type="unsupported")

    def test_create_pool_empty_provider_type(self):
        """测试空的提供商类型"""
        with pytest.raises(ValueError, match="provider_type 不能为空"):
            create_ip_pool(provider_type="")

    def test_create_pool_invalid_cache_ttl(self):
        """测试无效的缓存时间"""
        with pytest.raises(ValueError, match="cache_ttl 必须大于 0"):
            create_ip_pool(provider_type="kuaidaili", api_key="test_key", cache_ttl=0)


@pytest.mark.integration
class TestKuaiDaiLiProxyIntegration:
    """快代理集成测试"""

    @pytest.fixture
    def kuaidaili_proxy(self):
        """创建快代理实例（使用测试凭据）"""
        return KuaiDaiLiProxy(api_key="test_key", api_secret="test_secret")

    @pytest.mark.asyncio
    async def test_get_proxies_mock_response(self, kuaidaili_proxy):
        """测试获取代理（模拟响应）"""
        # 这里可以添加模拟 HTTP 响应的测试
        # 由于需要真实的 API 凭据，这里只做基本的接口测试
        try:
            proxies = await kuaidaili_proxy.get_proxies(count=1)
            # 如果有真实凭据，这里会返回代理列表
            assert isinstance(proxies, list)
        except Exception as e:
            # 预期会因为测试凭据或网络问题而失败
            error_msg = str(e)
            assert any(
                keyword in error_msg
                for keyword in [
                    "API",
                    "请求失败",
                    "Cannot connect",
                    "Connection",
                    "网络",
                    "ConnectError",
                    "获取快代理 IP 失败",
                    "认证失败",
                    "无效",
                ]
            )
        finally:
            await kuaidaili_proxy.close()

    @pytest.mark.asyncio
    async def test_proxy_validation(self):
        """测试代理池基本功能"""
        # 创建一个代理池
        pool = create_ip_pool(provider_type="kuaidaili", api_key="test_key", api_secret="test_secret")

        try:
            # 由于使用测试凭据，这里主要测试不会抛出配置错误
            # 测试基本的代理池功能
            initial_count = pool.get_proxy_count()
            assert initial_count == 0  # 初始时应该没有代理
        except Exception as e:
            # 预期会因为测试凭据而失败
            error_msg = str(e)
            assert any(keyword in error_msg for keyword in ["API", "认证", "网络", "连接"])


if __name__ == "__main__":
    pytest.main([__file__])
