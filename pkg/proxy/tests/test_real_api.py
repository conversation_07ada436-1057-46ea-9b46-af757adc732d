"""
代理模块真实 API 测试
这些测试需要真实的 API 凭据，通过 dynaconf 配置获取。
运行前请确保在配置文件中设置了相应的 API 密钥。

使用方法:
    pytest pkg/proxy/tests/test_real_api.py -m real_api -v
"""

import pytest
from loguru import logger

from pkg.proxy import IpInfoModel, KuaiDaiLiProxy, create_ip_pool, create_kuaidaili_pool
from settings.config import settings


@pytest.mark.real_api
@pytest.mark.ci_skip
class TestKuaiDaiLiRealAPI:
    """快代理真实 API 测试"""

    @pytest.fixture
    def api_credentials(self):
        """从 dynaconf 获取 API 凭据"""
        api_key = settings.get("KUAIDAILI_DPS_SECRET_ID")
        api_secret = settings.get("KUAIDAILI_DPS_SIGNATURE")

        if not api_key:
            pytest.skip("需要在配置中设置 KUAIDAILI_DPS_SECRET_ID")

        return {"api_key": api_key, "api_secret": api_secret or ""}

    @pytest.mark.asyncio
    async def test_kuaidaili_get_proxies(self, api_credentials):
        """测试快代理获取代理列表"""
        provider = KuaiDaiLiProxy(**api_credentials)

        try:
            proxies = await provider.get_proxies(count=2)

            logger.info(f"获取到 {len(proxies)} 个代理")

            # 验证返回结果
            assert isinstance(proxies, list)
            assert len(proxies) > 0

            # 验证每个代理的格式
            for proxy in proxies:
                assert isinstance(proxy, IpInfoModel)
                assert proxy.ip
                assert proxy.port > 0
                assert proxy.protocol in ["http", "https"]

                logger.info(f"代理: {proxy}")

        except Exception as e:
            logger.error(f"获取代理失败: {e}")
            raise
        finally:
            await provider.close()

    @pytest.mark.asyncio
    async def test_kuaidaili_provider_lifecycle(self, api_credentials):
        """测试快代理提供商的生命周期管理"""
        provider = KuaiDaiLiProxy(**api_credentials)

        try:
            # 测试多次获取代理
            for i in range(2):
                proxies = await provider.get_proxies(count=1)
                logger.info(f"第 {i+1} 次获取到 {len(proxies)} 个代理")
                assert isinstance(proxies, list)

        except Exception as e:
            logger.error(f"生命周期测试失败: {e}")
            raise
        finally:
            await provider.close()


@pytest.mark.real_api
@pytest.mark.ci_skip
class TestProxyPoolRealAPI:
    """代理池真实 API 测试"""

    @pytest.fixture
    def api_credentials(self):
        """从 dynaconf 获取 API 凭据"""
        api_key = settings.get("KUAIDAILI_DPS_SECRET_ID")
        api_secret = settings.get("KUAIDAILI_DPS_SIGNATURE")

        if not api_key:
            pytest.skip("需要在配置中设置 KUAIDAILI_DPS_SECRET_ID")

        return {"api_key": api_key, "api_secret": api_secret or ""}

    @pytest.mark.asyncio
    async def test_proxy_pool_load_and_get(self, api_credentials):
        """测试代理池加载和获取代理"""
        pool = create_ip_pool(provider_type="kuaidaili", **api_credentials)

        try:
            # 加载代理
            await pool.load_proxies(count=3)
            proxy_count = pool.get_proxy_count()
            logger.info(f"加载了 {proxy_count} 个代理")

            # 获取几个代理（代理不会从缓存中删除，可以复用）
            for i in range(min(2, proxy_count)):
                proxy = await pool.get_proxy()
                logger.info(f"获取代理 {i+1}: {proxy}")
                assert isinstance(proxy, IpInfoModel)

            # 再次检查代理数量（获取代理后数量不变）
            remaining_count = pool.get_proxy_count()
            logger.info(f"获取代理后剩余数量: {remaining_count}")
            assert remaining_count == proxy_count  # 获取代理不会减少数量

        except Exception as e:
            logger.error(f"代理池测试失败: {e}")
            raise

    @pytest.mark.asyncio
    async def test_proxy_pool_with_validation(self, api_credentials):
        """测试带验证的代理池"""
        pool = create_ip_pool(provider_type="kuaidaili", **api_credentials)

        try:
            # 加载代理
            await pool.load_proxies(count=2)
            proxy_count = pool.get_proxy_count()
            logger.info(f"加载了 {proxy_count} 个代理")

            # 尝试获取一个验证过的代理（不会从缓存中删除）
            proxy = await pool.get_proxy()
            logger.info(f"获取到验证过的代理: {proxy}")
            assert isinstance(proxy, IpInfoModel)

        except Exception as e:
            logger.error(f"验证代理池测试失败: {e}")
            # 验证可能失败，这是正常的
            logger.warning("代理验证失败可能是正常现象")

    @pytest.mark.asyncio
    async def test_proxy_pool_refresh(self, api_credentials):
        """测试代理池刷新功能"""
        pool = create_kuaidaili_pool(**api_credentials)

        try:
            # 初始加载
            await pool.load_proxies(count=2)
            initial_count = pool.get_proxy_count()
            logger.info(f"初始加载了 {initial_count} 个代理")

            # 刷新代理池
            await pool.refresh_proxies(count=2)
            refreshed_count = pool.get_proxy_count()
            logger.info(f"刷新后获得 {refreshed_count} 个代理")

            # 验证刷新结果
            assert refreshed_count > 0

            for i in range(min(2, refreshed_count)):
                proxy = await pool.get_proxy()
                assert isinstance(proxy, IpInfoModel)
                logger.info(f"刷新的代理: {proxy}")

            # 验证获取代理后数量不变
            final_count = pool.get_proxy_count()
            logger.info(f"获取代理后最终数量: {final_count}")
            assert final_count == refreshed_count  # 获取代理不会减少数量

        except Exception as e:
            logger.error(f"刷新测试失败: {e}")
            raise

    @pytest.mark.asyncio
    async def test_proxy_pool_batch_operations(self, api_credentials):
        """测试代理池批量操作"""
        pool = create_ip_pool(provider_type="kuaidaili", **api_credentials)

        try:
            # 加载代理
            await pool.load_proxies(count=3)

            # 获取多个代理（模拟批量操作，代理可以复用）
            batch_proxies = []
            initial_count = pool.get_proxy_count()
            for i in range(2):
                if pool.get_proxy_count() > 0:
                    proxy = await pool.get_proxy()
                    batch_proxies.append(proxy)

            logger.info(f"批量获取了 {len(batch_proxies)} 个代理")

            # 验证批量结果
            assert isinstance(batch_proxies, list)
            assert len(batch_proxies) <= 2

            for i, proxy in enumerate(batch_proxies):
                assert isinstance(proxy, IpInfoModel)
                logger.info(f"批量代理 {i+1}: {proxy}")

            # 检查剩余代理数量（获取代理不会减少数量）
            remaining_count = pool.get_proxy_count()
            logger.info(f"批量操作后剩余代理数量: {remaining_count}")
            assert remaining_count == initial_count  # 获取代理不会减少数量

        except Exception as e:
            logger.error(f"批量操作测试失败: {e}")
            raise

    @pytest.mark.asyncio
    async def test_proxy_pool_cache_operations(self, api_credentials):
        """测试代理池缓存操作"""
        pool = create_ip_pool(provider_type="kuaidaili", cache_ttl=60, **api_credentials)  # 1分钟缓存

        try:
            # 加载代理
            await pool.load_proxies(count=2)

            # 获取初始代理数量
            initial_count = pool.get_proxy_count()
            logger.info(f"初始代理数量: {initial_count}")

            # 清除缓存
            await pool.clear_cache()
            logger.info("缓存已清除")

            # 重新加载
            await pool.load_proxies(count=2)

            # 获取清除后代理数量
            final_count = pool.get_proxy_count()
            logger.info(f"清除缓存后代理数量: {final_count}")

        except Exception as e:
            logger.error(f"缓存操作测试失败: {e}")
            raise

    @pytest.mark.asyncio
    async def test_multiple_pools_isolation(self, api_credentials):
        """测试多个代理池的隔离性"""
        pool1 = create_kuaidaili_pool(**api_credentials)

        pool2 = create_kuaidaili_pool(**api_credentials)

        try:
            # 两个池分别加载代理
            await pool1.load_proxies(count=2)
            await pool2.load_proxies(count=2)

            count1 = pool1.get_proxy_count()
            count2 = pool2.get_proxy_count()

            logger.info(f"池1加载了 {count1} 个代理")
            logger.info(f"池2加载了 {count2} 个代理")

            # 从池1获取代理（不会从缓存中删除）
            proxy1 = await pool1.get_proxy()
            logger.info(f"从池1获取: {proxy1}")

            # 从池2获取代理（不会从缓存中删除）
            proxy2 = await pool2.get_proxy()
            logger.info(f"从池2获取: {proxy2}")

            # 验证两个池的状态独立（获取代理不会减少数量）
            remaining_count1 = pool1.get_proxy_count()
            remaining_count2 = pool2.get_proxy_count()

            logger.info(f"池1剩余代理数量: {remaining_count1}")
            logger.info(f"池2剩余代理数量: {remaining_count2}")

            # 验证获取代理后数量不变
            assert remaining_count1 == count1  # 获取代理不会减少数量
            assert remaining_count2 == count2  # 获取代理不会减少数量

            # 验证代理对象
            assert isinstance(proxy1, IpInfoModel)
            assert isinstance(proxy2, IpInfoModel)

        except Exception as e:
            logger.error(f"多池隔离测试失败: {e}")
            raise


if __name__ == "__main__":
    # 运行真实 API 测试
    pytest.main([__file__, "-v", "-s", "--tb=short", "-m", "real_api"])
