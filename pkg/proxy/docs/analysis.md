# Pkg/Proxy 模块分析

## 概述

`pkg/proxy` 模块的核心职责是提供一个高效、可靠的代理 IP 管理器。它能够从指定的代理服务商获取 IP，并将其缓存以备后续使用。同时，它还支持验证代理的有效性，并能将无效的 IP 从缓存和内存中移除，确保代理池的高可用性。

## 文件结构

```
pkg/proxy/
├── __init__.py           # 模块入口
├── base_proxy.py         # 定义代理提供商的基础接口
├── providers/            # 具体的代理提供商实现
│   ├── __init__.py
│   └── kuai_daili_proxy.py # 快代理服务商的实现
├── proxy_ip_pool.py      # 核心代理 IP 池的实现
├── models.py             # 数据模型定义
└── docs/
    └── analysis.md       # 分析文档
```

## 核心组件与设计

### 1. `ProxyProvider` (基础接口)

这是一个抽象基类，定义了所有代理服务商必须实现的接口。它的职责非常单一：

-   `get_proxies(count: int)`: 从服务商获取指定数量的代理 IP。

通过这种设计，我们将“获取代理”的逻辑与“管理代理”的逻辑完全解耦，使得系统更具扩展性。

### 2. `KuaiDaiLiProxy` (具体实现)

这是 `ProxyProvider` 的一个具体实现，负责与快代理服务商进行交互，获取代理 IP。

### 3. `ProxyIpPool` (核心管理器)

这是代理模块的核心，负责整个代理池的生命周期管理。其主要职责包括：

-   **获取与缓存**：通过 `load_proxies` 方法，它首先会检查缓存中是否存在可用的 IP 列表。如果缓存未命中，它将调用 `ProxyProvider` 的实现来获取新的 IP，并将其存入缓存。
-   **提供代理**：`get_proxy` 方法负责从内存中的代理列表（`proxy_list`）中随机提供一个代理，但不会从列表中删除该代理。这样设计允许代理被复用，直到被明确标记为无效。如果列表为空，它会自动触发 `_reload_proxies` 来重新加载。
-   **验证代理**：在提供代理之前，可以选择性地通过 `_is_valid_proxy` 方法来验证其有效性，确保返回的代理是可用的。
-   **标记无效**：`mark_ip_invalid` 是一个关键方法。当一个代理被确认为无效时，此方法会：
    1.  将其从内存列表 `proxy_list` 中移除。
    2.  将其从缓存中彻底移除，防止在下一次加载时被错误地恢复。
    这种设计确保代理只有在被明确标记为无效时才被删除，而不是在每次获取时都被删除。

### 4. `create_ip_pool` (工厂函数)

这是一个便捷的工厂函数，用于创建和配置 `ProxyIpPool` 实例，是外部调用方与本模块交互的推荐入口。

## 流程图

下图清晰地展示了从代理池获取一个代理 IP 的完整流程：

```mermaid
graph TD
    A[开始] --> B[调用 create_ip_pool];
    B --> C[初始化 ProxyIpPool 实例];
    C --> D[调用 load_proxies];
    D --> E{检查缓存}
    E -- 缓存命中 --> G[从缓存加载 IP 列表到 proxy_list];
    E -- 缓存未命中 --> F[调用 ip_provider.get_proxies 获取新 IP];
    F --> F1[将新 IP 存入缓存];
    F1 --> G;
    G --> H[返回 ProxyIpPool 实例];
    H --> I[调用 get_proxy];
    I --> J{proxy_list 是否为空?};
    J -- 是 --> K[调用 _reload_proxies 重新加载];
    K --> D;
    J -- 否 --> L[从 proxy_list 随机选择一个 IP];
    L --> M[从 proxy_list 中移除该 IP];
    M --> N{是否需要验证?};
    N -- 否 --> S[返回代理 IP];
    N -- 是 --> O[调用 _is_valid_proxy 验证];
    O --> P{验证结果};
    P -- 成功 --> S;
    P -- 失败 --> Q[调用 mark_ip_invalid];
    Q --> R[从缓存和内存中移除该 IP];
    R --> I;
    S --> T[结束];
```

## UML 类图

```mermaid
classDiagram
    class ProxyProvider {
        <<interface>>
        +get_proxies(count: int) List[IpInfoModel]
    }

    class KuaiDaiLiProxy {
        +get_proxies(count: int) List[IpInfoModel]
    }

    class ProxyIpPool {
        -proxy_list: List[IpInfoModel]
        -ip_provider: ProxyProvider
        -cache: BaseCache
        +load_proxies()
        +get_proxy() IpInfoModel
        +mark_ip_invalid(proxy: IpInfoModel)
    }

    class IpInfoModel {
        +ip: str
        +port: int
        +protocol: str
        +user: str
        +password: str
    }

    ProxyIpPool o-- ProxyProvider
    KuaiDaiLiProxy ..|> ProxyProvider
    ProxyIpPool ..> IpInfoModel
```

## 使用示例

```python
import asyncio
from pkg.proxy import create_ip_pool

async def main():
    # 1. 创建代理池
    pool = create_ip_pool(
        provider_type="kuaidaili",
        # ... 其他提供商需要的参数
    )
    
    try:
        # 2. 加载代理（会优先从缓存加载）
        await pool.load_proxies(count=20)
        
        # 3. 获取一个可用代理
        proxy = await pool.get_proxy()
        if proxy:
            print(f"成功获取代理: {proxy.to_proxy_url()}")
            
            # 模拟使用代理后发现无效
            # if is_proxy_failed(proxy):
            #     await pool.mark_ip_invalid(proxy)

    except Exception as e:
        print(f"操作失败: {e}")
    finally:
        # 4. (可选) 清理资源
        if pool.ip_provider and hasattr(pool.ip_provider, 'close'):
             await pool.ip_provider.close()

asyncio.run(main())
```

## 如何扩展

要支持一个新的代理服务商，只需：

1.  在 `pkg/proxy/providers/` 目录下创建一个新的实现文件。
2.  创建一个继承自 `ProxyProvider` 的新类。
3.  实现 `get_proxies` 方法，封装从新服务商获取代理的逻辑。
4.  在 `pkg/proxy/factory.py` 的 `create_ip_pool` 函数中，添加对新服务商类型的支持。
