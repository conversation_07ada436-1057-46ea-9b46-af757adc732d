"""
快代理的实现
"""

from typing import List, Optional

import httpx
from loguru import logger

from rpc.kuaidaili.manager import <PERSON>aidailiManager

from ..base_proxy import ProxyProvider
from ..models import IpInfoModel


class KuaiDaiLiProxy(ProxyProvider):
    """快代理提供商实现"""

    def __init__(self, api_key: str = None, api_secret: str = None, **kwargs):
        """
        初始化快代理客户端

        Args:
            api_key: API 密钥
            api_secret: API 密码
            **kwargs: 其他参数
        """
        self.api_key = api_key
        self.api_secret = api_secret
        self.kwargs = kwargs
        self._client: Optional[httpx.AsyncClient] = None
        self._manager: Optional[KuaidailiManager] = None

    async def _get_client(self) -> httpx.AsyncClient:
        """获取或创建 HTTP 客户端"""
        if self._client is None or self._client.is_closed:
            self._client = httpx.AsyncClient()
        return self._client

    async def _get_manager(self) -> KuaidailiManager:
        """获取或创建 KuaidailiManager 实例"""
        if self._manager is None:
            self._manager = KuaidailiManager()
        return self._manager

    async def get_proxies(self, count: int = 10) -> List[IpInfoModel]:
        """
        从快代理获取代理 IP 列表

        Args:
            count: 需要获取的代理 IP 数量

        Returns:
            代理 IP 信息列表

        Raises:
            Exception: 获取代理失败时抛出异常
        """
        try:
            # 使用 KuaidailiManager 获取格式化的代理列表
            manager = await self._get_manager()
            formatted_proxies = await manager.get_formatted_proxy(num=count)

            if not formatted_proxies:
                raise Exception("快代理返回空列表")

            # 解析代理 IP 列表
            proxy_list = []
            for proxy_str in formatted_proxies:
                # 解析格式为 'ip:port:username:password' 的代理字符串
                try:
                    # 首先检查是否包含协议前缀
                    if proxy_str.startswith("http://"):
                        # 去掉协议前缀后重新解析
                        clean_str = proxy_str.replace("http://", "")
                        parts = clean_str.split(":")
                    else:
                        parts = proxy_str.split(":")

                    # 处理各种可能的格式
                    if len(parts) == 4:
                        # 标准格式: ip:port:username:password
                        ip, port, username, password = parts

                        # 验证IP和端口的有效性
                        if not ip or not port or not port.isdigit():
                            logger.warning(f"代理字符串格式不正确: {proxy_str} - IP或端口无效")
                            continue

                        proxy = IpInfoModel(
                            ip=ip.strip(),
                            port=int(port.strip()),
                            protocol="http",
                            user=username.strip() if username else None,
                            password=password.strip() if password else None,
                        )
                        proxy_list.append(proxy)

                    elif len(parts) == 2:
                        # 支持无认证的代理格式 'ip:port'
                        ip, port = parts

                        if not ip or not port or not port.isdigit():
                            logger.warning(f"代理字符串格式不正确: {proxy_str} - IP或端口无效")
                            continue

                        proxy = IpInfoModel(
                            ip=ip.strip(), port=int(port.strip()), protocol="http", user=None, password=None
                        )
                        proxy_list.append(proxy)
                    else:
                        logger.warning(f"代理字符串格式不正确: {proxy_str} - 分割长度为{len(parts)}，期望2或4个部分")
                        continue

                except (ValueError, IndexError) as e:
                    logger.warning(f"解析代理字符串失败: {proxy_str}, 错误: {e}")
                    continue

            if not proxy_list:
                raise Exception("没有成功解析到有效的代理 IP")

            logger.info(f"从快代理获取到 {len(proxy_list)} 个代理 IP")
            return proxy_list

        except Exception as e:
            logger.error(f"获取快代理 IP 失败: {e}")
            raise

    def get_proxy(self, count: int = 10) -> List[IpInfoModel]:
        """
        同步版本：从快代理获取代理 IP 列表

        Args:
            count: 需要获取的代理 IP 数量

        Returns:
            代理 IP 信息列表

        Raises:
            Exception: 获取代理失败时抛出异常
        """
        try:
            # 使用异步方法获取代理（在同步上下文中运行）
            import asyncio

            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                result = loop.run_until_complete(self.get_proxies(count))
                return result
            finally:
                loop.close()

        except Exception as e:
            logger.error(f"同步获取快代理 IP 失败: {e}")
            raise

    async def get_proxy_async(self) -> Optional[IpInfoModel]:
        """
        异步获取单个代理信息

        Returns:
            IpInfoModel 实例，如果获取失败则返回 None
        """
        try:
            proxies = await self.get_proxies(count=1)
            if proxies:
                return proxies[0]
            return None
        except Exception as e:
            logger.error(f"异步获取代理失败: {e}")
            return None

    async def close(self):
        """关闭 HTTP 客户端和清理资源"""
        if self._client and not self._client.is_closed:
            await self._client.aclose()
        # 如果 manager 有清理方法，也调用它
        if self._manager and hasattr(self._manager, "close"):
            await self._manager.close()

    async def __aenter__(self):
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.close()
