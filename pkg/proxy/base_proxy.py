"""
代理提供者的基类接口
"""

from abc import ABC, abstractmethod
from typing import List

from .models import IpInfoModel


class ProxyProvider(ABC):
    """代理提供商的抽象基类"""

    @abstractmethod
    async def get_proxies(self, count: int = 10) -> List[IpInfoModel]:
        """
        获取代理 IP 列表

        Args:
            count: 需要获取的代理 IP 数量

        Returns:
            代理 IP 信息列表

        Raises:
            Exception: 获取代理 IP 失败时抛出异常
        """
        pass
