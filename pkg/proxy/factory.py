"""
代理 IP 池工厂模块

提供创建代理 IP 池实例的工厂函数，支持不同的代理提供商。
"""

from typing import Optional

from .providers import KuaiDaiLiProxy
from .proxy_ip_pool import ProxyIpPool

try:
    from aiocache.base import BaseCache
except ImportError:
    BaseCache = None


def create_ip_pool(
    provider_type: str = "kuaidaili",
    api_key: Optional[str] = None,
    api_secret: Optional[str] = None,
    cache: Optional[BaseCache] = None,
    cache_ttl: int = 3600,
    **kwargs,
) -> ProxyIpPool:
    """
    工厂函数，创建并初始化一个 ProxyIpPool 实例
    这是使用该模块的推荐入口点

    Args:
        provider_type: 代理提供商类型，目前支持 "kuaidaili"
        api_key: API 密钥
        api_secret: API 密码
        cache: 自定义缓存实例，如果为 None 则使用默认缓存
        cache_ttl: 缓存时间（秒），默认1小时
        **kwargs: 其他参数传递给代理提供商

    Returns:
        ProxyIpPool 实例

    Raises:
        ValueError: 不支持的代理提供商类型或参数错误
        Exception: 初始化失败

    Examples:
        >>> # 创建快代理池
        >>> pool = create_ip_pool(
        ...     provider_type="kuaidaili",
        ...     api_key="your_api_key",
        ...     api_secret="your_api_secret"
        ... )
        >>>
        >>> # 加载代理
        >>> await pool.load_proxies(count=10)
        >>>
        >>> # 获取代理
        >>> proxy = await pool.get_proxy()
        >>>
        >>> # 创建自定义缓存时间的池
        >>> pool = create_ip_pool(
        ...     provider_type="kuaidaili",
        ...     api_key="your_api_key",
        ...     cache_ttl=7200  # 2小时缓存
        ... )
    """
    # 参数验证
    if not provider_type:
        raise ValueError("provider_type 不能为空")

    provider_type = provider_type.lower().strip()

    # 创建代理提供商
    if provider_type == "kuaidaili":
        if not api_key:
            raise ValueError("快代理需要提供 api_key 参数")

        provider = KuaiDaiLiProxy(api_key=api_key, api_secret=api_secret or "", **kwargs)
    else:
        raise ValueError(f"不支持的代理提供商类型: {provider_type}。目前支持: kuaidaili")

    # 参数验证
    if cache_ttl <= 0:
        raise ValueError("cache_ttl 必须大于 0")

    # 创建代理池
    try:
        return ProxyIpPool(ip_provider=provider, cache=cache, cache_ttl=cache_ttl)
    except Exception as e:
        raise Exception(f"创建代理池失败: {e}") from e


def create_kuaidaili_pool(api_key: str, api_secret: Optional[str] = None, **kwargs) -> ProxyIpPool:
    """
    快代理专用的便捷工厂函数

    Args:
        api_key: 快代理 API 密钥
        api_secret: 快代理 API 密码
        **kwargs: 其他参数传递给 create_ip_pool

    Returns:
        ProxyIpPool 实例

    Examples:
        >>> pool = create_kuaidaili_pool(
        ...     api_key="your_api_key",
        ...     api_secret="your_api_secret",
        ...     cache_ttl=7200
        ... )
    """
    return create_ip_pool(provider_type="kuaidaili", api_key=api_key, api_secret=api_secret, **kwargs)
