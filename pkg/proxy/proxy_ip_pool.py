"""
核心的代理 IP 池实现
"""

import random
from typing import List, Optional

from loguru import logger

# 导入配置
try:
    from settings.config import settings
except ImportError:
    # 如果无法导入settings，使用默认值
    class MockSettings:
        def get(self, key, default=None):
            defaults = {"proxy_pool_default_count": 2, "proxy_pool_cache_ttl": 3600}
            return defaults.get(key, default)

    settings = MockSettings()

try:
    from aiocache import Cache
    from aiocache.base import BaseCache

    HAS_AIOCACHE = True
except ImportError:
    HAS_AIOCACHE = False

    # 简单的内存缓存实现
    class BaseCache:
        def __init__(self, *args, **kwargs):
            self._cache = {}

        async def get(self, key):
            return self._cache.get(key)

        async def set(self, key, value, ttl=None):
            self._cache[key] = value

        async def delete(self, key):
            self._cache.pop(key, None)

    Cache = BaseCache

try:
    from .base_proxy import ProxyProvider
    from .models import IpInfoModel
except ImportError:
    # 如果相对导入失败，尝试绝对导入
    from base_proxy import ProxyProvider

    from models import IpInfoModel


class ProxyIpPool:
    """代理 IP 池核心类

    负责代理 IP 的获取、缓存、验证和管理。
    支持从不同的代理提供商获取 IP，并对获取到的 IP 进行有效性验证。
    """

    def __init__(
        self,
        ip_provider: ProxyProvider,
        cache: Optional[BaseCache] = None,
        cache_ttl: int = 3600,  # 缓存时间，默认1小时
    ):
        """
        初始化代理 IP 池

        Args:
            ip_provider: 代理提供商实例
            cache: 缓存实例，如果为 None 则使用默认缓存
            cache_ttl: 缓存时间（秒）
        """
        self.ip_provider = ip_provider
        self.cache_ttl = cache_ttl

        # 初始化缓存
        if cache is not None:
            self.cache = cache
        elif HAS_AIOCACHE:
            self.cache = Cache(Cache.MEMORY)
        else:
            self.cache = BaseCache()

        # 代理列表
        self.proxy_list: List[IpInfoModel] = []

        # 缓存键
        self._cache_key = "proxy_ip_list"

    async def load_proxies(self, count: Optional[int] = None) -> None:
        """
        加载代理 IP 列表

        首先尝试从缓存获取，如果缓存未命中则从提供商获取新的代理。

        Args:
            count: 需要加载的代理 IP 数量，如果为None则使用配置文件中的默认值

        Raises:
            Exception: 加载代理失败时抛出异常
        """
        if count is None:
            count = settings.get("proxy_pool_default_count", 2)

        try:
            # 先尝试从缓存获取
            cached_proxies = await self.cache.get(self._cache_key)

            if cached_proxies and isinstance(cached_proxies, list) and len(cached_proxies) > 0:
                logger.info(f"从缓存加载了 {len(cached_proxies)} 个代理 IP")
                self.proxy_list = [
                    IpInfoModel(**proxy_data) if isinstance(proxy_data, dict) else proxy_data
                    for proxy_data in cached_proxies
                ]
                return

            # 缓存未命中，从提供商获取
            logger.info(f"缓存未命中，从提供商获取 {count} 个代理 IP")
            proxies = await self.ip_provider.get_proxies(count)

            if not proxies:
                raise Exception("代理提供商返回空列表")

            # 存入缓存
            proxy_data_list = [
                {
                    "ip": proxy.ip,
                    "port": proxy.port,
                    "protocol": proxy.protocol,
                    "user": proxy.user,
                    "password": proxy.password,
                }
                for proxy in proxies
            ]
            await self.cache.set(self._cache_key, proxy_data_list, ttl=self.cache_ttl)

            self.proxy_list = proxies
            logger.info(f"成功加载 {len(self.proxy_list)} 个代理 IP")

        except Exception as e:
            logger.error(f"加载代理 IP 失败: {e}")
            raise

    async def _reload_proxies(self, count: Optional[int] = None) -> None:
        """
        重新加载代理 IP 列表

        Args:
            count: 需要重新加载的代理 IP 数量，如果为None则使用配置文件中的默认值
        """
        if count is None:
            count = settings.get("proxy_pool_default_count", 2)

        logger.info("代理列表为空，重新加载...")
        # 清除缓存，强制从提供商获取新的代理
        await self.cache.delete(self._cache_key)
        await self.load_proxies(count)

    async def get_proxy(self) -> IpInfoModel:
        """
        获取一个可用的代理 IP

        从内存中的代理列表随机选择一个代理，但不从列表中删除。
        代理只有在被标记为无效时才会被删除，这样可以提高代理的复用性。

        Returns:
            代理 IP 信息

        Raises:
            Exception: 无可用代理时抛出异常
        """
        try:
            # 检查代理列表是否为空
            if not self.proxy_list:
                await self._reload_proxies()

            if not self.proxy_list:
                raise Exception("无法获取可用的代理 IP")

            # 随机选择一个代理，但不从列表中删除
            proxy = random.choice(self.proxy_list)

            logger.debug(f"获取到代理: {proxy}")
            return proxy

        except Exception as e:
            logger.error(f"获取代理时发生错误: {e}")
            raise Exception(f"获取代理失败: {e}")

    async def mark_ip_invalid(self, proxy: IpInfoModel) -> None:
        """
        标记代理 IP 为无效，并从缓存和内存中移除

        这是一个关键方法，确保无效的代理不会被重复使用。

        Args:
            proxy: 需要标记为无效的代理 IP 信息
        """
        # 从本地列表中移除
        if proxy in self.proxy_list:
            self.proxy_list.remove(proxy)

        # 从缓存中移除
        try:
            cached_proxies = await self.cache.get(self._cache_key)
            if cached_proxies and isinstance(cached_proxies, list):
                # 过滤掉无效的代理
                updated_proxies = [
                    p for p in cached_proxies if not (p.get("ip") == proxy.ip and p.get("port") == proxy.port)
                ]
                # 更新缓存
                if len(updated_proxies) < len(cached_proxies):
                    await self.cache.set(self._cache_key, updated_proxies, ttl=self.cache_ttl)
                    logger.debug(f"已从缓存中移除无效代理: {proxy}")
        except Exception as e:
            logger.warning(f"从缓存中移除代理时发生错误: {e}")

        logger.info(f"已标记代理 {proxy} 为无效")

    def get_proxy_count(self) -> int:
        """
        获取当前可用代理数量

        Returns:
            当前内存中可用的代理数量
        """
        return len(self.proxy_list)

    async def clear_cache(self) -> None:
        """
        清除代理缓存

        强制下次加载时从提供商获取新的代理。
        """
        await self.cache.delete(self._cache_key)
        logger.info("已清除代理缓存")

    async def refresh_proxies(self, count: Optional[int] = None) -> None:
        """
        刷新代理列表

        清除当前的代理列表和缓存，重新从提供商获取代理。

        Args:
            count: 需要获取的代理数量，如果为None则使用配置文件中的默认值
        """
        if count is None:
            count = settings.get("proxy_pool_default_count", 2)

        self.proxy_list.clear()
        await self.clear_cache()
        await self.load_proxies(count)
        logger.info(f"已刷新代理列表，当前有 {len(self.proxy_list)} 个代理")
