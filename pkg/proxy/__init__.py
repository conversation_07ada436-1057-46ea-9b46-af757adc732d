"""
代理 IP 池模块

提供代理 IP 池的功能，用于在网络请求中隐藏真实 IP 地址。
支持从不同的代理提供商获取 IP，并对获取到的 IP 进行有效性验证。
使用缓存机制对从提供商获取的 IP 列表进行缓存，提高性能。

主要特性：
- 支持多种代理提供商（目前支持快代理）
- 智能缓存机制，减少API调用
- 代理有效性验证
- 自动故障转移和重试
- 异步支持

基本使用：
    >>> import asyncio
    >>> from pkg.proxy import create_ip_pool
    >>> 
    >>> async def main():
    ...     # 创建代理池
    ...     pool = create_ip_pool(
    ...         provider_type="kuaidaili",
    ...         api_key="your_api_key"
    ...     )
    ...     
    ...     # 加载代理
    ...     await pool.load_proxies(count=10)
    ...     
    ...     # 获取代理
    ...     proxy = await pool.get_proxy()
    ...     logger.info(f"代理: {proxy.to_proxy_url()}")
    >>> 
    >>> asyncio.run(main())
"""

from log import logger

from .base_proxy import ProxyProvider
from .factory import create_ip_pool, create_kuaidaili_pool
from .models import IpInfoModel
from .providers import KuaiDaiLiProxy
from .proxy_ip_pool import ProxyIpPool

__version__ = "1.0.0"

__all__ = ["IpInfoModel", "ProxyProvider", "ProxyIpPool", "KuaiDaiLiProxy", "create_ip_pool", "create_kuaidaili_pool"]
