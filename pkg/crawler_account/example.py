"""
爬虫账号管理器使用示例

展示如何使用 AccountManager 类进行账号管理
"""

import asyncio
import json

from tortoise import Tortoise

from log import logger

from pkg.crawler_account.account_manager import AccountManager
from pkg.crawler_account.exceptions import AccountNotFoundError, NoAvailableAccountError
from settings.config import settings


async def init_database():
    """初始化数据库连接"""
    await Tortoise.init(config=settings.tortoise_orm)


async def close_database():
    """关闭数据库连接"""
    await Tortoise.close_connections()


async def basic_usage_example():
    """基础使用示例"""
    logger.info("=== 基础使用示例 ===")

    # 创建账号管理器
    manager = AccountManager()

    try:
        # 获取抖音平台的可用账号
        account = await manager.get_available_account("douyin")
        logger.info(f"✅ 成功获取账号: ID={account.id}, 名称={account.account_name}")

        # 查看账号状态
        status_info = await manager.get_account_status(account.id)
        logger.info(f"📊 账号状态: {json.dumps(status_info, indent=2, ensure_ascii=False)}")

        # 模拟使用账号进行爬取
        logger.info("🕷️ 正在使用账号进行数据爬取...")
        await asyncio.sleep(2)  # 模拟爬取过程

        # 使用完成 - 无需释放账号，状态保持可用
        logger.info(f"✅ 账号使用完成: ID={account.id} (无需释放)")

    except NoAvailableAccountError as e:
        logger.error(f"❌ 没有可用账号: {e}")
    except Exception as e:
        logger.error(f"❌ 发生错误: {e}")


async def error_handling_example():
    """错误处理示例"""
    logger.info("\n=== 错误处理示例 ===")

    manager = AccountManager()

    # 示例1: 处理没有可用账号的情况
    try:
        await manager.get_available_account("nonexistent_platform")
    except NoAvailableAccountError as e:
        logger.info(f"✅ 正确捕获异常: {e}")

    # 示例2: 处理账号不存在的情况
    try:
        await manager.mark_account_unavailable(99999, "测试标记")
    except AccountNotFoundError as e:
        logger.info(f"✅ 正确捕获异常: {e}")


async def concurrent_usage_example():
    """并发使用示例 - 展示账号共享"""
    logger.info("\n=== 并发使用示例 - 账号共享 ===")

    async def worker(worker_id: int, platform: str):
        """工作进程"""
        manager = AccountManager()
        try:
            account = await manager.get_available_account(platform)
            logger.info(f"🔄 Worker {worker_id} 获取到账号: ID={account.id}")

            # 模拟使用账号
            await asyncio.sleep(1)

            # 使用完成 - 无需释放，多个worker可以共享同一账号
            logger.info(f"✅ Worker {worker_id} 使用完成: ID={account.id} (账号可被其他worker共享)")

        except NoAvailableAccountError:
            logger.error(f"❌ Worker {worker_id} 没有可用账号")
        except Exception as e:
            logger.error(f"❌ Worker {worker_id} 发生错误: {e}")

    # 启动多个并发工作进程
    logger.info("📝 注意: 多个worker可能获取到相同的账号ID，这是正常的账号共享行为")
    tasks = [worker(i, "douyin") for i in range(5)]
    await asyncio.gather(*tasks)


async def account_management_example():
    """账号管理示例"""
    logger.info("\n=== 账号管理示例 ===")

    manager = AccountManager()

    # 获取平台账号统计信息
    stats = await manager.get_platform_account_stats("douyin")
    logger.info("📈 抖音平台账号统计:")
    logger.info(f"   总数: {stats['total_count']}")
    logger.info(f"   可用: {stats['available_count']}")
    logger.info(f"   不可用: {stats['unavailable_count']}")
    logger.info(f"   可用率: {stats['availability_rate']:.2%}")

    # 如果有可用账号，演示标记为不可用
    if stats["available_count"] > 0:
        try:
            account = await manager.get_available_account("douyin")
            logger.info(f"\n🔒 将账号 {account.id} 标记为不可用")
            await manager.mark_account_unavailable(account.id, "示例：账号被封禁")

            # 再次查看统计信息
            new_stats = await manager.get_platform_account_stats("douyin")
            logger.info("📈 更新后的统计信息:")
            available_diff = stats["available_count"] - new_stats["available_count"]
            unavailable_diff = new_stats["unavailable_count"] - stats["unavailable_count"]
            logger.info(f"   可用: {new_stats['available_count']} (减少了 {available_diff})")
            logger.info(f"   不可用: {new_stats['unavailable_count']} (增加了 {unavailable_diff})")

        except NoAvailableAccountError:
            logger.error("❌ 没有可用账号进行演示")


async def context_manager_example():
    """上下文管理器使用示例（高级用法）"""
    logger.info("\n=== 上下文管理器示例 ===")

    class AccountContext:
        """账号上下文管理器"""

        def __init__(self, manager: AccountManager, platform: str):
            self.manager = manager
            self.platform = platform
            self.account = None

        async def __aenter__(self):
            self.account = await self.manager.get_available_account(self.platform)
            logger.info(f"🔓 获取账号: ID={self.account.id}")
            return self.account

        async def __aexit__(self, exc_type, exc_val, exc_tb):
            if self.account:
                if exc_type:
                    # 如果发生异常，标记账号为不可用
                    await self.manager.mark_account_unavailable(
                        self.account.id, f"使用过程中发生异常: {exc_type.__name__}"
                    )
                    logger.info(f"❌ 账号 {self.account.id} 因异常被标记为不可用")
                else:
                    # 正常完成，无需释放账号
                    logger.info(f"✅ 账号 {self.account.id} 使用完成 (无需释放)")

    manager = AccountManager()

    try:
        # 正常使用场景
        async with AccountContext(manager, "douyin") as account:
            logger.info(f"🕷️ 使用账号 {account.id} 进行爬取...")
            await asyncio.sleep(1)
            # 正常完成，无需释放账号

        # 异常场景
        async with AccountContext(manager, "douyin") as account:
            logger.info(f"🕷️ 使用账号 {account.id} 进行爬取...")
            raise Exception("模拟爬取过程中的异常")

    except NoAvailableAccountError:
        logger.error("❌ 没有可用账号")
    except Exception as e:
        logger.info(f"✅ 异常已被正确处理: {e}")


async def main():
    """主函数"""
    logger.info("🚀 爬虫账号管理器示例程序")
    logger.info("=" * 50)

    # 初始化数据库
    await init_database()

    try:
        # 运行各种示例
        await basic_usage_example()
        await error_handling_example()
        await concurrent_usage_example()
        await account_management_example()
        await context_manager_example()

    finally:
        # 关闭数据库连接
        await close_database()

    logger.info("\n✅ 示例程序运行完成")


if __name__ == "__main__":
    asyncio.run(main())
