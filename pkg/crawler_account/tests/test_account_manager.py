"""
爬虫账号管理器测试

测试 AccountManager 类的各项功能，包括并发安全性和数据库交互
"""

import asyncio
import time

import pytest

from models.system.crawler import CrawlerCookiesAccount
from pkg.crawler_account.account_manager import AccountManager
from pkg.crawler_account.exceptions import AccountNotFoundError, NoAvailableAccountError


@pytest.mark.asyncio
async def test_get_available_account_success():
    """测试成功获取可用账号"""
    manager = AccountManager()

    # 获取账号前记录原始状态
    original_account = await CrawlerCookiesAccount.filter(
        platform_name="douyin", status=AccountManager.STATUS_AVAILABLE
    ).first()
    original_status = original_account.status if original_account else None

    account = await manager.get_available_account("douyin")

    assert account is not None
    assert account.platform_name == "douyin"
    assert account.status == AccountManager.STATUS_AVAILABLE

    # 验证账号状态未被修改（简化后不修改状态）
    updated_account = await CrawlerCookiesAccount.get(id=account.id)
    assert updated_account.status == AccountManager.STATUS_AVAILABLE
    assert updated_account.status == original_status  # 状态保持不变


@pytest.mark.asyncio
async def test_get_available_account_no_accounts():
    """测试没有可用账号的情况"""
    manager = AccountManager()
    with pytest.raises(NoAvailableAccountError):
        await manager.get_available_account("nonexistent_platform")


@pytest.mark.asyncio
async def test_get_available_account_all_unavailable():
    """测试所有账号都不可用的情况"""
    manager = AccountManager()
    # 将所有抖音账号标记为不可用
    await CrawlerCookiesAccount.filter(platform_name="douyin", status=0).update(status=-1)

    with pytest.raises(NoAvailableAccountError):
        await manager.get_available_account("douyin")


@pytest.mark.asyncio
async def test_mark_account_unavailable_success():
    """测试成功标记账号为不可用"""
    manager = AccountManager()
    available_account = await CrawlerCookiesAccount.get(account_name="test_available")
    await manager.mark_account_unavailable(available_account.id, "测试标记")

    # 验证账号状态已更新
    updated_account = await CrawlerCookiesAccount.get(id=available_account.id)
    assert updated_account.status == AccountManager.STATUS_UNAVAILABLE
    assert updated_account.invalid_timestamp > 0


@pytest.mark.asyncio
async def test_mark_account_unavailable_not_found():
    """测试标记不存在的账号"""
    manager = AccountManager()
    with pytest.raises(AccountNotFoundError):
        await manager.mark_account_unavailable(99999, "测试")


@pytest.mark.asyncio
async def test_get_account_status():
    """测试获取账号状态"""
    manager = AccountManager()
    available_account = await CrawlerCookiesAccount.get(account_name="test_available")
    status_info = await manager.get_account_status(available_account.id)

    assert status_info["id"] == available_account.id
    assert status_info["account_name"] == "test_available"
    assert status_info["platform_name"] == "douyin"
    assert status_info["status"] == 0
    assert status_info["status_text"] == "可用"


@pytest.mark.asyncio
async def test_get_account_status_not_found():
    """测试获取不存在账号的状态"""
    manager = AccountManager()
    with pytest.raises(AccountNotFoundError):
        await manager.get_account_status(99999)


@pytest.mark.asyncio
async def test_get_platform_account_stats():
    """测试获取平台账号统计（简化模型）"""
    manager = AccountManager()
    stats = await manager.get_platform_account_stats("douyin")

    # 验证统计信息结构
    assert stats["platform_name"] == "douyin"
    assert "total_count" in stats
    assert "available_count" in stats
    assert "unavailable_count" in stats
    assert "availability_rate" in stats

    # 验证统计数据的逻辑正确性
    assert stats["total_count"] == stats["available_count"] + stats["unavailable_count"]
    assert 0 <= stats["availability_rate"] <= 1

    # 验证简化模型：不包含"使用中"状态的统计
    assert "in_use_count" not in stats

    # 验证可用率计算正确
    if stats["total_count"] > 0:
        expected_rate = stats["available_count"] / stats["total_count"]
        assert abs(stats["availability_rate"] - expected_rate) < 0.001


@pytest.mark.integration
@pytest.mark.asyncio
async def test_simplified_workflow():
    """测试简化后的工作流程"""
    manager = AccountManager()
    # 创建测试账号
    account = await CrawlerCookiesAccount.create(
        account_name="workflow_test",
        platform_name="workflow_platform",
        cookies='{"test": "cookie"}',
        status=0,
        invalid_timestamp=0,
    )

    # 1. 获取账号（状态不会改变）
    obtained_account = await manager.get_available_account("workflow_platform")
    assert obtained_account.id == account.id
    assert obtained_account.status == AccountManager.STATUS_AVAILABLE

    # 2. 模拟使用账号
    await asyncio.sleep(0.1)

    # 3. 验证账号状态未改变（简化后不需要释放）
    current_account = await CrawlerCookiesAccount.get(id=account.id)
    assert current_account.status == AccountManager.STATUS_AVAILABLE

    # 4. 再次获取同一个账号（允许共享使用）
    reused_account = await manager.get_available_account("workflow_platform")
    assert reused_account.id == account.id


@pytest.mark.integration
@pytest.mark.asyncio
async def test_account_failure_workflow():
    """测试账号失效的工作流程"""
    manager = AccountManager()
    # 创建测试账号
    account = await CrawlerCookiesAccount.create(
        account_name="failure_test",
        platform_name="failure_platform",
        cookies='{"test": "cookie"}',
        status=0,
        invalid_timestamp=0,
    )

    # 1. 获取账号
    obtained_account = await manager.get_available_account("failure_platform")

    # 2. 模拟账号失效
    await manager.mark_account_unavailable(obtained_account.id, "账号被封禁")

    # 3. 验证账号状态
    failed_account = await CrawlerCookiesAccount.get(id=account.id)
    assert failed_account.status == AccountManager.STATUS_UNAVAILABLE

    # 4. 尝试再次获取账号应该失败
    with pytest.raises(NoAvailableAccountError):
        await manager.get_available_account("failure_platform")


@pytest.mark.asyncio
async def test_account_sharing():
    """测试账号共享功能 - 多个客户端可以获取相同账号"""
    manager = AccountManager()

    # 创建测试账号
    test_account = await CrawlerCookiesAccount.create(
        account_name="shared_test",
        platform_name="sharing_platform",
        cookies='{"test": "shared_cookie"}',
        status=AccountManager.STATUS_AVAILABLE,
        invalid_timestamp=0,
    )

    # 1. 第一个客户端获取账号
    account1 = await manager.get_available_account("sharing_platform")
    assert account1.id == test_account.id
    assert account1.status == AccountManager.STATUS_AVAILABLE

    # 2. 第二个客户端获取账号（应该能获取到相同账号）
    account2 = await manager.get_available_account("sharing_platform")
    assert account2.id == test_account.id
    assert account2.id == account1.id  # 相同账号
    assert account2.status == AccountManager.STATUS_AVAILABLE

    # 3. 第三个客户端也能获取相同账号
    account3 = await manager.get_available_account("sharing_platform")
    assert account3.id == test_account.id
    assert account3.status == AccountManager.STATUS_AVAILABLE

    # 4. 验证账号状态始终保持可用（未被锁定）
    current_account = await CrawlerCookiesAccount.get(id=test_account.id)
    assert current_account.status == AccountManager.STATUS_AVAILABLE

    # 5. 并发获取测试
    async def get_account_concurrent():
        return await manager.get_available_account("sharing_platform")

    # 同时发起多个请求
    tasks = [get_account_concurrent() for _ in range(5)]
    concurrent_accounts = await asyncio.gather(*tasks)

    # 验证所有并发请求都获取到相同账号
    for concurrent_account in concurrent_accounts:
        assert concurrent_account.id == test_account.id
        assert concurrent_account.status == AccountManager.STATUS_AVAILABLE


@pytest.mark.asyncio
async def test_simplified_status_boundary_cases():
    """测试简化状态模型的边界情况"""
    manager = AccountManager()

    # 1. 测试只有两种状态值的验证
    available_account = await CrawlerCookiesAccount.create(
        account_name="boundary_available",
        platform_name="boundary_platform",
        cookies='{"test": "available"}',
        status=AccountManager.STATUS_AVAILABLE,  # 0
        invalid_timestamp=0,
    )

    unavailable_account = await CrawlerCookiesAccount.create(
        account_name="boundary_unavailable",
        platform_name="boundary_platform",
        cookies='{"test": "unavailable"}',
        status=AccountManager.STATUS_UNAVAILABLE,  # -1
        invalid_timestamp=int(time.time()),
    )

    # 2. 验证只能获取可用状态的账号
    obtained_account = await manager.get_available_account("boundary_platform")
    assert obtained_account.id == available_account.id
    assert obtained_account.status == AccountManager.STATUS_AVAILABLE

    # 3. 验证状态映射只包含两种状态
    status_info = await manager.get_account_status(available_account.id)
    assert status_info["status_text"] == "可用"

    status_info_unavailable = await manager.get_account_status(unavailable_account.id)
    assert status_info_unavailable["status_text"] == "不可用"

    # 4. 验证统计信息不包含"使用中"状态
    stats = await manager.get_platform_account_stats("boundary_platform")
    assert "in_use_count" not in stats
    assert stats["total_count"] == 2
    assert stats["available_count"] == 1
    assert stats["unavailable_count"] == 1
    assert stats["availability_rate"] == 0.5

    # 5. 测试将可用账号标记为不可用后的状态变化
    await manager.mark_account_unavailable(available_account.id, "边界测试")

    # 验证账号状态已更新
    updated_account = await CrawlerCookiesAccount.get(id=available_account.id)
    assert updated_account.status == AccountManager.STATUS_UNAVAILABLE

    # 验证统计信息更新
    updated_stats = await manager.get_platform_account_stats("boundary_platform")
    assert updated_stats["available_count"] == 0
    assert updated_stats["unavailable_count"] == 2
    assert updated_stats["availability_rate"] == 0.0

    # 6. 验证没有可用账号时的异常
    with pytest.raises(NoAvailableAccountError):
        await manager.get_available_account("boundary_platform")


@pytest.mark.asyncio
async def test_enhanced_simplified_workflow():
    """测试增强的简化工作流程 - 完整的业务场景"""
    manager = AccountManager()

    # 创建多个测试账号
    accounts = []
    for i in range(3):
        account = await CrawlerCookiesAccount.create(
            account_name=f"workflow_enhanced_{i}",
            platform_name="enhanced_platform",
            cookies=f'{{"test": "cookie_{i}"}}',
            status=AccountManager.STATUS_AVAILABLE,
            invalid_timestamp=0,
        )
        accounts.append(account)

    # 1. 多个客户端同时获取账号（模拟真实并发场景）
    async def simulate_client_usage(client_id: int):
        """模拟客户端使用账号的完整流程"""
        # 获取账号
        account = await manager.get_available_account("enhanced_platform")

        # 模拟使用账号进行业务操作
        await asyncio.sleep(0.1)

        # 在简化模型中，不需要释放账号
        # 返回使用的账号信息用于验证
        return {
            "client_id": client_id,
            "account_id": account.id,
            "account_name": account.account_name,
            "status": account.status,
        }

    # 2. 并发执行多个客户端
    client_tasks = [simulate_client_usage(i) for i in range(10)]
    client_results = await asyncio.gather(*client_tasks)

    # 3. 验证所有客户端都成功获取到账号
    assert len(client_results) == 10
    for result in client_results:
        assert result["account_id"] in [acc.id for acc in accounts]
        assert result["status"] == AccountManager.STATUS_AVAILABLE

    # 4. 验证账号状态未被修改（简化模型的核心特性）
    for account in accounts:
        current_account = await CrawlerCookiesAccount.get(id=account.id)
        assert current_account.status == AccountManager.STATUS_AVAILABLE

    # 5. 模拟账号失效场景
    failed_account = accounts[0]
    await manager.mark_account_unavailable(failed_account.id, "模拟账号被封")

    # 6. 验证失效账号不再被获取
    remaining_account_ids = [acc.id for acc in accounts[1:]]
    for _ in range(5):
        obtained_account = await manager.get_available_account("enhanced_platform")
        assert obtained_account.id in remaining_account_ids
        assert obtained_account.id != failed_account.id

    # 7. 验证统计信息的准确性
    final_stats = await manager.get_platform_account_stats("enhanced_platform")
    assert final_stats["total_count"] == 3
    assert final_stats["available_count"] == 2
    assert final_stats["unavailable_count"] == 1
    assert abs(final_stats["availability_rate"] - (2 / 3)) < 0.001

    # 8. 测试极端情况：所有账号都失效
    for account in accounts[1:]:
        await manager.mark_account_unavailable(account.id, "批量失效测试")

    # 验证无可用账号时的异常处理
    with pytest.raises(NoAvailableAccountError):
        await manager.get_available_account("enhanced_platform")

    # 验证最终统计
    final_stats = await manager.get_platform_account_stats("enhanced_platform")
    assert final_stats["available_count"] == 0
    assert final_stats["unavailable_count"] == 3
    assert final_stats["availability_rate"] == 0.0


@pytest.mark.asyncio
async def test_status_consistency_validation():
    """测试状态一致性验证 - 确保简化模型的数据完整性"""
    manager = AccountManager()

    # 1. 创建测试账号
    test_account = await CrawlerCookiesAccount.create(
        account_name="consistency_test",
        platform_name="consistency_platform",
        cookies='{"test": "consistency"}',
        status=AccountManager.STATUS_AVAILABLE,
        invalid_timestamp=0,
    )

    # 2. 验证初始状态
    status_info = await manager.get_account_status(test_account.id)
    assert status_info["status"] == AccountManager.STATUS_AVAILABLE
    assert status_info["status_text"] == "可用"
    assert status_info["invalid_timestamp"] == 0

    # 3. 测试状态转换的一致性
    await manager.mark_account_unavailable(test_account.id, "一致性测试")

    # 验证状态转换后的一致性
    updated_status = await manager.get_account_status(test_account.id)
    assert updated_status["status"] == AccountManager.STATUS_UNAVAILABLE
    assert updated_status["status_text"] == "不可用"
    assert updated_status["invalid_timestamp"] > 0

    # 4. 验证数据库中的实际状态
    db_account = await CrawlerCookiesAccount.get(id=test_account.id)
    assert db_account.status == AccountManager.STATUS_UNAVAILABLE
    assert db_account.invalid_timestamp > 0

    # 5. 验证状态映射的完整性
    status_map_test_cases = [
        (AccountManager.STATUS_AVAILABLE, "可用"),
        (AccountManager.STATUS_UNAVAILABLE, "不可用"),
    ]

    for status_value, expected_text in status_map_test_cases:
        # 创建特定状态的账号进行测试
        test_status_account = await CrawlerCookiesAccount.create(
            account_name=f"status_map_test_{status_value}",
            platform_name="status_map_platform",
            cookies='{"test": "status_map"}',
            status=status_value,
            invalid_timestamp=int(time.time()) if status_value == AccountManager.STATUS_UNAVAILABLE else 0,
        )

        status_result = await manager.get_account_status(test_status_account.id)
        assert status_result["status_text"] == expected_text
