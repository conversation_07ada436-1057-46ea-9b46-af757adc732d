import os
import sys
import time

import pytest

# 添加项目根目录到 Python 路径，用于测试环境
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "../../.."))

from tortoise import Tortoise

from models.system.crawler import CrawlerCookiesAccount


@pytest.fixture(scope="function", autouse=True)
async def db_session():
    await Tortoise.init(
        config={
            "connections": {"default": "sqlite://:memory:"},
            "apps": {
                "models": {
                    "models": ["models.system.crawler", "models.douyin.models", "models.trendinsight.models"],
                    "default_connection": "default",
                }
            },
        }
    )
    await Tortoise.generate_schemas()
    await _create_test_accounts()
    yield
    await Tortoise.close_connections()


async def _create_test_accounts():
    await CrawlerCookiesAccount.create(
        account_name="test_available",
        platform_name="douyin",
        cookies='{"test": "cookie"}',
        status=0,
        invalid_timestamp=0,
    )
    # 移除 test_in_use 账号，因为简化模型不再有"使用中"状态
    await CrawlerCookiesAccount.create(
        account_name="test_unavailable",
        platform_name="douyin",
        cookies='{"test": "cookie"}',
        status=-1,
        invalid_timestamp=int(time.time()),
    )
    await CrawlerCookiesAccount.create(
        account_name="test_other",
        platform_name="xiaohongshu",
        cookies='{"test": "cookie"}',
        status=0,
        invalid_timestamp=0,
    )
