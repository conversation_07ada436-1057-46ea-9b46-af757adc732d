# 爬虫账号获取器设计文档

本文档旨在设计一个健壮的爬虫账号获取和管理模块。该模块的核心职责是为爬虫任务提供可用的账号，并处理账号在抓取失败后被标记为不可用的情况。

## ✅ 实现状态

**模块已完全实现并可投入使用！**

### 📁 文件结构
```
pkg/crawler_account/
├── README.md              # 设计文档和使用说明
├── __init__.py            # 模块初始化文件
├── exceptions.py          # 异常类定义
├── account_manager.py     # 核心管理类
├── example.py            # 详细使用示例
└── tests/                # 测试文件目录
    ├── test_account_manager.py # 单元测试
    └── test_simple.py     # 简单测试
```

## 1. 功能需求

1.  **获取可用账号**：从数据库表 `crawler_cookies_account` 中获取一个当前状态为“可用”的账号。
2.  **标记账号不可用**：提供一个接口，可以将指定的账号设置为“不可用”状态，通常在遇到账号被封禁或 Cookie 失效等情况时调用。
3.  **账号共享**：多个爬虫进程或线程可以共享使用同一个可用账号，简化并发处理逻辑。

## 2. 流程图

### 2.1 获取可用账号流程

```mermaid
graph TD
    A["开始: 请求一个可用账号(platform)"] --> B{查询数据库};
    B --> C[查找 status='available' 且 platform 匹配的账号];
    C --> D{是否存在可用账号?};
    D -- 是 --> E[随机选择一个账号];
    E --> F[直接返回账号信息];
    F --> G[结束];
    D -- 否 --> H[抛出 NoAvailableAccountError 异常];
    H --> G;
```

### 2.2 标记账号不可用流程

```mermaid
graph TD
    A["开始: 标记指定账号不可用"] --> B[接收 account_id];
    B --> C{根据 account_id 查询数据库};
    C --> D[找到对应账号];
    D --> E[更新账号 status='unavailable'];
    E --> F[记录操作日志];
    F --> G[结束];
```

## 3. UML 类图

下面是模块的 UML 类图，展示了核心类 `AccountManager` 和数据模型 `CrawlerCookiesAccount` 之间的关系。

```mermaid
classDiagram
    class AccountManager {
        -db_session: Session
        +get_available_account(platform_name: str) CrawlerCookiesAccount
        +mark_account_unavailable(account_id: int, reason: str) None
        +get_account_status(account_id: int) dict
        +get_platform_account_stats(platform_name: str) dict
    }

    class CrawlerCookiesAccount {
        +id: int
        +platform: str
        +account_name: str
        +cookies: str
        +status: str  // 'available', 'unavailable'
        +created_at: datetime
        +updated_at: datetime
    }

    AccountManager ..> CrawlerCookiesAccount : "uses"
```

### 类说明：

*   **`AccountManager`**:
    *   这是核心的管理类，负责与数据库交互以获取和更新账号状态。
    *   `get_available_account(platform_name: str)`: 实现“获取可用账号流程”，直接返回一个指定平台的可用账号对象，支持账号共享。
    *   `mark_account_unavailable()`: 实现“标记账号不可用流程”。

*   **`CrawlerCookiesAccount`**:
    *   这是一个数据模型类（例如，SQLAlchemy Model），直接映射到数据库中的 `crawler_cookies_account` 表。

## 4. 使用说明

### 4.1 快速开始

```python
import sys
import os
import asyncio

from account_manager import AccountManager
from exceptions import NoAvailableAccountError

async def main():
    # 创建账号管理器
    manager = AccountManager()

    try:
        # 获取可用账号
        account = await manager.get_available_account("douyin")
        print(f"获取到账号: {account.account_name}")

        # 使用账号进行爬取
        # ... 你的爬取逻辑 ...

        # 使用完成 - 无需释放账号，状态保持可用
        print("账号使用完成，无需释放")

    except NoAvailableAccountError:
        print("没有可用账号")
    except Exception as e:
        # 如果发生异常，标记账号为不可用
        if 'account' in locals():
            await manager.mark_account_unavailable(
                account.id,
                f"使用过程中发生异常: {str(e)}"
            )
        raise

# 运行
asyncio.run(main())
```

### 4.2 核心 API

#### AccountManager 类

- `get_available_account(platform_name: str)` - 获取指定平台的可用账号
- `mark_account_unavailable(account_id: int, reason: str)` - 标记账号为不可用
- `get_account_status(account_id: int)` - 获取账号状态信息
- `get_platform_account_stats(platform_name: str)` - 获取平台账号统计

#### 异常类

- `NoAvailableAccountError` - 没有可用账号
- `AccountNotFoundError` - 账号不存在
- `DatabaseOperationError` - 数据库操作失败

### 4.3 运行示例

```bash
# 运行详细示例（需要数据库连接）
cd pkg/crawler_account
python example.py

# 运行单元测试
python -m pytest tests/ -v
```

### 4.4 注意事项

1. **数据库连接**: 使用前需要确保 Tortoise ORM 已正确初始化
2. **测试数据**: 确保 `crawler_cookies_account` 表中有测试数据
3. **异常处理**: 在生产环境中要妥善处理各种异常情况
4. **账号共享**: 模块支持账号共享，多个进程/线程可以同时使用相同账号
5. **账号状态**:
   - 0: 可用
   - -1: 不可用
