"""
爬虫账号管理器

提供爬虫账号的获取、状态管理功能
"""

import time

from tortoise.exceptions import DoesNotExist

from log import logger
from models.system.crawler import CrawlerCookiesAccount
from pkg.crawler_account.exceptions import (
    AccountNotFoundError,
    DatabaseOperationError,
    NoAvailableAccountError,
)


class AccountManager:
    """爬虫账号管理器

    负责管理爬虫账号的获取和状态更新
    """

    # 账号状态常量
    STATUS_AVAILABLE = 0  # 可用
    STATUS_UNAVAILABLE = -1  # 不可用

    def __init__(self, enable_sql_logging: bool = False):
        """初始化账号管理器

        Args:
            enable_sql_logging: 是否启用 SQL 日志
        """
        self.logger = logger

        # 如果启用 SQL 日志，配置相关设置
        if enable_sql_logging:
            self._enable_sql_logging()

    def _enable_sql_logging(self):
        """启用 SQL 日志"""
        import logging

        # 设置 Tortoise 数据库日志级别为 DEBUG
        db_logger = logging.getLogger("tortoise.db_client")
        db_logger.setLevel(logging.DEBUG)

        # 创建控制台处理器
        if not db_logger.handlers:
            console_handler = logging.StreamHandler()
            console_handler.setLevel(logging.DEBUG)

            # 设置日志格式
            formatter = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
            console_handler.setFormatter(formatter)

            db_logger.addHandler(console_handler)

        self.logger.info("SQL 日志已启用")

    async def get_available_account(self, platform_name: str) -> CrawlerCookiesAccount:
        """获取指定平台的可用账号

        Args:
            platform_name: 平台名称

        Returns:
            CrawlerCookiesAccount: 可用的账号对象

        Raises:
            NoAvailableAccountError: 没有可用账号
            DatabaseOperationError: 数据库操作失败
        """
        logger.info(f"开始获取可用账号 - platform_name: {platform_name}")

        try:
            # 直接查询可用账号，无需事务和重试
            account = await CrawlerCookiesAccount.filter(
                platform_name=platform_name, status=self.STATUS_AVAILABLE, is_deleted=False
            ).first()

            if not account:
                logger.warning(f"没有找到可用账号 - platform_name: {platform_name}")
                raise NoAvailableAccountError(platform_name)

            self.logger.info(f"成功获取平台 '{platform_name}' 的账号: " f"ID={account.id}, name={account.account_name}")

            return account

        except NoAvailableAccountError:
            raise
        except Exception as e:
            raise DatabaseOperationError("get_available_account", e)

    async def mark_account_unavailable(self, account_id: int, reason: str = "账号失效") -> None:
        """标记账号为不可用状态

        Args:
            account_id: 账号ID
            reason: 标记原因

        Raises:
            AccountNotFoundError: 账号不存在
            DatabaseOperationError: 数据库操作失败
        """
        try:
            # 检查账号是否存在
            try:
                account = await CrawlerCookiesAccount.get(id=account_id, is_deleted=False)
            except DoesNotExist:
                raise AccountNotFoundError(account_id)

            # 更新账号状态为不可用
            await CrawlerCookiesAccount.filter(id=account_id).update(
                status=self.STATUS_UNAVAILABLE, invalid_timestamp=int(time.time())
            )

            self.logger.info(
                f"账号已标记为不可用: ID={account_id}, " f"platform={account.platform_name}, reason={reason}"
            )

        except (AccountNotFoundError, DatabaseOperationError):
            raise
        except Exception as e:
            raise DatabaseOperationError("mark_account_unavailable", e)

    async def get_account_status(self, account_id: int) -> dict:
        """获取账号状态信息

        Args:
            account_id: 账号ID

        Returns:
            dict: 账号状态信息

        Raises:
            AccountNotFoundError: 账号不存在
            DatabaseOperationError: 数据库操作失败
        """
        try:
            try:
                account = await CrawlerCookiesAccount.get(id=account_id, is_deleted=False)
            except DoesNotExist:
                raise AccountNotFoundError(account_id)

            status_map = {
                self.STATUS_AVAILABLE: "可用",
                self.STATUS_UNAVAILABLE: "不可用",
            }

            return {
                "id": account.id,
                "account_name": account.account_name,
                "platform_name": account.platform_name,
                "status": account.status,
                "status_text": status_map.get(account.status, "未知"),
                "invalid_timestamp": account.invalid_timestamp,
                "created_at": account.created_at,
                "updated_at": account.updated_at,
            }

        except (AccountNotFoundError, DatabaseOperationError):
            raise
        except Exception as e:
            raise DatabaseOperationError("get_account_status", e)

    async def get_platform_account_stats(self, platform_name: str) -> dict:
        """获取指定平台的账号统计信息

        Args:
            platform_name: 平台名称

        Returns:
            dict: 账号统计信息
        """
        logger.info(f"开始获取平台账号统计信息 - platform_name: {platform_name}")
        try:
            total_count = await CrawlerCookiesAccount.filter(platform_name=platform_name, is_deleted=False).count()

            available_count = await CrawlerCookiesAccount.filter(
                platform_name=platform_name, status=self.STATUS_AVAILABLE, is_deleted=False
            ).count()

            unavailable_count = await CrawlerCookiesAccount.filter(
                platform_name=platform_name, status=self.STATUS_UNAVAILABLE, is_deleted=False
            ).count()

            stats = {
                "platform_name": platform_name,
                "total_count": total_count,
                "available_count": available_count,
                "unavailable_count": unavailable_count,
                "availability_rate": available_count / total_count if total_count > 0 else 0,
            }

            logger.info(
                f"平台账号统计信息获取成功 - platform_name: {platform_name}, "
                f"总数: {total_count}, 可用: {available_count}, "
                f"不可用: {unavailable_count}"
            )

            return stats

        except Exception as e:
            raise DatabaseOperationError("get_platform_account_stats", e)
