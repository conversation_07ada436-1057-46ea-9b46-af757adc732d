"""
爬虫账号管理器异常定义

定义账号获取和管理过程中可能出现的各种异常
"""

from typing import Optional


class CrawlerAccountError(Exception):
    """爬虫账号管理器基础异常类"""

    def __init__(self, message: str, account_id: Optional[int] = None, platform_name: Optional[str] = None, **kwargs):
        super().__init__(message)
        self.message = message
        self.account_id = account_id
        self.platform_name = platform_name
        self.extra_data = kwargs

    def __str__(self):
        parts = [self.message]
        if self.platform_name:
            parts.append(f"platform={self.platform_name}")
        if self.account_id:
            parts.append(f"account_id={self.account_id}")
        return f"{self.__class__.__name__}: {', '.join(parts)}"


class NoAvailableAccountError(CrawlerAccountError):
    """没有可用账号异常

    当指定平台没有可用账号时抛出此异常
    """

    def __init__(self, platform_name: str, **kwargs):
        message = f"没有找到平台 '{platform_name}' 的可用账号"
        super().__init__(message, platform_name=platform_name, **kwargs)


class AccountNotFoundError(CrawlerAccountError):
    """账号不存在异常

    当指定的账号ID不存在时抛出此异常
    """

    def __init__(self, account_id: int, **kwargs):
        message = f"账号 ID {account_id} 不存在"
        super().__init__(message, account_id=account_id, **kwargs)


class DatabaseOperationError(CrawlerAccountError):
    """数据库操作异常

    当数据库操作失败时抛出此异常
    """

    def __init__(self, operation: str, original_error: Exception, **kwargs):
        message = f"数据库操作 '{operation}' 失败: {str(original_error)}"
        super().__init__(message, **kwargs)
        self.operation = operation
        self.original_error = original_error
