# =============================================================================
# 起号助手媒体爬虫项目 - 本地开发环境配置模板
# =============================================================================
# 复制此文件为 .env 并填入真实的配置数据

# =============================================================================
# Dynaconf 环境配置
# =============================================================================
# 指定使用的配置环境 (development, staging, production)
ENV_FOR_DYNACONF=development

# =============================================================================
# Python 环境配置
# =============================================================================
# 根据你的实际路径修改
PYTHONPATH=/path/to/your/project
CONDA_DEFAULT_ENV=base
CONDA_PREFIX=/opt/homebrew/anaconda3

# =============================================================================
# 应用基础配置 (使用 APP_ 前缀匹配 dynaconf)
# =============================================================================
# 应用服务配置
APP_HOST=127.0.0.1
APP_PORT=8000
APP_DEBUG=true
APP_LOG_LEVEL=DEBUG

# 安全配置 - 生产环境请使用强密钥
APP_SECRET_KEY=dev-secret-key-change-in-production-environment

# JWT 配置
APP_JWT_ALGORITHM=HS256
APP_JWT_ACCESS_TOKEN_EXPIRE_MINUTES=43200

# =============================================================================
# 数据库配置 (使用 APP_ 前缀匹配 dynaconf)
# =============================================================================
APP_TORTOISE_ORM='@json {"connections": {"default": "mysql://qihaozhushou:qihaozhushou2025%40@**************:3306/media_crawler"}, "apps": {"models": {"models": ["models", "aerich.models"], "default_connection": "default"}}}'

# =============================================================================
# Kuaidaili 代理配置 (使用 APP_ 前缀匹配 dynaconf)
# =============================================================================
# 请填入你的真实 Kuaidaili 配置
APP_KUAIDAILI_DPS_SECRET_ID=your_secret_id_here
APP_KUAIDAILI_DPS_SIGNATURE=your_signature_here
APP_KUAIDAILI_USERNAME=your_username_here
APP_KUAIDAILI_PASSWORD=your_password_here
# APP_KUAIDAILI_API_BASE_URL 已在 dynaconf 配置中设置默认值 (https://dps.kdlapi.com)，通常无需覆盖

# =============================================================================
# 测试配置
# =============================================================================
# 是否启用真实API测试
ENABLE_REAL_API_TESTS=false

# 测试用的视频和收藏夹ID - 请使用真实存在的ID
TEST_VIDEO_ID=7234567890123456789
TEST_COLLECTS_ID=7234567890123456789

# 测试用户ID (抖音用户的sec_user_id)
TEST_USER_ID=MS4wLjABAAAA_example_user_id_here

# =============================================================================
# 抖音测试 Cookies (定期更新)
# =============================================================================
# 从浏览器开发者工具获取，需要定期更新
DOUYIN_TEST_COOKIES="your_douyin_cookies_here"

# =============================================================================
# 趋势洞察测试 Cookies (定期更新)
# =============================================================================
# 从浏览器开发者工具获取，需要定期更新
TRENDINSIGHT_TEST_COOKIES="your_trendinsight_cookies_here"

# =============================================================================
# 开发工具配置
# =============================================================================
# 测试超时时间
API_TEST_TIMEOUT=30

# 测试重试次数
API_TEST_RETRIES=2

# 是否启用详细日志
ENABLE_TEST_LOGGING=true

# 测试日志级别
TEST_LOG_LEVEL=DEBUG

# =============================================================================
# 使用说明
# =============================================================================
# 1. 复制此文件为 .env
#    cp .env.local.example .env
#
# 2. 填入真实的配置数据（特别是 Kuaidaili 配置和 Cookies）
#
# 3. 所有以 APP_ 开头的环境变量会被 dynaconf 自动加载
#
# 4. ENV_FOR_DYNACONF 指定使用的配置环境：
#    - development: 开发环境
#    - staging: 测试环境
#    - production: 生产环境
#
# 5. 获取 Cookies 的方法：
#    a. 打开浏览器，访问对应网站并登录
#    b. 打开开发者工具（F12）
#    c. 切换到 Network 标签
#    d. 刷新页面，找到任意一个请求
#    e. 在请求头中找到 Cookie 字段，复制其值
#
# 6. 注意事项：
#    - Cookies 会过期，需要定期更新
#    - 不要将包含真实配置的 .env 文件提交到公共仓库
#    - 生产环境请使用强密钥和安全配置