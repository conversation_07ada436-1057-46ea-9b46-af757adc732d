# 项目开发指南

本文档提供了项目开发过程中需要遵循的规范和最佳实践。

## 目录

- [配置管理](#配置管理)
- [测试规范](#测试规范)
- [代码规范](#代码规范)
- [文档规范](#文档规范)

## 配置管理

### 使用 Dynaconf 进行配置管理

项目使用 [Dynaconf](https://www.dynaconf.com/) 作为配置管理工具，**禁止直接从环境变量获取配置**。

#### ✅ 正确的配置获取方式

```python
from settings.config import settings

# 获取配置值
api_key = settings.get("KUAIDAILI_DPS_SECRET_ID")
api_secret = settings.get("KUAIDAILI_DPS_SIGNATURE")
```

#### ❌ 错误的配置获取方式

```python
import os

# 不要直接使用 os.getenv()
api_key = os.getenv("KUAIDAILI_DPS_SECRET_ID")  # 禁止使用
api_secret = os.environ.get("KUAIDAILI_DPS_SIGNATURE")  # 禁止使用
```

#### 配置文件结构

项目配置文件位于 `settings/` 目录下：

- `settings/default.toml` - 默认配置
- `settings/development.toml` - 开发环境配置
- `settings/staging.toml` - 测试环境配置
- `settings/production.toml` - 生产环境配置
- `.secrets.toml` - 敏感信息配置（不提交到版本控制）

#### 配置优先级

Dynaconf 按以下优先级加载配置：

1. 环境变量（以 `APP_` 为前缀）
2. `.secrets.toml`
3. 环境特定配置文件（如 `production.toml`）
4. `default.toml`

#### 在测试中使用配置

测试代码中应该使用 dynaconf 获取配置，而不是直接读取环境变量：

```python
import pytest
from settings.config import settings

class TestExample:
    @pytest.fixture
    def api_credentials(self):
    """从 dynaconf 获取 API 凭据"""
    api_key = settings.get("KUAIDAILI_DPS_SECRET_ID")
    api_secret = settings.get("KUAIDAILI_DPS_SIGNATURE")
    
    if not api_key:
        pytest.skip("需要在配置中设置 KUAIDAILI_DPS_SECRET_ID")
        
        return {
            "api_key": api_key,
            "api_secret": api_secret or ""
        }
```

### 配置验证

项目在 `settings/config.py` 中定义了配置验证器，确保必要的配置项存在且类型正确。

## 测试规范

### 测试分类

- **单元测试**: 测试单个函数或类的功能
- **集成测试**: 测试模块间的交互
- **真实 API 测试**: 使用真实 API 的测试，标记为 `@pytest.mark.real_api`

### 测试配置

真实 API 测试需要配置相应的 API 密钥：

```toml
# .secrets.toml
[default]
KUAIDAILI_DPS_SECRET_ID = "your_secret_id"
KUAIDAILI_DPS_SIGNATURE = "your_signature"
```

### 运行测试

```bash
# 运行所有测试
pytest

# 运行单元测试（跳过真实 API 测试）
pytest -m "not real_api"

# 运行真实 API 测试
pytest -m real_api

# 运行特定模块的测试
pytest pkg/proxy/tests/ -v
```

## 代码规范

### 导入规范

- 使用绝对导入
- 按标准库、第三方库、项目内部模块的顺序组织导入
- 使用 `from pkg.module import Class` 而不是 `from pkg.module.file import Class`

### 异步代码规范

- 异步函数使用 `async def` 定义
- 异步调用使用 `await`
- 资源清理使用 `try/finally` 或上下文管理器

### 错误处理

- 使用具体的异常类型
- 提供有意义的错误消息
- 记录错误日志

## 文档规范

### 代码文档

- 所有公共类和函数必须有文档字符串
- 使用 Google 风格的文档字符串
- 包含参数说明、返回值说明和使用示例

### README 文档

每个模块应该包含 README.md 文件，说明：

- 模块功能
- 安装依赖
- 使用示例
- API 文档

### 更新日志

重要的功能更新应该记录在相应的 CHANGELOG.md 或 REFACTOR_SUMMARY.md 中。

---

## 贡献指南

1. 遵循本指南中的所有规范
2. 提交前运行测试确保代码质量
3. 更新相关文档
4. 编写清晰的提交消息

如有疑问，请参考项目中的示例代码或联系项目维护者。