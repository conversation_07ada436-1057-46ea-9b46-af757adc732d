# 生产环境配置
[production]
database_uri = "env_var_for_prod_db_uri"
log_level = "WARNING"
debug = false
secret_key = "prod-secret-key-change-this"
kuaidaili_dps_order_id = "your-prod-order-id-here"

# 数据库连接配置已移至 connections.toml
# 生产环境可以通过环境变量覆盖连接字符串
# 例如：APP_TORTOISE_ORM__CONNECTIONS__DEFAULT="mysql://..."

# 生产环境特定配置
app_title = "Vue FastAPI Admin"
project_name = "Vue FastAPI Admin"

# 生产环境严格的CORS设置
cors_origins = ["https://yourdomain.com", "https://www.yourdomain.com"]

# 生产环境JWT过期时间
jwt_access_token_expire_minutes = 1440  # 24 hours

# 生产环境日志配置覆盖
[production.logging]
level = "WARNING"
colorize = false

[production.logging.console]
enabled = false  # 生产环境不输出到控制台

[production.logging.file]
level = "WARNING"
rotation = "500 MB"
retention = "90 days"

[production.logging.json]
enabled = true  # 生产环境启用结构化日志
level = "INFO"
rotation = "500 MB"
retention = "90 days"

[production.logging.sql]
enabled = false  # 生产环境默认关闭 SQL 日志