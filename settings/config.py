import os

from dynaconf import Dynaconf, Validator

# Project root directory
PROJECT_ROOT = os.path.abspath(os.path.join(os.path.dirname(__file__), os.pardir))

settings = Dynaconf(
    envvar_prefix="APP",
    settings_files=[
        "settings/default.toml",        # 基础应用配置
        "settings/database.toml",       # 数据库应用配置（apps）
        "settings/development.toml",    # 开发环境特定配置
        "settings/staging.toml",        # 测试环境特定配置
        "settings/production.toml",     # 生产环境特定配置
        ".secrets.toml",                # 敏感信息配置
    ],
    environments=True,
    load_dotenv=True,
    # 启用合并功能，支持 @merge 语法
    merge_enabled=True,
    # Add dynamic values to settings
    PROJECT_ROOT=PROJECT_ROOT,
    BASE_DIR=PROJECT_ROOT,
    LOGS_ROOT=os.path.join(PROJECT_ROOT, "logs"),
)

# Validation
LOG_LEVELS = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]

settings.validators.register(
    # General App Settings
    Validator("version", default="0.1.0"),
    Validator("app_title", default="Vue FastAPI Admin"),
    Validator("project_name", default="Vue FastAPI Admin"),
    Validator("app_description", default="Description"),
    Validator("debug", default=True, is_type_of=bool),
    # CORS Settings
    Validator("cors_origins", default=["*"], is_type_of=list),
    Validator("cors_allow_credentials", default=True, is_type_of=bool),
    Validator("cors_allow_methods", default=["*"], is_type_of=list),
    Validator("cors_allow_headers", default=["*"], is_type_of=list),
    # Security Settings
    Validator("secret_key", must_exist=True, is_type_of=str),
    Validator("jwt_algorithm", default="HS256"),
    Validator("jwt_access_token_expire_minutes", default=60 * 24 * 7, is_type_of=int),
    # Database Settings (tortoise_orm)
    Validator("tortoise_orm.connections.default", must_exist=True),
    Validator("tortoise_orm.apps.models.models", default=["models", "aerich.models"]),
    Validator("tortoise_orm.apps.models.default_connection", default="default"),
    # Datetime format
    Validator("datetime_format", default="%Y-%m-%d %H:%M:%S"),
    # Kuaidaili settings
    Validator("kuaidaili_dps_secret_id", must_exist=True),
    Validator("kuaidaili_dps_signature", must_exist=True),
    Validator("kuaidaili_api_base_url", default="https://dps.kuaidaili.com"),
    Validator("kuaidaili_username", default=""),
    Validator("kuaidaili_password", default=""),
    
    # 日志配置验证器
    # 全局日志配置验证器
    Validator("logging.level", must_exist=True, is_in=LOG_LEVELS),
    Validator("logging.format", must_exist=True, is_type_of=str, len_min=10),
    Validator("logging.colorize", default=True, is_type_of=bool),
    
    # 控制台日志验证器
    Validator("logging.console.enabled", default=True, is_type_of=bool),
    Validator("logging.console.level", 
             default="INFO", 
             is_in=LOG_LEVELS,
             when=Validator("logging.console.enabled", eq=True)),
    Validator("logging.console.colorize", default=True, is_type_of=bool),
    Validator("logging.console.format", default="", is_type_of=str),
    
    # 文件日志验证器
    Validator("logging.file.enabled", default=True, is_type_of=bool),
    Validator("logging.file.path", 
             default="logs/application.log", 
             is_type_of=str,
             when=Validator("logging.file.enabled", eq=True)),
    Validator("logging.file.level", 
             default="INFO", 
             is_in=LOG_LEVELS,
             when=Validator("logging.file.enabled", eq=True)),
    Validator("logging.file.rotation", default="100 MB", is_type_of=str),
    Validator("logging.file.retention", default="30 days", is_type_of=str),
    Validator("logging.file.compression", default="gz", is_in=["gz", "zip", "tar.gz", ""]),
    Validator("logging.file.enqueue", default=True, is_type_of=bool),
    Validator("logging.file.format", default="", is_type_of=str),
    
    # JSON 日志验证器
    Validator("logging.json.enabled", default=False, is_type_of=bool),
    Validator("logging.json.path", 
             default="logs/application.json",
             is_type_of=str,
             when=Validator("logging.json.enabled", eq=True)),
    Validator("logging.json.level", 
             default="INFO", 
             is_in=LOG_LEVELS,
             when=Validator("logging.json.enabled", eq=True)),
    Validator("logging.json.serialize", default=True, is_type_of=bool),
    Validator("logging.json.rotation", default="100 MB", is_type_of=str),
    Validator("logging.json.retention", default="30 days", is_type_of=str),
    Validator("logging.json.compression", default="gz", is_in=["gz", "zip", "tar.gz", ""]),
    Validator("logging.json.enqueue", default=True, is_type_of=bool),
    
    # SQL 日志验证器
    Validator("logging.sql.enabled", default=False, is_type_of=bool),
    Validator("logging.sql.level", 
             default="DEBUG", 
             is_in=LOG_LEVELS,
             when=Validator("logging.sql.enabled", eq=True)),
    Validator("logging.sql.format", 
             must_exist=True, 
             is_type_of=str, 
             len_min=10,
             when=Validator("logging.sql.enabled", eq=True)),
    
    # 环境特定验证器
    Validator("logging.json.enabled", eq=True, env="production"),
    Validator("logging.console.enabled", eq=False, env="production"),
    Validator("logging.sql.enabled", eq=True, env="development"),
    # 移除过于严格的环境日志级别限制，允许动态调整
    # Validator("logging.level", is_in=["DEBUG", "INFO"], env="development"),
    # Validator("logging.level", is_in=["WARNING", "ERROR", "CRITICAL"], env="production"),
)

# It is recommended to call validate() at the application's entry point (e.g., in main.py)
# to ensure all modules are loaded before validation.
# settings.validators.validate()
