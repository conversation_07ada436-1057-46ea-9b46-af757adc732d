"""
SQL 日志配置模块

提供 Tortoise ORM SQL 日志的配置和管理功能
"""

import logging
from typing import Optional

from loguru import logger


class SQLLoggingConfig:
    """SQL 日志配置类"""

    def __init__(self):
        self._sql_logging_enabled = False
        self._original_handlers = {}

    def enable_sql_logging(self, level: str = "DEBUG", format_string: Optional[str] = None):
        """
        启用 SQL 日志

        Args:
            level: 日志级别 (DEBUG, INFO, WARNING, ERROR)
            format_string: 自定义日志格式
        """
        if self._sql_logging_enabled:
            logger.debug("SQL 日志已经启用")
            return

        # 默认日志格式
        if format_string is None:
            format_string = (
                "<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | "
                "<level>{level: <8}</level> | "
                "<cyan>SQL</cyan> | "
                "<level>{message}</level>"
            )

        # 配置 Tortoise 相关的日志器
        tortoise_loggers = [
            "tortoise.db_client",  # 数据库客户端日志（包含 SQL 查询）
            "tortoise",  # 通用 Tortoise 日志
            "tortoise.backends.mysql",  # MySQL 后端日志
            "tortoise.backends.sqlite",  # SQLite 后端日志
            "tortoise.backends.asyncpg",  # PostgreSQL 后端日志
        ]

        # 设置日志级别
        log_level = getattr(logging, level.upper(), logging.DEBUG)

        for logger_name in tortoise_loggers:
            py_logger = logging.getLogger(logger_name)
            py_logger.setLevel(log_level)

            # 保存原始处理器
            self._original_handlers[logger_name] = py_logger.handlers.copy()

            # 清除现有处理器
            py_logger.handlers.clear()

            # 添加自定义处理器
            handler = LoguruHandler(format_string)
            py_logger.addHandler(handler)
            py_logger.propagate = False

        self._sql_logging_enabled = True
        logger.info("SQL 日志已启用")

    def disable_sql_logging(self):
        """禁用 SQL 日志"""
        if not self._sql_logging_enabled:
            logger.debug("SQL 日志未启用")
            return

        # 恢复原始处理器
        for logger_name, handlers in self._original_handlers.items():
            py_logger = logging.getLogger(logger_name)
            py_logger.handlers.clear()
            py_logger.handlers.extend(handlers)
            py_logger.propagate = True

        self._original_handlers.clear()
        self._sql_logging_enabled = False
        logger.info("SQL 日志已禁用")

    def is_enabled(self) -> bool:
        """检查 SQL 日志是否已启用"""
        return self._sql_logging_enabled


class LoguruHandler(logging.Handler):
    """将 Python 标准日志重定向到 loguru 的处理器"""

    def __init__(self, format_string: str):
        super().__init__()
        self.format_string = format_string

    def emit(self, record):
        """发送日志记录到 loguru"""
        try:
            # 获取对应的 loguru 级别
            level = record.levelname

            # 格式化消息
            message = record.getMessage()

            # 如果是 SQL 查询，进行特殊格式化
            if "tortoise.db_client" in record.name and "query" in message.lower():
                message = self._format_sql_query(message)

            # 发送到 loguru
            logger.opt(depth=6, exception=record.exc_info).log(level, message)

        except Exception:
            self.handleError(record)

    def _format_sql_query(self, message: str) -> str:
        """格式化 SQL 查询消息"""
        try:
            # 简单的 SQL 格式化
            if message.startswith("query:"):
                sql_part = message[6:].strip()
                return f"🔍 SQL Query: {sql_part}"
            elif "args:" in message:
                return f"📝 SQL Args: {message}"
            else:
                return f"💾 SQL: {message}"
        except Exception:
            return message


# 全局 SQL 日志配置实例
sql_logging = SQLLoggingConfig()


def enable_sql_logging(level: str = "DEBUG", format_string: Optional[str] = None):
    """
    启用 SQL 日志的便捷函数

    Args:
        level: 日志级别
        format_string: 自定义日志格式
    """
    sql_logging.enable_sql_logging(level, format_string)


def disable_sql_logging():
    """禁用 SQL 日志的便捷函数"""
    sql_logging.disable_sql_logging()


def is_sql_logging_enabled() -> bool:
    """检查 SQL 日志是否已启用"""
    return sql_logging.is_enabled()
