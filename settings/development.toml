# 开发环境配置
[development]
database_uri = "postgresql://user:pass@localhost:5432/devdb"
log_level = "DEBUG"
debug = true
secret_key = "dev-secret-key-change-in-production"
kuaidaili_dps_order_id = "your-dev-order-id-here"

# 数据库连接配置已移至 connections.toml
# 环境变量可以通过 dunder 语法覆盖特定连接

# 开发环境特定配置
app_title = "Vue FastAPI Admin - Development"
project_name = "Vue FastAPI Admin - Dev"

# 开发环境允许更宽松的CORS设置
cors_origins = ["http://localhost:3000", "http://localhost:8080", "http://127.0.0.1:3000", "http://127.0.0.1:8080"]

# 开发环境JWT过期时间更长
jwt_access_token_expire_minutes = 43200  # 30 days

# 开发环境日志配置覆盖
[development.logging]
level = "DEBUG"
colorize = true

[development.logging.console]
enabled = true
level = "DEBUG"
colorize = true

[development.logging.file]
level = "DEBUG"
rotation = "50 MB"
retention = "7 days"

[development.logging.sql]
enabled = true
level = "DEBUG"