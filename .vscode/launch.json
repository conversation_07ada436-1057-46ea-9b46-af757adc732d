{
    // VSCode FastAPI Python 调试配置
    // 支持多种运行模式：开发、生产、测试等
    "version": "0.2.0",
    "configurations": [
        {
            "name": "🚀 FastAPI 开发服务器 (热重载)",
            "type": "debugpy",
            "request": "launch",
            "python": "${workspaceFolder}/.venv/bin/python",
            "program": "${workspaceFolder}/run.py",
            "cwd": "${workspaceFolder}",
            "env": {
                "PYTHONPATH": "${workspaceFolder}",
                "ENVIRONMENT": "development"
            },
            "console": "integratedTerminal",
            "justMyCode": false,
            "stopOnEntry": false,
            "autoReload": {
                "enable": true
            }
        },
        {
            "name": "🏭 FastAPI 生产模式",
            "type": "debugpy",
            "request": "launch",
            "python": "${workspaceFolder}/.venv/bin/python",
            "module": "uvicorn",
            "args": [
                "main:app",
                "--host", "0.0.0.0",
                "--port", "8000",
                "--workers", "1",
                "--log-level", "info",
                "--access-log",
                "--use-colors"
            ],
            "cwd": "${workspaceFolder}",
            "env": {
                "PYTHONPATH": "${workspaceFolder}",
                "ENVIRONMENT": "production"
            },
            "console": "integratedTerminal",
            "justMyCode": true,
            "stopOnEntry": false
        },
        {
            "name": "🧪 FastAPI 测试环境",
            "type": "debugpy",
            "request": "launch",
            "python": "${workspaceFolder}/.venv/bin/python",
            "module": "uvicorn",
            "args": [
                "main:app",
                "--host", "127.0.0.1",
                "--port", "9998",
                "--reload",
                "--log-level", "debug"
            ],
            "cwd": "${workspaceFolder}",
            "env": {
                "PYTHONPATH": "${workspaceFolder}",
                "ENVIRONMENT": "testing"
            },
            "console": "integratedTerminal",
            "justMyCode": false,
            "stopOnEntry": false
        },
        {
            "name": "🧪 运行 Pytest 测试",
            "type": "debugpy",
            "request": "launch",
            "python": "${workspaceFolder}/.venv/bin/python",
            "module": "pytest",
            "args": [
                ".",
                "-v",
                "--tb=short",
                "--color=yes",
                "--durations=10"
            ],
            "cwd": "${workspaceFolder}",
            "env": {
                "PYTHONPATH": "${workspaceFolder}",
                "ENVIRONMENT": "testing"
            },
            "console": "integratedTerminal",
            "justMyCode": false,
            "stopOnEntry": false
        },
        {
            "name": "🔍 调试当前 Python 文件",
            "type": "debugpy",
            "request": "launch",
            "python": "${workspaceFolder}/.venv/bin/python",
            "program": "${file}",
            "cwd": "${workspaceFolder}",
            "env": {
                "PYTHONPATH": "${workspaceFolder}",
                "ENVIRONMENT": "development"
            },
            "console": "integratedTerminal",
            "justMyCode": false,
            "stopOnEntry": false
        },
        {
            "name": "🐍 Python 交互式调试",
            "type": "debugpy",
            "request": "launch",
            "python": "${workspaceFolder}/.venv/bin/python",
            "module": "IPython",
            "args": [
                "--no-banner",
                "--no-confirm-exit"
            ],
            "cwd": "${workspaceFolder}",
            "env": {
                "PYTHONPATH": "${workspaceFolder}",
                "ENVIRONMENT": "development"
            },
            "console": "integratedTerminal",
            "justMyCode": false
        },
        {
            "name": "🔍 调试抖音 RPC 客户端",
            "type": "debugpy",
            "request": "launch",
            "python": "${workspaceFolder}/.venv/bin/python",
            "program": "${workspaceFolder}/rpc/douyin/examples/simple_example.py",
            "cwd": "${workspaceFolder}",
            "env": {
                "PYTHONPATH": "${workspaceFolder}",
                "ENVIRONMENT": "development"
            },
            "console": "integratedTerminal",
            "justMyCode": false,
            "stopOnEntry": false
        },
        {
            "name": "🔍 调试趋势洞察 RPC 客户端",
            "type": "debugpy",
            "request": "launch",
            "python": "${workspaceFolder}/.venv/bin/python",
            "program": "${workspaceFolder}/rpc/trendinsight/examples/simple_example.py",
            "cwd": "${workspaceFolder}",
            "env": {
                "PYTHONPATH": "${workspaceFolder}",
                "ENVIRONMENT": "development"
            },
            "console": "integratedTerminal",
            "justMyCode": false,
            "stopOnEntry": false
        }
    ]
}