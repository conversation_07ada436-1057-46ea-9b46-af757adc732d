# 环境变量配置示例
# 复制此文件为 .env 并根据需要修改配置

# 应用环境
APP_ENV=development

# 数据库配置
# 方式1: 只覆盖连接配置（推荐，但 dynaconf 可能不支持）
# APP_TORTOISE_ORM__CONNECTIONS__DEFAULT="mysql://user:pass@host:3306/database"
# APP_TORTOISE_ORM__CONNECTIONS__QIHAOZHUSHOU="sqlite://qihaozhushou_dev.sqlite3"

# 方式2: 完整配置覆盖（当前使用的方式）
# 注意：应用配置部分应与 default.toml 保持一致
APP_TORTOISE_ORM='@json {"connections": {"default": "mysql://qihaozhushou:qihaozhushou2025%40@**************:3306/media_crawler", "qihaozhushou": "sqlite://qihaozhushou_dev.sqlite3"}, "apps": {"models": {"models": ["models.douyin", "models.system", "models.trendinsight", "aerich.models"], "default_connection": "default"}, "qihaozhushou": {"models": ["models.qihaozhushou"], "default_connection": "qihaozhushou"}}}'

# 安全配置
APP_SECRET_KEY="your-secret-key-here-change-in-production"

# 快代理配置
APP_KUAIDAILI_DPS_SECRET_ID="your-secret-id-here"
APP_KUAIDAILI_DPS_SIGNATURE="your-signature-here"
APP_KUAIDAILI_DPS_ORDER_ID="your-order-id-here"

# 日志级别
APP_LOG_LEVEL="DEBUG"

# 调试模式
APP_DEBUG=true

# CORS 配置
APP_CORS_ORIGINS='["http://localhost:3000", "http://localhost:8080"]'

# JWT 配置
APP_JWT_ACCESS_TOKEN_EXPIRE_MINUTES=43200

# 应用信息
APP_APP_TITLE="Vue FastAPI Admin - Development"
APP_PROJECT_NAME="Vue FastAPI Admin - Dev"
