# 日志目录说明

本目录用于存放项目的所有日志文件。

## 目录结构

```
logs/
├── README.md              # 本说明文件
├── .gitkeep              # 保持目录在版本控制中
├── application.log       # 主应用日志
├── test_*.log           # 测试日志
├── api_*.log            # API 日志
├── example_*.log        # 示例脚本日志
└── *.log.*              # 轮转和压缩的日志文件
```

## 日志配置

项目使用统一的日志配置工具 `utils/logging_config.py`，提供以下功能：

### 基本使用

```python
from utils.logging_config import setup_file_logger
from loguru import logger

# 设置基本日志
log_path = setup_file_logger("my_app.log")
logger.info("这条日志会输出到 logs/my_app.log")
```

### 专用日志配置

```python
from utils.logging_config import (
    setup_test_logger,
    setup_module_logger,
    setup_api_logger,
    setup_example_logger
)

# 测试日志
test_log = setup_test_logger("account_manager")
# 输出到: logs/test_account_manager.log

# 模块日志
module_log = setup_module_logger("douyin_client")
# 输出到: logs/douyin_client.log

# API 日志
api_log = setup_api_logger("trendinsight")
# 输出到: logs/api_trendinsight.log

# 示例日志
example_log = setup_example_logger("usage_demo")
# 输出到: logs/example_usage_demo.log
```

## 日志特性

- **自动轮转**: 日志文件达到指定大小时自动轮转
- **自动压缩**: 旧日志文件自动压缩为 .gz 格式
- **自动清理**: 超过保留期的日志文件自动删除
- **异步写入**: 使用异步写入提高性能
- **统一格式**: 所有日志使用统一的格式

## 日志级别

- `DEBUG`: 详细的调试信息
- `INFO`: 一般信息
- `WARNING`: 警告信息
- `ERROR`: 错误信息
- `CRITICAL`: 严重错误

## 配置参数

| 日志类型 | 轮转大小 | 保留时间 | 默认级别 |
|---------|---------|---------|---------|
| 测试日志 | 5 MB    | 7 天     | DEBUG   |
| API 日志 | 20 MB   | 60 天    | INFO    |
| 示例日志 | 5 MB    | 14 天    | INFO    |
| 模块日志 | 10 MB   | 30 天    | INFO    |

## 迁移现有日志

如果项目中有现有的日志文件，可以使用迁移脚本：

```bash
python scripts/migrate_logs.py
```

该脚本会：
1. 查找项目中的所有 .log 文件
2. 将它们移动到 logs 目录
3. 重命名为 migrated_* 格式避免冲突

## 最佳实践

1. **使用专用配置函数**: 根据用途选择合适的日志配置函数
2. **合理设置日志级别**: 生产环境使用 INFO，开发环境使用 DEBUG
3. **避免敏感信息**: 不要在日志中记录密码、密钥等敏感信息
4. **结构化日志**: 使用一致的日志格式便于分析

## 示例

### 测试文件中的日志配置

```python
# tests/test_something.py
import pytest
from utils.logging_config import setup_test_logger
from loguru import logger

def test_something():
    # 设置测试专用日志
    log_path = setup_test_logger("something")
    
    logger.info("开始测试")
    # 测试代码...
    logger.info("测试完成")
```

### API 文件中的日志配置

```python
# api/trendinsight.py
from utils.logging_config import setup_api_logger
from loguru import logger

# 在模块级别设置日志
log_path = setup_api_logger("trendinsight")

async def some_api_function():
    logger.info("API 调用开始")
    # API 代码...
    logger.info("API 调用完成")
```

### 示例脚本中的日志配置

```python
# examples/demo.py
from utils.logging_config import setup_example_logger
from loguru import logger

def main():
    # 设置示例专用日志
    log_path = setup_example_logger("demo")
    
    logger.info("示例开始")
    # 示例代码...
    logger.info("示例完成")

if __name__ == "__main__":
    main()
```
