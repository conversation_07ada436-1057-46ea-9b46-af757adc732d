{"enabled": true, "name": "Auto Translation Generator", "description": "Monitors changes to localization files (JSON, YAML, TOML) containing user-facing text content and automatically generates translations for all configured target languages while maintaining proper context and regional conventions", "version": "1", "when": {"type": "fileEdited", "patterns": ["**/*.json", "**/*.yaml", "**/*.yml", "**/*.toml", "**/locales/**/*", "**/i18n/**/*", "**/translations/**/*", "**/lang/**/*"]}, "then": {"type": "askAgent", "prompt": "A localization file has been modified. Please analyze the changes and:\n\n1. Identify new or modified text content that requires translation\n2. Detect the source language of the content\n3. Generate accurate translations for all configured target languages\n4. Ensure translations maintain proper context and meaning\n5. Follow regional conventions and cultural appropriateness\n6. Preserve any formatting, placeholders, or special characters\n7. Update or create corresponding translation files for each target language\n\nPlease provide the translations in the appropriate file format and structure, maintaining consistency with existing localization patterns in the project."}}