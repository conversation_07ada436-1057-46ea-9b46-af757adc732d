{"enabled": true, "name": "Design Documentation Validator", "description": "Monitors design documentation files and ensures they contain required flowcharts and UML diagrams", "version": "1", "when": {"type": "fileEdited", "patterns": ["docs/**/*.md", "*.md", "**/*design*.md", "**/*arch*.md", "**/*ARCH*.md", "**/*README*.md"]}, "then": {"type": "askAgent", "prompt": "A design document has been modified. Please review the content and ensure it includes:\n\n1. **流程图 (Flowcharts)**: Visual representation of processes, workflows, or system flows\n2. **UML 图 (UML Diagrams)**: Appropriate UML diagrams such as:\n   - Class diagrams for system structure\n   - Sequence diagrams for interactions\n   - Activity diagrams for workflows\n   - Component diagrams for architecture\n\nIf the document is missing these required diagrams, please:\n- Identify what type of diagrams would be most appropriate for the content\n- Suggest specific flowcharts or UML diagrams that should be added\n- Provide guidance on where in the document these diagrams should be placed\n- Recommend tools or formats for creating the diagrams (Mermaid, PlantUML, etc.)\n\nThe requirement is: 出设计的时候必须包含流程图和 UML (Design documents must include flowcharts and UML diagrams)"}}