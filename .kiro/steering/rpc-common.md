---
inclusion: fileMatch
fileMatchPattern: 'rpc/**'
---

# RPC 服务开发指导

## 通用架构

所有 RPC 客户端都应遵循统一的架构模式：

```
rpc/[service]/
├── client.py          # 主客户端类
├── api.py            # API 方法定义
├── config.py         # 配置管理
├── enhancer.py       # 参数增强
├── exceptions.py     # 异常定义
├── schemas/          # 数据模型
├── signer/           # 签名逻辑
├── tests/            # 测试文件
└── examples/         # 使用示例
```

## 开发原则

1. **统一接口**: 所有客户端实现相同的基础接口
2. **异步优先**: 使用 async/await 进行网络操作
3. **错误处理**: 统一的异常类型和错误处理
4. **配置驱动**: 通过配置文件管理 API 端点和参数
5. **可测试性**: 支持 mock 和集成测试

## 代码规范

- 使用 `BaseClient` 作为所有客户端的基类
- 实现 `Transport` 接口进行 HTTP 通信
- 使用 Pydantic 模型进行数据验证
- 遵循项目的命名约定和代码风格

## 配置管理

```python
# config.py 示例
from rpc.common.transport import BaseConfig

class ServiceConfig(BaseConfig):
    base_url: str = "https://api.service.com"
    timeout: int = 30
    max_retries: int = 3
```

## 错误处理

```python
# exceptions.py 示例
from rpc.common.exceptions import RPCException

class ServiceAPIError(RPCException):
    """服务 API 错误"""
    pass

class ServiceAuthError(ServiceAPIError):
    """服务认证错误"""
    pass
```