---
inclusion: fileMatch
fileMatchPattern: 'rpc/douyin/**'
---

# 抖音 RPC 客户端指导

## 架构原则

- 使用异步 HTTP 客户端进行 API 调用
- 实现参数签名和验证机制
- 支持代理和账户轮换
- 统一的错误处理和重试逻辑

## 关键组件

- **Client**: 主要的 API 客户端类
- **Signer**: 参数签名逻辑
- **Enhancer**: 参数增强和验证
- **Schemas**: 请求和响应数据模型

## 开发规范

- 所有 API 方法都应该是异步的
- 使用 Pydantic 模型进行数据验证
- 实现适当的日志记录
- 编写单元测试和集成测试

## 常见模式

```python
# API 客户端方法示例
async def get_user_info(self, user_id: str) -> UserInfoResponse:
    params = await self.enhancer.enhance_params({
        'user_id': user_id
    })
    response = await self.transport.request('GET', '/user/info', params=params)
    return UserInfoResponse.model_validate(response)
```