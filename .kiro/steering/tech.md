# 技术栈

## 核心框架和库

- **FastAPI**: 现代、快速的 API 构建 Web 框架
- **Tortoise ORM**: 用于数据库操作的异步 ORM
- **Pydantic**: 数据验证和序列化 (v2.10.5)
- **Uvicorn**: 运行应用程序的 ASGI 服务器
- **Aerich**: Tortoise ORM 的数据库迁移工具

## 数据库

- **SQLite**: 默认数据库（文件：`db.sqlite3`）
- **异步支持**: 完整的 async/await 数据库操作
- **迁移**: 通过 Aerich 管理

## 开发工具

- **Black**: 代码格式化（行长度：120）
- **Ruff**: 快速 Python 代码检查器
- **isort**: 使用 Black 配置的导入排序
- **pytest**: 支持异步的测试框架
- **pytest-asyncio**: 异步测试支持

## 配置管理

- **Dynaconf**: 动态配置管理
- **基于环境**: 支持开发、预发布、生产环境配置
- **TOML 格式**: 配置文件位于 `settings/` 目录

## 常用命令

### 开发

```bash
# 启动开发服务器
make start
# 或者
python run.py

# 安装依赖
make install

# 代码格式化和检查
make format          # 使用 Black 和 isort 格式化代码
make check          # 检查格式并运行代码检查器
make lint           # 仅运行 Ruff 代码检查器
```

### 数据库管理

```bash
# 快速数据库设置（推荐用于开发）
make db-quick

# 数据库迁移
make migrate        # 生成迁移文件
make upgrade        # 应用迁移

# 数据库工具
make db-init        # 安全初始化（保留数据）
make db-reset       # 重置数据库（破坏性操作）
make db-check       # 检查数据库连接
make clean-db       # 删除数据库和迁移文件
```

### 测试

```bash
make test           # 运行 pytest 测试套件
```

## Python 要求

- **Python**: >= 3.11
- **虚拟环境**: 推荐用于开发
- **UV**: 现代 Python 包管理器（可选但推荐）

## 关键配置文件

- `pyproject.toml`: 项目元数据和依赖
- `settings/config.py`: Dynaconf 配置设置
- `settings/default.toml`: 默认配置值
- `.env`: 环境变量（不在仓库中）
- `Makefile`: 开发命令和自动化