# 项目结构

## 目录组织

```text
├── api/                    # API 层 - 路由定义
│   ├── router.py          # 主 API 路由器
│   └── v1/                # API 版本 1
│       ├── router.py      # V1 路由聚合器
│       ├── users/         # 用户相关端点
│       ├── roles/         # 角色管理端点
│       ├── menus/         # 菜单管理端点
│       ├── depts/         # 部门端点
│       ├── apis/          # API 管理端点
│       ├── douyin/        # 抖音集成端点
│       └── trendinsight/  # 巨量引擎集成端点
│
├── controllers/           # 业务逻辑层
│   ├── user.py           # 用户业务逻辑
│   ├── role.py           # 角色管理逻辑
│   ├── menu.py           # 菜单管理逻辑
│   ├── dept.py           # 部门逻辑
│   ├── api.py            # API 管理逻辑
│   ├── douyin.py         # 抖音服务逻辑
│   └── trendinsight.py   # 巨量引擎服务逻辑
│
├── models/               # 数据模型 (Tortoise ORM)
│   ├── base.py          # 基础模型（含软删除）
│   ├── admin.py         # 管理相关模型
│   ├── enums.py         # 通用枚举
│   ├── douyin/          # 抖音特定模型
│   ├── trendinsight/    # 巨量引擎模型
│   └── system/          # 系统模型
│
├── schemas/             # Pydantic 验证模式
│   ├── base.py         # 基础模式
│   ├── users.py        # 用户模式
│   ├── roles.py        # 角色模式
│   ├── menus.py        # 菜单模式
│   ├── responses.py    # 响应模式
│   └── [service].py    # 服务特定模式
│
├── rpc/                # 外部服务 RPC 客户端
│   ├── common/         # 通用 RPC 工具
│   ├── douyin/         # 抖音 API 客户端
│   ├── trendinsight/   # 巨量引擎 API 客户端
│   ├── kuaidaili/      # 代理服务客户端
│   └── providers/      # 服务提供者
│
├── core/               # 核心应用组件
│   ├── init_app.py     # 应用初始化
│   ├── dependency.py   # FastAPI 依赖
│   ├── exceptions.py   # 异常处理器
│   ├── middlewares.py  # 自定义中间件
│   └── openapi_config.py # OpenAPI 文档配置
│
├── settings/           # 配置管理
│   ├── config.py       # Dynaconf 设置
│   ├── default.toml    # 默认设置
│   ├── development.toml # 开发环境
│   ├── staging.toml    # 预发布环境
│   └── production.toml # 生产环境
│
├── utils/              # 工具函数
│   ├── jwt_utils.py    # JWT 令牌工具
│   ├── password.py     # 密码哈希
│   ├── cache_manager.py # 缓存管理
│   └── douyin/         # 抖音特定工具
│
├── pkg/                # 可重用包
│   ├── crawler_account/ # 爬虫账户管理
│   └── proxy/          # 代理管理工具
│
├── scripts/            # 数据库和设置脚本
│   ├── init_database.py # 数据库初始化
│   ├── quick_init.py   # 快速设置脚本
│   └── check_database.py # 数据库健康检查
│
├── migrations/         # 数据库迁移 (Aerich)
├── logs/              # 应用日志
└── tests/             # 测试文件
```

## 架构模式

### 分层架构

- **API 层**: 路由定义和请求/响应处理
- **控制器层**: 业务逻辑和编排
- **模型层**: 数据模型和数据库操作
- **模式层**: 数据验证和序列化

### 关键约定

1. **命名**: 文件和函数使用 snake_case，类使用 PascalCase
2. **导入**: 优先使用绝对导入，由 Black/isort 组织
3. **异步/等待**: 所有数据库操作都是异步的
4. **软删除**: 使用 BaseModel 实现自动软删除功能
5. **配置**: 环境特定配置在 `settings/` 目录中
6. **错误处理**: 在 `core/exceptions.py` 中集中处理异常

### 模型模式

- 所有模型继承自 `BaseModel`（包含软删除、时间戳）
- 使用 Tortoise ORM 字段类型和关系
- 按领域组织模型（admin、douyin、trendinsight、system）

### API 模式

- 遵循 FastAPI 约定的 RESTful 端点
- 在 `/api/v1/` 下的版本化 API
- 使用 Pydantic 的一致响应模式
- 用于认证和权限的依赖注入

### 服务集成

- 通过 RPC 客户端抽象外部服务
- HTTP 操作的通用传输层
- 可插拔服务实现的提供者模式
- 配置驱动的服务选择