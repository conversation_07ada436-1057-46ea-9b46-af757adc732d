# 实施计划

- [ ] 1. 创建合并后的主控制器文件
  - 创建 `controllers/douyin/controller.py` 文件
  - 将 `controllers/douyin.py` 的完整内容复制到新文件中
  - 将 `controllers/douyin/video_controller.py` 的内容合并到新文件中，避免重复方法
  - 解决导入冲突和重复的方法定义
  - _需求: 1.1, 1.2, 4.1, 4.2_

- [ ] 2. 更新模块初始化文件以保持向后兼容性
  - 修改 `controllers/douyin/__init__.py` 文件
  - 添加从新 `controller.py` 文件的导入
  - 确保所有现有的导入路径（如 `from controllers.douyin import DouyinController`）继续工作
  - 添加适当的 `__all__` 列表以明确导出的接口
  - _需求: 2.2, 3.1, 3.3_

- [ ] 3. 更新主控制器文件的导入引用
  - 修改 `controllers/douyin/main_controller.py` 文件
  - 将导入从 `.video_controller` 更改为 `.controller`
  - 确保 `DouyinController` 类的引用正确指向新的合并文件
  - 验证所有方法调用仍然有效
  - _需求: 1.3, 2.1, 4.3_

- [ ] 4. 更新 API 路由文件中的导入
  - 检查 `api/v1/douyin/router.py` 中的导入语句
  - 验证 `from controllers.douyin import douyin_controller` 仍然有效
  - 如果需要，更新导入路径以确保正确的控制器访问
  - 测试所有 API 端点仍然正常工作
  - _需求: 1.3, 4.4_

- [ ] 5. 更新其他文件中的 douyin 控制器导入
  - 修改 `controllers/trendinsight.py` 中的导入语句
  - 将 `from controllers.douyin import DouyinController` 更新为使用新结构
  - 确保所有对 douyin 控制器方法的调用仍然有效
  - 验证数据转换方法的访问正常
  - _需求: 1.3, 4.3_

- [ ] 6. 更新示例和文档文件
  - 修改 `controllers/douyin/examples/` 目录下的所有示例文件
  - 更新 `controllers/douyin/examples/usage_examples.py` 中的导入语句
  - 更新 `controllers/douyin/examples/quick_test.py` 中的导入和测试代码
  - 修改 `docs/api/douyin_html_extraction.md` 中的导入示例
  - _需求: 1.3, 3.3_

- [ ] 7. 更新测试文件中的导入
  - 修改 `tests/test_html_controller_jingxuan.py` 中的导入（如果需要）
  - 修改 `tests/test_html_controller_integration.py` 中的导入（如果需要）
  - 确保所有测试仍然能够正确导入和使用控制器
  - 运行测试以验证没有导入错误
  - _需求: 1.3, 4.3_

- [ ] 8. 运行完整的测试套件验证功能
  - 执行所有相关的单元测试
  - 验证所有 API 端点的集成测试
  - 确保没有回归错误或功能丢失
  - 测试向后兼容性，确保现有导入路径仍然工作
  - _需求: 4.1, 4.2, 4.3, 4.4_

- [ ] 9. 清理旧文件
  - 删除原始的 `controllers/douyin.py` 文件
  - 删除 `controllers/douyin/video_controller.py` 文件
  - 确认删除后所有导入和功能仍然正常工作
  - 验证没有遗留的引用指向已删除的文件
  - _需求: 1.1, 3.2_

- [ ] 10. 最终验证和文档更新
  - 运行完整的应用程序以确保所有功能正常
  - 验证所有 douyin 相关的 API 端点响应正确
  - 更新任何剩余的文档引用以反映新的文件结构
  - 确认重组完成且没有功能丢失
  - _需求: 1.4, 2.1, 3.2, 4.4_