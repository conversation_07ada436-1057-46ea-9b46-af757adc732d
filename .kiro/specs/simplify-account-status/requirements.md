# 简化账号状态管理需求文档

## 介绍

当前的爬虫账号管理系统使用三种状态：可用(0)、使用中(1)、不可用(-1)。根据业务需求，需要简化状态管理，去掉"使用中"状态，只保留"可用"和"不可用"两种状态。

## 需求

### 需求 1：简化状态枚举

**用户故事：** 作为系统管理员，我希望账号状态更加简单明确，只需要区分账号是否可用，而不需要跟踪使用状态。

#### 验收标准

1. WHEN 系统初始化时 THEN 账号状态只有两种：可用(0)和不可用(-1)
2. WHEN 获取可用账号时 THEN 不需要将状态改为"使用中"
3. WHEN 账号使用完毕时 THEN 不需要执行释放操作

### 需求 2：修改账号获取逻辑

**用户故事：** 作为开发者，我希望获取账号时不需要锁定状态，简化并发处理逻辑。

#### 验收标准

1. WHEN 调用 get_available_account() 时 THEN 直接返回可用账号，不修改状态
2. WHEN 多个进程同时获取账号时 THEN 可以返回相同的账号（允许共享使用）
3. WHEN 账号获取失败时 THEN 只因为没有可用账号，不因为并发冲突

### 需求 3：移除释放账号功能

**用户故事：** 作为开发者，我希望不需要手动释放账号，减少代码复杂度。

#### 验收标准

1. WHEN 账号使用完毕时 THEN 不需要调用 release_account() 方法
2. WHEN 系统运行时 THEN 不存在"使用中"状态的账号
3. WHEN 查询账号统计时 THEN 只显示可用和不可用账号数量

### 需求 4：保留标记不可用功能

**用户故事：** 作为系统管理员，我希望仍然能够将失效的账号标记为不可用。

#### 验收标准

1. WHEN 账号失效时 THEN 可以调用 mark_account_unavailable() 将状态设为不可用(-1)
2. WHEN 账号被标记为不可用时 THEN 设置 invalid_timestamp 时间戳
3. WHEN 查询可用账号时 THEN 排除不可用状态的账号

### 需求 5：更新相关查询逻辑

**用户故事：** 作为系统用户，我希望所有依赖账号状态的功能都能正常工作。

#### 验收标准

1. WHEN 控制器查询账号时 THEN 只查询状态为可用(0)的账号
2. WHEN 获取账号统计时 THEN 统计信息准确反映简化后的状态
3. WHEN 缓存管理器获取账号时 THEN 使用简化后的状态逻辑

### 需求 6：向后兼容性

**用户故事：** 作为系统维护者，我希望现有数据能够平滑迁移到新的状态模式。

#### 验收标准

1. WHEN 系统升级时 THEN 现有"使用中"状态的账号自动转为"可用"状态
2. WHEN 数据迁移完成时 THEN 数据库中不存在状态为1的账号记录
3. WHEN 迁移过程中 THEN 系统功能不受影响

## 影响范围

### 需要修改的文件：
- `pkg/crawler_account/account_manager.py` - 核心账号管理逻辑
- `controllers/trendinsight.py` - TrendInsight 控制器中的账号查询
- `rpc/common/` - RPC 通用模块中的账号获取逻辑
- 相关测试文件

### 需要移除的功能：
- `AccountManager.release_account()` 方法
- 并发重试逻辑（因为不再需要状态锁定）
- "使用中"状态相关的统计和查询

### 需要保留的功能：
- `AccountManager.get_available_account()` 方法（简化实现）
- `AccountManager.mark_account_unavailable()` 方法
- `AccountManager.get_account_status()` 方法
- `AccountManager.get_platform_account_stats()` 方法（更新统计逻辑）