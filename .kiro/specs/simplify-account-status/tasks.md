# 简化账号状态管理实现计划

## 任务列表

- [x] 1. 更新 AccountManager 核心类
  - 移除 STATUS_IN_USE 常量和相关逻辑
  - 简化 get_available_account() 方法实现
  - 移除 release_account() 方法
  - 更新状态映射和统计逻辑
  - _需求: 1.1, 2.1, 3.1_

- [x] 2. 更新异常处理机制
  - 移除 ConcurrencyError 和 AccountStatusError 异常类
  - 简化错误处理逻辑，移除重试机制
  - 更新异常导入和导出
  - _需求: 2.2, 2.3_

- [x] 3. 修改控制器中的账号查询逻辑
  - 更新 TrendInsightController 中的账号获取方法
  - 移除账号释放相关调用
  - 简化 cookies 获取逻辑
  - _需求: 5.1_

- [x] 4. 更新 RPC 通用模块
  - 修改 rpc/common 中的账号提供者逻辑
  - 更新示例代码中的账号使用方式
  - 移除账号释放相关代码
  - _需求: 5.1_

- [x] 5. 创建数据库迁移脚本
  - 编写迁移脚本将"使用中"状态转为"可用"状态
  - 添加状态字段约束检查
  - 验证迁移脚本的正确性
  - _需求: 6.1, 6.2_

- [x] 6. 更新单元测试
  - 修改 test_get_available_account_success() 测试
  - 移除 test_release_account_* 相关测试
  - 移除 test_concurrent_account_access() 测试
  - 更新 test_get_platform_account_stats() 测试
  - _需求: 测试覆盖_

- [x] 7. 添加新的测试用例
  - 创建 test_account_sharing() 测试验证账号共享
  - 创建 test_simplified_workflow() 测试完整流程
  - 添加状态简化相关的边界测试
  - _需求: 测试覆盖_

- [x] 8. 更新使用示例和文档
  - 修改 pkg/crawler_account/example.py 示例代码
  - 更新 README.md 中的使用说明
  - 移除释放账号相关的文档说明
  - _需求: 文档更新_

- [x] 9. 验证系统集成
  - 运行完整的测试套件验证功能正常
  - 测试数据库迁移过程
  - 验证现有业务逻辑不受影响
  - _需求: 6.3_

- [x] 10. 清理和优化
  - 移除未使用的导入和变量
  - 优化代码结构和注释
  - 确保代码风格一致性
  - _需求: 代码质量_