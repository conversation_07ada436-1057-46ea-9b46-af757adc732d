# 简化账号状态管理设计文档

## 概述

本设计文档描述了如何简化爬虫账号管理系统，从三状态模式（可用、使用中、不可用）简化为二状态模式（可用、不可用），以降低系统复杂度并提高可维护性。

## 架构设计

### 状态模型简化

#### 当前状态模型
```python
STATUS_AVAILABLE = 0      # 可用
STATUS_IN_USE = 1        # 使用中 ← 需要移除
STATUS_UNAVAILABLE = -1  # 不可用
```

#### 简化后状态模型
```python
STATUS_AVAILABLE = 0      # 可用
STATUS_UNAVAILABLE = -1  # 不可用
# 移除 STATUS_IN_USE = 1
```

### 核心组件设计

#### 1. AccountManager 类重构

**简化前的工作流程：**
```mermaid
sequenceDiagram
    participant Client
    participant AccountManager
    participant Database
    
    Client->>AccountManager: get_available_account()
    AccountManager->>Database: 查询可用账号
    AccountManager->>Database: 更新状态为使用中
    AccountManager-->>Client: 返回账号
    Note over Client: 使用账号
    Client->>AccountManager: release_account()
    AccountManager->>Database: 更新状态为可用
```

**简化后的工作流程：**
```mermaid
sequenceDiagram
    participant Client
    participant AccountManager
    participant Database
    
    Client->>AccountManager: get_available_account()
    AccountManager->>Database: 查询可用账号
    AccountManager-->>Client: 直接返回账号
    Note over Client: 使用账号（无需释放）
```

#### 2. 方法设计变更

**保留的方法：**
- `get_available_account()` - 简化实现，移除状态更新
- `mark_account_unavailable()` - 保持不变
- `get_account_status()` - 更新状态映射
- `get_platform_account_stats()` - 更新统计逻辑

**移除的方法：**
- `release_account()` - 完全移除
- 并发重试相关逻辑 - 不再需要

### 数据库设计

#### 状态字段约束
```sql
-- 简化后只允许两个值
CHECK (status IN (0, -1))
```

#### 数据迁移策略
```sql
-- 将所有"使用中"状态转为"可用"状态
UPDATE crawler_cookies_account 
SET status = 0 
WHERE status = 1;
```

## 组件接口设计

### AccountManager 类接口

```python
class AccountManager:
    """简化的爬虫账号管理器"""
    
    # 状态常量
    STATUS_AVAILABLE = 0      # 可用
    STATUS_UNAVAILABLE = -1   # 不可用
    
    async def get_available_account(self, platform_name: str) -> CrawlerCookiesAccount:
        """获取可用账号（简化版）
        
        Args:
            platform_name: 平台名称
            
        Returns:
            CrawlerCookiesAccount: 可用账号
            
        Raises:
            NoAvailableAccountError: 无可用账号
        """
        
    async def mark_account_unavailable(self, account_id: int, reason: str = "账号失效") -> None:
        """标记账号为不可用（保持不变）"""
        
    async def get_account_status(self, account_id: int) -> dict:
        """获取账号状态（更新状态映射）"""
        
    async def get_platform_account_stats(self, platform_name: str) -> dict:
        """获取平台统计（简化统计项）"""
        
    # 移除的方法：
    # async def release_account(self, account_id: int) -> None:
```

### 状态查询接口

```python
# 简化的状态映射
status_map = {
    self.STATUS_AVAILABLE: "可用",
    self.STATUS_UNAVAILABLE: "不可用",
    # 移除: self.STATUS_IN_USE: "使用中"
}

# 简化的统计信息
stats = {
    "platform_name": platform_name,
    "total_count": total_count,
    "available_count": available_count,
    "unavailable_count": unavailable_count,
    "availability_rate": available_count / total_count,
    # 移除: "in_use_count": in_use_count
}
```

## 错误处理设计

### 异常类型调整

**保留的异常：**
- `NoAvailableAccountError` - 无可用账号
- `AccountNotFoundError` - 账号不存在
- `DatabaseOperationError` - 数据库操作错误

**移除的异常：**
- `ConcurrencyError` - 不再需要并发控制
- `AccountStatusError` - 不再需要状态验证

### 错误处理逻辑

```python
async def get_available_account(self, platform_name: str) -> CrawlerCookiesAccount:
    """简化的错误处理"""
    try:
        # 直接查询可用账号，无需事务和重试
        account = await CrawlerCookiesAccount.filter(
            platform_name=platform_name, 
            status=self.STATUS_AVAILABLE, 
            is_deleted=False
        ).first()
        
        if not account:
            raise NoAvailableAccountError(platform_name)
            
        return account
        
    except NoAvailableAccountError:
        raise
    except Exception as e:
        raise DatabaseOperationError("get_available_account", e)
```

## 测试策略

### 单元测试更新

**需要更新的测试：**
- `test_get_available_account_success()` - 验证不修改状态
- `test_get_platform_account_stats()` - 验证简化的统计信息
- `test_get_account_status()` - 验证简化的状态映射

**需要移除的测试：**
- `test_release_account_*()` - 所有释放账号相关测试
- `test_concurrent_account_access()` - 并发访问测试
- 状态转换相关测试

**新增测试：**
- `test_account_sharing()` - 验证多个客户端可以获取相同账号
- `test_simplified_workflow()` - 验证简化后的完整工作流程

### 集成测试

```python
async def test_simplified_account_workflow():
    """测试简化后的账号使用流程"""
    manager = AccountManager()
    
    # 1. 获取账号（不修改状态）
    account1 = await manager.get_available_account("douyin")
    account2 = await manager.get_available_account("douyin")
    
    # 2. 验证可以获取相同账号
    assert account1.id == account2.id
    assert account1.status == AccountManager.STATUS_AVAILABLE
    
    # 3. 标记账号不可用
    await manager.mark_account_unavailable(account1.id)
    
    # 4. 验证无法再获取该账号
    with pytest.raises(NoAvailableAccountError):
        await manager.get_available_account("douyin")
```

## 迁移计划

### 阶段 1：代码重构
1. 更新 `AccountManager` 类，移除相关方法和逻辑
2. 更新异常处理和状态常量
3. 修改相关控制器中的账号获取逻辑

### 阶段 2：数据库迁移
1. 创建数据迁移脚本
2. 将现有"使用中"状态转为"可用"状态
3. 添加状态字段约束

### 阶段 3：测试更新
1. 更新现有单元测试
2. 移除不相关的测试用例
3. 添加新的测试场景

### 阶段 4：文档更新
1. 更新 README 和使用示例
2. 更新 API 文档
3. 更新架构图和流程图

## 性能影响

### 预期改进
- **减少数据库操作**：每次获取账号减少一次 UPDATE 操作
- **降低锁竞争**：移除状态锁定，减少并发冲突
- **简化代码路径**：移除重试和事务逻辑，提高执行效率

### 潜在风险
- **账号共享使用**：多个进程可能使用相同账号，需要确保业务逻辑能够处理
- **状态一致性**：需要确保账号失效时能及时标记为不可用

## 向后兼容性

### API 兼容性
- 保持 `get_available_account()` 方法签名不变
- 保持返回数据结构不变
- 移除的 `release_account()` 方法调用将被忽略（可选择抛出警告）

### 数据兼容性
- 现有数据通过迁移脚本自动转换
- 状态值保持数字格式不变
- 时间戳字段保持不变