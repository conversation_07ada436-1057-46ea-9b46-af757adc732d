# 实现计划

- [x] 1. 创建具有核心功能的JingxuanDataExtractor类
  - 在 `utils/douyin/extract/` 目录中实现新的提取器类
  - 添加具有适当头部和超时配置的HTTP客户端设置
  - 实现用于资源管理的上下文管理器方法
  - _需求: 1.1, 1.2, 3.1_

- [ ] 2. 修复HTML解析和pace_f数据提取逻辑
  - 修改现有的pace_f提取逻辑，不再固定查找第6个条目
  - 实现查找所有 `self.__pace_f.push` 条目并匹配包含当前aweme_id的条目
  - 添加aweme_id匹配验证逻辑，确保提取的数据包含正确的视频ID
  - 更新错误处理逻辑，当找不到匹配的条目时提供更准确的错误信息
  - _需求: 1.2, 1.3, 3.2_

- [x] 3. 添加URI解码和JSON解析功能
  - 在Python中实现等效的 `decodeURIComponent` 功能
  - 添加对格式错误数据的适当错误处理的JSON解析
  - 创建验证逻辑以确保解码数据包含所需的视频信息
  - _需求: 1.4, 1.5, 3.3, 3.4_

- [x] 4. 实现到AwemeItem结构的数据转换
  - 创建从精选JSON数据到AwemeItem格式的转换逻辑
  - 确保与现有AwemeItem类型定义的兼容性
  - 为所有必需的视频元数据添加字段映射和验证
  - 包含对可选字段和缺失数据场景的处理
  - _需求: 4.1, 4.2_

- [x] 5. 创建用于一致返回类型的JingxuanProcessResult类
  - 定义具有成功/失败状态和错误信息的结果类
  - 实现以一致格式访问结果数据的方法
  - 添加对字典式访问的向后兼容性支持
  - _需求: 2.3, 3.1_

- [x] 6. 将精选提取集成到DouyinVideoProcessor中
  - 向现有视频处理器添加 `_process_by_jingxuan_url` 方法
  - 实现到提取管道优先级顺序的适当集成
  - 为精选提取数据质量添加验证逻辑
  - 包含标记为"jingxuan_extraction"的来源以便跟踪
  - _需求: 2.1, 2.4, 4.4_

- [x] 7. 更新提取管道流程控制
  - 修改 `process_video_id` 方法以包含精选提取步骤
  - 确保适当的备用序列：数据库 → 移动端URL → 精选 → RPC API
  - 为每个提取方法转换添加错误处理和日志记录
  - _需求: 2.1, 2.2, 3.4_

- [x] 8. 实现全面的错误处理和日志记录
  - 为精选提取失败添加特定错误类型
  - 实现用于调试HTML解析问题的详细日志记录
  - 创建不会破坏提取管道的优雅错误恢复
  - 为不同失败场景添加有意义的错误消息
  - _需求: 3.1, 3.2, 3.3, 3.4, 3.5_

- [x] 9. 添加数据验证和一致性检查
  - 实现验证逻辑以确保精选数据匹配预期格式
  - 在精选和其他提取方法之间添加一致性检查
  - 使用现有验证基础设施创建下载URL验证
  - _需求: 4.1, 4.3, 4.5_

- [x] 10. 为JingxuanDataExtractor创建全面的单元测试
  - 编写使用有效和无效pace_f数据的HTML解析测试
  - 创建使用各种编码字符串格式的URI解码测试
  - 添加使用格式错误和不完整数据的JSON解析测试
  - 实现使用边缘情况的AwemeItem转换测试
  - _需求: 1.1, 1.2, 1.3, 1.4, 1.5_

- [x] 11. 为视频处理器增强编写集成测试
  - 创建精选提取过程的端到端测试
  - 测试从移动端URL到精选提取的备用机制
  - 验证使用适当来源标记的数据库保存
  - 确保所有提取方法的响应格式一致性
  - _需求: 2.1, 2.2, 2.3, 4.4, 4.5_

- [ ] 12. 添加模拟测试和错误场景覆盖
  - 创建具有受控pace_f数据的模拟HTML响应以进行测试
  - 实现网络失败和超时场景的测试
  - 添加各种解码和解析失败模式的测试
  - 测试精选提取失败时的优雅降级
  - _需求: 3.1, 3.2, 3.3, 3.4_