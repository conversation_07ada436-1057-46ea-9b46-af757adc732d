# 实现计划

## 系统架构概览

### 任务系统类图 (UML Class Diagram)

```mermaid
classDiagram
    class TaskManager {
        -tasks: Dict[str, Type[BaseTask]]
        -logger: TaskLogger
        +register_task(task_type: str, task_class: Type[BaseTask])
        +execute_task(config: TaskConfig) TaskResult
        +validate_config(config: dict) TaskConfig
    }
    
    class BaseTask {
        <<abstract>>
        #config: TaskConfig
        #logger: TaskLogger
        +execute()* TaskResult
        +validate_params() bool
    }
    
    class TrendRefreshTask {
        -controller: TrendInsightController
        +execute() TaskResult
        -_query_stale_videos() AsyncIterator[TrendInsightVideo]
        -_refresh_video_trend(video: TrendInsightVideo) bool
        -_batch_process_videos() TaskResult
    }
    
    class TaskConfig {
        +task_type: str
        +batch_size: int
        +timeout: int
        +max_age_hours: int
        +filters: Optional[Dict]
    }
    
    class TaskResult {
        +task_type: str
        +status: str
        +start_time: datetime
        +end_time: datetime
        +duration: float
        +processed_count: int
        +success_count: int
        +failed_count: int
        +errors: List[str]
    }
    
    class TaskLogger {
        -logger: Logger
        +log_task_start(config: TaskConfig)
        +log_progress(processed: int, total: int)
        +log_task_complete(result: TaskResult)
        +log_error(error: str)
    }
    
    TaskManager --> BaseTask : creates
    TaskManager --> TaskLogger : uses
    BaseTask <|-- TrendRefreshTask : inherits
    BaseTask --> TaskConfig : uses
    BaseTask --> TaskResult : returns
    TrendRefreshTask --> TaskLogger : uses
```

### 实现流程图 (Implementation Flow)

```mermaid
flowchart TD
    A[开始实现] --> B[创建基础架构<br/>tasks/ 目录]
    B --> C[实现 TaskManager<br/>任务管理器]
    C --> D[实现 BaseTask<br/>抽象基类]
    D --> E[创建数据模型<br/>TaskConfig/TaskResult]
    E --> F[实现命令行入口<br/>tasks/main.py]
    F --> G[实现日志系统<br/>TaskLogger]
    G --> H[实现趋势刷新任务<br/>TrendRefreshTask]
    H --> I[集成 TrendInsight<br/>控制器接口]
    I --> J[实现错误处理<br/>容错机制]
    J --> K[添加统计输出<br/>JSON 摘要]
    K --> L[添加配置支持<br/>灵活参数]
    L --> M[编写单元测试<br/>功能验证]
    M --> N[集成测试<br/>端到端验证]
    N --> O[性能优化<br/>监控指标]
    O --> P[完善文档<br/>部署配置]
    P --> Q[实现完成]
    
    style A fill:#e1f5fe
    style Q fill:#e8f5e8
    style H fill:#fff3e0
    style I fill:#fff3e0
```

### 任务执行序列图 (Task Execution Sequence)

```mermaid
sequenceDiagram
    participant CLI as Command Line
    participant Main as tasks/main.py
    participant TM as TaskManager
    participant TRT as TrendRefreshTask
    participant TL as TaskLogger
    participant TC as TrendInsightController
    participant DB as Database
    
    CLI->>Main: python tasks/main.py + JSON
    Main->>Main: 解析命令行参数
    Main->>TM: 创建 TaskManager
    TM->>TM: 验证 JSON 配置
    TM->>TRT: 创建 TrendRefreshTask
    TM->>TRT: execute()
    
    TRT->>TL: log_task_start()
    TRT->>DB: 查询过期视频
    
    loop 批处理视频
        TRT->>TC: fetch_video_trend_scores()
        TC-->>TRT: 返回趋势数据
        TRT->>DB: 更新视频记录
        TRT->>TL: log_progress()
    end
    
    TRT->>TL: log_task_complete()
    TRT-->>TM: 返回 TaskResult
    TM-->>Main: 返回执行结果
    Main->>CLI: 输出 JSON 摘要 + 退出码
```

## 实现任务列表

- [x] 1. 创建任务系统基础架构
  - 创建 tasks/ 目录结构和核心模块
  - 实现 TaskManager 任务管理器类
  - 实现 BaseTask 抽象基类
  - 创建任务配置和结果数据模型
  - _需求: 1.1, 1.2, 1.3_

- [x] 2. 实现统一入口点和多环境支持
  - 创建 tasks/main.py 统一入口文件
  - 实现运行时环境检测（Function Compute vs 命令行）
  - 实现 Function Compute handler 函数
  - 实现命令行 main 函数
  - 添加时间触发器事件解析逻辑
  - 实现统一的任务执行接口
  - _需求: 1.1, 1.4, 1.5_

- [x] 3. 实现任务日志记录系统
  - 创建 TaskLogger 日志记录器类
  - 实现结构化日志输出格式
  - 添加进度记录功能（每100条记录）
  - 实现错误日志和堆栈跟踪记录
  - _需求: 4.1, 4.2, 4.3, 4.4, 4.5_

- [x] 4. 实现趋势刷新任务核心逻辑
  - 创建 TrendRefreshTask 任务类
  - 实现流式查询过期视频数据的方法
  - 实现批处理视频处理逻辑
  - 添加任务参数验证功能
  - _需求: 2.1, 2.2, 3.1, 3.5_

- [x] 5. 集成 TrendInsight 控制器接口
  - 实现视频趋势分数获取逻辑
  - 调用 trendinsight_controller.fetch_video_trend_scores 接口
  - 处理 API 调用成功和失败的情况
  - 实现数据库更新操作
  - _需求: 2.3, 2.4, 2.5_

- [x] 6. 实现错误处理和容错机制
  - 添加 API 调用失败的错误处理
  - 实现单个视频处理失败时的跳过逻辑
  - 添加数据库连接错误的重试机制
  - 实现优雅的任务中断处理（SIGTERM）
  - _需求: 2.5, 5.4_

- [x] 7. 实现任务执行结果统计和输出
  - 添加成功/失败计数统计
  - 实现执行时长计算
  - 创建 JSON 格式的执行摘要输出
  - 添加标准输出和标准错误的分离
  - _需求: 2.6, 4.5, 5.5_

- [x] 8. 添加任务配置灵活性支持
  - 实现批处理大小配置
  - 添加超时时间配置支持
  - 实现自定义查询条件过滤
  - 添加时间范围和特定视频ID过滤
  - _需求: 3.2, 3.3, 3.4_

- [x] 9. 创建任务系统单元测试
  - 编写 TaskManager 单元测试
  - 创建 TrendRefreshTask 功能测试
  - 实现 TaskLogger 测试用例
  - 添加错误处理场景测试
  - _需求: 所有需求的测试覆盖_

- [x] 10. 集成测试和端到端验证
  - 创建完整的任务执行集成测试
  - 测试数据库交互和 API 集成
  - 验证流式处理和内存使用
  - 测试各种错误场景和恢复机制
  - _需求: 所有需求的集成验证_

- [x] 11. 添加监控和性能优化
  - 实现任务执行指标收集
  - 添加内存使用监控
  - 优化批处理性能
  - 创建性能基准测试
  - _需求: 4.5, 性能要求_

- [x] 12. 实现 Function Compute 部署支持
  - 创建 s.yaml Function Compute 部署配置文件
  - 实现时间触发器配置和 payload 处理
  - 添加 Function Compute 环境变量配置
  - 创建部署脚本和测试命令
  - 实现 Function Compute 特定的日志和监控
  - _需求: 5.1, 5.2, 5.3_

- [x] 13. 实现作者监控任务核心逻辑
  - 创建 AuthorMonitorTask 任务类
  - 实现查询过期作者数据的方法（updated_at 超过 1 小时）
  - 实现使用 douyin_user_id 查询新视频的逻辑
  - 实现视频数据同步到 trendinsight_video 表
  - 实现视频与作者关联关系记录到 trendinsight_video_related 表
  - 实现更新作者 updated_at 时间戳
  - _需求: 5.1, 5.2, 5.3, 5.4, 5.5_

- [x] 14. 实现关键词监控任务核心逻辑
  - 创建 KeywordMonitorTask 任务类
  - 实现查询过期关键词数据的方法（updated_at 超过 1 小时）
  - 实现调用关键词查询接口检查新视频的逻辑（基础框架，待API实现）
  - 实现视频数据同步到 trendinsight_video 表
  - 实现视频与关键词关联关系记录到 trendinsight_video_related 表
  - 实现更新关键词 updated_at 时间戳
  - _需求: 6.1, 6.2, 6.3, 6.4, 6.5_

- [x] 15. 扩展任务管理器支持新任务类型
  - 在 TaskManager 中注册 AuthorMonitorTask 和 KeywordMonitorTask
  - 更新任务配置模型支持新的任务类型参数
  - 实现任务类型路由逻辑
  - 添加新任务类型的参数验证
  - _需求: 5.1, 6.1_

- [x] 16. 集成抖音视频查询接口
  - 实现基于 douyin_user_id 的视频查询逻辑
  - 实现基于关键词的视频搜索逻辑（框架完成，待具体搜索API实现）
  - 处理 API 调用的错误和限流情况
  - 实现视频数据的去重和过滤
  - _需求: 5.2, 6.2_

- [x] 17. 实现视频关联关系管理
  - 创建视频关联关系的创建和更新逻辑
  - 实现关联关系的去重处理
  - 添加关联关系的批量插入优化
  - 实现关联关系的数据完整性检查
  - _需求: 5.4, 6.4_

- [x] 18. 创建监控任务的单元测试
  - 编写 AuthorMonitorTask 功能测试
  - 创建 KeywordMonitorTask 功能测试
  - 实现视频关联关系测试用例
  - 添加错误处理场景测试
  - _需求: 5.1-5.5, 6.1-6.5 的测试覆盖_

- [x] 19. 集成测试新监控功能
  - 创建作者监控的端到端测试
  - 创建关键词监控的端到端测试
  - 测试视频关联关系的完整性
  - 验证任务调度和执行流程
  - _需求: 所有新需求的集成验证_

- [x] 20. 完善文档和多环境部署配置
  - 创建任务系统使用文档
  - 添加配置参数说明
  - 创建部署示例（Function Compute、Docker、K8s、Cron）
  - 编写多环境故障排查指南
  - 添加 Function Compute 特定的部署和调试文档
  - 更新文档包含新的监控任务说明
  - _需求: 7.1, 7.2, 7.3_