# 需求文档

## 介绍

此功能实现了一个独立的定时任务系统，用于自动刷新 TrendInsight 视频的趋势分数。系统将作为独立的任务服务部署，通过命令行方式调用（如 `python tasks/main.py`），并接收 JSON 参数来指定任务类型和参数。系统将定期查询最近未更新的视频，并使用现有的 TrendInsight 控制器接口刷新其趋势分数。

## 需求

### 需求 1

**用户故事：** 作为系统管理员，我希望有一个独立的任务执行系统，以便我可以通过外部调度器（如 cron）来运行定时任务。

#### 验收标准

1. 当执行 `python tasks/main.py` 时，系统应接受 JSON 格式的任务配置参数
2. 当接收到任务配置时，系统应解析并验证 JSON 参数的有效性
3. 当任务类型被识别时，系统应路由到相应的任务处理器
4. 如果 JSON 参数无效，系统应返回错误码并记录详细错误信息
5. 当任务执行完成时，系统应返回适当的退出码（0 表示成功，非 0 表示失败）

### 需求 2

**用户故事：** 作为数据分析师，我希望过期视频数据的趋势分数能够自动刷新，以便我的分析基于当前信息。

#### 验收标准

1. 当接收到趋势刷新任务参数时，系统应查询 trendinsight_video 表中 updated_at 超过 1 小时的记录
2. 当发现过期记录时，系统应以流式批处理方式处理它们以避免内存问题
3. 当处理每个视频记录时，系统应调用 trendinsight_controller.fetch_video_trend_scores 来刷新趋势数据
4. 如果趋势分数获取成功，系统应使用新的趋势数据和当前时间戳更新视频记录
5. 如果趋势分数获取失败，系统应记录错误并继续处理下一条记录
6. 当所有记录处理完成时，系统应记录成功和失败更新的摘要并退出

### 需求 3

**用户故事：** 作为系统操作员，我希望任务参数可以通过 JSON 配置传递，以便我可以灵活地配置任务行为。

#### 验收标准

1. 当传入 JSON 参数时，系统应支持指定任务类型（如 "trend_refresh"）
2. 当传入任务参数时，系统应支持配置批处理大小、超时时间等选项
3. 当传入过滤条件时，系统应支持自定义查询条件（如时间范围、特定视频 ID）
4. 如果 JSON 参数缺少必需字段，系统应返回错误并提供清晰的错误消息
5. 当任务执行时，系统应根据传入的参数调整执行行为

### 需求 4

**用户故事：** 作为开发人员，我希望有全面的日志记录和监控，以便我可以排查问题并监控系统性能。

#### 验收标准

1. 当任务开始时，系统应记录任务类型、开始时间和传入的参数
2. 当处理视频记录时，系统应定期记录进度（每 100 条记录）
3. 当任务完成时，系统应记录执行持续时间、处理的记录数、成功/失败计数
4. 如果执行期间发生错误，系统应记录详细的错误信息，包括堆栈跟踪
5. 当任务结束时，系统应输出结构化的执行结果到标准输出，便于外部调度器解析

### 需求 5

**用户故事：** 作为数据分析师，我希望系统能够自动监控 trendinsight_author 表中的作者数据，以便及时同步作者的新视频内容。

#### 验收标准

1. 当接收到作者监控任务参数时，系统应查询 trendinsight_author 表中 updated_at 超过 1 小时的记录
2. 当发现需要更新的作者记录时，系统应使用 douyin_user_id 查询是否有新增视频
3. 当发现新视频时，系统应同步视频数据到 trendinsight_video 表
4. 当同步视频成功时，系统应将视频与作者的关联关系记录到 trendinsight_video_related 表
5. 当处理完成时，系统应更新作者记录的 updated_at 时间戳

### 需求 6

**用户故事：** 作为数据分析师，我希望系统能够自动监控 trendinsight_keyword 表中的关键词数据，以便及时发现与关键词相关的新视频。

#### 验收标准

1. 当接收到关键词监控任务参数时，系统应查询 trendinsight_keyword 表中 updated_at 超过 1 小时的记录
2. 当发现需要更新的关键词记录时，系统应调用关键词查询接口检查是否有新视频
3. 当发现新视频时，系统应同步视频数据到 trendinsight_video 表
4. 当同步视频成功时，系统应将视频与关键词的关联关系记录到 trendinsight_video_related 表
5. 当处理完成时，系统应更新关键词记录的 updated_at 时间戳

### 需求 7

**用户故事：** 作为系统管理员，我希望任务系统能够与外部调度器（如 cron）良好集成，以便我可以灵活地安排任务执行。

#### 验收标准

1. 当通过命令行调用时，系统应支持标准的命令行参数格式
2. 当任务执行成功时，系统应返回退出码 0
3. 当任务执行失败时，系统应返回非零退出码并输出错误信息到标准错误
4. 如果任务被中断（如 SIGTERM），系统应优雅地停止处理并保存当前状态
5. 当任务完成时，系统应输出 JSON 格式的执行摘要到标准输出