# 设计文档

## 概述

本设计实现了一个独立的定时任务系统，专门用于刷新 TrendInsight 视频的趋势分数。系统采用命令行方式调用，通过 JSON 参数配置任务类型和执行参数，支持流式批处理以避免内存问题，并提供完整的日志记录和监控功能。

## 架构

### 系统架构图

```mermaid
graph TB
    subgraph "外部调度器 (如 cron)"
        A[定时调度器]
    end
    
    subgraph "任务系统"
        B[tasks/main.py<br/>命令行入口]
        C[TaskManager<br/>任务管理器]
        D[TrendRefreshTask<br/>趋势刷新任务]
        E[TaskLogger<br/>日志记录器]
    end
    
    subgraph "现有系统组件"
        F[TrendInsightController<br/>趋势控制器]
        G[TrendInsightVideo<br/>视频模型]
        H[数据库<br/>SQLite]
    end
    
    A -->|python tasks/main.py + JSON| B
    B --> C
    C --> D
    D --> F
    F --> G
    G --> H
    D --> E
    E -->|日志输出| I[日志文件/标准输出]
    
    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#f3e5f5
    style D fill:#f3e5f5
    style E fill:#f3e5f5
    style F fill:#e8f5e8
    style G fill:#e8f5e8
    style H fill:#e8f5e8
```

### 部署架构

系统支持多种部署模式，包括传统的独立服务部署和现代的 Function Compute 部署：

#### 1. 独立服务部署模式

1. **主服务**: 正常的 FastAPI Web 服务，处理 API 请求
2. **任务服务**: 独立的任务执行环境，通过命令行调用执行定时任务

#### 2. Function Compute 部署模式

基于阿里云 Function Compute 的 Serverless 部署，支持时间触发器自动调度：

```mermaid
graph TB
    subgraph "阿里云 Function Compute"
        subgraph "时间触发器"
            A1[定时触发器<br/>cron: 0 */1 * * *]
        end
        
        subgraph "函数实例"
            B1[main.py 入口函数]
            B2[TaskManager]
            B3[TrendRefreshTask]
        end
        
        subgraph "共享资源"
            C1[数据库连接]
            C2[日志服务]
            C3[监控指标]
        end
    end
    
    subgraph "外部服务"
        D1[TrendInsight API]
        D2[数据库]
    end
    
    A1 -->|触发执行| B1
    B1 --> B2
    B2 --> B3
    B3 --> C1
    C1 --> D2
    B3 --> D1
    B1 --> C2
    B1 --> C3
    
    style A1 fill:#fff3e0
    style B1 fill:#f3e5f5
    style D1 fill:#e8f5e8
    style D2 fill:#e8f5e8
```

#### 3. 混合部署架构

```mermaid
graph LR
    subgraph "生产环境"
        subgraph "主服务部署"
            A1[FastAPI 应用]
            A2[Web API 服务]
        end
        
        subgraph "任务服务部署选项"
            B1[独立进程模式]
            B2[Function Compute 模式]
            B3[K8s CronJob 模式]
        end
        
        C[共享数据库]
        D1[外部调度器<br/>cron/k8s cronjob]
        D2[时间触发器<br/>Function Compute]
    end
    
    A1 --> C
    B1 --> C
    B2 --> C
    B3 --> C
    D1 -->|python tasks/main.py| B1
    D2 -->|handler(event, context)| B2
    
    style A1 fill:#e3f2fd
    style B2 fill:#f3e5f5
    style C fill:#e8f5e8
    style D2 fill:#fff3e0
```

## 入口点设计

### Function Compute 入口点模式

基于阿里云 Function Compute 时间触发器的文档，main.py 需要支持两种调用模式：

#### 1. 命令行模式 (传统部署)

```python
# 命令行调用: python tasks/main.py '{"task_type": "trend_refresh"}'
def main():
    """命令行入口点"""
    import sys
    import json
    
    try:
        # 解析命令行参数
        if len(sys.argv) > 1:
            config_json = sys.argv[1]
        else:
            config_json = sys.stdin.read()
            
        config = json.loads(config_json)
        
        # 执行任务
        result = asyncio.run(execute_task(config))
        
        # 输出结果
        print(json.dumps(result.dict(), ensure_ascii=False))
        sys.exit(0 if result.status == "success" else 1)
        
    except Exception as e:
        print(json.dumps({"error": str(e)}, ensure_ascii=False), file=sys.stderr)
        sys.exit(1)

if __name__ == "__main__":
    main()
```

#### 2. Function Compute 处理器模式

```python
# Function Compute 入口点
def handler(event, context):
    """
    Function Compute 时间触发器处理器
    
    Args:
        event: 触发器事件对象，包含时间触发器信息
        context: 运行时上下文对象
        
    Returns:
        dict: 执行结果
    """
    import json
    import asyncio
    from datetime import datetime
    
    try:
        # 从环境变量或事件中获取任务配置
        config = get_task_config_from_event(event, context)
        
        # 记录触发信息
        logger.info(f"Function triggered at {datetime.now()}")
        logger.info(f"Event: {json.dumps(event)}")
        logger.info(f"Context: request_id={context.request_id}")
        
        # 执行任务
        result = asyncio.run(execute_task(config))
        
        # 返回结果给 Function Compute
        return {
            "statusCode": 200,
            "body": result.dict(),
            "headers": {"Content-Type": "application/json"}
        }
        
    except Exception as e:
        logger.error(f"Function execution failed: {str(e)}")
        return {
            "statusCode": 500,
            "body": {"error": str(e)},
            "headers": {"Content-Type": "application/json"}
        }

def get_task_config_from_event(event, context):
    """从事件和上下文中提取任务配置"""
    import os
    
    # 默认配置
    default_config = {
        "task_type": "trend_refresh",
        "batch_size": int(os.environ.get("BATCH_SIZE", "100")),
        "timeout": int(os.environ.get("TIMEOUT", "3600")),
        "max_age_hours": int(os.environ.get("MAX_AGE_HOURS", "1"))
    }
    
    # 如果事件中包含配置，则合并
    if isinstance(event, dict) and "config" in event:
        default_config.update(event["config"])
    
    return default_config
```

#### 3. 统一入口点架构

```python
# tasks/main.py 统一入口点设计
import os
import sys
import json
import asyncio
from typing import Dict, Any, Optional

async def execute_task(config: Dict[str, Any]) -> TaskResult:
    """统一的任务执行逻辑"""
    # 验证配置
    task_config = TaskConfig(**config)
    
    # 创建任务管理器
    manager = TaskManager()
    manager.register_task("trend_refresh", TrendRefreshTask)
    
    # 执行任务
    return await manager.execute_task(task_config)

def handler(event, context):
    """Function Compute 处理器入口"""
    # Function Compute 模式处理逻辑
    pass

def main():
    """命令行入口"""
    # 命令行模式处理逻辑
    pass

# 自动检测运行环境
if __name__ == "__main__":
    # 检查是否在 Function Compute 环境中
    if os.environ.get("FC_RUNTIME_API"):
        # Function Compute 环境，等待触发器调用
        pass
    else:
        # 命令行环境，直接执行
        main()
```

### 环境检测和适配

```python
class RuntimeEnvironment:
    """运行时环境检测和适配"""
    
    @staticmethod
    def is_function_compute() -> bool:
        """检测是否在 Function Compute 环境中"""
        return bool(os.environ.get("FC_RUNTIME_API"))
    
    @staticmethod
    def is_kubernetes() -> bool:
        """检测是否在 Kubernetes 环境中"""
        return bool(os.environ.get("KUBERNETES_SERVICE_HOST"))
    
    @staticmethod
    def get_runtime_type() -> str:
        """获取运行时类型"""
        if RuntimeEnvironment.is_function_compute():
            return "function_compute"
        elif RuntimeEnvironment.is_kubernetes():
            return "kubernetes"
        else:
            return "standalone"
    
    @staticmethod
    def get_config_source() -> str:
        """获取配置来源"""
        runtime = RuntimeEnvironment.get_runtime_type()
        if runtime == "function_compute":
            return "environment_variables"
        elif runtime == "kubernetes":
            return "config_map"
        else:
            return "command_line"
```

## 组件和接口

### 核心组件

#### 1. TaskManager (任务管理器)

```python
class TaskManager:
    """任务管理器 - 负责任务路由和执行控制"""
    
    def __init__(self):
        self.tasks = {}  # 注册的任务类型
        self.logger = TaskLogger()
    
    def register_task(self, task_type: str, task_class: Type[BaseTask]):
        """注册任务类型"""
        
    async def execute_task(self, config: TaskConfig) -> TaskResult:
        """执行任务"""
        
    def validate_config(self, config: dict) -> TaskConfig:
        """验证任务配置"""
```

#### 2. BaseTask (任务基类)

```python
class BaseTask:
    """任务基类 - 定义任务执行接口"""
    
    def __init__(self, config: TaskConfig, logger: TaskLogger):
        self.config = config
        self.logger = logger
    
    async def execute(self) -> TaskResult:
        """执行任务 - 子类必须实现"""
        raise NotImplementedError
    
    async def validate_params(self) -> bool:
        """验证任务参数"""
        return True
```

#### 3. TrendRefreshTask (趋势刷新任务)

```python
class TrendRefreshTask(BaseTask):
    """趋势刷新任务 - 实现视频趋势分数刷新逻辑"""
    
    async def execute(self) -> TaskResult:
        """执行趋势刷新任务"""
        
    async def _query_stale_videos(self) -> AsyncIterator[TrendInsightVideo]:
        """流式查询过期视频"""
        
    async def _refresh_video_trend(self, video: TrendInsightVideo) -> bool:
        """刷新单个视频的趋势分数"""
```

#### 4. AuthorMonitorTask (作者监控任务)

```python
class AuthorMonitorTask(BaseTask):
    """作者监控任务 - 监控作者数据并同步新视频"""
    
    async def execute(self) -> TaskResult:
        """执行作者监控任务"""
        
    async def _query_stale_authors(self) -> AsyncIterator[TrendInsightAuthor]:
        """流式查询过期作者数据"""
        
    async def _fetch_author_videos(self, author: TrendInsightAuthor) -> List[VideoData]:
        """获取作者的新视频"""
        
    async def _sync_videos_and_relations(self, videos: List[VideoData], author_id: str) -> bool:
        """同步视频数据并创建关联关系"""
```

#### 5. KeywordMonitorTask (关键词监控任务)

```python
class KeywordMonitorTask(BaseTask):
    """关键词监控任务 - 监控关键词并搜索相关新视频"""
    
    async def execute(self) -> TaskResult:
        """执行关键词监控任务"""
        
    async def _query_stale_keywords(self) -> AsyncIterator[TrendInsightKeyword]:
        """流式查询过期关键词数据"""
        
    async def _search_keyword_videos(self, keyword: str) -> List[VideoData]:
        """搜索关键词相关的新视频"""
        
    async def _sync_videos_and_relations(self, videos: List[VideoData], keyword_id: str) -> bool:
        """同步视频数据并创建关联关系"""
```

### 数据模型

#### TaskConfig (任务配置)

```python
class TaskConfig(BaseModel):
    """任务配置模型"""
    task_type: str  # 任务类型，如 "trend_refresh", "author_monitor", "keyword_monitor"
    batch_size: int = 100  # 批处理大小
    timeout: int = 3600  # 超时时间（秒）
    max_age_hours: int = 1  # 数据过期时间（小时）
    filters: Optional[Dict] = None  # 过滤条件
    
    # 作者监控特定配置
    author_video_limit: int = 50  # 每个作者最多获取的视频数量
    
    # 关键词监控特定配置
    keyword_video_limit: int = 100  # 每个关键词最多获取的视频数量
    keyword_search_days: int = 7  # 关键词搜索的天数范围
```

#### TaskResult (任务结果)

```python
class TaskResult(BaseModel):
    """任务执行结果"""
    task_type: str
    status: str  # "success" | "failed" | "partial"
    start_time: datetime
    end_time: datetime
    duration: float  # 执行时长（秒）
    processed_count: int  # 处理的记录数
    success_count: int  # 成功数量
    failed_count: int  # 失败数量
    errors: List[str]  # 错误信息列表
```

### 流程图

#### 主流程

```mermaid
flowchart TD
    A[开始: python tasks/main.py] --> B[解析命令行参数]
    B --> C[验证 JSON 配置]
    C --> D{配置有效?}
    D -->|否| E[输出错误信息<br/>退出码: 1]
    D -->|是| F[初始化 TaskManager]
    F --> G[创建任务实例]
    G --> H[执行任务]
    H --> I[记录执行结果]
    I --> J[输出 JSON 摘要]
    J --> K[退出码: 0]
    
    style A fill:#e1f5fe
    style E fill:#ffebee
    style K fill:#e8f5e8
```

#### 趋势刷新任务流程

```mermaid
flowchart TD
    A[开始趋势刷新任务] --> B[记录任务开始]
    B --> C[查询过期视频<br/>updated_at > 1小时]
    C --> D{有过期视频?}
    D -->|否| E[记录无数据需要处理]
    D -->|是| F[初始化批处理器]
    F --> G[流式处理视频]
    G --> H[调用 TrendInsight API<br/>获取趋势分数]
    H --> I{API 调用成功?}
    I -->|是| J[更新视频记录<br/>trend_score + updated_at]
    I -->|否| K[记录错误<br/>继续下一个]
    J --> L[记录进度<br/>每100条]
    K --> L
    L --> M{还有视频?}
    M -->|是| G
    M -->|否| N[记录任务完成<br/>统计结果]
    E --> N
    N --> O[返回执行结果]
    
    style A fill:#e1f5fe
    style O fill:#e8f5e8
    style K fill:#fff3e0

#### 作者监控任务流程

```mermaid
flowchart TD
    A[开始作者监控任务] --> B[记录任务开始]
    B --> C[查询过期作者<br/>updated_at > 1小时]
    C --> D{有过期作者?}
    D -->|否| E[记录无数据需要处理]
    D -->|是| F[初始化批处理器]
    F --> G[流式处理作者]
    G --> H[使用 douyin_user_id<br/>查询作者新视频]
    H --> I{有新视频?}
    I -->|是| J[同步视频到<br/>trendinsight_video]
    I -->|否| K[更新作者 updated_at]
    J --> L[创建视频关联关系<br/>trendinsight_video_related]
    L --> M[更新作者 updated_at]
    M --> N[记录进度<br/>每100条]
    K --> N
    N --> O{还有作者?}
    O -->|是| G
    O -->|否| P[记录任务完成<br/>统计结果]
    E --> P
    P --> Q[返回执行结果]
    
    style A fill:#e1f5fe
    style Q fill:#e8f5e8

#### 关键词监控任务流程

```mermaid
flowchart TD
    A[开始关键词监控任务] --> B[记录任务开始]
    B --> C[查询过期关键词<br/>updated_at > 1小时]
    C --> D{有过期关键词?}
    D -->|否| E[记录无数据需要处理]
    D -->|是| F[初始化批处理器]
    F --> G[流式处理关键词]
    G --> H[调用关键词搜索接口<br/>查询相关新视频]
    H --> I{有新视频?}
    I -->|是| J[同步视频到<br/>trendinsight_video]
    I -->|否| K[更新关键词 updated_at]
    J --> L[创建视频关联关系<br/>trendinsight_video_related]
    L --> M[更新关键词 updated_at]
    M --> N[记录进度<br/>每100条]
    K --> N
    N --> O{还有关键词?}
    O -->|是| G
    O -->|否| P[记录任务完成<br/>统计结果]
    E --> P
    P --> Q[返回执行结果]
    
    style A fill:#e1f5fe
    style Q fill:#e8f5e8
```

## 数据模型

### UML 类图

```mermaid
classDiagram
    class TaskManager {
        -tasks: Dict[str, Type[BaseTask]]
        -logger: TaskLogger
        +register_task(task_type: str, task_class: Type[BaseTask])
        +execute_task(config: TaskConfig) TaskResult
        +validate_config(config: dict) TaskConfig
    }
    
    class BaseTask {
        <<abstract>>
        #config: TaskConfig
        #logger: TaskLogger
        +execute()* TaskResult
        +validate_params() bool
    }
    
    class TrendRefreshTask {
        -controller: TrendInsightController
        +execute() TaskResult
        -_query_stale_videos() AsyncIterator[TrendInsightVideo]
        -_refresh_video_trend(video: TrendInsightVideo) bool
        -_batch_process_videos() TaskResult
    }
    
    class AuthorMonitorTask {
        -douyin_controller: DouyinController
        +execute() TaskResult
        -_query_stale_authors() AsyncIterator[TrendInsightAuthor]
        -_fetch_author_videos(author: TrendInsightAuthor) List[VideoData]
        -_sync_videos_and_relations(videos: List[VideoData], author_id: str) bool
    }
    
    class KeywordMonitorTask {
        -douyin_controller: DouyinController
        +execute() TaskResult
        -_query_stale_keywords() AsyncIterator[TrendInsightKeyword]
        -_search_keyword_videos(keyword: str) List[VideoData]
        -_sync_videos_and_relations(videos: List[VideoData], keyword_id: str) bool
    }
    
    class TaskConfig {
        +task_type: str
        +batch_size: int
        +timeout: int
        +max_age_hours: int
        +filters: Optional[Dict]
    }
    
    class TaskResult {
        +task_type: str
        +status: str
        +start_time: datetime
        +end_time: datetime
        +duration: float
        +processed_count: int
        +success_count: int
        +failed_count: int
        +errors: List[str]
    }
    
    class TaskLogger {
        -logger: Logger
        +log_task_start(config: TaskConfig)
        +log_progress(processed: int, total: int)
        +log_task_complete(result: TaskResult)
        +log_error(error: str)
    }
    
    class TrendInsightController {
        +fetch_video_trend_scores(video_id: str) VideoIndexResponse
    }
    
    class TrendInsightVideo {
        +id: str
        +trend_score: float
        +updated_at: datetime
    }
    
    TaskManager --> BaseTask : creates
    TaskManager --> TaskLogger : uses
    BaseTask <|-- TrendRefreshTask : inherits
    BaseTask <|-- AuthorMonitorTask : inherits
    BaseTask <|-- KeywordMonitorTask : inherits
    BaseTask --> TaskConfig : uses
    BaseTask --> TaskResult : returns
    TrendRefreshTask --> TrendInsightController : uses
    TrendRefreshTask --> TrendInsightVideo : queries/updates
    TaskManager --> TaskConfig : validates
    TaskManager --> TaskResult : returns
```

### 数据库交互

```mermaid
erDiagram
    TrendInsightVideo {
        string id PK "视频ID (aweme_id)"
        float trend_score "趋势分数"
        float trend_radio "趋势系数"
        datetime created_at "创建时间"
        datetime updated_at "更新时间"
        boolean is_deleted "软删除标记"
    }
    
    TrendInsightAuthor {
        int id PK "主键ID"
        string user_id UK "用户ID"
        string user_name "用户名"
        string douyin_user_id "抖音用户ID"
        datetime created_at "创建时间"
        datetime updated_at "更新时间"
        boolean is_deleted "软删除标记"
    }
    
    TrendInsightKeyword {
        int id PK "主键ID"
        string keyword "关键词"
        string keyword_hash UK "关键词哈希"
        datetime created_at "创建时间"
        datetime updated_at "更新时间"
        boolean is_deleted "软删除标记"
    }
    
    TrendInsightVideoRelated {
        int id PK "主键ID"
        string source_id "来源ID"
        string source_type "来源类型"
        string video_id "视频ID"
        datetime created_at "创建时间"
        datetime updated_at "更新时间"
        boolean is_deleted "软删除标记"
    }
    
    TaskExecution {
        int id PK "执行ID"
        string task_type "任务类型"
        string status "执行状态"
        datetime start_time "开始时间"
        datetime end_time "结束时间"
        int processed_count "处理数量"
        int success_count "成功数量"
        int failed_count "失败数量"
        text errors "错误信息"
        text config "任务配置"
    }
    
    TrendInsightVideo ||--o{ TrendInsightVideoRelated : "related to"
    TrendInsightAuthor ||--o{ TrendInsightVideoRelated : "creates"
    TrendInsightKeyword ||--o{ TrendInsightVideoRelated : "matches"
    TrendInsightVideo ||--o{ TaskExecution : "processed by"
    TrendInsightAuthor ||--o{ TaskExecution : "monitored by"
    TrendInsightKeyword ||--o{ TaskExecution : "monitored by"
```

## 错误处理

### 错误分类和处理策略

1. **配置错误**
   - JSON 格式错误
   - 必需参数缺失
   - 参数类型错误
   - 处理：立即退出，返回错误码 1

2. **数据库连接错误**
   - 数据库不可用
   - 连接超时
   - 处理：重试 3 次，失败后退出

3. **API 调用错误**
   - TrendInsight API 不可用
   - 认证失败
   - 限流
   - 处理：记录错误，跳过当前视频，继续处理下一个

4. **数据处理错误**
   - 数据格式异常
   - 更新失败
   - 处理：记录错误，继续处理

### 错误恢复机制

```mermaid
flowchart TD
    A[遇到错误] --> B{错误类型}
    B -->|配置错误| C[立即退出<br/>错误码: 1]
    B -->|数据库错误| D[重试机制<br/>最多3次]
    B -->|API错误| E[记录错误<br/>跳过当前项]
    B -->|数据错误| F[记录错误<br/>继续处理]
    
    D --> G{重试成功?}
    G -->|是| H[继续执行]
    G -->|否| I[退出<br/>错误码: 2]
    
    E --> H
    F --> H
    H --> J[处理下一项]
    
    style C fill:#ffebee
    style I fill:#ffebee
    style H fill:#e8f5e8
```

## 测试策略

### 单元测试

1. **TaskManager 测试**
   - 任务注册功能
   - 配置验证
   - 任务执行流程

2. **TrendRefreshTask 测试**
   - 视频查询逻辑
   - API 调用处理
   - 批处理功能
   - 错误处理

3. **TaskLogger 测试**
   - 日志格式
   - 进度记录
   - 错误记录

### 集成测试

1. **端到端测试**
   - 完整任务执行流程
   - 数据库交互
   - API 集成

2. **错误场景测试**
   - 网络异常
   - 数据库异常
   - API 异常

### 性能测试

1. **内存使用测试**
   - 流式处理验证
   - 大数据量处理

2. **执行时间测试**
   - 批处理性能
   - 并发处理能力

## 监控和日志

### 日志级别和内容

1. **INFO 级别**
   - 任务开始/结束
   - 处理进度（每100条）
   - 执行摘要

2. **WARNING 级别**
   - API 调用失败
   - 数据异常
   - 重试操作

3. **ERROR 级别**
   - 系统错误
   - 配置错误
   - 致命异常

### 监控指标

1. **执行指标**
   - 任务执行时长
   - 处理记录数量
   - 成功/失败比率

2. **性能指标**
   - 内存使用量
   - API 响应时间
   - 数据库查询时间

3. **错误指标**
   - 错误类型分布
   - 错误频率
   - 重试次数

### 日志输出格式

```json
{
  "timestamp": "2025-01-21T10:00:00Z",
  "level": "INFO",
  "task_type": "trend_refresh",
  "message": "任务执行完成",
  "metrics": {
    "duration": 1800.5,
    "processed": 1000,
    "success": 950,
    "failed": 50
  }
}
```

## 配置管理

### 任务配置示例

#### 趋势刷新任务配置

```json
{
  "task_type": "trend_refresh",
  "batch_size": 100,
  "timeout": 3600,
  "max_age_hours": 1,
  "filters": {
    "video_ids": ["123", "456"],
    "date_range": {
      "start": "2025-01-20",
      "end": "2025-01-21"
    }
  }
}
```

#### 作者监控任务配置

```json
{
  "task_type": "author_monitor",
  "batch_size": 50,
  "timeout": 7200,
  "max_age_hours": 1,
  "author_video_limit": 50,
  "filters": {
    "author_ids": ["author123", "author456"],
    "min_fans_count": 10000
  }
}
```

#### 关键词监控任务配置

```json
{
  "task_type": "keyword_monitor",
  "batch_size": 20,
  "timeout": 7200,
  "max_age_hours": 1,
  "keyword_video_limit": 100,
  "keyword_search_days": 7,
  "filters": {
    "keywords": ["美食", "旅游"],
    "exclude_keywords": ["广告"]
  }
}
```

### 环境配置

任务系统将复用现有的 Dynaconf 配置系统：

- `settings/default.toml`: 默认配置
- `settings/production.toml`: 生产环境配置
- 环境变量覆盖机制

## 部署考虑

### 容器化部署

```dockerfile
# 任务服务 Dockerfile
FROM python:3.11-slim

WORKDIR /app
COPY . .
RUN pip install -r requirements.txt

# 任务执行入口
ENTRYPOINT ["python", "tasks/main.py"]
```

### Kubernetes CronJob

```yaml
apiVersion: batch/v1
kind: CronJob
metadata:
  name: trend-refresh-task
spec:
  schedule: "0 */1 * * *"  # 每小时执行
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: trend-refresh
            image: app:latest
            command: ["python", "tasks/main.py"]
            args: ['{"task_type": "trend_refresh", "batch_size": 100}']
          restartPolicy: OnFailure
```

### Function Compute 部署

#### 函数配置 (s.yaml)

```yaml
edition: 3.0.0
name: trend-refresh-app
access: default

resources:
  trend-refresh-function:
    component: fc3
    props:
      region: cn-hangzhou
      functionName: trend-refresh-task
      description: 定时刷新视频趋势分数任务
      runtime: python3.11
      code: ./
      handler: tasks.main.handler
      memorySize: 512
      timeout: 900
      environmentVariables:
        BATCH_SIZE: "100"
        TIMEOUT: "3600"
        MAX_AGE_HOURS: "1"
        PYTHONPATH: "/code"
      triggers:
        - triggerName: trend-refresh-trigger
          triggerType: timer
          triggerConfig:
            cronExpression: "0 0 */1 * * *"  # 每小时执行
            enable: true
            payload: |
              {
                "task_type": "trend_refresh",
                "batch_size": 100
              }
        - triggerName: author-monitor-trigger
          triggerType: timer
          triggerConfig:
            cronExpression: "0 15 */1 * * *"  # 每小时15分执行
            enable: true
            payload: |
              {
                "task_type": "author_monitor",
                "batch_size": 50,
                "author_video_limit": 50
              }
        - triggerName: keyword-monitor-trigger
          triggerType: timer
          triggerConfig:
            cronExpression: "0 30 */1 * * *"  # 每小时30分执行
            enable: true
            payload: |
              {
                "task_type": "keyword_monitor",
                "batch_size": 20,
                "keyword_video_limit": 100,
                "keyword_search_days": 7
              }
```

#### 函数入口点配置

```python
# tasks/main.py - Function Compute 完整实现
import os
import sys
import json
import asyncio
import logging
from datetime import datetime
from typing import Dict, Any, Optional

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def handler(event, context):
    """
    Function Compute 时间触发器处理器
    
    根据阿里云 Function Compute 文档：
    - event: 时间触发器事件，包含 triggerTime, triggerName, payload 等
    - context: 运行时上下文，包含 request_id, function_name 等
    """
    try:
        # 记录触发信息
        logger.info(f"Function triggered by timer at {datetime.now()}")
        logger.info(f"Event: {json.dumps(event, ensure_ascii=False)}")
        logger.info(f"Context: request_id={context.request_id}, function_name={context.function_name}")
        
        # 从事件中获取任务配置
        config = get_task_config_from_event(event, context)
        logger.info(f"Task config: {json.dumps(config, ensure_ascii=False)}")
        
        # 执行任务
        result = asyncio.run(execute_task(config))
        
        # 记录执行结果
        logger.info(f"Task completed: {result.status}")
        logger.info(f"Processed: {result.processed_count}, Success: {result.success_count}, Failed: {result.failed_count}")
        
        # 返回结果给 Function Compute
        response = {
            "statusCode": 200,
            "body": result.dict(),
            "headers": {"Content-Type": "application/json"}
        }
        
        return response
        
    except Exception as e:
        error_msg = f"Function execution failed: {str(e)}"
        logger.error(error_msg, exc_info=True)
        
        return {
            "statusCode": 500,
            "body": {"error": error_msg, "timestamp": datetime.now().isoformat()},
            "headers": {"Content-Type": "application/json"}
        }

def get_task_config_from_event(event, context):
    """从时间触发器事件中提取任务配置"""
    # 默认配置
    default_config = {
        "task_type": "trend_refresh",
        "batch_size": int(os.environ.get("BATCH_SIZE", "100")),
        "timeout": int(os.environ.get("TIMEOUT", "3600")),
        "max_age_hours": int(os.environ.get("MAX_AGE_HOURS", "1"))
    }
    
    # 从时间触发器的 payload 中获取配置
    if isinstance(event, dict):
        # 时间触发器的 payload 字段
        if "payload" in event:
            try:
                payload = json.loads(event["payload"]) if isinstance(event["payload"], str) else event["payload"]
                if isinstance(payload, dict):
                    default_config.update(payload)
            except (json.JSONDecodeError, TypeError) as e:
                logger.warning(f"Failed to parse payload: {e}")
        
        # 直接从事件中获取配置
        if "config" in event:
            default_config.update(event["config"])
    
    return default_config

async def execute_task(config: Dict[str, Any]) -> 'TaskResult':
    """统一的任务执行逻辑"""
    from .manager import TaskManager
    from .models import TaskConfig
    from .trend_refresh import TrendRefreshTask
    
    # 验证配置
    task_config = TaskConfig(**config)
    
    # 创建任务管理器
    manager = TaskManager()
    manager.register_task("trend_refresh", TrendRefreshTask)
    
    # 执行任务
    return await manager.execute_task(task_config)

def main():
    """命令行入口点"""
    try:
        # 解析命令行参数
        if len(sys.argv) > 1:
            config_json = sys.argv[1]
        else:
            config_json = sys.stdin.read().strip()
            
        if not config_json:
            raise ValueError("No configuration provided")
            
        config = json.loads(config_json)
        
        # 执行任务
        result = asyncio.run(execute_task(config))
        
        # 输出结果
        print(json.dumps(result.dict(), ensure_ascii=False))
        sys.exit(0 if result.status == "success" else 1)
        
    except Exception as e:
        error_result = {
            "error": str(e),
            "timestamp": datetime.now().isoformat(),
            "status": "failed"
        }
        print(json.dumps(error_result, ensure_ascii=False), file=sys.stderr)
        sys.exit(1)

# 运行时环境检测
if __name__ == "__main__":
    # 检查是否在 Function Compute 环境中
    if os.environ.get("FC_RUNTIME_API"):
        # Function Compute 环境，函数会被运行时调用
        logger.info("Running in Function Compute environment")
    else:
        # 命令行环境，直接执行
        main()
```

#### 部署脚本

```bash
#!/bin/bash
# deploy.sh - Function Compute 部署脚本

# 安装 Serverless Devs 工具
npm install -g @serverless-devs/s

# 配置访问凭证
s config add --AccessKeyID your-access-key-id --AccessKeySecret your-access-key-secret

# 部署函数
s deploy

# 查看部署状态
s info

# 测试函数
s invoke --event '{"task_type": "trend_refresh", "batch_size": 50}'
```

### 传统 Cron 部署

```bash
# crontab 配置
0 */1 * * * cd /app && python tasks/main.py '{"task_type": "trend_refresh", "batch_size": 100}' >> /var/log/trend-refresh.log 2>&1
```