# 设计文档

## 概述

本设计文档描述了抖音视频获取器重组的架构设计，包括统一的视频获取控制器和专用的数据映射器系统。设计目标是整合现有的三种获取方法（mobile、jingxuan、RPC），提供一致的接口和清晰的数据转换层。

## 架构

### 整体架构图

```mermaid
graph TB
    subgraph "API Layer"
        A[API Endpoints]
    end
    
    subgraph "Controller Layer"
        B[DouyinController]
        C[DouyinHTMLController]
        D[VideoFetcherController]
    end
    
    subgraph "Mapper Layer"
        E[MobileDataMapper]
        F[JingxuanDataMapper]
        G[RPCDataMapper]
        H[BaseDataMapper]
    end
    
    subgraph "Data Sources"
        I[Mobile Share Pages]
        J[Jingxuan Pages]
        K[RPC API]
    end
    
    subgraph "Database"
        L[DouyinAweme Model]
    end
    
    A --> B
    A --> C
    B --> D
    C --> D
    D --> E
    D --> F
    D --> G
    E --> H
    F --> H
    G --> H
    D --> I
    D --> J
    D --> K
    H --> L
```

### 类图设计

```mermaid
classDiagram
    class VideoFetcherController {
        -mobile_fetcher: MobileFetcher
        -jingxuan_fetcher: JingxuanFetcher
        -rpc_fetcher: RPCFetcher
        -mobile_mapper: MobileDataMapper
        -jingxuan_mapper: JingxuanDataMapper
        -rpc_mapper: RPCDataMapper
        -config: FetcherConfig
        +fetch_video_data(aweme_id, method, options) Dict
        +fetch_with_fallback(aweme_id, methods, options) Dict
        +batch_fetch(aweme_ids, method, options) List[Dict]
        -_fetch_mobile_data(aweme_id, options) Dict
        -_fetch_jingxuan_data(aweme_id, options) Dict
        -_fetch_rpc_data(aweme_id, options) Dict
        -_apply_fallback_strategy(aweme_id, failed_methods, options) Dict
    }
    
    class BaseDataMapper {
        <<abstract>>
        +map_to_standard_format(raw_data) Dict
        +validate_raw_data(raw_data) bool
        +extract_basic_info(raw_data) Dict
        +extract_user_info(raw_data) Dict
        +extract_statistics(raw_data) Dict
        +extract_media_urls(raw_data) Dict
        #_safe_extract(data, path, default) Any
        #_convert_timestamp(timestamp) int
        #_validate_required_fields(data, fields) bool
    }
    
    class MobileDataMapper {
        +map_to_standard_format(raw_data) Dict
        +validate_raw_data(raw_data) bool
        -_extract_mobile_specific_data(raw_data) Dict
        -_process_mobile_video_urls(raw_data) Dict
    }
    
    class JingxuanDataMapper {
        +map_to_standard_format(raw_data) Dict
        +validate_raw_data(raw_data) bool
        -_extract_jingxuan_specific_data(raw_data) Dict
        -_process_jingxuan_video_urls(raw_data) Dict
    }
    
    class RPCDataMapper {
        +map_to_standard_format(raw_data) Dict
        +validate_raw_data(raw_data) bool
        -_extract_rpc_specific_data(raw_data) Dict
        -_process_rpc_video_urls(raw_data) Dict
    }
    
    class FetcherConfig {
        +retry_attempts: int
        +timeout: int
        +fallback_methods: List[str]
        +use_proxy: bool
        +enable_caching: bool
        +cache_ttl: int
    }
    
    class FetchResult {
        +success: bool
        +data: Dict
        +source: str
        +method: str
        +response_time: float
        +error: Optional[str]
        +retry_count: int
    }
    
    VideoFetcherController --> BaseDataMapper
    BaseDataMapper <|-- MobileDataMapper
    BaseDataMapper <|-- JingxuanDataMapper
    BaseDataMapper <|-- RPCDataMapper
    VideoFetcherController --> FetcherConfig
    VideoFetcherController --> FetchResult
```

## 组件和接口

### VideoFetcherController

主要的视频获取控制器，负责协调所有获取方法和数据映射。

**主要方法：**
- `fetch_video_data(aweme_id, method, options)`: 使用指定方法获取视频数据
- `fetch_with_fallback(aweme_id, methods, options)`: 使用回退策略获取数据
- `batch_fetch(aweme_ids, method, options)`: 批量获取视频数据

### 数据映射器接口

所有映射器都继承自 `BaseDataMapper` 抽象基类，确保接口一致性。

**标准输出格式：**
```python
{
    "aweme_id": str,
    "title": str,
    "desc": str,
    "create_time": int,
    "user_info": {
        "user_id": str,
        "nickname": str,
        "avatar": str,
        "sec_uid": str
    },
    "statistics": {
        "liked_count": int,
        "comment_count": int,
        "share_count": int,
        "collected_count": int
    },
    "media_urls": {
        "cover_url": str,
        "video_download_url": str,
        "aweme_url": str
    },
    "metadata": {
        "source": str,
        "fetch_time": int,
        "ip_location": str
    }
}
```

### 获取流程图

```mermaid
flowchart TD
    A[开始获取视频数据] --> B{指定了获取方法?}
    B -->|是| C[使用指定方法]
    B -->|否| D[使用默认方法顺序]
    
    C --> E{方法类型}
    E -->|mobile| F[调用移动端获取]
    E -->|jingxuan| G[调用精选页获取]
    E -->|rpc| H[调用RPC获取]
    
    D --> I[尝试第一个方法]
    I --> J{获取成功?}
    J -->|是| K[使用对应映射器转换数据]
    J -->|否| L{还有其他方法?}
    L -->|是| M[尝试下一个方法]
    L -->|否| N[返回失败结果]
    M --> J
    
    F --> O{获取成功?}
    G --> O
    H --> O
    O -->|是| P[使用对应映射器]
    O -->|否| Q{启用回退?}
    Q -->|是| R[尝试回退方法]
    Q -->|否| S[返回错误]
    
    P --> T[MobileDataMapper]
    P --> U[JingxuanDataMapper]
    P --> V[RPCDataMapper]
    
    T --> W[验证和转换数据]
    U --> W
    V --> W
    K --> W
    
    W --> X{转换成功?}
    X -->|是| Y[保存到数据库]
    X -->|否| Z[返回转换错误]
    
    Y --> AA[返回成功结果]
    R --> I
    
    AA --> BB[结束]
    S --> BB
    N --> BB
    Z --> BB
```

### 错误处理流程

```mermaid
flowchart TD
    A[发生错误] --> B{错误类型}
    B -->|网络错误| C[记录网络错误日志]
    B -->|解析错误| D[记录解析错误日志]
    B -->|映射错误| E[记录映射错误日志]
    B -->|验证错误| F[记录验证错误日志]
    
    C --> G{启用重试?}
    D --> H{启用回退?}
    E --> I[尝试其他映射器]
    F --> J[返回验证错误详情]
    
    G -->|是| K[执行重试逻辑]
    G -->|否| L[返回网络错误]
    H -->|是| M[尝试其他获取方法]
    H -->|否| N[返回解析错误]
    
    K --> O{重试成功?}
    O -->|是| P[继续正常流程]
    O -->|否| Q{达到最大重试次数?}
    Q -->|是| L
    Q -->|否| K
    
    M --> R{回退成功?}
    R -->|是| P
    R -->|否| S{还有其他方法?}
    S -->|是| M
    S -->|否| T[返回所有方法失败]
    
    I --> U{映射成功?}
    U -->|是| P
    U -->|否| V[返回映射失败]
    
    L --> W[构建错误响应]
    N --> W
    J --> W
    T --> W
    V --> W
    W --> X[记录最终错误日志]
    X --> Y[返回错误结果]
    P --> Z[返回成功结果]
```

## 数据模型

### 配置模型

```python
@dataclass
class FetcherConfig:
    """获取器配置"""
    retry_attempts: int = 3
    timeout: int = 30
    fallback_methods: List[str] = field(default_factory=lambda: ["jingxuan", "mobile", "rpc"])
    use_proxy: bool = True
    enable_caching: bool = True
    cache_ttl: int = 3600
    max_concurrent: int = 5
    rate_limit_per_minute: int = 60
```

### 结果模型

```python
@dataclass
class FetchResult:
    """获取结果"""
    success: bool
    data: Optional[Dict] = None
    source: str = ""
    method: str = ""
    response_time: float = 0.0
    error: Optional[str] = None
    retry_count: int = 0
    request_id: str = ""
```

## 错误处理

### 异常层次结构

```mermaid
classDiagram
    class VideoFetcherException {
        <<abstract>>
        +message: str
        +error_code: str
        +context: Dict
    }
    
    class NetworkException {
        +status_code: int
        +url: str
        +timeout: bool
    }
    
    class ParseException {
        +parsing_stage: str
        +content_length: int
        +content_type: str
    }
    
    class MappingException {
        +mapper_type: str
        +failed_field: str
        +raw_data_sample: str
    }
    
    class ValidationException {
        +validation_type: str
        +failed_fields: List[str]
        +expected_format: str
    }
    
    VideoFetcherException <|-- NetworkException
    VideoFetcherException <|-- ParseException
    VideoFetcherException <|-- MappingException
    VideoFetcherException <|-- ValidationException
```

### 错误处理策略

1. **网络错误**: 自动重试，使用指数退避
2. **解析错误**: 尝试其他获取方法
3. **映射错误**: 记录详细错误信息，尝试降级处理
4. **验证错误**: 返回具体的验证失败信息

## 测试策略

### 单元测试

- 每个映射器独立测试
- 模拟不同数据源的响应
- 测试错误处理逻辑
- 测试配置参数的影响

### 集成测试

- 端到端的数据获取流程
- 回退机制的正确性
- 数据库保存的完整性
- 性能基准测试

### 测试数据

为每种数据源准备标准的测试数据集：
- 正常情况的完整数据
- 缺失字段的不完整数据
- 格式异常的错误数据
- 边界情况的特殊数据

## 性能考虑

### 缓存策略

- 内存缓存热点数据
- Redis缓存中等频率数据
- 数据库缓存低频率数据

### 并发控制

- 使用信号量控制并发数
- 实现请求队列避免过载
- 监控资源使用情况

### 监控指标

- 各方法的成功率
- 平均响应时间
- 错误分布统计
- 资源使用情况