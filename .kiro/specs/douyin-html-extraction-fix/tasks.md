# 抖音HTML数据提取修复实现计划

## 实现策略

本修复将利用现有的 `JingxuanDataExtractor` 类（位于 `utils/douyin/extract/jingxuan_data_extractor.py`），该类已经实现了精选页面的数据提取逻辑，包括pace_f条目匹配、aweme_id验证、URI解码等功能。我们需要在HTML控制器中集成这个提取器。

## 任务列表

- [x] 1. 在HTML控制器中添加精选数据提取方法
  - 在 `DouyinHTMLController` 类中添加 `_extract_jingxuan_data_from_html` 方法
  - 实现基本的方法签名和文档字符串
  - 添加必要的导入和类型注解
  - _需求: 1.1, 3.1_

- [x] 2. 集成现有的JingxuanDataExtractor
  - 在HTML控制器中导入 `JingxuanDataExtractor` 类
  - 在 `_extract_jingxuan_data_from_html` 方法中使用JingxuanDataExtractor
  - 处理JingxuanDataExtractor的返回结果并转换为标准格式
  - 添加适当的错误处理和日志记录
  - _需求: 1.2, 1.3, 1.4, 1.5, 4.1_

- [x] 3. 修复JingxuanDataExtractor中的aweme_id匹配逻辑
  - 检查现有的 `extract_pace_f_data` 方法是否正确实现aweme_id匹配
  - 如果需要，修改逻辑从固定查找第6个条目改为查找包含aweme_id的条目
  - 确保URI解码和JSON解析逻辑正确工作
  - 添加更详细的匹配失败日志记录
  - _需求: 1.3, 4.2_

- [x] 4. 验证和完善数据转换逻辑
  - 验证JingxuanDataExtractor返回的数据格式是否符合AwemeItem标准
  - 如果需要，添加额外的数据转换和验证逻辑
  - 确保转换后的数据包含所有必需字段
  - 添加对缺失字段的处理和默认值设置
  - _需求: 1.5, 4.4, 4.5_

- [x] 5. 完善错误处理和日志记录
  - 为JingxuanDataExtractor的各种异常添加适当的处理
  - 实现结构化日志记录，包含请求ID和上下文信息
  - 添加HTML内容诊断功能，帮助调试解析问题
  - 实现优雅的错误恢复，确保不影响其他提取方法
  - _需求: 4.1, 4.2, 4.3, 4.4, 4.5_

- [x] 6. 修改fetch_jingxuan_video_data方法
  - 修改 `fetch_jingxuan_video_data` 方法调用新的 `_extract_jingxuan_data_from_html`
  - 更新错误处理逻辑以适应新的提取方法
  - 确保响应格式与其他方法保持一致
  - 添加source标识以区分数据来源
  - _需求: 3.1, 3.4_

- [x] 7. 添加全面的错误处理和日志记录
  - 为每个步骤添加具体的异常类型和错误消息
  - 实现结构化日志记录，包含请求ID和上下文信息
  - 添加HTML内容诊断功能，帮助调试解析问题
  - 实现优雅的错误恢复，确保不影响其他提取方法
  - _需求: 4.1, 4.2, 4.3, 4.4, 4.5_

- [x] 8. 确保向后兼容性
  - 验证mobile和pc页面的提取逻辑保持不变
  - 确保API接口和响应格式的一致性
  - 运行现有测试确保没有回归问题
  - 验证错误处理方式的一致性
  - _需求: 2.1, 2.2, 2.3, 2.4, 2.5_

- [ ] 9. 创建单元测试
  - 为新的 `_extract_jingxuan_data_from_html` 方法创建单元测试
  - 测试各种HTML格式和pace_f条目结构
  - 测试aweme_id匹配逻辑的各种场景
  - 测试URI解码和JSON解析的边缘情况
  - 测试错误处理和异常情况
  - _需求: 1.1, 1.2, 1.3, 1.4, 1.5_

- [ ] 10. 创建集成测试
  - 测试完整的精选页面数据提取流程
  - 验证与现有mobile和pc提取方法的兼容性
  - 测试数据库保存功能的正确性
  - 验证响应格式的一致性
  - 测试并发请求的处理能力
  - _需求: 3.1, 3.2, 3.3, 3.4, 3.5_

- [ ] 11. 性能优化和配置
  - 预编译正则表达式模式以提高性能
  - 添加可配置的超时和重试设置
  - 实现内存优化的HTML处理
  - 添加性能监控和指标收集
  - _需求: 所有需求的性能方面_

- [ ] 12. 文档和示例
  - 更新API文档说明不同页面类型的处理方式
  - 创建使用示例和最佳实践指南
  - 添加故障排除指南
  - 更新架构文档反映新的设计
  - _需求: 3.4, 4.1, 4.2, 4.3, 4.4, 4.5_