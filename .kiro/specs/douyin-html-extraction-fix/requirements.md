# 抖音HTML数据提取修复需求文档

## 介绍

当前的抖音HTML控制器对所有页面类型（jingxuan、mobile、pc）都使用相同的数据提取逻辑，但实际上不同页面的HTML结构不同，需要不同的提取方法。

### 当前架构问题

```
DouyinHTMLController
├── fetch_jingxuan_video_data() ──┐
├── fetch_mobile_share_video_data() ──┼── _extract_video_data_from_html()
└── fetch_pc_video_data() ──┘         └── DouyinDataExtractor (查找_ROUTER_DATA)
```

**问题**：所有页面类型都使用相同的提取逻辑，但精选页面使用不同的HTML结构。

### 页面结构差异

- **移动端/PC端页面**：包含 `_ROUTER_DATA` 结构，可以直接提取视频信息
- **精选页面**：包含 `self.__pace_f.push` 结构，需要查找包含目标aweme_id的条目并进行URI解码

### 现有资源

项目中已经存在 `JingxuanDataExtractor` 类（位于 `utils/douyin/extract/jingxuan_data_extractor.py`），但当前的HTML控制器没有使用它，而是对所有页面类型都使用标准的 `DouyinDataExtractor`。

## 需求

### 需求1

**用户故事：** 作为开发者，我希望精选页面（jingxuan）能够使用正确的数据提取逻辑，这样系统能够成功从精选页面提取视频数据。

#### 验收标准

1. 当处理精选页面HTML时，系统应使用专门的精选数据提取器
2. 当解析精选页面HTML时，系统应查找所有 `self.__pace_f.push` 条目
3. 当找到pace_f条目时，系统应检查每个条目的内容是否包含当前请求的aweme_id
4. 当找到匹配的条目时，系统应使用 `decodeURIComponent` 对内容进行解码
5. 当解码完成时，系统应将JSON数据转换为标准的AwemeItem格式

### 需求2

**用户故事：** 作为开发者，我希望移动端和PC端页面继续使用现有的_ROUTER_DATA提取逻辑，这样不会破坏现有的功能。

#### 验收标准

1. 当处理移动端分享页面HTML时，系统应继续使用DouyinDataExtractor
2. 当处理PC端视频页面HTML时，系统应继续使用DouyinDataExtractor
3. 当使用DouyinDataExtractor时，系统应查找_ROUTER_DATA结构
4. 当提取成功时，系统应返回与之前相同格式的数据
5. 当现有功能正常工作时，系统不应引入回归问题

### 需求3

**用户故事：** 作为开发者，我希望HTML控制器能够根据页面类型自动选择正确的提取方法，这样调用者无需关心底层实现细节。

#### 验收标准

1. 当调用fetch_jingxuan_video_data时，系统应自动使用精选数据提取器
2. 当调用fetch_mobile_share_video_data时，系统应自动使用标准数据提取器
3. 当调用fetch_pc_video_data时，系统应自动使用标准数据提取器
4. 当提取失败时，系统应提供清晰的错误信息指明使用的提取方法
5. 当提取成功时，系统应返回统一格式的响应数据

### 需求4

**用户故事：** 作为开发者，我希望精选数据提取器具有完善的错误处理和日志记录，这样我可以有效地调试和监控系统。

#### 验收标准

1. 当HTML解析失败时，系统应记录详细的错误信息
2. 当找不到包含aweme_id的pace_f条目时，系统应记录所有找到的条目索引
3. 当URI解码失败时，系统应记录编码数据的预览和错误详情
4. 当JSON解析失败时，系统应记录解码后数据的预览
5. 当数据转换失败时，系统应记录验证错误的具体字段信息