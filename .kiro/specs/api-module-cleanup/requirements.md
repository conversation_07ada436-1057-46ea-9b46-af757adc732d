# 需求文档

## 介绍

此功能涉及清理 API 代码库，删除除 Douyin 和 TrendInsight 功能之外的所有模块。目标是通过删除未使用的管理功能（用户、角色、菜单、部门、API）及其相关的模型和控制器来简化应用程序，同时保留核心的社交媒体数据分析功能。

## 需求

### 需求 1

**用户故事：** 作为开发者，我希望删除未使用的管理模块，以便代码库得到简化并专注于社交媒体数据分析功能。

#### 验收标准

1. 当删除 API 模块时，系统应保留 api/v1/douyin 和 api/v1/trendinsight 模块
2. 当删除 API 模块时，系统应删除 api/v1/users、api/v1/roles、api/v1/menus、api/v1/depts 和 api/v1/apis 模块
3. 当删除 API 模块时，系统应删除 api/v1/base 模块
4. 当删除 API 模块时，系统应更新 api/v1/router.py 以仅包含 douyin 和 trendinsight 路由

### 需求 2

**用户故事：** 作为开发者，我希望删除未使用的控制器模块，以便业务逻辑得到简化并仅包含社交媒体分析功能。

#### 验收标准

1. 当删除控制器时，系统应保留 controllers/douyin.py 和 controllers/trendinsight.py
2. 当删除控制器时，系统应删除 controllers/user.py、controllers/role.py、controllers/menu.py、controllers/dept.py 和 controllers/api.py

### 需求 3

**用户故事：** 作为开发者，我希望删除未使用的数据模型，以便数据库架构仅包含与社交媒体数据分析相关的模型。

#### 验收标准

1. 当删除模型时，系统应保留 models/douyin/ 和 models/trendinsight/ 目录
2. 当删除模型时，系统应保留 models/system/ 目录（用于爬虫功能）
3. 当删除模型时，系统应删除 models/admin.py
4. 当删除模型时，系统应更新 models/__init__.py 以仅导入保留的模型
5. 当删除模型时，系统应保留 models/base.py 和 models/enums.py，因为它们可能被保留的模型使用

### 需求 4

**用户故事：** 作为开发者，我希望删除未使用的模式定义，以便数据验证仅涵盖社交媒体分析数据结构。

#### 验收标准

1. 当删除模式时，系统应保留 schemas/douyin.py 和 schemas/trendinsight.py
2. 当删除模式时，系统应删除 schemas/users.py、schemas/roles.py、schemas/menus.py、schemas/depts.py 和 schemas/apis.py
3. 当删除模式时，系统应保留 schemas/base.py 和 schemas/responses.py，因为它们可能被保留的模式使用
4. 当删除模式时，系统应删除 schemas/login.py

### 需求 5

**用户故事：** 作为开发者，我希望更新应用程序初始化，以便应用程序仅为保留的模块注册路由和依赖项。

#### 验收标准

1. 当更新应用程序初始化时，系统应更新 core/init_app.py 以仅包含 douyin 和 trendinsight 路由
2. 当更新依赖项时，系统应审查 core/dependency.py 并删除未被保留模块使用的身份验证/授权依赖项
3. 当更新中间件时，系统应审查 core/middlewares.py 并仅保留保留功能所需的中间件

### 需求 6

**用户故事：** 作为开发者，我希望清理数据库迁移，以便数据库架构仅反映保留的功能。

#### 验收标准

1. 当清理迁移时，系统应审查现有迁移文件并识别哪些与删除的模块相关
2. 当清理迁移时，系统应创建新的迁移以删除与删除模块相关的表
3. 当清理迁移时，系统应确保保留的表（douyin、trendinsight、system）保持完整

### 需求 7

**用户故事：** 作为开发者，我希望更新配置和脚本，以便它们仅引用保留的功能。

#### 验收标准

1. 当更新脚本时，系统应审查 scripts/ 目录并删除或更新引用删除模块的脚本
2. 当更新配置时，系统应审查设置文件并删除与删除模块相关的配置
3. 当更新测试时，系统应删除与删除模块相关的测试文件