# 设计文档

## 概述

此设计文档描述了如何清理 API 代码库，删除除 Douyin 和 TrendInsight 功能之外的所有管理模块。清理过程将涉及删除文件、更新导入、修改路由配置以及创建数据库迁移来删除相关表。

## 架构

### 当前架构分析

当前应用程序包含以下主要模块：
- **管理模块**：用户、角色、菜单、部门、API 管理
- **社交媒体分析模块**：Douyin、TrendInsight
- **系统模块**：爬虫账户管理

### 目标架构

清理后的架构将仅保留：
- **社交媒体分析模块**：Douyin、TrendInsight
- **系统模块**：爬虫相关功能
- **基础组件**：基础模型、响应模式、核心中间件

## 组件和接口

### 保留的组件

#### API 层
- `api/v1/douyin/` - Douyin API 端点
- `api/v1/trendinsight/` - TrendInsight API 端点

#### 控制器层
- `controllers/douyin/` - Douyin 业务逻辑（目录结构）
- `controllers/douyin.py` - Douyin 控制器（如果存在）
- `controllers/trendinsight.py` - TrendInsight 控制器

#### 模型层
- `models/douyin/` - Douyin 数据模型
- `models/trendinsight/` - TrendInsight 数据模型
- `models/system/` - 系统模型（爬虫相关）
- `models/base.py` - 基础模型类
- `models/enums.py` - 通用枚举

#### 模式层
- `schemas/douyin.py` - Douyin 数据验证模式
- `schemas/trendinsight.py` - TrendInsight 数据验证模式
- `schemas/base.py` - 基础模式
- `schemas/responses.py` - 响应模式

### 删除的组件

#### API 层
- `api/v1/users/` - 用户管理端点
- `api/v1/roles/` - 角色管理端点
- `api/v1/menus/` - 菜单管理端点
- `api/v1/depts/` - 部门管理端点
- `api/v1/apis/` - API 管理端点
- `api/v1/base/` - 基础端点

#### 控制器层
- `controllers/user.py` - 用户控制器
- `controllers/role.py` - 角色控制器
- `controllers/menu.py` - 菜单控制器
- `controllers/dept.py` - 部门控制器
- `controllers/api.py` - API 控制器

#### 模型层
- `models/admin.py` - 管理相关模型（User、Role、Menu、Dept、Api 等）

#### 模式层
- `schemas/users.py` - 用户模式
- `schemas/roles.py` - 角色模式
- `schemas/menus.py` - 菜单模式
- `schemas/depts.py` - 部门模式
- `schemas/apis.py` - API 模式
- `schemas/login.py` - 登录模式

## 数据模型

### 保留的数据表
- Douyin 相关表（来自 `models/douyin/models.py`）
- TrendInsight 相关表（来自 `models/trendinsight/models.py`）
- 系统相关表（来自 `models/system/crawler.py`）

### 删除的数据表
- `user` - 用户表
- `role` - 角色表
- `menu` - 菜单表
- `dept` - 部门表
- `api` - API 表
- `deptclosure` - 部门层级关系表
- `auditlog` - 审计日志表
- 相关的多对多关系表

## 错误处理

### 依赖项清理
由于删除了用户认证和权限管理模块，需要处理以下情况：

1. **认证依赖项**：`core/dependency.py` 中的认证相关代码可能不再需要
2. **权限检查**：基于角色的权限检查将被移除
3. **JWT 认证**：如果 Douyin 和 TrendInsight 模块不需要认证，可以移除 JWT 相关代码

### 路由更新
- 更新 `api/v1/router.py` 以仅包含 douyin 和 trendinsight 路由
- 移除权限依赖项的使用
- 更新 `core/init_app.py` 中的路由注册

### 应用初始化清理
- 移除 `core/init_app.py` 中的用户、角色、菜单、API 初始化代码
- 保留数据库初始化的核心逻辑
- 移除不再需要的导入

## 测试策略

### 单元测试清理
- 删除与移除模块相关的测试文件
- 保留 Douyin 和 TrendInsight 相关的测试
- 更新集成测试以反映新的架构

### 验证策略
1. **功能验证**：确保 Douyin 和 TrendInsight API 仍然正常工作
2. **数据库验证**：确认数据库迁移正确删除了相关表
3. **依赖验证**：确认没有遗留的导入或依赖项引用已删除的模块

## 实施步骤

### 阶段 1：文件删除
1. 删除 API 模块文件
2. 删除控制器文件
3. 删除模式文件
4. 删除相关测试文件

### 阶段 2：配置更新
1. 更新路由配置
2. 更新模型导入
3. 更新应用初始化
4. 清理依赖项

### 阶段 3：数据库迁移
1. 创建迁移脚本删除相关表
2. 测试迁移脚本
3. 应用迁移

### 阶段 4：验证和清理
1. 验证应用启动
2. 测试保留的 API 功能
3. 清理任何遗留的引用

## 风险和注意事项

### 数据安全
- 数据库迁移将永久删除用户、角色等数据
- 建议在执行前备份数据库

### 功能影响
- 移除认证和权限管理后，Douyin 和 TrendInsight API 将不再有访问控制
- 如果需要访问控制，需要在应用层面实现新的认证机制

### 依赖关系
- 需要仔细检查 Douyin 和 TrendInsight 模块是否依赖于被删除的组件
- 特别注意共享的工具函数和基础类