# 实施计划

- [ ] 1. 删除 API 模块文件和目录
  - 删除 api/v1/users/ 目录及其所有文件
  - 删除 api/v1/roles/ 目录及其所有文件
  - 删除 api/v1/menus/ 目录及其所有文件
  - 删除 api/v1/depts/ 目录及其所有文件
  - 删除 api/v1/apis/ 目录及其所有文件
  - 删除 api/v1/base/ 目录及其所有文件
  - _需求: 1.1, 1.2, 1.3_

- [ ] 2. 删除控制器文件
  - 删除 controllers/user.py 文件
  - 删除 controllers/role.py 文件
  - 删除 controllers/menu.py 文件
  - 删除 controllers/dept.py 文件
  - 删除 controllers/api.py 文件
  - _需求: 2.1, 2.2_

- [ ] 3. 删除模型文件
  - 删除 models/admin.py 文件
  - _需求: 3.1, 3.3_

- [ ] 4. 删除模式文件
  - 删除 schemas/users.py 文件
  - 删除 schemas/roles.py 文件
  - 删除 schemas/menus.py 文件
  - 删除 schemas/depts.py 文件
  - 删除 schemas/apis.py 文件
  - 删除 schemas/login.py 文件
  - _需求: 4.1, 4.2, 4.4_

- [ ] 5. 更新路由配置
  - 修改 api/v1/router.py 文件，移除已删除模块的导入和路由注册
  - 仅保留 douyin_router 和 trendinsight_router 的注册
  - 移除 DependPermissionBearer 依赖项的使用
  - _需求: 1.4, 5.1_

- [ ] 6. 更新模型导入配置
  - 修改 models/__init__.py 文件，移除 admin 模块的导入
  - 仅保留 douyin、system、trendinsight 模块的导入
  - _需求: 3.4_

- [ ] 7. 更新应用初始化代码
  - 修改 core/init_app.py 文件，移除与已删除模块相关的导入
  - 删除 init_superuser、init_menus、init_apis、init_roles 函数
  - 更新 init_data 函数，移除对已删除初始化函数的调用
  - 移除对 models.admin 中类的引用
  - _需求: 5.1_

- [ ] 8. 清理依赖项配置
  - 检查 core/dependency.py 文件中的认证和权限相关代码
  - 如果 douyin 和 trendinsight 模块不需要认证，移除相关的认证依赖项
  - 移除对 User 和 Role 模型的导入和引用
  - _需求: 5.2_

- [ ] 9. 创建数据库迁移脚本
  - 创建新的迁移文件来删除相关数据表
  - 包含删除 user、role、menu、dept、api、deptclosure、auditlog 表的 SQL
  - 包含删除相关多对多关系表的 SQL
  - 确保迁移脚本是可逆的（包含回滚逻辑）
  - _需求: 6.1, 6.2, 6.3_

- [ ] 10. 删除相关测试文件
  - 删除与已移除模块相关的测试文件
  - 保留 douyin 和 trendinsight 相关的测试文件
  - _需求: 7.3_

- [ ] 11. 更新脚本文件
  - 检查 scripts/ 目录中的脚本文件
  - 移除或更新引用已删除模块的脚本
  - 确保数据库初始化脚本不再创建已删除的表
  - _需求: 7.1_

- [ ] 12. 验证应用启动和功能
  - 编写测试代码验证应用能够正常启动
  - 编写测试代码验证 douyin API 端点仍然正常工作
  - 编写测试代码验证 trendinsight API 端点仍然正常工作
  - 编写测试代码验证数据库连接和基础功能正常
  - _需求: 所有需求的验证_