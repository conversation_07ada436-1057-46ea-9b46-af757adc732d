"""
视频数据处理器 - 处理视频数据的获取和格式化
"""

from datetime import datetime
from typing import Dict, List, Optional, Union

from services.base import BaseService, ValidationError
from models.douyin import DouyinAweme


class VideoDataProcessor(BaseService):
    """视频数据处理器"""

    def __init__(self, logger=None):
        super().__init__(logger)

    def validate_video_data(self, video_data: Union[DouyinAweme, Dict]) -> bool:
        """
        验证视频数据格式

        Args:
            video_data: DouyinAweme 对象或视频数据字典

        Returns:
            bool: 是否有效
        """
        if isinstance(video_data, DouyinAweme):
            return bool(video_data.aweme_id)

        # 兼容字典格式
        required_fields = ["aweme_id"]
        for field in required_fields:
            if not video_data.get(field):
                return False

        return True

    def format_video_list(self, raw_video_list: List[Union[DouyinAweme, Dict]]) -> List[Dict[str, str]]:
        """
        格式化视频列表数据

        Args:
            raw_video_list: DouyinAweme 对象列表或原始视频数据字典列表

        Returns:
            List[Dict[str, str]]: 格式化后的视频列表
        """
        if not raw_video_list:
            return []

        formatted_list = []

        for video_data in raw_video_list:
            if not self.validate_video_data(video_data):
                self.logger.warning(f"跳过无效视频数据: {video_data}")
                continue

            # 处理 DouyinAweme 对象
            if isinstance(video_data, DouyinAweme):
                formatted_video = {
                    "aweme_id": str(video_data.aweme_id),
                    "publish_time": self._format_publish_time(video_data.create_time)
                }
            else:
                # 兼容字典格式
                formatted_video = {
                    "aweme_id": str(video_data.get("aweme_id", "")),
                    "publish_time": self._format_publish_time(video_data.get("publish_time"))
                }

            formatted_list.append(formatted_video)

        self.service_logger.log_business_event(
            "视频列表格式化完成",
            {
                "original_count": len(raw_video_list),
                "formatted_count": len(formatted_list),
                "filtered_count": len(raw_video_list) - len(formatted_list)
            }
        )

        return formatted_list

    def _format_publish_time(self, publish_time) -> str:
        """
        格式化发布时间
        
        Args:
            publish_time: 发布时间（可能是时间戳、字符串或datetime对象）
            
        Returns:
            str: 格式化后的时间字符串
        """
        if not publish_time:
            return ""

        try:
            # 如果是时间戳（整数）
            if isinstance(publish_time, (int, float)):
                # 处理秒级和毫秒级时间戳
                if publish_time > 1e10:  # 毫秒级时间戳
                    publish_time = publish_time / 1000
                dt = datetime.fromtimestamp(publish_time)
                return dt.strftime("%Y-%m-%d %H:%M:%S")
            
            # 如果是字符串
            elif isinstance(publish_time, str):
                # 尝试解析常见的时间格式
                try:
                    # ISO格式
                    dt = datetime.fromisoformat(publish_time.replace('Z', '+00:00'))
                    return dt.strftime("%Y-%m-%d %H:%M:%S")
                except ValueError:
                    # 如果是时间戳字符串
                    try:
                        timestamp = float(publish_time)
                        if timestamp > 1e10:  # 毫秒级
                            timestamp = timestamp / 1000
                        dt = datetime.fromtimestamp(timestamp)
                        return dt.strftime("%Y-%m-%d %H:%M:%S")
                    except ValueError:
                        # 直接返回原字符串
                        return str(publish_time)
            
            # 如果是datetime对象
            elif isinstance(publish_time, datetime):
                return publish_time.strftime("%Y-%m-%d %H:%M:%S")
            
            # 其他类型转为字符串
            else:
                return str(publish_time)
                
        except Exception as e:
            self.logger.warning(f"格式化发布时间失败: {publish_time}, 错误: {e}")
            return str(publish_time) if publish_time else ""

    def extract_video_ids(self, video_list: List[Dict]) -> List[str]:
        """
        从视频列表中提取视频ID
        
        Args:
            video_list: 视频列表
            
        Returns:
            List[str]: 视频ID列表
        """
        video_ids = []
        
        for video in video_list:
            aweme_id = video.get("aweme_id")
            if aweme_id:
                video_ids.append(str(aweme_id))
        
        return video_ids

    def filter_videos_by_time_range(
        self,
        video_list: List[Union[DouyinAweme, Dict]],
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None
    ) -> List[Union[DouyinAweme, Dict]]:
        """
        根据时间范围过滤视频
        
        Args:
            video_list: 视频列表
            start_time: 开始时间
            end_time: 结束时间
            
        Returns:
            List[Dict]: 过滤后的视频列表
        """
        if not start_time and not end_time:
            return video_list

        filtered_list = []
        
        for video in video_list:
            publish_time_str = video.get("publish_time", "")
            if not publish_time_str:
                continue
                
            try:
                # 尝试解析发布时间
                publish_time = datetime.strptime(publish_time_str, "%Y-%m-%d %H:%M:%S")
                
                # 检查时间范围
                if start_time and publish_time < start_time:
                    continue
                if end_time and publish_time > end_time:
                    continue
                    
                filtered_list.append(video)
                
            except ValueError:
                # 时间格式解析失败，跳过该视频
                self.logger.warning(f"无法解析视频发布时间: {publish_time_str}")
                continue

        self.service_logger.log_business_event(
            "视频时间过滤完成",
            {
                "original_count": len(video_list),
                "filtered_count": len(filtered_list),
                "start_time": start_time.isoformat() if start_time else None,
                "end_time": end_time.isoformat() if end_time else None
            }
        )

        return filtered_list

    def deduplicate_videos(self, video_list: List[Union[DouyinAweme, Dict]]) -> List[Union[DouyinAweme, Dict]]:
        """
        去重视频列表（基于aweme_id）

        Args:
            video_list: DouyinAweme 对象列表或视频字典列表

        Returns:
            List[Union[DouyinAweme, Dict]]: 去重后的视频列表
        """
        seen_ids = set()
        deduplicated_list = []

        for video in video_list:
            if isinstance(video, DouyinAweme):
                aweme_id = video.aweme_id
            else:
                aweme_id = video.get("aweme_id")

            if aweme_id and aweme_id not in seen_ids:
                seen_ids.add(aweme_id)
                deduplicated_list.append(video)

        self.service_logger.log_business_event(
            "视频去重完成",
            {
                "original_count": len(video_list),
                "deduplicated_count": len(deduplicated_list),
                "duplicates_removed": len(video_list) - len(deduplicated_list)
            }
        )

        return deduplicated_list

    def batch_process_videos(
        self,
        video_list: List[Union[DouyinAweme, Dict]],
        batch_size: int = 100
    ) -> List[List[Union[DouyinAweme, Dict]]]:
        """
        将视频列表分批处理
        
        Args:
            video_list: 视频列表
            batch_size: 批次大小
            
        Returns:
            List[List[Dict]]: 分批后的视频列表
        """
        if not video_list:
            return []

        batches = []
        for i in range(0, len(video_list), batch_size):
            batch = video_list[i:i + batch_size]
            batches.append(batch)

        self.service_logger.log_business_event(
            "视频分批处理完成",
            {
                "total_videos": len(video_list),
                "batch_size": batch_size,
                "batch_count": len(batches)
            }
        )

        return batches

    def validate_source_data(self, source_id: str, source_type: str) -> bool:
        """
        验证来源数据
        
        Args:
            source_id: 来源ID
            source_type: 来源类型
            
        Returns:
            bool: 是否有效
        """
        if not source_id or not source_type:
            return False
            
        # 导入枚举类型进行验证
        from models.qihaozhushou import SourceType
        valid_types = [SourceType.AUTHOR.value, SourceType.KEYWORD.value, SourceType.COLLECT.value]
        if source_type not in valid_types:
            return False
            
        return True

    def create_processing_summary(
        self,
        original_count: int,
        processed_count: int,
        error_count: int = 0
    ) -> Dict[str, int]:
        """
        创建处理摘要
        
        Args:
            original_count: 原始数量
            processed_count: 处理数量
            error_count: 错误数量
            
        Returns:
            Dict[str, int]: 处理摘要
        """
        return {
            "original_count": original_count,
            "processed_count": processed_count,
            "error_count": error_count,
            "success_rate": round((processed_count / original_count * 100), 2) if original_count > 0 else 0
        }
