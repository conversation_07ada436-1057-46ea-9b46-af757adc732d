"""
收件箱服务使用示例
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List

from services.inbox import InboxService, InboxRelationService, VideoDataProcessor
from models.qihaozhushou import UserInboxSourceRelated, SourceType


# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def example_basic_usage():
    """基础使用示例"""
    print("=== 基础使用示例 ===")
    
    # 初始化服务
    inbox_service = InboxService(logger)
    
    # 模拟用户和来源数据
    user_uuid = "18e0bd37d19142fe9623707a1e6b28fe"
    source_id = "eaeiigcdgaijhbah"
    source_type = "author"
    
    # 模拟 DouyinAweme 对象列表
    from models.douyin import DouyinAweme

    raw_video_list = [
        DouyinAweme(
            aweme_id="7123456789012345678",
            create_time=datetime.fromtimestamp(1640995200),
            title="测试视频1"
        ),
        DouyinAweme(
            aweme_id="7123456789012345679",
            create_time=datetime.strptime("2022-01-01 12:00:00", "%Y-%m-%d %H:%M:%S"),
            title="测试视频2"
        ),
        DouyinAweme(
            aweme_id="7123456789012345680",
            create_time=datetime.now(),
            title="测试视频3"
        )
    ]
    
    try:
        # 方法1: 使用新的对象方式处理来源视频
        user_inbox_source_related = await inbox_service.get_or_create_source_relation(
            user_uuid, source_id, source_type
        )

        result = await inbox_service.process_source_videos(
            user_inbox_source_related=user_inbox_source_related,
            raw_video_list=raw_video_list,
            enable_deduplication=True
        )

        print(f"处理结果（对象方式）: {result}")

        # 方法2: 使用兼容性方法处理来源视频
        result2 = await inbox_service.process_source_videos_by_params(
            user_uuid=user_uuid,
            source_id=source_id,
            source_type=source_type,
            raw_video_list=raw_video_list,
            enable_deduplication=True
        )
        
        print(f"处理结果（兼容性方式）: {result2}")
        
        # 获取用户收件箱摘要
        summary = await inbox_service.get_user_inbox_summary(user_uuid)
        print(f"用户收件箱摘要: {summary}")
        
    except Exception as e:
        print(f"处理失败: {e}")


async def example_batch_processing():
    """批量处理示例"""
    print("\n=== 批量处理示例 ===")
    
    inbox_service = InboxService(logger)
    user_uuid = "user123456789"
    
    # 模拟多个来源的数据
    source_data_list = [
        {
            "source_id": "author_001",
            "source_type": SourceType.AUTHOR.value,
            "videos": [
                {"aweme_id": "7001", "publish_time": 1640995200},
                {"aweme_id": "7002", "publish_time": 1640995300}
            ]
        },
        {
            "source_id": "keyword_001",
            "source_type": SourceType.KEYWORD.value,
            "videos": [
                {"aweme_id": "7003", "publish_time": 1640995400},
                {"aweme_id": "7004", "publish_time": 1640995500}
            ]
        }
    ]
    
    try:
        # 批量处理
        result = await inbox_service.batch_process_multiple_sources(
            user_uuid=user_uuid,
            source_data_list=source_data_list,
            batch_size=5
        )
        
        print(f"批量处理结果: {result}")
        
    except Exception as e:
        print(f"批量处理失败: {e}")


async def example_time_filtering():
    """时间过滤示例"""
    print("\n=== 时间过滤示例 ===")
    
    inbox_service = InboxService(logger)
    user_uuid = "user123456789"
    source_id = "author_time_test"
    source_type = "author"
    
    # 模拟不同时间的视频数据
    now = datetime.now()
    raw_video_list = [
        {
            "aweme_id": "7201",
            "publish_time": (now - timedelta(days=7)).timestamp()  # 7天前
        },
        {
            "aweme_id": "7202", 
            "publish_time": (now - timedelta(days=3)).timestamp()  # 3天前
        },
        {
            "aweme_id": "7203",
            "publish_time": (now - timedelta(days=1)).timestamp()  # 1天前
        }
    ]
    
    try:
        # 只处理最近3天的视频
        start_time = now - timedelta(days=3)
        
        # 使用兼容性方法进行时间过滤
        result = await inbox_service.process_source_videos_by_params(
            user_uuid=user_uuid,
            source_id=source_id,
            source_type=source_type,
            raw_video_list=raw_video_list,
            enable_time_filter=True,
            start_time=start_time
        )
        
        print(f"时间过滤处理结果: {result}")
        
    except Exception as e:
        print(f"时间过滤处理失败: {e}")


async def example_video_data_processing():
    """视频数据处理示例"""
    print("\n=== 视频数据处理示例 ===")
    
    processor = VideoDataProcessor(logger)
    
    # 模拟原始视频数据（包含重复和无效数据）
    raw_videos = [
        {"aweme_id": "7301", "publish_time": 1640995200, "title": "视频1"},
        {"aweme_id": "7302", "publish_time": "2022-01-01 12:00:00", "title": "视频2"},
        {"aweme_id": "7301", "publish_time": 1640995200, "title": "重复视频1"},  # 重复
        {"aweme_id": "", "publish_time": 1640995300, "title": "无效视频"},  # 无效ID
        {"aweme_id": "7303", "publish_time": None, "title": "视频3"},  # 无发布时间
    ]
    
    try:
        # 格式化视频数据
        formatted_videos = processor.format_video_list(raw_videos)
        print(f"格式化后的视频: {formatted_videos}")
        
        # 去重
        deduplicated_videos = processor.deduplicate_videos(formatted_videos)
        print(f"去重后的视频: {deduplicated_videos}")
        
        # 提取视频ID
        video_ids = processor.extract_video_ids(deduplicated_videos)
        print(f"视频ID列表: {video_ids}")
        
        # 分批处理
        batches = processor.batch_process_videos(deduplicated_videos, batch_size=2)
        print(f"分批结果: {batches}")
        
        # 生成处理摘要
        summary = processor.create_processing_summary(
            len(raw_videos), len(deduplicated_videos), 1
        )
        print(f"处理摘要: {summary}")
        
    except Exception as e:
        print(f"视频数据处理失败: {e}")


async def example_relation_service():
    """关联服务示例"""
    print("\n=== 关联服务示例 ===")
    
    relation_service = InboxRelationService(logger)
    user_uuid = "user123456789"
    
    try:
        # 获取用户的来源关联
        source_relations = await relation_service.get_user_source_relations(
            user_uuid, source_type=SourceType.AUTHOR.value
        )
        print(f"用户来源关联: {len(source_relations)} 条")
        
        # 获取用户的视频关联（最近10条）
        video_relations = await relation_service.get_user_video_relations(
            user_uuid, limit=10
        )
        print(f"用户视频关联: {len(video_relations)} 条")
        
        # 删除特定来源关联
        if source_relations:
            first_relation = source_relations[0]
            deleted = await relation_service.remove_source_relation(
                user_uuid, first_relation.source_id, first_relation.source_type
            )
            print(f"删除来源关联结果: {deleted}")
        
    except Exception as e:
        print(f"关联服务操作失败: {e}")


async def example_error_handling():
    """错误处理示例"""
    print("\n=== 错误处理示例 ===")
    
    inbox_service = InboxService(logger)
    
    try:
        # 测试参数验证错误
        await inbox_service.process_source_videos_by_params(
            user_uuid="",  # 空用户UUID
            source_id="test",
            source_type="invalid_type",  # 无效类型
            raw_video_list=[]
        )
    except Exception as e:
        print(f"参数验证错误（预期）: {e}")

    try:
        # 测试无效来源类型
        await inbox_service.process_source_videos_by_params(
            user_uuid="user123",
            source_id="test",
            source_type="invalid",  # 无效类型
            raw_video_list=[{"aweme_id": "123"}]
        )
    except Exception as e:
        print(f"无效来源类型错误（预期）: {e}")

    try:
        # 测试无效的 user_inbox_source_related 对象
        invalid_relation = UserInboxSourceRelated(
            user_uuid="",  # 空用户UUID
            source_id="test",
            source_type="invalid"
        )
        await inbox_service.process_source_videos(
            user_inbox_source_related=invalid_relation,
            raw_video_list=[]
        )
    except Exception as e:
        print(f"无效对象错误（预期）: {e}")


async def main():
    """主函数 - 运行所有示例"""
    print("收件箱服务使用示例")
    print("=" * 50)
    
    # 注意：这些示例需要数据库连接才能正常运行
    # 在实际使用中，请确保已正确配置数据库连接
    
    try:
        await example_basic_usage()
        # await example_batch_processing()
        # await example_time_filtering()
        # await example_video_data_processing()
        # await example_relation_service()
        # await example_error_handling()
        
    except Exception as e:
        print(f"示例运行失败: {e}")
        print("请确保数据库连接已正确配置")


if __name__ == "__main__":
    # 运行示例
    asyncio.run(main())
