"""
用户收件箱关联处理服务 - 实现文档中定义的业务逻辑
"""

import uuid
from datetime import datetime
from typing import Dict, List, Optional, Tuple

from models.qihaozhushou import UserInboxSourceRelated, UserInboxVideoRelated, SourceType
from services.base import BaseService, DatabaseError, ValidationError
from services.base.database_optimizer import DatabaseOptimizer


class InboxRelationService(BaseService):
    """用户收件箱关联处理服务"""

    def __init__(self, logger=None):
        super().__init__(logger)
        self.db_optimizer = DatabaseOptimizer(self.service_logger)

    def _generate_uuid(self) -> str:
        """生成32位UUID（去除连字符）"""
        return str(uuid.uuid4()).replace('-', '')

    async def process_user_inbox_relation(
        self,
        user_uuid: str,
        source_id: str,
        source_type: str,
        video_list: List[Dict[str, str]]
    ) -> Dict[str, int]:
        """
        处理用户收件箱关联逻辑

        Args:
            user_uuid: 用户UUID
            source_id: 来源ID
            source_type: 来源类型（AUTHOR/KEYWORD/COLLECT）
            video_list: 视频列表 [{"aweme_id": "xxx", "publish_time": "xxx"}]

        Returns:
            Dict[str, int]: 处理结果统计
        """
        # 参数验证
        self.validate_required_params(
            user_uuid=user_uuid,
            source_id=source_id,
            source_type=source_type
        )
        
        # 验证 source_type 是否为有效的枚举值
        valid_source_types = [SourceType.AUTHOR.value, SourceType.KEYWORD.value, SourceType.COLLECT.value]
        if source_type not in valid_source_types:
            raise ValidationError(f"不支持的来源类型: {source_type}，支持的类型: {valid_source_types}")

        operation_id = self.performance_monitor.start_operation("process_user_inbox_relation")
        
        try:
            # 记录业务事件
            self.service_logger.log_business_event(
                "开始处理用户收件箱关联",
                {
                    "user_uuid": user_uuid,
                    "source_id": source_id,
                    "source_type": source_type,
                    "video_count": len(video_list)
                }
            )

            # 1. 处理 user_inbox_source_related 表
            source_relation_created = await self._ensure_source_relation(
                user_uuid, source_id, source_type
            )

            # 2. 处理 user_inbox_video_related 表
            # 暂时跳过视频关联处理，因为表可能不存在
            try:
                video_relations_created = await self._process_video_relations(
                    user_uuid, source_id, source_type, video_list
                )
            except Exception as e:
                # 如果表不存在或字段不匹配，记录警告但不中断流程
                self.service_logger.log_business_event(
                    "跳过视频关联处理",
                    {
                        "reason": f"表结构问题: {str(e)}",
                        "user_uuid": user_uuid,
                        "source_type": source_type,
                        "video_count": len(video_list)
                    }
                )
                video_relations_created = 0

            result = {
                "source_relations_created": source_relation_created,
                "video_relations_created": video_relations_created,
                "total_videos_processed": len(video_list)
            }

            self.service_logger.log_business_event(
                "用户收件箱关联处理完成",
                result
            )

            return result

        except Exception as e:
            self.error_tracker.track_error("process_user_inbox_relation", e)
            raise DatabaseError(f"处理用户收件箱关联失败: {str(e)}")
        finally:
            self.performance_monitor.finish_operation(operation_id)

    async def _ensure_source_relation(
        self,
        user_uuid: str,
        source_id: str,
        source_type: str
    ) -> int:
        """
        确保 user_inbox_source_related 记录存在

        Returns:
            int: 创建的记录数量（0或1）
        """
        try:
            # 将字符串 source_type 转换为枚举类型
            source_type_enum = self._convert_source_type_to_enum(source_type)

            # 查询是否已存在有效记录
            existing = await UserInboxSourceRelated.filter(
                user_uuid=user_uuid,
                source_id=source_id,
                source_type=source_type_enum,
                is_deleted=False
            ).first()

            if existing:
                self.service_logger.log_business_event(
                    "跳过来源关联创建",
                    {"reason": "记录已存在", "existing_uuid": existing.uuid}
                )
                return 0

            # 创建新记录
            new_relation = await UserInboxSourceRelated.create(
                uuid=self._generate_uuid(),
                user_uuid=user_uuid,
                source_id=source_id,
                source_type=source_type_enum,
                is_deleted=False,
                deleted_at=""
            )

            self.service_logger.log_business_event(
                "来源关联创建成功",
                {"new_uuid": new_relation.uuid}
            )

            return 1

        except Exception as e:
            raise DatabaseError(f"处理来源关联失败: {str(e)}")

    def _convert_source_type_to_enum(self, source_type: str) -> SourceType:
        """
        将字符串 source_type 转换为枚举类型

        Args:
            source_type: 字符串类型的来源类型

        Returns:
            SourceType: 对应的枚举值

        Raises:
            ValidationError: 如果 source_type 无效
        """
        # 如果传入的是枚举类型，直接返回
        if isinstance(source_type, SourceType):
            return source_type

        # 将字符串转换为大写，以支持大小写不敏感的输入
        source_type_upper = str(source_type).upper()

        # 字符串到枚举的映射
        mapping = {
            "AUTHOR": SourceType.AUTHOR,
            "KEYWORD": SourceType.KEYWORD,
            "COLLECT": SourceType.COLLECT,
        }

        if source_type_upper in mapping:
            return mapping[source_type_upper]
        else:
            valid_types = list(mapping.keys())
            raise ValidationError(f"不支持的来源类型: {source_type}，支持的类型: {valid_types}")

    def _convert_crawler_type_to_enum(self, source_type: str):
        """
        将字符串类型的 source_type 转换为爬取类型枚举

        Args:
            source_type: 字符串类型的来源类型

        Returns:
            CrawlerType: 对应的枚举值

        Raises:
            ValidationError: 不支持的来源类型
        """
        from models.qihaozhushou.models import CrawlerType

        # 映射关系
        mapping = {
            "AUTHOR": CrawlerType.AUTHOR,
            "KEYWORD": CrawlerType.KEYWORD,
            "COLLECT": CrawlerType.COLLECT,
        }

        source_type_upper = source_type.upper()

        if source_type_upper in mapping:
            return mapping[source_type_upper]
        else:
            valid_types = list(mapping.keys())
            raise ValidationError(f"不支持的爬取类型: {source_type}，支持的类型: {valid_types}")

    async def _process_video_relations(
        self,
        user_uuid: str,
        source_id: str,
        source_type: str,
        video_list: List[Dict[str, str]]
    ) -> int:
        """
        批量处理视频关联记录
        
        Returns:
            int: 创建的记录数量
        """
        if not video_list:
            return 0

        try:
            # 将字符串 source_type 转换为爬取类型枚举
            crawler_type_enum = self._convert_crawler_type_to_enum(source_type)

            # 提取视频信息列表
            video_data = []
            for video in video_list:
                aweme_id = video.get("aweme_id")
                publish_time = video.get("publish_time")
                if aweme_id and publish_time:
                    video_data.append({
                        "aweme_id": aweme_id,
                        "publish_time": publish_time
                    })

            if not video_data:
                self.service_logger.log_business_event(
                    "跳过视频关联处理",
                    {"reason": "无有效视频数据"}
                )
                return 0

            # 检查表结构是否支持新的字段
            try:
                # 尝试进行一个简单的查询来验证表结构
                await UserInboxVideoRelated.filter(
                    user_uuid=user_uuid,
                    is_deleted=False
                ).limit(1).all()

                # 批量检查已存在的记录（基于aweme_id）
                aweme_ids = [item["aweme_id"] for item in video_data]
                existing_aweme_ids = await self.db_optimizer.batch_exists_check(
                    model=UserInboxVideoRelated,
                    field_name="aweme_id",
                    values=aweme_ids,
                    additional_filters={
                        "user_uuid": user_uuid,
                        "source_type": crawler_type_enum,
                        "is_deleted": False
                    }
                )

            except Exception as table_error:
                # 如果表结构不匹配或字段不存在，记录警告并跳过
                self.service_logger.log_business_event(
                    "跳过视频关联处理 - 表结构不兼容",
                    {
                        "user_uuid": user_uuid,
                        "source_type": source_type,
                        "video_count": len(video_data),
                        "error": str(table_error),
                        "reason": "数据库表结构与模型定义不匹配，可能缺少必要字段"
                    }
                )

                # 返回0表示没有创建任何关联，但不抛出异常
                return 0

            # 计算需要创建的新记录
            new_video_data = [item for item in video_data if item["aweme_id"] not in existing_aweme_ids]

            if not new_video_data:
                self.service_logger.log_business_event(
                    "跳过视频关联创建",
                    {"reason": "所有关联已存在"}
                )
                return 0

            # 准备批量创建数据
            new_relations = []
            for video_item in new_video_data:
                relation = UserInboxVideoRelated(
                    uuid=self._generate_uuid(),
                    user_uuid=user_uuid,
                    source_id=source_id,  # 使用传入的source_id
                    source_type=crawler_type_enum,
                    aweme_id=video_item["aweme_id"],
                    publish_time=video_item["publish_time"],
                    is_deleted=False
                )
                new_relations.append(relation)

            # 批量创建
            created_count = await self.db_optimizer.batch_create_optimized(
                model=UserInboxVideoRelated,
                records=new_relations,
                batch_size=500
            )

            self.service_logger.log_business_event(
                "视频关联批量创建完成",
                {
                    "total_videos": len(video_data),
                    "existing_relations": len(existing_aweme_ids),
                    "new_relations": created_count
                }
            )

            return created_count

        except Exception as e:
            raise DatabaseError(f"处理视频关联失败: {str(e)}")

    async def get_user_source_relations(
        self,
        user_uuid: str,
        source_type: Optional[str] = None
    ) -> List[UserInboxSourceRelated]:
        """
        获取用户的来源关联记录
        
        Args:
            user_uuid: 用户UUID
            source_type: 可选的来源类型过滤
            
        Returns:
            List[UserInboxSourceRelated]: 关联记录列表
        """
        self.validate_required_params(user_uuid=user_uuid)
        
        try:
            query = UserInboxSourceRelated.filter(
                user_uuid=user_uuid,
                is_deleted=False
            )
            
            if source_type:
                source_type_enum = self._convert_source_type_to_enum(source_type)
                query = query.filter(source_type=source_type_enum)
            
            return await query.all()
            
        except Exception as e:
            raise DatabaseError(f"查询用户来源关联失败: {str(e)}")

    async def get_user_video_relations(
        self,
        user_uuid: str,
        source_type: Optional[str] = None,
        limit: Optional[int] = None
    ) -> List[UserInboxVideoRelated]:
        """
        获取用户的视频关联记录
        
        Args:
            user_uuid: 用户UUID
            source_type: 可选的来源类型过滤
            limit: 可选的记录数量限制
            
        Returns:
            List[UserInboxVideoRelated]: 关联记录列表
        """
        self.validate_required_params(user_uuid=user_uuid)
        
        try:
            query = UserInboxVideoRelated.filter(
                user_uuid=user_uuid,
                is_deleted=False
            ).order_by("-create_time")
            
            if source_type:
                source_type_enum = self._convert_source_type_to_enum(source_type)
                query = query.filter(source_type=source_type_enum)
                
            if limit:
                query = query.limit(limit)
            
            return await query.all()
            
        except Exception as e:
            raise DatabaseError(f"查询用户视频关联失败: {str(e)}")

    async def remove_source_relation(
        self,
        user_uuid: str,
        source_id: str,
        source_type: str
    ) -> bool:
        """
        软删除来源关联记录
        
        Returns:
            bool: 是否成功删除
        """
        self.validate_required_params(
            user_uuid=user_uuid,
            source_id=source_id,
            source_type=source_type
        )
        
        try:
            # 将字符串 source_type 转换为枚举类型
            source_type_enum = self._convert_source_type_to_enum(source_type)

            # 软删除来源关联
            updated_count = await UserInboxSourceRelated.filter(
                user_uuid=user_uuid,
                source_id=source_id,
                source_type=source_type_enum,
                is_deleted=False
            ).update(
                is_deleted=True,
                deleted_at=str(int(datetime.now().timestamp()))
            )
            
            if updated_count > 0:
                self.service_logger.log_business_event(
                    "来源关联删除成功",
                    {"user_uuid": user_uuid, "source_id": source_id, "source_type": source_type}
                )
                return True
            
            return False
            
        except Exception as e:
            raise DatabaseError(f"删除来源关联失败: {str(e)}")
