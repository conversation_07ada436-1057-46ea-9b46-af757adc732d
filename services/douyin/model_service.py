"""
抖音数据模型转换服务

负责处理抖音数据的模型转换
"""

from typing import Any, Dict

from loguru import logger

from mappers.douyin.pydantic_models import DouyinVideoData
from services.base import BaseService
from controllers.douyin.models import ModelConversionResult


class DouyinModelService(BaseService):
    """抖音数据模型转换服务"""

    def convert_to_douyin_video_data(self, raw_data: Dict[str, Any], source: str = "unknown") -> ModelConversionResult:
        """
        将原始数据转换为DouyinVideoData模型

        Args:
            raw_data: 原始数据字典
            source: 数据来源 ("mobile", "jingxuan", "rpc")

        Returns:
            ModelConversionResult: 模型转换结果
        """
        try:
            # 使用对应的映射器转换数据
            from models.douyin import DouyinFetchMethod

            if source == DouyinFetchMethod.MOBILE.value:
                from mappers.douyin.mobile_mapper import MobileDataMapper

                mapper = MobileDataMapper()
            elif source == DouyinFetchMethod.JINGXUAN.value:
                from mappers.douyin.jingxuan_mapper import JingxuanDataMapper

                mapper = JingxuanDataMapper()
            elif source == DouyinFetchMethod.RPC.value:
                from mappers.douyin.rpc_mapper import RPCDataMapper

                mapper = RPCDataMapper()
            else:
                return ModelConversionResult(success=False, error_message=f"不支持的数据源: {source}")

            # 验证原始数据
            if not mapper.validate_raw_data(raw_data):
                return ModelConversionResult(
                    success=False,
                    error_message="原始数据验证失败",
                    validation_errors={"raw_data": "数据格式不符合要求"},
                )

            # 转换为标准格式
            standard_data = mapper.map_to_standard_format(raw_data)

            # 创建DouyinVideoData模型实例
            video_data_model = self._create_douyin_video_data_model(standard_data)

            return ModelConversionResult(success=True, model=video_data_model)

        except Exception as e:
            logger.error(f"模型转换失败 source={source}: {e}")
            return ModelConversionResult(success=False, error_message=f"模型转换异常: {str(e)}")

    def _create_douyin_video_data_model(self, standard_data: Dict[str, Any]) -> DouyinVideoData:
        """
        根据标准化数据创建DouyinVideoData模型实例

        Args:
            standard_data: 标准化的数据字典

        Returns:
            DouyinVideoData: 创建的模型实例
        """
        # 直接使用Pydantic模型进行数据验证和创建
        return DouyinVideoData(**standard_data)
