"""
抖音HTML获取服务

负责处理抖音HTML页面的数据获取
"""

from typing import Dict, Optional

from loguru import logger

from services.base import BaseService
from controllers.douyin.models import HTMLFetchResult


class DouyinHTMLService(BaseService):
    """抖音HTML获取服务"""

    async def fetch_mobile_html(
        self, aweme_id: str, use_proxy: bool = True, custom_headers: Optional[Dict[str, str]] = None, timeout: int = 30
    ) -> HTMLFetchResult:
        """
        获取移动端HTML内容

        Args:
            aweme_id: 视频ID
            use_proxy: 是否使用代理
            custom_headers: 自定义请求头
            timeout: 超时时间

        Returns:
            HTMLFetchResult: HTML获取结果
        """
        try:
            from rpc.douyin.html_handler.client import html_client
            from rpc.douyin.html_handler.schemas import MobileShareRequest

            request = MobileShareRequest(
                aweme_id=aweme_id, use_proxy=use_proxy, custom_headers=custom_headers or {}, timeout=timeout
            )

            response = await html_client.fetch_mobile_share_page(request)

            if response.success and response.content:
                return HTMLFetchResult(
                    success=True,
                    content=response.content,
                    status_code=getattr(response, "status_code", 200),
                    response_time=getattr(response, "response_time", None),
                )
            else:
                return HTMLFetchResult(
                    success=False,
                    error_message=response.error_message or "Unknown error",
                    status_code=getattr(response, "status_code", None),
                )

        except Exception as e:
            logger.error(f"移动端HTML获取失败 {aweme_id}: {e}")
            return HTMLFetchResult(success=False, error_message=f"HTML获取异常: {str(e)}")

    async def fetch_jingxuan_html(
        self, aweme_id: str, use_proxy: bool = True, custom_headers: Optional[Dict[str, str]] = None, timeout: int = 30
    ) -> HTMLFetchResult:
        """
        获取精选页面HTML内容

        Args:
            aweme_id: 视频ID
            use_proxy: 是否使用代理
            custom_headers: 自定义请求头
            timeout: 超时时间

        Returns:
            HTMLFetchResult: HTML获取结果
        """
        try:
            from rpc.douyin.html_handler.client import html_client
            from rpc.douyin.html_handler.schemas import JingxuanRequest

            request = JingxuanRequest(
                aweme_id=aweme_id, use_proxy=use_proxy, custom_headers=custom_headers or {}, timeout=timeout
            )

            response = await html_client.fetch_jingxuan_page(request)

            if response.success and response.content:
                return HTMLFetchResult(
                    success=True,
                    content=response.content,
                    status_code=getattr(response, "status_code", 200),
                    response_time=getattr(response, "response_time", None),
                )
            else:
                return HTMLFetchResult(
                    success=False,
                    error_message=response.error_message or "Unknown error",
                    status_code=getattr(response, "status_code", None),
                )

        except Exception as e:
            logger.error(f"精选页面HTML获取失败 {aweme_id}: {e}")
            return HTMLFetchResult(success=False, error_message=f"HTML获取异常: {str(e)}")
