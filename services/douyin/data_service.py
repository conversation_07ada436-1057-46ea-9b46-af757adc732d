"""
抖音完整数据获取服务

组合各个步骤，提供完整的数据获取流程
"""

from typing import Dict, Optional

from loguru import logger

from services.base import BaseService
from controllers.douyin.models import (
    JingxuanDataFetchResult,
    MobileDataFetchResult,
    RPCDataFetchResult,
)
from .html_service import DouyinHTMLService
from .parse_service import DouyinParseService
from .model_service import DouyinModelService


class DouyinDataService(BaseService):
    """抖音完整数据获取服务 - 组合各个步骤"""

    def __init__(self, logger_instance=None):
        super().__init__(logger_instance)
        self.html_service = DouyinHTMLService(logger_instance)
        self.parse_service = DouyinParseService(logger_instance)
        self.model_service = DouyinModelService(logger_instance)

    async def fetch_mobile_data(
        self, aweme_id: str, use_proxy: bool = True, custom_headers: Optional[Dict[str, str]] = None, timeout: int = 30
    ) -> MobileDataFetchResult:
        """
        获取移动端数据的完整流程

        Args:
            aweme_id: 视频ID
            use_proxy: 是否使用代理
            custom_headers: 自定义请求头
            timeout: 超时时间

        Returns:
            MobileDataFetchResult: 完整的获取结果
        """
        result = MobileDataFetchResult(success=False, aweme_id=aweme_id)

        # 步骤1: 获取HTML内容
        logger.info(f"步骤1: 获取移动端HTML内容 {aweme_id}")
        html_result = await self.html_service.fetch_mobile_html(aweme_id, use_proxy, custom_headers, timeout)
        result.html_result = html_result

        if not html_result.success:
            result.failed_step = "html_fetch"
            result.error_message = f"HTML获取失败: {html_result.error_message}"
            return result

        # 步骤2: 解析HTML中的JSON
        logger.info(f"步骤2: 解析移动端JSON数据 {aweme_id}")
        json_result = self.parse_service.parse_mobile_json(html_result.content, aweme_id)
        result.json_result = json_result

        if not json_result.success:
            result.failed_step = "json_parse"
            result.error_message = f"JSON解析失败: {json_result.error_message}"
            return result

        # 步骤3: 转换为数据库模型
        logger.info(f"步骤3: 转换为DouyinVideoData模型 {aweme_id}")
        model_result = self.model_service.convert_to_douyin_video_data(json_result.data, "mobile")
        result.model_result = model_result

        if not model_result.success:
            result.failed_step = "model_conversion"
            result.error_message = f"模型转换失败: {model_result.error_message}"
            return result

        # 成功完成所有步骤
        result.success = True
        result.final_model = model_result.model
        logger.info(f"移动端数据获取成功 {aweme_id}")

        return result

    async def fetch_jingxuan_data(
        self, aweme_id: str, use_proxy: bool = True, custom_headers: Optional[Dict[str, str]] = None, timeout: int = 30
    ) -> JingxuanDataFetchResult:
        """
        获取精选页面数据的完整流程

        Args:
            aweme_id: 视频ID
            use_proxy: 是否使用代理
            custom_headers: 自定义请求头
            timeout: 超时时间

        Returns:
            JingxuanDataFetchResult: 完整的获取结果
        """
        result = JingxuanDataFetchResult(success=False, aweme_id=aweme_id)

        # 步骤1: 获取HTML内容
        logger.info(f"步骤1: 获取精选页面HTML内容 {aweme_id}")
        html_result = await self.html_service.fetch_jingxuan_html(aweme_id, use_proxy, custom_headers, timeout)
        result.html_result = html_result

        if not html_result.success:
            result.failed_step = "html_fetch"
            result.error_message = f"HTML获取失败: {html_result.error_message}"
            return result

        # 步骤2: 解析HTML中的JSON
        logger.info(f"步骤2: 解析精选页面JSON数据 {aweme_id}")
        json_result = self.parse_service.parse_jingxuan_json(html_result.content, aweme_id)
        result.json_result = json_result

        if not json_result.success:
            result.failed_step = "json_parse"
            result.error_message = f"JSON解析失败: {json_result.error_message}"
            return result

        # 步骤3: 转换为数据库模型
        logger.info(f"步骤3: 转换为DouyinVideoData模型 {aweme_id}")
        model_result = self.model_service.convert_to_douyin_video_data(json_result.data, "jingxuan")
        result.model_result = model_result

        if not model_result.success:
            result.failed_step = "model_conversion"
            result.error_message = f"模型转换失败: {model_result.error_message}"
            return result

        # 成功完成所有步骤
        result.success = True
        result.final_model = model_result.model
        logger.info(f"精选页面数据获取成功 {aweme_id}")

        return result

    async def fetch_rpc_data(self, aweme_id: str, cookies: Optional[str] = None) -> RPCDataFetchResult:
        """
        获取RPC数据的完整流程

        Args:
            aweme_id: 视频ID
            cookies: 可选的cookies

        Returns:
            RPCDataFetchResult: 完整的获取结果
        """
        result = RPCDataFetchResult(success=False, aweme_id=aweme_id)

        try:
            # 步骤1: 调用RPC接口
            logger.info(f"步骤1: 调用RPC接口获取数据 {aweme_id}")
            # 延迟导入避免循环依赖
            from controllers.douyin.video.rpc_video_controller import DouyinRPCVideoController

            controller = DouyinRPCVideoController()
            if cookies:
                rpc_response = await controller.get_video_db_with_cookies(aweme_id, cookies)
            else:
                rpc_response = await controller.get_video_db_auto_cookies(aweme_id)
            result.rpc_response = rpc_response

            if not rpc_response:
                result.failed_step = "rpc_call"
                result.error_message = "RPC调用返回空结果"
                return result

            # 步骤2: 转换RPC数据为DouyinVideoData模型
            logger.info(f"步骤2: 转换RPC数据为DouyinVideoData模型 {aweme_id}")
            model_result = self.model_service.convert_to_douyin_video_data(rpc_response, "rpc")
            result.model_result = model_result

            if not model_result.success:
                result.failed_step = "model_conversion"
                result.error_message = f"模型转换失败: {model_result.error_message}"
                return result

            # 成功完成所有步骤
            result.success = True
            result.final_model = model_result.model
            logger.info(f"RPC数据获取成功 {aweme_id}")

            return result

        except Exception as e:
            logger.error(f"RPC数据获取失败 {aweme_id}: {e}")
            result.failed_step = "rpc_call"
            result.error_message = f"RPC调用异常: {str(e)}"
            return result
