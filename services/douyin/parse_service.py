"""
抖音数据解析服务

负责处理抖音HTML页面的数据解析
"""

from loguru import logger

from services.base import BaseService
from controllers.douyin.models import JSONParseResult


class DouyinParseService(BaseService):
    """抖音数据解析服务"""

    def parse_mobile_json(self, html_content: str, aweme_id: str) -> JSONParseResult:
        """
        从移动端HTML中解析JSON数据

        Args:
            html_content: HTML内容
            aweme_id: 视频ID

        Returns:
            JSONParseResult: JSON解析结果
        """
        try:
            from utils.douyin.extract.html_parser import parse_mobile_html

            parsed_data = parse_mobile_html(html_content, aweme_id)

            if parsed_data:
                return JSONParseResult(
                    success=True,
                    data=parsed_data,
                    raw_content=html_content[:500] + "..." if len(html_content) > 500 else html_content,
                )
            else:
                return JSONParseResult(
                    success=False,
                    error_message="未能从HTML中解析出有效的JSON数据",
                    raw_content=html_content[:500] + "..." if len(html_content) > 500 else html_content,
                )

        except Exception as e:
            logger.error(f"移动端JSON解析失败 {aweme_id}: {e}")
            return JSONParseResult(
                success=False,
                error_message=f"JSON解析异常: {str(e)}",
                raw_content=html_content[:500] + "..." if len(html_content) > 500 else html_content,
            )

    def parse_jingxuan_json(self, html_content: str, aweme_id: str) -> JSONParseResult:
        """
        从精选页面HTML中解析JSON数据

        Args:
            html_content: HTML内容
            aweme_id: 视频ID

        Returns:
            JSONParseResult: JSON解析结果
        """
        try:
            from utils.douyin.extract.html_parser import parse_jingxuan_html

            parsed_data = parse_jingxuan_html(html_content, aweme_id)

            if parsed_data:
                return JSONParseResult(
                    success=True,
                    data=parsed_data,
                    raw_content=html_content[:500] + "..." if len(html_content) > 500 else html_content,
                )
            else:
                return JSONParseResult(
                    success=False,
                    error_message="未能从HTML中解析出有效的pace_f数据",
                    raw_content=html_content[:500] + "..." if len(html_content) > 500 else html_content,
                )

        except Exception as e:
            logger.error(f"精选页面JSON解析失败 {aweme_id}: {e}")
            return JSONParseResult(
                success=False,
                error_message=f"JSON解析异常: {str(e)}",
                raw_content=html_content[:500] + "..." if len(html_content) > 500 else html_content,
            )
