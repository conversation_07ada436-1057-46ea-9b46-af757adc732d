"""
服务层日志配置
"""

import logging
from datetime import datetime
from typing import Any, Dict, Optional


class ServiceLogger:
    """服务层专用日志记录器"""
    
    def __init__(self, service_name: str, logger: Optional[logging.Logger] = None):
        """
        初始化服务日志记录器
        
        Args:
            service_name: 服务名称
            logger: 可选的外部日志记录器
        """
        self.service_name = service_name
        self.logger = logger or logging.getLogger(f"services.{service_name}")
        
    def log_operation_start(self, operation: str, **context):
        """记录操作开始"""
        self.logger.info(f"[{self.service_name}] 开始执行: {operation}", extra=context)
    
    def log_operation_success(self, operation: str, duration: float, **context):
        """记录操作成功"""
        self.logger.info(
            f"[{self.service_name}] 操作成功: {operation} (耗时: {duration:.2f}s)", 
            extra=context
        )
    
    def log_operation_error(self, operation: str, error: Exception, **context):
        """记录操作错误"""
        self.logger.error(
            f"[{self.service_name}] 操作失败: {operation} - {str(error)}", 
            extra=context,
            exc_info=True
        )
    
    def log_performance_metrics(self, operation: str, metrics: Dict[str, Any]):
        """记录性能指标"""
        self.logger.info(
            f"[{self.service_name}] 性能指标 - {operation}: {metrics}"
        )
    
    def log_business_event(self, event: str, details: Dict[str, Any]):
        """记录业务事件"""
        self.logger.info(
            f"[{self.service_name}] 业务事件: {event}",
            extra={"event_details": details}
        )
    
    def log_data_validation(self, validation_type: str, result: bool, details: Optional[str] = None):
        """记录数据验证结果"""
        level = logging.INFO if result else logging.WARNING
        message = f"[{self.service_name}] 数据验证 - {validation_type}: {'通过' if result else '失败'}"
        if details:
            message += f" - {details}"
        self.logger.log(level, message)
    
    def log_database_operation(self, operation: str, table: str, count: int, duration: float):
        """记录数据库操作"""
        self.logger.debug(
            f"[{self.service_name}] 数据库操作: {operation} - 表: {table}, "
            f"记录数: {count}, 耗时: {duration:.3f}s"
        )
    
    def log_external_api_call(self, api_name: str, success: bool, duration: float, **context):
        """记录外部API调用"""
        status = "成功" if success else "失败"
        self.logger.info(
            f"[{self.service_name}] 外部API调用: {api_name} - {status} (耗时: {duration:.2f}s)",
            extra=context
        )


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, service_name: str, logger: ServiceLogger):
        self.service_name = service_name
        self.logger = logger
        self.metrics = {}
    
    def start_operation(self, operation_name: str) -> str:
        """开始监控操作"""
        operation_id = f"{operation_name}_{datetime.now().timestamp()}"
        self.metrics[operation_id] = {
            "operation": operation_name,
            "start_time": datetime.now(),
            "items_processed": 0,
            "errors": 0
        }
        return operation_id
    
    def update_progress(self, operation_id: str, items_processed: int, errors: int = 0):
        """更新操作进度"""
        if operation_id in self.metrics:
            self.metrics[operation_id]["items_processed"] = items_processed
            self.metrics[operation_id]["errors"] = errors
    
    def finish_operation(self, operation_id: str) -> Dict[str, Any]:
        """完成操作监控"""
        if operation_id not in self.metrics:
            return {}
        
        metrics = self.metrics[operation_id]
        end_time = datetime.now()
        duration = (end_time - metrics["start_time"]).total_seconds()
        
        result = {
            "operation": metrics["operation"],
            "duration": duration,
            "items_processed": metrics["items_processed"],
            "errors": metrics["errors"],
            "success_rate": (metrics["items_processed"] - metrics["errors"]) / max(metrics["items_processed"], 1),
            "throughput": metrics["items_processed"] / max(duration, 0.001)
        }
        
        # 记录性能指标
        self.logger.log_performance_metrics(metrics["operation"], result)
        
        # 清理
        del self.metrics[operation_id]
        
        return result


class ErrorTracker:
    """错误跟踪器"""
    
    def __init__(self, service_name: str, logger: ServiceLogger):
        self.service_name = service_name
        self.logger = logger
        self.error_counts = {}
    
    def track_error(self, error_type: str, error: Exception, context: Optional[Dict] = None):
        """跟踪错误"""
        if error_type not in self.error_counts:
            self.error_counts[error_type] = 0
        
        self.error_counts[error_type] += 1
        
        # 记录错误详情
        self.logger.log_operation_error(
            f"错误类型: {error_type}",
            error,
            error_count=self.error_counts[error_type],
            context=context or {}
        )
    
    def get_error_summary(self) -> Dict[str, int]:
        """获取错误摘要"""
        return self.error_counts.copy()
    
    def reset_counters(self):
        """重置错误计数器"""
        self.error_counts.clear()


def create_service_logger(service_name: str, external_logger: Optional[logging.Logger] = None) -> ServiceLogger:
    """创建服务日志记录器的工厂函数"""
    return ServiceLogger(service_name, external_logger)


def create_performance_monitor(service_name: str, logger: ServiceLogger) -> PerformanceMonitor:
    """创建性能监控器的工厂函数"""
    return PerformanceMonitor(service_name, logger)


def create_error_tracker(service_name: str, logger: ServiceLogger) -> ErrorTracker:
    """创建错误跟踪器的工厂函数"""
    return ErrorTracker(service_name, logger)
