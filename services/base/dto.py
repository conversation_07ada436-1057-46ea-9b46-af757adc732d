"""
数据传输对象 (DTOs) - 定义服务间数据传输格式
"""

from dataclasses import dataclass, field
from typing import Dict, List, Optional


@dataclass
class VideoSyncResult:
    """视频同步结果"""
    video_ids: List[str] = field(default_factory=list)
    new_videos_count: int = 0
    updated_videos_count: int = 0
    failed_videos: List[str] = field(default_factory=list)
    errors: List[str] = field(default_factory=list)
    
    @property
    def total_processed(self) -> int:
        """总处理数量"""
        return len(self.video_ids) + len(self.failed_videos)
    
    @property
    def success_rate(self) -> float:
        """成功率"""
        total = self.total_processed
        return len(self.video_ids) / total if total > 0 else 0.0


@dataclass
class RelationResult:
    """关联关系处理结果"""
    created_count: int = 0
    existing_count: int = 0
    failed_count: int = 0
    errors: List[str] = field(default_factory=list)
    
    @property
    def total_processed(self) -> int:
        """总处理数量"""
        return self.created_count + self.existing_count + self.failed_count
    
    @property
    def success_rate(self) -> float:
        """成功率"""
        total = self.total_processed
        success = self.created_count + self.existing_count
        return success / total if total > 0 else 0.0


@dataclass
class UserInboxSyncResult:
    """用户收件箱同步结果"""
    updated_users_count: int = 0
    new_relations_count: int = 0
    existing_relations_count: int = 0
    failed_users: List[str] = field(default_factory=list)
    errors: List[str] = field(default_factory=list)
    
    @property
    def total_users_processed(self) -> int:
        """总处理用户数量"""
        return self.updated_users_count + len(self.failed_users)


@dataclass
class KeywordProcessResult:
    """关键词处理结果"""
    keyword_id: str
    keyword_text: str
    is_new_keyword: bool = False
    video_count_updated: bool = False
    timestamp_updated: bool = False
    errors: List[str] = field(default_factory=list)


@dataclass
class BatchProcessResult:
    """批量处理结果"""
    total_items: int = 0
    processed_items: int = 0
    successful_items: int = 0
    failed_items: int = 0
    errors: List[str] = field(default_factory=list)
    details: Dict[str, any] = field(default_factory=dict)
    
    @property
    def success_rate(self) -> float:
        """成功率"""
        return self.successful_items / self.total_items if self.total_items > 0 else 0.0
    
    @property
    def completion_rate(self) -> float:
        """完成率"""
        return self.processed_items / self.total_items if self.total_items > 0 else 0.0


@dataclass
class FilterCriteria:
    """过滤条件"""
    min_index: Optional[int] = None
    max_index: Optional[int] = None
    exclude_video_ids: List[str] = field(default_factory=list)
    include_video_ids: List[str] = field(default_factory=list)
    custom_filters: Dict[str, any] = field(default_factory=dict)
    
    def should_include_video(self, video_data: any) -> bool:
        """判断视频是否应该被包含"""
        # 检查视频ID包含/排除列表
        video_id = getattr(video_data, 'aweme_id', None) or getattr(video_data, 'item_id', None)
        if video_id:
            if self.exclude_video_ids and video_id in self.exclude_video_ids:
                return False
            if self.include_video_ids and video_id not in self.include_video_ids:
                return False
        
        # 检查索引范围
        if hasattr(video_data, 'index') and video_data.index is not None:
            index_value = float(video_data.index) if isinstance(video_data.index, str) else video_data.index
            if self.min_index is not None and index_value < self.min_index:
                return False
            if self.max_index is not None and index_value > self.max_index:
                return False
        
        return True
