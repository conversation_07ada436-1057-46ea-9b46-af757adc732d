"""
服务层异常定义
"""


class ServiceError(Exception):
    """服务层基础异常"""
    
    def __init__(self, message: str, details: dict = None):
        self.message = message
        self.details = details or {}
        super().__init__(self.message)


class ValidationError(ServiceError):
    """数据验证异常"""
    pass


class DatabaseError(ServiceError):
    """数据库操作异常"""
    pass


class ExternalAPIError(ServiceError):
    """外部API调用异常"""
    pass


class BusinessLogicError(ServiceError):
    """业务逻辑异常"""
    pass
