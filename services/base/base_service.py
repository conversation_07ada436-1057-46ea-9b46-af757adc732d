"""
基础服务类 - 提供通用功能
"""

import asyncio
import logging
from datetime import datetime
from functools import wraps
from typing import Any, Callable, Dict, List, Optional, TypeVar

from .exceptions import ServiceError
from .logging_config import ServiceLogger, PerformanceMonitor, ErrorTracker

T = TypeVar('T')


class BaseService:
    """基础服务类，提供通用功能"""

    def __init__(self, logger: Optional[logging.Logger] = None):
        """
        初始化基础服务

        Args:
            logger: 可选的日志记录器，如果不提供则使用类名创建
        """
        self.logger = logger or logging.getLogger(self.__class__.__name__)
        self.service_logger = ServiceLogger(self.__class__.__name__, self.logger)
        self.performance_monitor = PerformanceMonitor(self.__class__.__name__, self.service_logger)
        self.error_tracker = ErrorTracker(self.__class__.__name__, self.service_logger)
    
    async def batch_process(
        self, 
        items: List[T], 
        batch_size: int, 
        processor: Callable[[List[T]], Any],
        delay_between_batches: float = 0.1
    ) -> List[Any]:
        """
        通用批量处理方法
        
        Args:
            items: 要处理的项目列表
            batch_size: 批次大小
            processor: 处理函数，接收一个批次的项目列表
            delay_between_batches: 批次间延迟时间（秒）
            
        Returns:
            List[Any]: 处理结果列表
        """
        results = []
        total_items = len(items)
        
        for i in range(0, total_items, batch_size):
            batch = items[i:i + batch_size]
            batch_num = i // batch_size + 1
            total_batches = (total_items + batch_size - 1) // batch_size
            
            try:
                self.logger.debug(f"处理批次 {batch_num}/{total_batches}，包含 {len(batch)} 个项目")
                result = await processor(batch)
                results.append(result)
                
                # 批次间短暂休息，避免系统压力
                if i + batch_size < total_items and delay_between_batches > 0:
                    await asyncio.sleep(delay_between_batches)
                    
            except Exception as e:
                self.logger.error(f"批次 {batch_num} 处理失败: {str(e)}")
                raise ServiceError(f"批量处理失败: {str(e)}", {"batch_num": batch_num, "batch_size": len(batch)})
        
        return results
    
    def handle_errors(self, operation_name: str):
        """
        通用错误处理装饰器
        
        Args:
            operation_name: 操作名称，用于日志记录
        """
        def decorator(func):
            @wraps(func)
            async def wrapper(*args, **kwargs):
                start_time = datetime.now()
                try:
                    result = await func(*args, **kwargs)
                    duration = (datetime.now() - start_time).total_seconds()
                    self.logger.debug(f"{operation_name} 执行成功，耗时 {duration:.2f}s")
                    return result
                except ServiceError:
                    # 服务层异常直接重新抛出
                    raise
                except Exception as e:
                    duration = (datetime.now() - start_time).total_seconds()
                    error_msg = f"{operation_name} 执行失败: {str(e)}"
                    self.logger.error(f"{error_msg}，耗时 {duration:.2f}s")
                    raise ServiceError(error_msg, {"operation": operation_name, "duration": duration})
            return wrapper
        return decorator
    
    async def log_performance(self, operation: str, start_time: datetime, count: int = 0):
        """
        性能日志记录
        
        Args:
            operation: 操作名称
            start_time: 开始时间
            count: 处理的项目数量
        """
        duration = (datetime.now() - start_time).total_seconds()
        if count > 0:
            rate = count / duration if duration > 0 else 0
            self.logger.info(f"{operation} 完成: 处理 {count} 项，耗时 {duration:.2f}s，速率 {rate:.1f} 项/秒")
        else:
            self.logger.info(f"{operation} 完成: 耗时 {duration:.2f}s")
    
    def validate_required_params(self, **params):
        """
        验证必需参数

        Args:
            **params: 参数字典，key为参数名，value为参数值

        Raises:
            ValidationError: 当有参数为空时抛出
        """
        from .exceptions import ValidationError

        missing_params = [name for name, value in params.items() if value is None or (isinstance(value, str) and not value)]
        if missing_params:
            raise ValidationError(f"缺少必需参数: {', '.join(missing_params)}")
    
    async def safe_execute(self, operation: Callable, *args, **kwargs) -> tuple[bool, Any, Optional[str]]:
        """
        安全执行操作，返回执行结果

        Args:
            operation: 要执行的操作
            *args: 位置参数
            **kwargs: 关键字参数

        Returns:
            tuple: (是否成功, 结果, 错误信息)
        """
        operation_name = getattr(operation, '__name__', str(operation))
        start_time = datetime.now()

        try:
            self.service_logger.log_operation_start(operation_name)
            result = await operation(*args, **kwargs)

            duration = (datetime.now() - start_time).total_seconds()
            self.service_logger.log_operation_success(operation_name, duration)

            return True, result, None
        except Exception as e:
            self.error_tracker.track_error(operation_name, e)
            error_msg = str(e)
            return False, None, error_msg

    async def execute_with_retry(
        self,
        operation: Callable,
        max_retries: int = 3,
        delay: float = 1.0,
        *args,
        **kwargs
    ) -> tuple[bool, Any, Optional[str]]:
        """
        带重试的安全执行操作

        Args:
            operation: 要执行的操作
            max_retries: 最大重试次数
            delay: 重试间隔（秒）
            *args: 位置参数
            **kwargs: 关键字参数

        Returns:
            tuple: (是否成功, 结果, 错误信息)
        """
        operation_name = getattr(operation, '__name__', str(operation))

        for attempt in range(max_retries + 1):
            success, result, error = await self.safe_execute(operation, *args, **kwargs)

            if success:
                if attempt > 0:
                    self.service_logger.log_business_event(
                        "重试成功",
                        {"operation": operation_name, "attempts": attempt + 1}
                    )
                return True, result, None

            if attempt < max_retries:
                self.service_logger.log_business_event(
                    "操作重试",
                    {"operation": operation_name, "attempt": attempt + 1, "error": error}
                )
                await asyncio.sleep(delay * (2 ** attempt))  # 指数退避

        return False, None, error

    def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能摘要"""
        return {
            "service": self.__class__.__name__,
            "errors": self.error_tracker.get_error_summary(),
            "timestamp": datetime.now().isoformat()
        }
