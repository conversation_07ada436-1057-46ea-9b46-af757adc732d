"""
DouyinAweme 数据服务

负责处理 DouyinAweme 表的数据库操作
"""

import logging
from typing import List, Optional, Tuple, Union

from mappers.douyin.pydantic_models import DouyinVideoData, DouyinVideoDataItem
from mappers.trendinsight.video_mapper import TrendInsightVideoMapper
from models.douyin.models import DouyinAweme
from schemas.trendinsight import AuthorData, DouyinAwemeData
from services.base import BaseService

# 添加日志记录器
logger = logging.getLogger(__name__)


class DouyinAwemeService(BaseService):
    """DouyinAweme 数据服务类"""

    def __init__(self, logger_instance=None):
        super().__init__(logger_instance)

    @staticmethod
    async def ensure_douyin_aweme_records(
        video_data_list: Union[DouyinVideoData, List[DouyinAwemeData]],
        video_ids: List[str],
        author_data: Optional[AuthorData] = None,
    ) -> Tuple[int, int]:
        """
        确保 DouyinAweme 表中存在所需的记录，智能批量处理创建和更新操作

        改进特性：
        1. 批量查询已存在记录，避免逐条查询
        2. 对已存在记录进行批量更新（统计数据等）
        3. 对不存在记录进行批量创建
        4. 使用集合运算优化性能
        5. 支持传入作者信息，补充视频数据中缺失的用户信息

        Args:
            video_data_list: 转换后的视频数据列表
            video_ids: 视频ID列表
            author_data: 可选的作者信息，用于补充视频数据中缺失的用户信息

        Returns:
            tuple: (已创建记录数量, 已存在/更新记录数量)
        """
        if not video_ids:
            return 0, 0

        logger.info(f"开始处理 {len(video_ids)} 个视频记录...")

        # 0. 标准化输入数据格式
        if isinstance(video_data_list, list):
            # 如果输入是 List[DouyinAwemeData]，转换为 DouyinVideoDataItem 格式
            video_data_items = []
            for aweme_data in video_data_list:
                video_item = DouyinVideoDataItem(
                    aweme_id=aweme_data.aweme_id,
                    aweme_type=aweme_data.aweme_type,
                    title=aweme_data.title,
                    desc=aweme_data.desc,
                    create_time=aweme_data.create_time,
                    user_id=aweme_data.user_id,
                    sec_uid=aweme_data.sec_uid,
                    short_user_id=aweme_data.short_user_id,
                    user_unique_id=aweme_data.user_unique_id,
                    nickname=aweme_data.nickname,
                    avatar=aweme_data.avatar,
                    user_signature=aweme_data.user_signature,
                    ip_location=aweme_data.ip_location,
                    liked_count=aweme_data.liked_count,
                    comment_count=aweme_data.comment_count,
                    share_count=aweme_data.share_count,
                    collected_count=aweme_data.collected_count,
                    aweme_url=aweme_data.aweme_url,
                    cover_url=aweme_data.cover_url,
                    video_download_url=aweme_data.video_download_url,
                    source_keyword=aweme_data.source_keyword,
                )
                video_data_items.append(video_item)
            video_data_list = DouyinVideoData(root=video_data_items)

        # 1. 如果提供了作者信息，补充视频数据中缺失的用户信息
        if author_data:
            logger.info(f"使用作者信息补充视频数据: {author_data.user_name}")
            video_data_list = DouyinAwemeService._enrich_video_data_with_author_info(video_data_list, author_data)

        # 2. 批量查询已存在的记录（关键优化：一次查询获取所有记录）
        existing_records = await DouyinAweme.filter(aweme_id__in=video_ids)
        existing_aweme_ids = {record.aweme_id for record in existing_records}

        logger.info(f"已存在记录: {len(existing_aweme_ids)} 个")

        # 3. 使用集合运算计算需要创建的记录（性能优化）
        all_aweme_ids = set(video_ids)
        new_aweme_ids = all_aweme_ids - existing_aweme_ids

        logger.info(f"需要创建记录: {len(new_aweme_ids)} 个")

        # 4. 批量创建新记录
        records_created = 0
        if new_aweme_ids:
            new_records: List[DouyinAweme] = []

            # 创建映射字典，便于快速查找
            video_data_map = {data.aweme_id: data for data in video_data_list.root}

            for aweme_id in new_aweme_ids:
                video_data = video_data_map.get(aweme_id)
                if video_data:
                    new_record = TrendInsightVideoMapper.douyin_aweme_data_to_model(video_data)
                    new_records.append(new_record)

            # 执行批量创建
            if new_records:
                await DouyinAweme.bulk_create(new_records)
                records_created = len(new_records)
                logger.info(f"成功创建 {records_created} 个新记录")

        # 5. 批量更新已存在的记录（新增功能）
        records_updated = 0
        if existing_records:
            # 创建视频数据映射以便快速查找最新数据
            video_data_map = {data.aweme_id: data for data in video_data_list.root}

            # 准备需要更新的记录
            records_to_update: List[DouyinAweme] = []

            for existing_record in existing_records:
                latest_data = video_data_map.get(existing_record.aweme_id)
                if latest_data:
                    # 检查是否需要更新（避免不必要的更新）
                    needs_update = False

                    # 更新统计数据（这些数据可能会变化）
                    if existing_record.liked_count != latest_data.liked_count:
                        existing_record.liked_count = latest_data.liked_count
                        needs_update = True

                    if existing_record.comment_count != latest_data.comment_count:
                        existing_record.comment_count = latest_data.comment_count
                        needs_update = True

                    if existing_record.share_count != latest_data.share_count:
                        existing_record.share_count = latest_data.share_count
                        needs_update = True

                    if existing_record.collected_count != latest_data.collected_count:
                        existing_record.collected_count = latest_data.collected_count
                        needs_update = True

                    # 更新标题和描述（可能会有修改）
                    if existing_record.title != latest_data.title:
                        existing_record.title = latest_data.title
                        needs_update = True

                    if existing_record.desc != latest_data.desc:
                        existing_record.desc = latest_data.desc
                        needs_update = True

                    # 更新来源关键词（用于追踪）
                    if existing_record.source_keyword != latest_data.source_keyword:
                        existing_record.source_keyword = latest_data.source_keyword
                        needs_update = True

                    if needs_update:
                        records_to_update.append(existing_record)

            # 执行批量更新
            if records_to_update:
                await DouyinAweme.bulk_update(
                    records_to_update,
                    fields=[
                        "liked_count",
                        "comment_count",
                        "share_count",
                        "collected_count",
                        "title",
                        "desc",
                        "source_keyword",
                    ],
                )
                records_updated = len(records_to_update)
                logger.info(f"成功更新 {records_updated} 个已存在记录")

        total_existing = len(existing_aweme_ids)
        logger.info(
            f"DouyinAweme 批量处理完成 - 创建: {records_created}, 更新: {records_updated}, 总计已存在: {total_existing}"
        )

        return records_created, total_existing

    @staticmethod
    def _enrich_video_data_with_author_info(
        video_data_list: DouyinVideoData, author_data: Optional[AuthorData]
    ) -> DouyinVideoData:
        """
        使用作者信息补充视频数据中缺失的用户信息

        Args:
            video_data_list: 原始视频数据列表
            author_data: 作者信息，可以为 None

        Returns:
            DouyinVideoData: 补充了用户信息的视频数据列表
        """
        if not author_data:
            return video_data_list

        enriched_items = []
        for item in video_data_list.root:
            # DouyinVideoDataItem 模型直接更新用户信息字段
            # 如果昵称为空，使用作者信息补充
            if not item.nickname:
                item.nickname = author_data.user_name
            # 如果头像为空，使用作者信息补充
            if not item.avatar:
                item.avatar = author_data.user_head_logo
            enriched_items.append(item)

        return DouyinVideoData(root=enriched_items)
