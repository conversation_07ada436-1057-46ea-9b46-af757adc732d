"""
视频数据同步服务 - 统一处理视频数据同步逻辑
"""

from datetime import datetime
from typing import List, Optional, Tuple

from mappers.trendinsight import TrendInsightVideoMapper
from models.trendinsight import TrendInsightVideo
from schemas.trendinsight import DouyinAwemeData
from services.base import BaseService
from services.base.dto import FilterCriteria, VideoSyncResult
from services.base.exceptions import DatabaseError, ValidationError


class VideoSyncService(BaseService):
    """视频数据同步服务 - 统一处理视频数据同步逻辑"""
    
    def __init__(self, logger=None):
        super().__init__(logger)
    
    async def sync_videos_from_search_results(
        self, 
        videos: List, 
        source_keyword: str,
        filter_criteria: Optional[FilterCriteria] = None
    ) -> VideoSyncResult:
        """
        从搜索结果同步视频数据
        
        Args:
            videos: 搜索返回的视频列表
            source_keyword: 搜索来源关键字
            filter_criteria: 可选的过滤条件
            
        Returns:
            VideoSyncResult: 同步结果
        """
        start_time = datetime.now()
        result = VideoSyncResult()
        
        # 验证输入参数
        self.validate_required_params(videos=videos, source_keyword=source_keyword)
        
        if not videos:
            self.logger.info("搜索结果为空，跳过视频同步")
            return result
        
        try:
            # 1. 转换视频数据格式
            self.logger.debug(f"开始转换 {len(videos)} 个视频数据")
            video_data_list, video_ids = TrendInsightVideoMapper.keyword_videos_to_douyin_aweme_data_list(
                videos=videos, source_keyword=source_keyword
            )
            
            # 2. 应用过滤条件
            if filter_criteria:
                filtered_data = []
                filtered_ids = []
                
                for i, video_data in enumerate(video_data_list):
                    if i < len(video_ids) and filter_criteria.should_include_video(video_data):
                        filtered_data.append(video_data)
                        filtered_ids.append(video_ids[i])
                
                video_data_list = filtered_data
                video_ids = filtered_ids
                
                self.logger.debug(f"过滤后保留 {len(video_data_list)} 个视频")
            
            result.video_ids = video_ids
            
            # 3. 确保 TrendInsightVideo 记录存在
            new_trendinsight_count = await self.ensure_trendinsight_videos(video_ids)
            
            # 4. 确保 DouyinAweme 记录存在（复用现有服务）
            if video_data_list:
                from services.trendinsight import DouyinAwemeService
                
                aweme_created, aweme_updated = await DouyinAwemeService.ensure_douyin_aweme_records(
                    video_data_list=video_data_list, video_ids=video_ids
                )
                
                result.new_videos_count = max(new_trendinsight_count, aweme_created)
                result.updated_videos_count = aweme_updated
                
                self.logger.info(
                    f"视频同步完成: TrendInsight新增 {new_trendinsight_count}, "
                    f"DouyinAweme新增 {aweme_created}, 更新 {aweme_updated}"
                )
            
            await self.log_performance("视频数据同步", start_time, len(video_ids))
            return result
            
        except Exception as e:
            error_msg = f"视频数据同步失败: {str(e)}"
            result.errors.append(error_msg)
            self.logger.error(error_msg)
            raise DatabaseError(error_msg)
    
    async def ensure_trendinsight_videos(self, video_ids: List[str]) -> int:
        """
        确保 TrendInsightVideo 记录存在
        
        Args:
            video_ids: 视频ID列表
            
        Returns:
            int: 新创建的记录数量
        """
        if not video_ids:
            return 0
        
        try:
            # 批量查询已存在的记录
            existing_videos = await TrendInsightVideo.filter(id__in=video_ids).values_list("id", flat=True)
            existing_set = set(existing_videos)
            
            # 计算需要创建的新记录
            new_video_ids = [vid for vid in video_ids if vid not in existing_set]
            
            if new_video_ids:
                # 批量创建新记录
                new_records = []
                for video_id in new_video_ids:
                    record = TrendInsightVideo(
                        id=video_id,
                        trend_score=0.0,
                        trend_radio=0.0,
                        created_at=datetime.now(),
                        updated_at=datetime.now(),
                        is_deleted=False,
                    )
                    new_records.append(record)
                
                await TrendInsightVideo.bulk_create(new_records)
                self.logger.debug(f"创建了 {len(new_records)} 个新的 TrendInsightVideo 记录")
                return len(new_records)
            else:
                self.logger.debug("所有 TrendInsightVideo 记录已存在")
                return 0
                
        except Exception as e:
            error_msg = f"确保 TrendInsightVideo 记录失败: {str(e)}"
            self.logger.error(error_msg)
            raise DatabaseError(error_msg)
    
    async def ensure_douyin_aweme_records(
        self, 
        video_data_list: List[DouyinAwemeData], 
        video_ids: List[str]
    ) -> Tuple[int, int]:
        """
        确保 DouyinAweme 记录存在（复用现有服务）
        
        Args:
            video_data_list: 视频数据列表
            video_ids: 视频ID列表
            
        Returns:
            Tuple[int, int]: (新创建数量, 更新数量)
        """
        try:
            from services.trendinsight import DouyinAwemeService
            
            return await DouyinAwemeService.ensure_douyin_aweme_records(
                video_data_list=video_data_list, video_ids=video_ids
            )
        except Exception as e:
            error_msg = f"确保 DouyinAweme 记录失败: {str(e)}"
            self.logger.error(error_msg)
            raise DatabaseError(error_msg)
