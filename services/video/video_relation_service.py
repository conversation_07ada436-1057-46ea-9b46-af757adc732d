"""
视频关联关系服务 - 管理各种视频关联关系
"""

from datetime import datetime
from typing import List, Set

from models.enums import Platform, SourceType
from models.trendinsight import TrendInsightVideoRelated
from services.base import BaseService
from services.base.dto import RelationResult
from services.base.exceptions import DatabaseError, ValidationError


class VideoRelationService(BaseService):
    """视频关联关系服务 - 管理各种视频关联关系"""
    
    def __init__(self, logger=None):
        super().__init__(logger)
    
    async def create_keyword_video_relations(
        self, 
        keyword_id: str, 
        video_ids: List[str]
    ) -> RelationResult:
        """
        创建关键词-视频关联关系
        
        Args:
            keyword_id: 关键词ID
            video_ids: 视频ID列表
            
        Returns:
            RelationResult: 关联创建结果
        """
        start_time = datetime.now()
        result = RelationResult()
        
        # 验证输入参数
        self.validate_required_params(keyword_id=keyword_id, video_ids=video_ids)
        
        if not video_ids:
            self.logger.info("视频ID列表为空，跳过关联创建")
            return result
        
        try:
            # 1. 批量检查已存在的关联关系
            existing_video_ids = await self.batch_check_existing_relations(
                source_id=keyword_id,
                video_ids=video_ids,
                relation_type="keyword"
            )
            
            result.existing_count = len(existing_video_ids)
            
            # 2. 计算需要创建的新关联
            all_video_ids_set = set(video_ids)
            new_video_ids = all_video_ids_set - existing_video_ids
            
            self.logger.debug(f"关键词 {keyword_id}: 已存在关联 {len(existing_video_ids)} 个，需创建 {len(new_video_ids)} 个")
            
            # 3. 批量创建新关联记录
            if new_video_ids:
                new_relations = []
                for video_id in new_video_ids:
                    relation = TrendInsightVideoRelated(
                        source_type=SourceType.KEYWORD,
                        source_id=keyword_id,
                        video_id=video_id,
                        platform=Platform.DOUYIN,
                        created_at=datetime.now(),
                        updated_at=datetime.now(),
                        is_deleted=False,
                    )
                    new_relations.append(relation)
                
                await TrendInsightVideoRelated.bulk_create(new_relations)
                result.created_count = len(new_relations)
                
                self.logger.info(f"成功创建 {len(new_relations)} 个关键词视频关联记录")
            else:
                self.logger.info("所有关键词视频关联已存在，无需创建")
            
            await self.log_performance("关键词视频关联创建", start_time, len(video_ids))
            return result
            
        except Exception as e:
            error_msg = f"创建关键词视频关联失败: {str(e)}"
            result.errors.append(error_msg)
            self.logger.error(error_msg)
            raise DatabaseError(error_msg)
    
    async def batch_check_existing_relations(
        self, 
        source_id: str, 
        video_ids: List[str], 
        relation_type: str
    ) -> Set[str]:
        """
        批量检查已存在的关联关系
        
        Args:
            source_id: 来源ID
            video_ids: 视频ID列表
            relation_type: 关联类型 ("keyword", "author", etc.)
            
        Returns:
            Set[str]: 已存在关联的视频ID集合
        """
        if not video_ids:
            return set()
        
        try:
            # 根据关联类型确定查询条件
            query_filters = {
                "source_id": source_id,
                "video_id__in": video_ids,
                "is_deleted": False
            }
            
            # 添加特定的源类型过滤
            if relation_type == "keyword":
                query_filters["source_type"] = SourceType.KEYWORD
            elif relation_type == "author":
                query_filters["source_type"] = SourceType.AUTHOR
            
            existing_relations = await TrendInsightVideoRelated.filter(**query_filters).values_list(
                "video_id", flat=True
            )
            
            return set(existing_relations)
            
        except Exception as e:
            error_msg = f"批量检查关联关系失败: {str(e)}"
            self.logger.error(error_msg)
            raise DatabaseError(error_msg)
