"""
用户收件箱服务 - 处理用户订阅关联更新
"""

import uuid
from datetime import datetime
from typing import List

from models.qihaozhushou import UserInboxSourceRelated, UserInboxVideoRelated, SourceType, CrawlerType
from services.base import BaseService
from services.base.dto import UserInboxSyncResult
from services.base.exceptions import DatabaseError, ValidationError
from services.base.database_optimizer import DatabaseOptimizer


class UserInboxService(BaseService):
    """用户收件箱服务 - 处理用户订阅关联更新"""
    
    def __init__(self, logger=None):
        super().__init__(logger)
        self.db_optimizer = DatabaseOptimizer(self.service_logger)
    
    async def sync_user_keyword_subscriptions(
        self,
        keyword_id: str,
        video_ids: List[str]
    ) -> UserInboxSyncResult:
        """
        同步用户关键词订阅关联

        Args:
            keyword_id: 关键词ID
            video_ids: 新增的视频ID列表

        Returns:
            UserInboxSyncResult: 同步结果
        """
        operation_id = self.performance_monitor.start_operation("sync_user_keyword_subscriptions")
        result = UserInboxSyncResult()

        # 验证输入参数
        self.validate_required_params(keyword_id=keyword_id)

        # 记录业务事件
        self.service_logger.log_business_event(
            "开始用户订阅同步",
            {"keyword_id": keyword_id, "video_count": len(video_ids)}
        )

        if not video_ids:
            self.service_logger.log_business_event("跳过同步", {"reason": "视频列表为空"})
            return result

        try:
            # 1. 查找订阅了该关键词的用户
            subscribed_users = await self.find_subscribed_users(keyword_id, SourceType.KEYWORD.value)

            if not subscribed_users:
                self.service_logger.log_business_event(
                    "无订阅用户",
                    {"keyword_id": keyword_id}
                )
                return result

            self.service_logger.log_business_event(
                "找到订阅用户",
                {"keyword_id": keyword_id, "user_count": len(subscribed_users)}
            )

            # 2. 为每个订阅用户创建视频关联
            for i, user_uuid in enumerate(subscribed_users):
                try:
                    user_relations_count = await self.create_user_video_relations(
                        user_uuid, video_ids, "KEYWORD", source_id=keyword_id
                    )

                    if user_relations_count > 0:
                        result.updated_users_count += 1
                        result.new_relations_count += user_relations_count

                        self.service_logger.log_business_event(
                            "用户关联创建成功",
                            {"user_uuid": user_uuid, "relations_count": user_relations_count}
                        )

                    # 更新进度
                    self.performance_monitor.update_progress(operation_id, i + 1, len(result.failed_users))

                except Exception as e:
                    error_msg = f"为用户 {user_uuid} 创建视频关联失败: {str(e)}"
                    result.errors.append(error_msg)
                    result.failed_users.append(user_uuid)

                    self.error_tracker.track_error(
                        "user_video_relation_creation",
                        e,
                        {"user_uuid": user_uuid, "video_count": len(video_ids)}
                    )

            # 完成性能监控
            metrics = self.performance_monitor.finish_operation(operation_id)

            self.service_logger.log_business_event(
                "用户订阅同步完成",
                {
                    "keyword_id": keyword_id,
                    "updated_users": result.updated_users_count,
                    "new_relations": result.new_relations_count,
                    "failed_users": len(result.failed_users),
                    "performance": metrics
                }
            )

            return result

        except Exception as e:
            error_msg = f"同步用户关键词订阅失败: {str(e)}"
            result.errors.append(error_msg)

            self.error_tracker.track_error(
                "sync_user_keyword_subscriptions",
                e,
                {"keyword_id": keyword_id, "video_count": len(video_ids)}
            )

            raise DatabaseError(error_msg)
    
    async def find_subscribed_users(self, source_id: str, source_type: str) -> List[str]:
        """
        查找订阅了特定来源的用户

        Args:
            source_id: 来源ID（关键词ID或作者ID）
            source_type: 来源类型（"keyword" 或 "author"）

        Returns:
            List[str]: 订阅用户的UUID列表
        """
        self.validate_required_params(source_id=source_id, source_type=source_type)

        try:
            # 将字符串 source_type 转换为枚举类型
            source_type_enum = self._convert_source_type_to_enum(source_type)

            # 查询订阅了该来源的用户
            user_uuids = await UserInboxSourceRelated.filter(
                source_id=source_id,
                source_type=source_type_enum,
                is_deleted=False
            ).values_list("user_uuid", flat=True)

            # 去重
            unique_users = list(set(user_uuids))

            self.logger.debug(f"找到 {len(unique_users)} 个用户订阅了 {source_type}:{source_id}")
            return unique_users

        except Exception as e:
            error_msg = f"查找订阅用户失败: {str(e)}"
            self.logger.log_error(error_msg)
            raise DatabaseError(error_msg)

    def _convert_source_type_to_enum(self, source_type: str) -> SourceType:
        """
        将字符串 source_type 转换为 SourceType 枚举类型

        Args:
            source_type: 字符串类型的来源类型

        Returns:
            SourceType: 对应的枚举值

        Raises:
            ValidationError: 如果 source_type 无效
        """
        # 如果传入的是枚举类型，直接返回
        if isinstance(source_type, SourceType):
            return source_type

        # 将字符串转换为大写，以支持大小写不敏感的输入
        source_type_upper = str(source_type).upper()

        # 字符串到枚举的映射
        mapping = {
            "AUTHOR": SourceType.AUTHOR,
            "KEYWORD": SourceType.KEYWORD,
            "COLLECT": SourceType.COLLECT,
        }

        if source_type_upper in mapping:
            return mapping[source_type_upper]
        else:
            valid_types = list(mapping.keys())
            raise ValidationError(f"不支持的来源类型: {source_type}，支持的类型: {valid_types}")

    def _convert_source_type_to_crawler_type(self, source_type: str) -> CrawlerType:
        """
        将字符串 source_type 转换为 CrawlerType 枚举类型

        Args:
            source_type: 字符串类型的来源类型

        Returns:
            CrawlerType: 对应的枚举值

        Raises:
            ValidationError: 如果 source_type 无效
        """
        # 如果传入的是枚举类型，直接返回
        if isinstance(source_type, CrawlerType):
            return source_type

        # 将字符串转换为大写，以支持大小写不敏感的输入
        source_type_upper = str(source_type).upper()

        # 字符串到枚举的映射
        mapping = {
            "AUTHOR": CrawlerType.AUTHOR,
            "KEYWORD": CrawlerType.KEYWORD,
            "COLLECT": CrawlerType.COLLECT,
        }

        if source_type_upper in mapping:
            return mapping[source_type_upper]
        else:
            valid_types = list(mapping.keys())
            raise ValidationError(f"不支持的爬取类型: {source_type}，支持的类型: {valid_types}")
    
    async def create_user_video_relations(
        self,
        user_uuid: str,
        video_ids: List[str],
        source_type: str,
        source_id: str = None
    ) -> int:
        """
        为用户创建视频关联关系（使用 UserInboxVideoRelated 表）

        Args:
            user_uuid: 用户UUID
            video_ids: 视频ID列表（aweme_id）
            source_type: 来源类型（KEYWORD/AUTHOR/COLLECT）
            source_id: 来源ID（关键词ID或作者ID），如果不提供则使用第一个video_id

        Returns:
            int: 创建的关联数量
        """
        self.validate_required_params(user_uuid=user_uuid, video_ids=video_ids, source_type=source_type)

        if not video_ids:
            return 0

        # 如果没有提供 source_id，使用第一个 video_id 作为 source_id
        if source_id is None:
            source_id = video_ids[0] if video_ids else ""

        try:
            # 转换 source_type 为枚举类型
            crawler_type = self._convert_source_type_to_crawler_type(source_type)

            # 1. 使用优化器批量检查已存在的关联
            existing_video_ids = await self.db_optimizer.batch_exists_check(
                model=UserInboxVideoRelated,
                field_name="aweme_id",
                values=video_ids,
                additional_filters={
                    "user_uuid": user_uuid,
                    "source_type": crawler_type,
                    "is_deleted": False
                }
            )

            # 2. 计算需要创建的新关联
            new_video_ids = [vid for vid in video_ids if vid not in existing_video_ids]

            if not new_video_ids:
                self.service_logger.log_business_event(
                    "跳过视频关联创建",
                    {"user_uuid": user_uuid, "reason": "所有关联已存在"}
                )
                return 0

            # 3. 准备批量创建数据
            new_relations = []
            for video_id in new_video_ids:
                relation_uuid = str(uuid.uuid4()).replace('-', '')
                relation = UserInboxVideoRelated(
                    uuid=relation_uuid,
                    user_uuid=user_uuid,
                    source_id=source_id,  # 关键词ID或作者ID
                    source_type=crawler_type,
                    aweme_id=video_id,  # 视频ID
                    publish_time=datetime.now(),  # 默认使用当前时间，实际应该从视频数据中获取
                    is_deleted=False
                )
                new_relations.append(relation)

            # 4. 使用优化器批量创建
            created_count = await self.db_optimizer.batch_create_optimized(
                model=UserInboxVideoRelated,
                records=new_relations,
                batch_size=500
            )

            self.service_logger.log_business_event(
                "用户视频关联创建完成",
                {
                    "user_uuid": user_uuid,
                    "source_id": source_id,
                    "source_type": source_type,
                    "total_videos": len(video_ids),
                    "existing_relations": len(existing_video_ids),
                    "new_relations": created_count
                }
            )

            return created_count

        except Exception as e:
            error_msg = f"创建用户视频关联失败: {str(e)}"
            self.error_tracker.track_error(
                "create_user_video_relations",
                e,
                {"user_uuid": user_uuid, "video_count": len(video_ids)}
            )
            raise DatabaseError(error_msg)
    
    async def update_user_inbox_relations(
        self, 
        user_uuids: List[str], 
        source_id: str, 
        source_type: str
    ) -> int:
        """
        批量更新用户收件箱关联关系
        
        Args:
            user_uuids: 用户UUID列表
            source_id: 来源ID
            source_type: 来源类型
            
        Returns:
            int: 更新的关联数量
        """
        self.validate_required_params(user_uuids=user_uuids, source_id=source_id, source_type=source_type)
        
        if not user_uuids:
            return 0
        
        try:
            # 批量更新时间戳
            updated_count = await UserInboxSourceRelated.filter(
                user_uuid__in=user_uuids,
                source_id=source_id,
                source_type=source_type,
                is_deleted=False
            ).update(update_time=datetime.now())
            
            self.logger.debug(f"批量更新了 {updated_count} 个用户收件箱关联")
            return updated_count
            
        except Exception as e:
            error_msg = f"批量更新用户收件箱关联失败: {str(e)}"
            self.logger.log_error(error_msg)
            raise DatabaseError(error_msg)
