"""
关键词处理服务 - 统一关键词相关操作
"""

import hashlib
from datetime import datetime
from typing import Optional, Tuple

from models.trendinsight import TrendInsightKeyword
from services.base import BaseService
from services.base.dto import KeywordProcessResult
from services.base.exceptions import DatabaseError, ValidationError


class KeywordService(BaseService):
    """关键词处理服务 - 统一关键词相关操作"""
    
    def __init__(self, logger=None):
        super().__init__(logger)
    
    def calculate_keyword_hash(self, keyword: str) -> str:
        """
        计算关键词哈希值
        
        Args:
            keyword: 关键词文本
            
        Returns:
            str: MD5哈希值
        """
        if not keyword:
            raise ValidationError("关键词不能为空")
        
        return hashlib.md5(keyword.encode("utf-8")).hexdigest()
    
    async def ensure_keyword_exists(self, keyword: str) -> <PERSON><PERSON>[TrendInsightKeyword, bool]:
        """
        确保关键词记录存在，返回(关键词对象, 是否新创建)
        
        Args:
            keyword: 关键词文本
            
        Returns:
            Tuple[TrendInsightKeyword, bool]: (关键词对象, 是否新创建)
        """
        self.validate_required_params(keyword=keyword)
        
        try:
            # 计算关键词哈希
            keyword_hash = self.calculate_keyword_hash(keyword)
            
            # 查询是否已存在
            existing_keyword = await TrendInsightKeyword.filter(keyword_hash=keyword_hash).first()
            
            if existing_keyword:
                self.logger.debug(f"关键词已存在: {keyword}")
                return existing_keyword, False
            else:
                # 创建新关键词记录
                new_keyword = await TrendInsightKeyword.create(
                    keyword=keyword,
                    keyword_hash=keyword_hash,
                    video_count=0,
                    created_at=datetime.now(),
                    updated_at=datetime.now(),
                    is_deleted=False,
                )
                
                self.logger.info(f"创建新关键词: {keyword} (ID: {new_keyword.id})")
                return new_keyword, True
                
        except Exception as e:
            error_msg = f"确保关键词存在失败: {str(e)}"
            self.logger.error(error_msg)
            raise DatabaseError(error_msg)
    
    async def update_keyword_video_count(self, keyword_id: str, video_count: int):
        """
        更新关键词视频计数
        
        Args:
            keyword_id: 关键词ID
            video_count: 视频数量
        """
        self.validate_required_params(keyword_id=keyword_id)
        
        if video_count < 0:
            raise ValidationError("视频数量不能为负数")
        
        try:
            keyword = await TrendInsightKeyword.get(id=keyword_id)
            
            if keyword.video_count != video_count:
                keyword.video_count = video_count
                keyword.updated_at = datetime.now()
                await keyword.save()
                
                self.logger.debug(f"更新关键词 {keyword_id} 视频计数: {video_count}")
            else:
                self.logger.debug(f"关键词 {keyword_id} 视频计数无变化: {video_count}")
                
        except TrendInsightKeyword.DoesNotExist:
            raise ValidationError(f"关键词不存在: {keyword_id}")
        except Exception as e:
            error_msg = f"更新关键词视频计数失败: {str(e)}"
            self.logger.error(error_msg)
            raise DatabaseError(error_msg)
    
    async def update_keyword_timestamp(self, keyword_id: str):
        """
        更新关键词时间戳
        
        Args:
            keyword_id: 关键词ID
        """
        self.validate_required_params(keyword_id=keyword_id)
        
        try:
            keyword = await TrendInsightKeyword.get(id=keyword_id)
            keyword.updated_at = datetime.now()
            await keyword.save()
            
            self.logger.debug(f"更新关键词 {keyword_id} 时间戳")
            
        except TrendInsightKeyword.DoesNotExist:
            raise ValidationError(f"关键词不存在: {keyword_id}")
        except Exception as e:
            error_msg = f"更新关键词时间戳失败: {str(e)}"
            self.logger.error(error_msg)
            raise DatabaseError(error_msg)
    
    async def process_keyword(
        self, 
        keyword: str, 
        video_count: Optional[int] = None
    ) -> KeywordProcessResult:
        """
        完整处理关键词（确保存在、更新计数、更新时间戳）
        
        Args:
            keyword: 关键词文本
            video_count: 可选的视频数量，如果提供则更新计数
            
        Returns:
            KeywordProcessResult: 处理结果
        """
        start_time = datetime.now()
        
        try:
            # 1. 确保关键词存在
            keyword_obj, is_new = await self.ensure_keyword_exists(keyword)
            
            result = KeywordProcessResult(
                keyword_id=str(keyword_obj.id),
                keyword_text=keyword,
                is_new_keyword=is_new
            )
            
            # 2. 更新视频计数（如果提供）
            if video_count is not None:
                await self.update_keyword_video_count(str(keyword_obj.id), video_count)
                result.video_count_updated = True
            
            # 3. 更新时间戳
            await self.update_keyword_timestamp(str(keyword_obj.id))
            result.timestamp_updated = True
            
            await self.log_performance("关键词处理", start_time)
            return result
            
        except Exception as e:
            error_msg = f"处理关键词失败: {str(e)}"
            self.logger.error(error_msg)
            result = KeywordProcessResult(
                keyword_id="",
                keyword_text=keyword,
                errors=[error_msg]
            )
            return result
