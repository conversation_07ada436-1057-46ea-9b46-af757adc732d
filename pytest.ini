[pytest]
# pytest 配置文件

# 测试发现 - 扫描项目内所有目录
testpaths =
    .
    tests
    rpc
    api
    controllers
    core
    models
    pkg
    schemas
    utils
    ai
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# 异步测试支持
asyncio_mode = auto
asyncio_default_fixture_loop_scope = function

# 输出配置
addopts =
    -v
    --tb=short
    --strict-markers
    --disable-warnings
    --color=yes
    -s
    --log-cli-level=INFO
    --log-cli-format="%(asctime)s [%(levelname)8s] %(name)s: %(message)s"
    --log-cli-date-format="%Y-%m-%d %H:%M:%S"

# 标记定义
markers =
    unit: 单元测试
    integration: 集成测试 - 真实调用第三方服务
    performance: 性能测试
    slow: 慢速测试
    network: 需要网络的测试
    real_api: 真实API调用测试 - 仅本地开发使用
    ci_skip: CI环境跳过的测试

# 最小版本要求
minversion = 6.0

# 过滤警告
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:httpx.*
    ignore::UserWarning:pydantic.*
