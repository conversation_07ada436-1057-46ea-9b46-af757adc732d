# Python 后端项目

这是一个独立的 Python FastAPI 后端项目，已从原混合项目中重构而来。

## 项目结构

```
app/
├── api/                    # API 路由
│   └── v1/                # API v1 版本
├── controllers/           # 业务逻辑控制器
├── core/                  # 核心功能模块
├── log/                   # 日志模块
├── models/                # 数据模型
├── rpc/                   # RPC 客户端
├── schemas/               # Pydantic 模式
├── settings/              # 配置文件
├── utils/                 # 工具函数
├── run.py                 # 应用启动文件
├── requirements.txt       # Python 依赖
├── pyproject.toml        # 项目配置
├── uv.lock               # 依赖锁定文件
├── Makefile              # 构建工具
└── start.sh              # 启动脚本
```

## 快速开始

### 1. 安装依赖

```bash
# 推荐使用虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或者 venv\Scripts\activate  # Windows

# 安装依赖
pip install -r requirements.txt
```

### 2. 启动服务

```bash
# 方法1: 使用启动脚本
./start.sh

# 方法2: 直接运行
python run.py

# 方法3: 使用 Makefile
make start
```

### 3. 访问服务

- 服务地址: http://localhost:8000
- API 文档: http://localhost:8000/docs
- ReDoc 文档: http://localhost:8000/redoc

## 开发工具

```bash
# 代码格式化
make format

# 代码检查
make check

# 运行测试
make test

# 数据库迁移
make migrate
make upgrade

# 清理数据库
make clean-db
```

## 配置说明

主要配置文件位于 `settings/config.py`，包括：

- 数据库配置
- JWT 配置
- 日志配置
- 应用设置

## 数据库

项目使用 SQLite 数据库，数据库文件为 `db.sqlite3`。

使用 Tortoise ORM 进行数据库操作，使用 Aerich 进行数据库迁移。

## 部署说明

本项目已针对 serverless 部署进行优化，移除了 Docker 和传统部署相关文件。

## 注意事项

1. 确保 Python 版本 >= 3.11
2. 建议使用虚拟环境
3. 首次运行前需要安装依赖
4. 数据库会自动创建和初始化
