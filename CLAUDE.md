# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Running the Application
```bash
# Start the FastAPI server
python run.py
# or
make start
# or
./start.sh
```

### Code Quality and Testing
```bash
# Run all checks (format check + lint)
make check

# Format code
make format

# Run linting
make lint

# Run tests
make test

# Run specific test file
pytest tests/test_specific.py -v
```

### Database Management
```bash
# Generate migration files
make migrate

# Apply database migrations
make upgrade

# Clean database and migrations
make clean-db
```

### Dependencies
```bash
# Install dependencies
pip install -r requirements.txt

# For development with UV
uv add pyproject.toml
```

## Project Architecture

### FastAPI Application Structure
- **Core Application**: FastAPI app initialized in `core/init_app.py`
- **Entry Point**: `run.py` starts uvicorn server on port 8000
- **Database**: SQLite with Tortoise ORM, configured in `settings/config.py`
- **Authentication**: JWT-based auth with 7-day token expiration

### Key Components
- **API Routes**: Organized in `api/` with versioned endpoints (`api/v1/`)
- **Controllers**: Business logic in `controllers/` directory
- **Models**: Database models in `models/` with platform-specific subdirectories
- **Schemas**: Pydantic models in `schemas/` for request/response validation
- **RPC Services**: External service clients in `rpc/` (Douyin, TrendInsight)

### RPC Client Architecture
The project includes specialized RPC clients for social media platforms:
- **Douyin Client**: `rpc/douyin/client.py` - TikTok-like platform integration
- **TrendInsight Client**: `rpc/trendinsight/client.py` - Analytics platform integration

Both clients include:
- Signature generation with JavaScript execution
- Request parameter validation
- Comprehensive test suites
- Integration tests for real API calls

### Database Configuration
- **ORM**: Tortoise ORM with async support
- **Default**: SQLite database (`db.sqlite3`)
- **Migrations**: Aerich for database schema management
- **Timezone**: Asia/Shanghai

### Testing Configuration
- **Framework**: pytest with asyncio support
- **Test Discovery**: Scans all directories for `test_*.py` files
- **Markers**: `unit`, `integration`, `performance`, `network`, `real_api`
- **CI Integration**: Special markers for CI environment tests

## Development Guidelines

### Code Style
- **Formatter**: Black with 120 character line length
- **Linter**: Ruff with custom ignore rules (F403, F405)
- **Import Sorting**: isort with black profile
- **Python Version**: >= 3.11 required

### Testing Strategy
- Unit tests for individual components
- Integration tests for external API calls
- Mock data provided in `tests/mock/` directories
- Real API tests marked with `@pytest.mark.real_api` (local development only)

### Key Features
- **Auto-initialization**: Database, superuser, menus, and roles created on first run
- **CORS Configuration**: Configured for development with wildcard origins
- **Middleware Stack**: Audit logging, background tasks, and CORS handling
- **Exception Handling**: Centralized error handling with custom exceptions

## Environment Setup

### Required Python Version
Python >= 3.11

### Database Initialization
Database and initial data are automatically created on first application startup including:
- Admin superuser (username: admin, password: 123456)
- System management menus
- Basic roles and permissions
- API endpoint registration

### API Documentation
- Swagger UI: port 8000/docs
- ReDoc: port 8000/redoc