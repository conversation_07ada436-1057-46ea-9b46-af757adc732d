name: Layer Publish

on:
  push:
    branches:
      - layer
    paths:
      - pyproject.toml
      - .github/workflows/layer.yml

env:
  SERVICE_NAME: 起号助手爬虫 Layer
  LAYER_NAME: mediacrawler-python312

jobs:
  vars:
    runs-on: ubuntu-22.04
    outputs:
      service-name: ${{ env.SERVICE_NAME }}
      service-url: ${{ vars.TEST_MANAGEMENT_URL }}
      version: ${{ steps.get-version.outputs.version }}
    steps:
      - uses: kokoroX/get-version-action@main
        id: get-version

  serverless-devs-cd:
    needs: [vars]
    runs-on: ubuntu-22.04
    environment: taidong
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v2
        with:
          node-version: 16
          registry-url: https://registry.npmjs.org/
      - run: npm install -g @serverless-devs/s

      - name: Install uv
        uses: astral-sh/setup-uv@v5
        with:
          # Install a specific version of uv.
          version: "0.7.13"
          python-version: 3.12
      - run: uv pip install --target ./lib/python -r <(uv pip compile pyproject.toml)
      - run: cd ./lib/python && ls
      - run: s config add --AccessKeyID ${{secrets.ALIYUN_ACCESS_KEY_ID}} --AccessKeySecret ${{secrets.ALIYUN_ACCESS_KEY_SECRET}} -a tdid -f
      # --code ./lib/ 必须带结尾的 / 不然文件夹会变 hash 名
      # 自动打包的 layer 存在问题 需要自己下载了解包重新打包上传
      - run: s cli fc3 layer publish --access tdid --region cn-shanghai --layer-name ${{ env.LAYER_NAME }} --code ./lib/ --compatible-runtime "custom.debian12,python3.12"


  feishu-success-notice:
    needs: [vars, serverless-devs-cd]
    uses: 0xTeams/reuse-workflows/.github/workflows/feishu.success.notice.yml@main
    secrets: inherit
    with:
      version: ${{ needs.vars.outputs.version }}
      service-name: ${{ needs.vars.outputs.service-name }}
      service-url: ${{ needs.vars.outputs.service-url }}
      environment-name: 钛动环境


  feishu-failure-notice:
    if: failure()
    needs: [vars, serverless-devs-cd]
    uses: 0xTeams/reuse-workflows/.github/workflows/feishu.failure.notice.yml@main
    secrets: inherit
    with:
      service-name: ${{ needs.vars.outputs.service-name }}
      version: ${{ needs.vars.outputs.version }}
      environment-name: 钛动环境