"""
快代理管理器
"""

from rpc.kuaidaili.client import get_kuaidaili_client
from settings.config import settings


class KuaidailiManager:
    """快代理管理器，用于获取和格式化代理 IP"""

    def __init__(self):
        self.username = settings.get("kuaida<PERSON>_username")
        self.password = settings.get("kuaidaili_password")

    async def get_formatted_proxy(self, num: int = 1, **kwargs) -> list[str]:
        """
        获取格式化后的代理 IP 列表

        :param num: 获取的代理数量
        :param kwargs: 传递给 get_dps 的其他参数
        :return: 格式为 'ip:port:username:password' 的代理列表
        """
        formatted_proxies = []
        async with get_kuaidaili_client() as client:
            response = await client.get_dps(num=num, **kwargs)
            if response and response.code == 0 and response.data:
                for proxy in response.data.proxy_list:
                    formatted_proxies.append(f"{proxy}:{self.username}:{self.password}")
        return formatted_proxies


def get_kuaidaili_manager() -> KuaidailiManager:
    """获取快代理管理器的实例"""
    return KuaidailiManager()
