"""
快代理 RPC 客户端实现
"""

from typing import Optional

import httpx

from .schemas import GetDpsParams, GetDpsResponse


class KuaidailiRpcClient:
    """快代理 RPC 客户端"""

    def __init__(self, secret_id: str, signature: str, base_url: str):
        """
        初始化快代理 RPC 客户端

        Args:
            secret_id: 订单 SecretId (必需)
            signature: 订单 signature (必需, 用于 token 签名)
            base_url: API 基础URL (必需)
        """
        if not secret_id:
            raise ValueError("secret_id 不能为空")
        if not signature:
            raise ValueError("signature 不能为空")
        if not base_url:
            raise ValueError("base_url 不能为空")

        self.secret_id = secret_id
        self.signature = signature
        self.base_url = base_url
        self.http_client = httpx.AsyncClient()

    async def get_dps(
        self,
        num: int = 1,
        area: Optional[str] = None,
        area_ex: Optional[str] = None,
        dedup: Optional[int] = None,
        carrier: Optional[int] = None,
        format: str = "json",
    ) -> GetDpsResponse:
        """
        调用快代理 getdps 接口获取代理

        Args:
            num: 获取数量
            area: 地区筛选
            area_ex: 排除地区
            dedup: 过滤今日已提取
            carrier: 运营商筛选
            format: 返回格式

        Returns:
            GetDpsResponse: 包含代理列表的响应对象

        Raises:
            Exception: 请求或 API 错误时抛出
        """
        # 构建请求参数
        params = GetDpsParams(
            secret_id=self.secret_id,
            signature=self.signature,
            num=num,
            area=area,
            area_ex=area_ex,
            dedup=dedup,
            carrier=carrier,
            format=format,
        )

        # 过滤掉值为 None 的字段
        request_params = params.model_dump(exclude_none=True)

        try:
            # 构建完整的 API URL
            api_url = f"{self.base_url}/api/getdps"

            # 发起请求
            response = await self.http_client.get(api_url, params=request_params)
            response.raise_for_status()  # 如果状态码不是 2xx，则抛出异常

            # 解析响应
            response_data = response.json()

            # 检查业务状态码
            if response_data.get("code") != 0:
                error_msg = response_data.get("msg", "未知错误")
                raise Exception(f"Kuaidaili API Error: {error_msg}")

            # 解析并返回响应数据
            return GetDpsResponse(**response_data)

        except httpx.HTTPStatusError as e:
            raise Exception(f"HTTP 请求失败: {e.response.status_code} - {e.response.text}")
        except Exception as e:
            if "Kuaidaili API Error" in str(e):
                raise e
            raise Exception(f"请求处理失败: {str(e)}")

    async def close(self):
        """关闭 HTTP 客户端"""
        await self.http_client.aclose()

    async def __aenter__(self):
        """异步上下文管理器入口"""
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.close()


# 创建全局客户端实例 (可选)
def get_kuaidaili_client() -> KuaidailiRpcClient:
    """
    获取快代理客户端实例

    这是一个工厂函数，可以在 FastAPI 的依赖注入中使用
    从配置文件中读取必要的参数
    """
    from settings.config import settings

    secret_id = settings.KUAIDAILI_DPS_SECRET_ID
    signature = settings.KUAIDAILI_DPS_SIGNATURE
    base_url = settings.KUAIDAILI_API_BASE_URL

    if not secret_id:
        raise ValueError("配置中缺少 KUAIDAILI_DPS_SECRET_ID")
    if not signature:
        raise ValueError("配置中缺少 KUAIDAILI_DPS_SIGNATURE")
    if not base_url:
        raise ValueError("配置中缺少 KUAIDAILI_API_BASE_URL")

    return KuaidailiRpcClient(secret_id=secret_id, signature=signature, base_url=base_url)
