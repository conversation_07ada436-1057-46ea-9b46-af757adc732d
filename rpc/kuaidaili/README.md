# 快代理 RPC 客户端

这是一个用于调用快代理 `getdps` 接口的异步 RPC 客户端实现。

## 功能特性

- ✅ 异步 HTTP 请求，完美兼容 FastAPI
- ✅ 类型安全的 Pydantic 数据模型
- ✅ 自动错误处理和异常管理
- ✅ 支持上下文管理器（自动资源清理）
- ✅ 可配置的参数（数量、协议类型、地区等）
- ✅ FastAPI 依赖注入支持
- ✅ 参数验证和配置解耦

## 快速开始

### 1. 配置

在 `settings/config.py` 中配置您的快代理信息：

```python
KUAIDAILI_DPS_ORDER_ID: str = "your_order_id"
KUAIDAILI_API_BASE_URL: str = "https://dps.kdlapi.com"
```

### 2. 基础使用

#### 方式一：使用工厂函数（推荐）

```python
from rpc.kuaidaili.client import get_kuaidaili_client

# 使用工厂函数，自动从配置文件读取参数
async with get_kuaidaili_client() as client:
    response = await client.get_dps(num=5)
    for proxy in response.data.proxy_list:
        print(proxy)  # 输出格式：IP:PORT
```

#### 方式二：直接指定参数

```python
from rpc.kuaidaili.client import KuaidailiRpcClient

# 直接指定参数，不依赖配置文件
async with KuaidailiRpcClient(
    order_id="your_order_id",
    base_url="https://dps.kdlapi.com"
) as client:
    response = await client.get_dps(num=5)
    for proxy in response.data.proxy_list:
        print(proxy)
```

### 3. 在 FastAPI 中使用

```python
from fastapi import FastAPI, Depends
from rpc.kuaidaili.client import KuaidailiRpcClient, get_kuaidaili_client

app = FastAPI()

@app.get("/proxy")
async def get_proxy(client: KuaidailiRpcClient = Depends(get_kuaidaili_client)):
    response = await client.get_dps(num=5)
    return {
        "proxies": response.data.proxy_list,
        "count": response.data.count,
        "order_left_count": response.data.order_left_count
    }
```

### 4. 高级用法

```python
# 获取指定地区的 HTTPS 代理
response = await client.get_dps(
    num=10,
    pt=2,  # HTTPS
    area="北京"
)

print(f"获取到 {response.data.count} 个代理")
print(f"订单剩余: {response.data.order_left_count}")
```

## API 参数

| 参数 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| `num` | int | 1 | 获取代理数量 (1-1000) |
| `pt` | Optional[int] | None | 协议类型 (1: HTTP, 2: HTTPS) |
| `area` | Optional[str] | None | 地区筛选 |
| `format` | str | "json" | 返回格式 |

## 响应数据结构

```python
class GetDpsResponse(BaseModel):
    code: int  # 状态码，0表示成功
    msg: str   # 响应消息
    data: GetDpsData  # 代理数据

class GetDpsData(BaseModel):
    count: int  # 实际返回的代理数量
    proxy_list: List[str]  # 代理列表，格式：["IP:PORT", ...]
    order_left_count: int  # 订单剩余数量
    dedup_count: int  # 去重后的数量
```

## 错误处理

客户端会自动处理以下错误：

- 参数验证错误（空的 order_id 或 base_url）
- HTTP 请求错误（网络问题、超时等）
- 快代理 API 业务错误（订单过期、余额不足等）
- 数据解析错误

```python
try:
    # 参数验证
    client = KuaidailiRpcClient(
        order_id="your_order_id",
        base_url="https://dps.kdlapi.com"
    )
    
    # API 调用
    response = await client.get_dps(num=5)
    
except ValueError as e:
    print(f"参数错误: {e}")
except Exception as e:
    print(f"获取代理失败: {e}")
```

## 文件结构

```text
rpc/kuaidaili/
├── __init__.py          # 模块导出
├── client.py            # 核心客户端实现
├── utils.py             # 工具函数
├── examples.py          # 使用示例
├── tests/               # 测试目录
│   ├── __init__.py
│   ├── test_client.py   # 单元测试
│   └── test_real_api.py # 真实 API 测试
└── README.md            # 文档
```

## 测试

### 运行单元测试

```bash
# 运行所有单元测试
python -m pytest rpc/kuaidaili/tests/test_client.py -v

# 只运行单元测试（跳过真实 API 测试）
python -m pytest rpc/kuaidaili/tests/ -m "unit" -v
```

### 运行真实 API 测试

```bash
# 运行真实 API 测试（需要有效的配置）
python -m pytest rpc/kuaidaili/tests/test_real_api.py -m "real_api" -v
```

### 运行示例

```bash
python -m rpc.kuaidaili.examples
```

## 设计优势

### 1. 配置解耦
- 客户端不直接依赖 `settings`，提高了可测试性
- 支持运行时动态配置
- 工厂函数负责配置读取，职责分离清晰

### 2. 参数验证
- 构造函数进行参数验证，快速失败
- 明确的错误信息，便于调试

### 3. 灵活的使用方式
- 工厂函数：适合大多数场景，自动读取配置
- 直接构造：适合需要动态配置的场景

### 4. 完善的测试覆盖
- 单元测试：使用 mock，快速执行
- 真实 API 测试：验证实际集成，标记为可选

## 注意事项

1. 请确保您有有效的快代理订单 ID
2. 建议使用上下文管理器或依赖注入来自动管理资源
3. 在生产环境中请适当设置请求超时和重试机制
4. 真实 API 测试需要有效的配置，在 CI/CD 中会被跳过
