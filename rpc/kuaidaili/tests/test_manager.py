"""
快代理管理器测试
"""

from unittest.mock import AsyncMock, patch

import pytest

from rpc.kuaidaili.manager import KuaidailiManager
from rpc.kuaidaili.schemas.kuaidaili import GetDpsData, GetDpsResponse
from rpc.kuaidaili.utils import validate_proxy_with_auth_format


@pytest.mark.asyncio
async def test_get_formatted_proxy():
    """测试获取格式化后的代理 IP"""
    # 模拟 settings
    with patch("rpc.kuaidaili.manager.settings") as mock_settings:
        mock_settings.get.side_effect = lambda key: {
            "kuaidaili_username": "testuser",
            "kuaidaili_password": "testpass",
        }.get(key)

        # 模拟 get_kuaidaili_client
        mock_response = GetDpsResponse(
            code=0,
            msg="success",
            data=GetDpsData(
                count=1, proxy_list=["127.0.0.1:8080"], order_left_count=100, dedup_count=0, today_left_count=100
            ),
        )

        mock_client = AsyncMock()
        mock_client.get_dps.return_value = mock_response

        # 创建一个异步上下文管理器模拟
        class AsyncContextManager:
            async def __aenter__(self):
                return mock_client

            async def __aexit__(self, exc_type, exc, tb):
                pass

        with patch("rpc.kuaidaili.manager.get_kuaidaili_client", return_value=AsyncContextManager()):
            manager = KuaidailiManager()
            proxies = await manager.get_formatted_proxy(num=1)

            assert len(proxies) == 1
            expected_proxy = "127.0.0.1:8080:testuser:testpass"
            assert proxies[0] == expected_proxy
            assert validate_proxy_with_auth_format(proxies[0])


@pytest.mark.asyncio
async def test_get_formatted_proxy_api_error():
    """测试 API 返回错误时的情况"""
    with patch("rpc.kuaidaili.manager.settings") as mock_settings:
        mock_settings.get.side_effect = lambda key: {
            "kuaidaili_username": "testuser",
            "kuaidaili_password": "testpass",
        }.get(key)

        mock_response = GetDpsResponse(code=-1, msg="error", data=None)
        mock_client = AsyncMock()
        mock_client.get_dps.return_value = mock_response

        class AsyncContextManager:
            async def __aenter__(self):
                return mock_client

            async def __aexit__(self, exc_type, exc, tb):
                pass

        with patch("rpc.kuaidaili.manager.get_kuaidaili_client", return_value=AsyncContextManager()):
            manager = KuaidailiManager()
            proxies = await manager.get_formatted_proxy(num=1)
            assert len(proxies) == 0
