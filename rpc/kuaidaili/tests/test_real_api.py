"""
快代理 RPC 客户端真实 API 测试

这些测试会调用真实的快代理 API 服务，仅用于本地开发和调试。
在 CI/CD 环境中会被跳过。

使用方法：
1. 确保在 settings 中配置了有效的快代理订单ID和API地址
2. 运行测试：pytest rpc/kuaidaili/tests/test_real_api.py -m real_api
"""

import asyncio
import logging

import pytest

from rpc.kuaidaili.schemas import GetDpsResponse
from rpc.kuaidaili.utils import validate_proxy_format
from settings.config import settings


@pytest.mark.real_api
@pytest.mark.ci_skip
class TestKuaidailiRealApi:
    """快代理真实 API 测试类"""

    @pytest.fixture
    async def real_client(self):
        """真实客户端 fixture"""
        # 检查是否配置了必要的参数
        if not all(
            [settings.KUAIDAILI_DPS_SECRET_ID, settings.KUAIDAILI_DPS_SIGNATURE, settings.KUAIDAILI_API_BASE_URL]
        ):
            pytest.skip("快代理配置未设置 (SECRET_ID, SIGNATURE, BASE_URL)，跳过真实 API 测试")

        from rpc.kuaidaili.client import get_kuaidaili_client

        client = get_kuaidaili_client()
        yield client
        await client.close()

    @pytest.mark.asyncio
    async def test_real_get_dps_single(self, real_client):
        """测试获取单个代理"""
        try:
            result = await real_client.get_dps(num=1)

            assert isinstance(result, GetDpsResponse)
            assert result.code == 0
            assert result.msg is not None
            assert result.data.count >= 0
            assert isinstance(result.data.proxy_list, list)
            assert result.data.order_left_count >= 0

            if result.data.count > 0:
                # 验证代理格式
                proxy = result.data.proxy_list[0]
                assert validate_proxy_format(proxy), f"代理格式无效: {proxy}"

            logging.info(f"✓ 成功获取 {result.data.count} 个代理")
            logging.info(f"✓ 订单剩余: {result.data.order_left_count}")

        except Exception as e:
            pytest.fail(f"真实 API 调用失败: {str(e)}")

    @pytest.mark.asyncio
    async def test_real_get_dps_multiple(self, real_client):
        """测试获取多个代理"""
        try:
            result = await real_client.get_dps(num=5)

            assert isinstance(result, GetDpsResponse)
            assert result.code == 0
            assert result.data.count <= 5  # 实际返回数量可能少于请求数量

            # 验证所有代理格式
            for proxy in result.data.proxy_list:
                assert ":" in proxy
                ip, port = proxy.split(":")
                assert len(ip.split(".")) == 4
                assert port.isdigit()

            logging.info(f"✓ 成功获取 {result.data.count} 个代理")

        except Exception as e:
            pytest.fail(f"真实 API 调用失败: {str(e)}")

    @pytest.mark.asyncio
    async def test_real_context_manager(self):
        """测试真实环境下的上下文管理器"""
        if not all(
            [settings.KUAIDAILI_DPS_SECRET_ID, settings.KUAIDAILI_DPS_SIGNATURE, settings.KUAIDAILI_API_BASE_URL]
        ):
            pytest.skip("快代理配置未设置，跳过真实 API 测试")

        try:
            from rpc.kuaidaili.client import get_kuaidaili_client

            async with get_kuaidaili_client() as client:
                result = await client.get_dps(num=1)
                assert isinstance(result, GetDpsResponse)
                logging.info("✓ 上下文管理器测试通过")

        except Exception as e:
            pytest.fail(f"上下文管理器测试失败: {str(e)}")


# 运行真实 API 测试的脚本
if __name__ == "__main__":
    logging.info("运行快代理真实 API 测试...")
    logging.info("注意：这将调用真实的快代理 API 服务")

    async def run_real_tests():
        """运行真实 API 测试"""
        try:
            # 检查配置
            if not settings.KUAIDAILI_DPS_SECRET_ID:
                logging.error("✗ 未配置 KUAIDAILI_DPS_SECRET_ID")
                return

            if not settings.KUAIDAILI_DPS_SIGNATURE:
                logging.error("✗ 未配置 KUAIDAILI_DPS_SIGNATURE")
                return

            if not settings.KUAIDAILI_API_BASE_URL:
                logging.error("✗ 未配置 KUAIDAILI_API_BASE_URL")
                return

            # 创建客户端并测试
            from rpc.kuaidaili.client import get_kuaidaili_client

            async with get_kuaidaili_client() as client:
                logging.info("✓ 客户端创建成功")

                # 测试获取代理
                result = await client.get_dps(num=1)
                logging.info(f"✓ API 调用成功，获取 {result.data.count} 个代理")
                logging.info(f"✓ 订单剩余: {result.data.order_left_count}")

                if result.data.proxy_list:
                    logging.info(f"✓ 示例代理: {result.data.proxy_list[0]}")

        except Exception as e:
            logging.error(f"✗ 测试失败: {e}")

    asyncio.run(run_real_tests())
    logging.info("测试完成")
