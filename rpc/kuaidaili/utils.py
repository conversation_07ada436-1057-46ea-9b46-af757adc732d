"""
快代理 RPC 工具函数
"""

from typing import AsyncGenerator

from .client import KuaidailiRpcClient


async def get_kuaidaili_client() -> AsyncGenerator[KuaidailiRpcClient, None]:
    """
    获取快代理客户端实例的依赖注入函数

    可在 FastAPI 路由中使用：
    @app.get("/proxy")
    async def get_proxy(client: KuaidailiRpcClient = Depends(get_kuaidaili_client)):
        response = await client.get_dps(num=5)
        return response.data
    """
    client = KuaidailiRpcClient()
    try:
        yield client
    finally:
        await client.close()


def validate_proxy_with_auth_format(proxy: str) -> bool:
    """
    验证带认证信息的代理 IP 格式是否正确

    :param proxy: 代理 IP 字符串，例如 "127.0.0.1:8080:user:pass"
    :return: 如果格式正确则返回 True，否则返回 False
    """
    if not isinstance(proxy, str):
        return False

    parts = proxy.split(":")
    if len(parts) != 4:
        return False

    ip, port, username, password = parts

    # 验证 ip:port 部分
    if not validate_proxy_format(f"{ip}:{port}"):
        return False

    # 验证用户名和密码不为空
    if not username or not password:
        return False

    return True


def validate_proxy_format(proxy: str) -> bool:
    """
    验证代理 IP 格式是否正确

    :param proxy: 代理 IP 字符串，例如 "127.0.0.1:8080"
    :return: 如果格式正确则返回 True，否则返回 False
    """
    if not isinstance(proxy, str) or ":" not in proxy:
        return False

    parts = proxy.split(":")
    if len(parts) != 2:
        return False

    ip, port = parts

    # 验证 IP 地址格式 (简易)
    ip_parts = ip.split(".")
    if len(ip_parts) != 4:
        return False
    for part in ip_parts:
        if not part.isdigit() or not 0 <= int(part) <= 255:
            return False

    # 验证端口号
    if not port.isdigit() or not 0 <= int(port) <= 65535:
        return False

    return True
