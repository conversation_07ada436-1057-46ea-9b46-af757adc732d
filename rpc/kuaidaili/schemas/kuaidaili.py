"""
快代理相关的数据模型
"""

from typing import List, Optional

from pydantic import BaseModel, Field, field_validator


class GetDpsParams(BaseModel):
    """getdps 接口请求参数模型"""

    secret_id: str = Field(description="订单 SecretId")
    signature: str = Field(description="请求签名")
    num: int = Field(default=1, ge=1, description="提取数量")
    format: str = Field(default="json", description="返回内容的格式 (json, text, xml)")
    area: Optional[str] = Field(default=None, description="地区筛选，多个地区用英文逗号分隔")
    area_ex: Optional[str] = Field(default=None, description="排除地区，多个地区用英文逗号分隔")
    dedup: Optional[int] = Field(default=None, description="过滤今天提取过的IP (传1过滤)")
    carrier: Optional[int] = Field(default=None, description="运营商筛选 (0:不筛选, 1:联通, 2:电信, 3:移动)")

    @field_validator("secret_id", mode="before")
    @classmethod
    def validate_secret_id(cls, v):
        """将 secret_id 转换为字符串类型"""
        if isinstance(v, (int, float)):
            return str(v)
        return v


class GetDpsData(BaseModel):
    """getdps 接口返回的 data 字段模型"""

    count: int = Field(description="返回的代理数量")
    proxy_list: List[str] = Field(default_factory=list, description="代理列表，格式为 'IP:端口'")
    order_left_count: Optional[int] = Field(default=None, description="订单剩余提取余额(按IP付费订单专有)")
    dedup_count: Optional[int] = Field(
        default=None, description="返回的不重复的代理ip数量(按IP付费、包年包月集中提取订单专有)"
    )
    today_left_count: Optional[int] = Field(default=None, description="今天剩余提取余额(包年包月集中提取订单专有)")


class GetDpsResponse(BaseModel):
    """getdps 接口成功响应模型 (JSON 格式)"""

    code: int = Field(description="状态码，0表示成功")
    msg: str = Field(description="响应消息")
    data: Optional[GetDpsData] = Field(default=None, description="响应数据")


class KuaidailiError(BaseModel):
    """快代理 API 错误响应模型"""

    code: int = Field(description="错误码")
    msg: str = Field(description="错误消息")
