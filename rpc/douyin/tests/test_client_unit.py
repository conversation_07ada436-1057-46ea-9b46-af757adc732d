"""
抖音客户端单元测试

只包含不依赖外部服务的单元测试
"""

import pytest

# 直接导入，不使用 try-except
from rpc.douyin import (
    AwemeCommentsRequest,
    CollectVideoListRequest,
    DouyinConfig,
    DouyinSignRequest,
    DouyinValidationError,
    SearchInfoRequest,
    UserAwemePostsRequest,
    UserInfoRequest,
    VideoDetailRequest,
    async_douyin_api,
)

# 导入算签客户端和签名请求模型
from rpc.douyin.utils import DouyinSignClient


# 简单的功能测试
@pytest.mark.unit
def test_imports():
    """测试导入"""
    assert async_douyin_api is not None
    assert DouyinConfig is not None
    assert CollectVideoListRequest is not None


@pytest.mark.unit
def test_pytest_working():
    """测试 pytest 是否正常工作"""
    assert True
    assert 1 + 1 == 2


@pytest.mark.unit
class TestDouyinClientUnit:
    """抖音客户端单元测试类"""

    @pytest.fixture
    def config(self):
        """测试配置"""
        return DouyinConfig(enable_logging=False, enable_sign=False, timeout=5.0, max_retries=1)

    def test_config_creation(self, config):
        """测试配置创建"""
        assert config.enable_logging is False
        assert config.enable_sign is False
        assert config.timeout == 5.0
        assert config.max_retries == 1

    def test_config_validation(self):
        """测试配置验证"""
        # 测试有效配置
        valid_config = DouyinConfig(enable_logging=True, enable_sign=True, timeout=10.0, max_retries=3)
        assert valid_config.enable_logging is True
        assert valid_config.enable_sign is True
        assert valid_config.timeout == 10.0
        assert valid_config.max_retries == 3

    def test_request_model_validation(self):
        """测试请求模型验证"""
        # 测试有效请求
        valid_request = CollectVideoListRequest(collects_id="test_id", count=20, cursor=0)
        assert valid_request.collects_id == "test_id"
        assert valid_request.count == 20
        assert valid_request.cursor == 0

    def test_client_creation(self, config):
        """测试客户端创建"""
        assert async_douyin_api is not None

    def test_edge_cases_config(self):
        """测试边界情况配置"""
        # 测试最小值
        edge_config = DouyinConfig(timeout=0.1, max_retries=0)  # 最小超时  # 不重试
        assert edge_config.timeout == 0.1
        assert edge_config.max_retries == 0


# CommonParams 相关测试已移除，因为该类在新架构中不存在或已更改


@pytest.mark.unit
class TestDouyinSignClient:
    """算签客户端单元测试类"""

    def test_sign_client_creation(self):
        """测试算签客户端创建"""
        client = DouyinSignClient()
        assert client is not None

    def test_sign_request_model(self):
        """测试签名请求模型"""
        request = DouyinSignRequest(
            uri="/aweme/v1/web/aweme/detail/",
            query_params="aweme_id=test_id&device_platform=webapp",
            user_agent="Mozilla/5.0 (Test)",
            cookies="test=cookie",
        )
        assert request.uri == "/aweme/v1/web/aweme/detail/"
        assert request.query_params == "aweme_id=test_id&device_platform=webapp"
        assert request.user_agent == "Mozilla/5.0 (Test)"
        assert request.cookies == "test=cookie"

    def test_sign_client_methods(self):
        """测试算签客户端方法"""
        client = DouyinSignClient()

        # 测试方法存在
        assert hasattr(client, "sign")
        assert hasattr(client, "get_stats")
        assert callable(client.sign)
        assert callable(client.get_stats)

    def test_sign_client_stats(self):
        """测试算签客户端统计"""
        client = DouyinSignClient()
        stats = client.get_stats()

        # 统计应该是字典格式
        assert isinstance(stats, dict)
        assert "total_requests" in stats
        assert "successful_requests" in stats
        assert "failed_requests" in stats


@pytest.mark.unit
class TestRequestModels:
    """请求模型单元测试类"""

    def test_collect_video_list_request(self):
        """测试收藏视频列表请求模型"""
        request = CollectVideoListRequest(collects_id="test_id", count=20, cursor=0)
        assert request.collects_id == "test_id"
        assert request.count == 20
        assert request.cursor == 0

    def test_search_info_request(self):
        """测试搜索信息请求模型"""
        request = SearchInfoRequest(keyword="测试", count=10, offset=0)
        assert request.keyword == "测试"
        assert request.count == 10
        assert request.offset == 0

    def test_user_info_request(self):
        """测试用户信息请求模型"""
        request = UserInfoRequest(sec_user_id="test_user_id")
        assert request.sec_user_id == "test_user_id"

    def test_video_detail_request(self):
        """测试视频详情请求模型"""
        request = VideoDetailRequest(aweme_id="test_video_id")
        assert request.aweme_id == "test_video_id"

    def test_aweme_comments_request(self):
        """测试视频评论请求模型"""
        request = AwemeCommentsRequest(aweme_id="test_video_id", count=20, cursor=0)
        assert request.aweme_id == "test_video_id"
        assert request.count == 20
        assert request.cursor == 0

    def test_user_aweme_posts_request(self):
        """测试用户视频列表请求模型"""
        request = UserAwemePostsRequest(sec_user_id="test_user_id", count=20, max_cursor=0)
        assert request.sec_user_id == "test_user_id"
        assert request.count == 20
        assert request.max_cursor == 0


if __name__ == "__main__":
    # 本地开发时可以直接运行这个文件
    pytest.main([__file__, "-v", "-m", "unit", "--tb=short"])
