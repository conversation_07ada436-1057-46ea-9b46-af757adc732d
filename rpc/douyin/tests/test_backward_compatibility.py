"""
抖音模块向后兼容性测试

验证重构后的模块与现有 rpc/douyin 功能完全兼容
"""

from unittest.mock import patch

import pytest
from loguru import logger

# 测试新架构组件是否可以正常导入
from rpc.douyin import (  # 新架构组件; 传统模型（应该仍然可用）; 异常（应该仍然可用）; 签名相关（应该仍然可用）
    AsyncDouyinAPI,
    AwemeCommentsRequest,
    DouyinAuthenticationError,
    DouyinClientError,
    DouyinConfig,
    DouyinRateLimitError,
    DouyinRequestEnhancer,
    DouyinResponseError,
    DouyinSignRequest,
    DouyinSignResponse,
    DouyinTimeoutError,
    DouyinValidationError,
    SearchInfoRequest,
    UserAwemePostsRequest,
    UserInfoRequest,
    VideoDetailRequest,
    async_douyin_api,
    client_manager,
)


class TestNewArchitecture:
    """向后兼容性测试"""

    def test_new_architecture_import(self):
        """测试新架构组件导入"""
        # 确保新架构组件可以正常导入
        # DouyinAPI has been removed, only AsyncDouyinAPI is available
        assert AsyncDouyinAPI is not None
        assert DouyinRequestEnhancer is not None
        assert client_manager is not None
        assert async_douyin_api is not None

        logger.info("✅ 新架构组件导入测试通过")

    def test_models_import(self):
        """测试模型导入"""
        # 确保所有模型仍然可以导入
        assert SearchInfoRequest is not None
        assert VideoDetailRequest is not None
        assert AwemeCommentsRequest is not None
        assert UserInfoRequest is not None
        assert UserAwemePostsRequest is not None

        logger.info("✅ 模型导入测试通过")

    def test_exceptions_import(self):
        """测试异常导入"""
        # 确保所有异常仍然可以导入
        assert DouyinClientError is not None
        assert DouyinResponseError is not None
        assert DouyinValidationError is not None
        assert DouyinTimeoutError is not None
        assert DouyinRateLimitError is not None
        assert DouyinAuthenticationError is not None

        logger.info("✅ 异常导入测试通过")

    def test_sign_models_import(self):
        """测试签名模型导入"""
        # 确保签名相关模型仍然可以导入
        assert DouyinSignRequest is not None
        assert DouyinSignResponse is not None

        logger.info("✅ 签名模型导入测试通过")

    def test_model_creation(self):
        """测试模型创建"""
        # 测试各种请求模型的创建
        search_request = SearchInfoRequest(keyword="test")
        assert search_request.keyword == "test"

        video_request = VideoDetailRequest(aweme_id="123")
        assert video_request.aweme_id == "123"

        comment_request = AwemeCommentsRequest(aweme_id="123")
        assert comment_request.aweme_id == "123"

        user_request = UserInfoRequest(sec_user_id="user123")
        assert user_request.sec_user_id == "user123"

        posts_request = UserAwemePostsRequest(sec_user_id="user123")
        assert posts_request.sec_user_id == "user123"

        logger.info("✅ 模型创建测试通过")

    def test_config_creation(self):
        """测试配置创建"""
        # 测试配置的创建和属性设置
        config = DouyinConfig()
        assert config.base_url == "https://www.douyin.com"
        assert config.timeout == 30.0
        assert config.enable_sign == True

        # 测试自定义配置
        custom_config = DouyinConfig(timeout=60.0, enable_sign=False, verify_ssl=False)
        assert custom_config.timeout == 60.0
        assert custom_config.enable_sign == False
        assert custom_config.verify_ssl == False

        logger.info("✅ 配置创建测试通过")

    def test_exception_creation(self):
        """测试异常创建"""
        # 测试各种异常的创建
        client_error = DouyinClientError("test error")
        assert str(client_error) == "test error"

        response_error = DouyinResponseError("response error", status_code=400)
        assert str(response_error) == "response error"
        assert response_error.status_code == 400

        validation_error = DouyinValidationError("validation error")
        assert str(validation_error) == "validation error"

        logger.info("✅ 异常创建测试通过")

    @patch("httpx.AsyncClient")
    def test_new_api_creation(self, mock_async_client):
        """测试新 API 实例创建"""
        # 确保默认实例存在
        assert async_douyin_api is not None

        # 创建新架构 API
        new_api = AsyncDouyinAPI(async_client=mock_async_client)

        # 确保可以创建新的实例
        assert new_api is not None
        assert new_api.async_client == mock_async_client

        logger.info("✅ 异步客户端和新架构共存测试通过")

    def test_all_exports_available(self):
        """测试所有导出项都可用"""
        # 检查 __all__ 中的所有项目都可以导入
        import rpc.douyin as douyin_module
        from rpc.douyin import __all__

        missing_exports = []
        for export_name in __all__:
            if not hasattr(douyin_module, export_name):
                missing_exports.append(export_name)

        if missing_exports:
            pytest.fail(f"以下导出项不可用: {missing_exports}")

        logger.info(f"✅ 所有 {len(__all__)} 个导出项都可用")

    def test_api_interface_consistency(self):
        """测试 API 接口一致性"""
        # 确保新旧 API 的接口保持一致

        # 检查预配置的异步客户端
        assert async_douyin_api is not None
        assert hasattr(async_douyin_api, "search_videos")
        assert hasattr(async_douyin_api, "get_video_detail")

        # 检查新 API 的方法
        new_api = AsyncDouyinAPI()
        assert hasattr(new_api, "_handle_response")
        assert hasattr(new_api, "search_videos")
        assert hasattr(new_api, "get_video_detail")
        assert hasattr(new_api, "get_video_comments")
        assert hasattr(new_api, "get_user_info")
        assert hasattr(new_api, "get_user_videos")
        assert hasattr(new_api, "pong")

        logger.info("✅ API 接口一致性测试通过")


@pytest.mark.integration
class TestIntegrationCompatibility:
    """集成兼容性测试"""

    def test_convenience_functions_work(self):
        """测试便捷函数正常工作"""
        # 测试预配置的 API 客户端是否可用
        try:
            # 检查预配置的异步客户端是否可用
            assert async_douyin_api is not None
            assert hasattr(async_douyin_api, "search_videos")
            logger.info("✅ 便捷函数工作正常")
        except Exception as e:
            pytest.fail(f"便捷函数测试失败: {e}")

    def test_client_creation_functions_work(self):
        """测试客户端创建函数正常工作"""
        try:
            # 测试预配置的异步客户端
            assert async_douyin_api is not None

            # 测试客户端功能
            assert hasattr(async_douyin_api, "search_videos")

            logger.info("✅ 客户端创建函数工作正常")
        except Exception as e:
            pytest.fail(f"客户端创建函数测试失败: {e}")

    def test_client_manager_works(self):
        """测试客户端管理器正常工作"""
        try:
            # 创建客户端
            client = client_manager.create_client()
            assert client is not None

            # 关闭客户端
            client_manager.close_client(client)

            logger.info("✅ 客户端管理器工作正常")
        except Exception as e:
            pytest.fail(f"客户端管理器测试失败: {e}")


if __name__ == "__main__":
    # 配置日志
    logger.add("logs/test_backward_compatibility.log", rotation="1 MB")

    # 运行测试
    pytest.main([__file__, "-v", "-s", "--tb=short"])  # 显示输出  # 简短的错误信息
