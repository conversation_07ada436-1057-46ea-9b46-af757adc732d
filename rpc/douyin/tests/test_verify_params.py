# -*- coding: utf-8 -*-
"""
验证参数生成器测试
"""

import asyncio
from unittest.mock import AsyncMock, MagicMock, patch

import pytest

from rpc.douyin.exceptions import DouyinClientError
from rpc.douyin.schemas.base import CommonVerifyParams
from rpc.douyin.verify_params import (
    TokenManager,
    VerifyFpManager,
    VerifyParamsGenerator,
    get_current_timestamp,
    get_random_str,
    get_web_id,
)


class TestUtilityFunctions:
    """测试工具函数"""

    def test_get_current_timestamp(self):
        """测试时间戳生成"""
        timestamp = get_current_timestamp()
        assert isinstance(timestamp, int)
        assert timestamp > 0
        assert len(str(timestamp)) == 13  # 13位毫秒时间戳

    def test_get_random_str(self):
        """测试随机字符串生成"""
        # 测试默认长度
        random_str = get_random_str()
        assert isinstance(random_str, str)
        assert len(random_str) == 12

        # 测试自定义长度
        random_str_20 = get_random_str(20)
        assert len(random_str_20) == 20

        # 测试生成的字符串不同
        str1 = get_random_str(10)
        str2 = get_random_str(10)
        assert str1 != str2

    def test_get_web_id(self):
        """测试 webid 生成"""
        web_id = get_web_id()
        assert isinstance(web_id, str)
        assert len(web_id) == 19
        assert web_id.isdigit()


class TestVerifyFpManager:
    """测试验证指纹管理器"""

    def test_gen_verify_fp(self):
        """测试 verify_fp 生成"""
        verify_fp = VerifyFpManager.gen_verify_fp()
        assert isinstance(verify_fp, str)
        assert verify_fp.startswith("verify_")
        assert len(verify_fp) > 40  # verify_fp 应该比较长

        # 测试生成的 verify_fp 不同
        verify_fp2 = VerifyFpManager.gen_verify_fp()
        assert verify_fp != verify_fp2

    def test_gen_s_v_web_id(self):
        """测试 s_v_web_id 生成"""
        s_v_web_id = VerifyFpManager.gen_s_v_web_id()
        assert isinstance(s_v_web_id, str)
        assert s_v_web_id.startswith("verify_")

    def test_gen_uifid(self):
        """测试 uifid 生成"""
        uifid = VerifyFpManager.gen_uifid()
        assert isinstance(uifid, str)
        assert len(uifid) == 32


class TestTokenManager:
    """测试 Token 管理器"""

    def test_init(self):
        """测试初始化"""
        manager = TokenManager()
        assert manager._user_agent is not None

        custom_ua = "Custom User Agent"
        manager_custom = TokenManager(custom_ua)
        assert manager_custom._user_agent == custom_ua

    def test_gen_fake_msToken(self):
        """测试假 msToken 生成"""
        fake_token = TokenManager.gen_fake_msToken()
        assert isinstance(fake_token, str)
        assert len(fake_token) == 128  # 126 + "=="
        assert fake_token.endswith("==")

    @pytest.mark.asyncio
    async def test_gen_real_msToken_success(self):
        """测试真实 msToken 生成成功"""
        manager = TokenManager()

        # Mock httpx.AsyncClient
        mock_response = MagicMock()
        mock_response.raise_for_status.return_value = None
        mock_cookie = MagicMock()
        mock_cookie.name = "msToken"
        mock_cookie.value = "a" * 120  # 120位的 msToken
        mock_response.cookies = [mock_cookie]

        with patch("httpx.AsyncClient") as mock_client:
            mock_client.return_value.__aenter__.return_value.post = AsyncMock(return_value=mock_response)

            result = await manager.gen_real_msToken()
            assert result == "a" * 120

    @pytest.mark.asyncio
    async def test_gen_real_msToken_failure(self):
        """测试真实 msToken 生成失败"""
        manager = TokenManager()

        with patch("httpx.AsyncClient") as mock_client:
            mock_client.return_value.__aenter__.return_value.post = AsyncMock(side_effect=Exception("Network error"))

            with pytest.raises(Exception):
                await manager.gen_real_msToken()

    @pytest.mark.asyncio
    async def test_get_msToken_fallback(self):
        """测试 msToken 获取失败时的降级处理"""
        manager = TokenManager()

        with patch.object(manager, "gen_real_msToken", side_effect=Exception("Network error")):
            with patch.object(manager, "gen_fake_msToken", return_value="fake_token"):
                result = await manager.get_msToken()
                assert result == "fake_token"

    @pytest.mark.asyncio
    async def test_gen_webid_success(self):
        """测试 webid 生成成功"""
        manager = TokenManager()

        mock_response = MagicMock()
        mock_response.raise_for_status.return_value = None
        mock_response.json.return_value = {"web_id": "1234567890123456789"}

        with patch("httpx.AsyncClient") as mock_client:
            mock_client.return_value.__aenter__.return_value.post = AsyncMock(return_value=mock_response)

            result = await manager.gen_webid()
            assert result == "1234567890123456789"

    @pytest.mark.asyncio
    async def test_gen_webid_fallback(self):
        """测试 webid 生成失败时的降级处理"""
        manager = TokenManager()

        with patch("httpx.AsyncClient") as mock_client:
            mock_client.return_value.__aenter__.return_value.post = AsyncMock(side_effect=Exception("Network error"))

            with patch("rpc.douyin.verify_params.get_web_id", return_value="fallback_webid"):
                result = await manager.gen_webid()
                assert result == "fallback_webid"


class TestVerifyParamsGenerator:
    """测试验证参数生成器"""

    def test_init(self):
        """测试初始化"""
        generator = VerifyParamsGenerator()
        assert generator.user_agent is not None
        assert generator.token_manager is not None

    @pytest.mark.asyncio
    async def test_get_common_verify_params_success(self):
        """测试成功获取验证参数"""
        generator = VerifyParamsGenerator()

        # Mock TokenManager 方法
        with patch.object(generator.token_manager, "get_msToken", return_value="mock_ms_token"):
            with patch.object(generator.token_manager, "gen_webid", return_value="mock_webid"):
                with patch.object(VerifyFpManager, "gen_verify_fp", return_value="mock_verify_fp"):
                    with patch.object(VerifyFpManager, "gen_s_v_web_id", return_value="mock_s_v_web_id"):
                        with patch.object(VerifyFpManager, "gen_uifid", return_value="mock_uifid"):

                            result = await generator.get_common_verify_params()

                            assert isinstance(result, CommonVerifyParams)
                            assert result.ms_token == "mock_ms_token"
                            assert result.webid == "mock_webid"
                            assert result.verify_fp == "mock_verify_fp"
                            assert result.s_v_web_id == "mock_s_v_web_id"
                            assert result.uifid == "mock_uifid"

    @pytest.mark.asyncio
    async def test_get_common_verify_params_failure(self):
        """测试获取验证参数失败"""
        generator = VerifyParamsGenerator()

        with patch.object(generator.token_manager, "get_msToken", side_effect=Exception("Network error")):
            with pytest.raises(DouyinClientError):
                await generator.get_common_verify_params()

    def test_get_fake_verify_params(self):
        """测试获取假验证参数"""
        generator = VerifyParamsGenerator()

        result = generator.get_fake_verify_params()

        assert isinstance(result, CommonVerifyParams)
        assert result.ms_token is not None
        assert result.webid is not None
        assert result.verify_fp is not None
        assert result.s_v_web_id is not None
        assert result.uifid is not None


class TestCommonVerifyParams:
    """测试通用验证参数模型"""

    def test_model_validation(self):
        """测试模型验证"""
        params = CommonVerifyParams(
            ms_token="test_ms_token",
            webid="test_webid",
            verify_fp="test_verify_fp",
            s_v_web_id="test_s_v_web_id",
            uifid="test_uifid",
        )

        assert params.ms_token == "test_ms_token"
        assert params.webid == "test_webid"
        assert params.verify_fp == "test_verify_fp"
        assert params.s_v_web_id == "test_s_v_web_id"
        assert params.uifid == "test_uifid"

    def test_model_optional_uifid(self):
        """测试 uifid 可选"""
        params = CommonVerifyParams(
            ms_token="test_ms_token", webid="test_webid", verify_fp="test_verify_fp", s_v_web_id="test_s_v_web_id"
        )

        assert params.uifid is None


@pytest.mark.asyncio
async def test_integration():
    """集成测试"""
    generator = VerifyParamsGenerator()

    try:
        # 尝试获取真实参数（可能失败）
        params = await generator.get_common_verify_params()
        assert isinstance(params, CommonVerifyParams)
    except DouyinClientError:
        # 如果失败，获取假参数
        fake_params = generator.get_fake_verify_params()
        assert isinstance(fake_params, CommonVerifyParams)


if __name__ == "__main__":
    # 运行简单测试
    asyncio.run(test_integration())
