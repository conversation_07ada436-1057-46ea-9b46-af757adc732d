"""
抖音新架构测试

测试基于三步增强模式的新抖音 API 客户端
"""

from unittest.mock import Mock

import pytest

from rpc.douyin import (  # 新架构组件; 模型; 配置; 异常
    SearchInfoRequest,
    UserInfoRequest,
    VideoDetailRequest,
)


class TestDouyinRequestEnhancer:
    """测试抖音请求增强器"""

    def test_search_videos_convenience(self, MockAsyncDouyinAPI):
        """测试连通性测试便捷函数"""
        # 模拟 API 实例和其方法
        mock_api_instance = Mock()
        mock_response = {"status": "ok"}
        mock_api_instance.pong.return_value = mock_response
        MockAsyncDouyinAPI.return_value.__enter__.return_value = mock_api_instance

        result = pong()

        assert result == mock_response
        mock_api_instance.pong.assert_called_once()


@pytest.mark.slow
class TestRequestModels:
    """测试请求模型"""

    def test_search_info_request(self):
        """测试搜索信息请求模型"""
        request = SearchInfoRequest(keyword="美食", count="20", offset=0)

        assert request.keyword == "美食"
        assert request.count == "20"
        assert request.offset == 0
        assert request.search_channel == "aweme_general"  # 默认值

    def test_video_detail_request(self):
        """测试视频详情请求模型"""
        request = VideoDetailRequest(aweme_id="123456789")

        assert request.aweme_id == "123456789"

    def test_user_info_request(self):
        """测试用户信息请求模型"""
        request = UserInfoRequest(sec_user_id="user_123")

        assert request.sec_user_id == "user_123"


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
