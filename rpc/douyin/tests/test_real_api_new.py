"""
抖音新架构真实 API 集成测试

测试新架构与真实抖音 API 的集成
"""

import asyncio

import pytest
from loguru import logger

from rpc.douyin import (  # 新架构组件; 模型; 异常
    AsyncDouyinAPI,
    DouyinClientError,
    DouyinResponseError,
    SearchInfoRequest,
    VideoDetailRequest,
    async_douyin_api,
)


@pytest.mark.real_api
@pytest.mark.ci_skip
class TestRealAPINewArchitecture:
    """真实 API 测试（新架构）"""

    @pytest.mark.asyncio
    async def test_douyin_api_class(self):
        """测试抖音 API 类"""
        logger.info("=== 测试抖音 API 类 ===")

        try:
            # 测试连通性
            pong_result = await async_douyin_api.pong()
            logger.info(f"API 类连通性测试: {pong_result}")

            # 测试搜索
            search_request = SearchInfoRequest(keyword="科技", count=2, offset=0)

            search_result = await async_douyin_api.search_videos(search_request)
            logger.info(f"API 类搜索结果: {len(search_result.data) if search_result.data else 0} 个视频")

            assert hasattr(search_result, "data")
            logger.success("✅ 抖音 API 类测试成功")

        except Exception as e:
            logger.error(f"❌ 抖音 API 类测试失败: {e}")
            pytest.fail(f"API 类测试失败: {e}")

    @pytest.mark.asyncio
    async def test_async_douyin_api_class(self):
        """测试异步抖音 API 类"""
        logger.info("=== 测试异步抖音 API 类 ===")

        try:
            # 测试异步连通性
            pong_result = await async_douyin_api.pong()
            logger.info(f"异步 API 类连通性测试: {pong_result}")

            # 测试异步搜索
            search_request = SearchInfoRequest(keyword="旅游", count=2, offset=0)

            search_result = await async_douyin_api.search_videos(search_request)
            logger.info(f"异步 API 类搜索结果: {len(search_result.data) if search_result.data else 0} 个视频")

            assert hasattr(search_result, "data")
            logger.success("✅ 异步抖音 API 类测试成功")

        except Exception as e:
            logger.error(f"❌ 异步抖音 API 类测试失败: {e}")
            pytest.fail(f"异步 API 类测试失败: {e}")

    def test_different_configurations(self):
        """测试不同配置"""
        logger.info("=== 测试不同配置 ===")

        try:
            # 使用预配置的客户端进行测试
            api = AsyncDouyinAPI()

            result = api.pong()
            logger.info(f"配置测试成功: {result}")

            assert isinstance(result, dict)

            logger.success("✅ 配置测试完成")

        except Exception as e:
            logger.error(f"❌ 配置测试失败: {e}")
            # 不使用 pytest.fail，允许其他测试继续
            pass

    @pytest.mark.asyncio
    async def test_concurrent_requests(self):
        """测试并发请求"""
        logger.info("=== 测试并发请求 ===")

        try:
            async_api = AsyncDouyinAPI()

            # 创建多个并发任务
            tasks = []
            keywords = ["美食", "科技", "旅游"]

            for keyword in keywords:
                request = SearchInfoRequest(keyword=keyword, count=1, offset=0)
                task = async_api.search_videos(request)
                tasks.append(task)

            # 并发执行
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # 检查结果
            success_count = 0
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    logger.warning(f"任务 {i} 失败: {result}")
                else:
                    success_count += 1
                    logger.info(f"任务 {i} 成功: {len(result.data) if result.data else 0} 个结果")

            logger.info(f"并发请求完成: {success_count}/{len(tasks)} 成功")
            assert success_count > 0, "至少应有一个请求成功"

            logger.success("✅ 并发请求测试成功")

        except Exception as e:
            logger.error(f"❌ 并发请求测试失败: {e}")
            pytest.fail(f"并发请求测试失败: {e}")

    def test_error_handling(self):
        """测试错误处理"""
        logger.info("=== 测试错误处理 ===")

        try:
            api = AsyncDouyinAPI()

            # 测试无效的视频详情请求
            try:
                invalid_request = VideoDetailRequest(aweme_id="invalid_id_12345")
                result = api.get_video_detail(invalid_request)
                logger.info(f"无效请求结果: {result}")
            except (DouyinResponseError, DouyinClientError) as e:
                logger.info(f"预期的错误: {e}")

            logger.success("✅ 错误处理测试成功")

        except Exception as e:
            logger.error(f"❌ 错误处理测试失败: {e}")
            pytest.fail(f"错误处理测试失败: {e}")


if __name__ == "__main__":
    # 配置日志
    logger.add("logs/test_real_api_new.log", rotation="1 MB")

    # 运行测试
    pytest.main(
        [__file__, "-v", "-s", "-m", "real_api", "--tb=short"]  # 显示输出  # 只运行真实 API 测试  # 简短的错误信息
    )
