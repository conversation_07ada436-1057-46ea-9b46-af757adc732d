# 抖音RPC客户端测试指南

本文档介绍如何运行和维护抖音RPC客户端的测试用例。

## 🎯 核心特性

✅ **智能环境检测**: CI环境自动跳过集成测试
✅ **灵活测试分类**: 单元测试、集成测试、真实API测试
✅ **简洁命令**: 直接使用pytest，无需额外脚本
✅ **安全配置**: 敏感数据通过环境变量管理

## 测试类型

### 1. 单元测试 (Unit Tests)
- **标记**: `@pytest.mark.unit`
- **特点**: 不依赖外部服务，使用模拟数据
- **运行环境**: 本地开发 + CI环境
- **用途**: 测试代码逻辑、参数验证、错误处理等

### 2. 集成测试 (Integration Tests)
- **标记**: `@pytest.mark.integration`
- **特点**: 测试组件间的交互，但不调用真实API
- **运行环境**: 本地开发 + CI环境（有限制）
- **用途**: 测试请求构建、响应解析、参数增强等

### 3. 真实API测试 (Real API Tests)
- **标记**: `@pytest.mark.real_api` + `@pytest.mark.ci_skip`
- **特点**: 真实调用第三方API服务
- **运行环境**: 仅本地开发
- **用途**: 验证与真实服务的兼容性

## 快速开始

### 1. 环境准备

```bash
# 安装测试依赖
pip install pytest pytest-asyncio pytest-cov

# 复制环境配置文件
cp .env.test.example .env.test

# 编辑配置文件，填入真实的测试数据
vim .env.test
```

### 2. 运行测试

```bash
# 单元测试（快速，本地+CI都运行）
pytest -v -m "unit" rpc/douyin/tests/

# 集成测试（模拟API调用）
pytest -v -m "integration and not real_api" rpc/douyin/integration_tests/

# 真实API测试（仅本地开发，需要有效cookies）
ENABLE_REAL_API_TESTS=true pytest -v -m "real_api" rpc/douyin/integration_tests/

# CI环境测试（跳过集成测试）
CI=true pytest -v -m "not ci_skip" rpc/douyin/

# 运行所有测试
pytest -v rpc/douyin/

# 生成覆盖率报告
pytest -v rpc/douyin/ --cov=rpc.douyin --cov-report=html
```

## 测试文件结构

```
项目根目录/
├── conftest.py                    # 全局pytest配置和fixtures
├── pytest.ini                    # pytest配置文件
├── rpc/douyin/tests/              # 单元测试目录
│   ├── __init__.py
│   ├── test_client_unit.py           # 单元测试
│   ├── test_verify_params.py         # 参数验证测试
│   ├── mock/                          # 模拟数据目录
│   ├── reports/                       # 测试报告目录
│   └── coverage/                      # 覆盖率报告目录
└── rpc/douyin/integration_tests/  # 集成测试目录
    ├── __init__.py
    ├── test_client_integration.py    # 模拟API集成测试
    ├── test_auto_params.py           # 自动参数增强测试
    └── test_real_api.py               # 真实API测试
```

## 环境变量配置

### 必需的环境变量

```bash
# 启用真实API测试
ENABLE_REAL_API_TESTS=true

# 测试用的cookies（从浏览器获取）
DOUYIN_TEST_COOKIES="your_cookies_here"

# 测试用的收藏夹ID（用于分页测试）
TEST_COLLECTS_ID="your_collects_id_here"
```

### 可选的环境变量

```bash
# 测试用的视频ID和用户ID
TEST_VIDEO_ID=7509429111140420874
TEST_USER_ID=MS4wLjABAAAADY7u-UqfGogzrFtNghKD7Rgnz1QnWBJUrBA4AYrCX20zcMMCg1d8xoslUJiVTQyP

# API测试配置
API_TEST_TIMEOUT=30
API_TEST_RETRIES=2

# CI环境标识（由CI系统设置）
CI=true
```

## 获取测试Cookies

1. 打开浏览器，访问 https://www.douyin.com
2. 登录你的抖音账号
3. 打开开发者工具（F12）
4. 切换到 Network 标签
5. 刷新页面，找到任意一个请求
6. 在请求头中找到 Cookie 字段，复制其值
7. 将复制的值设置为 `DOUYIN_TEST_COOKIES` 环境变量

## CI/CD集成

### GitHub Actions示例

```yaml
# .github/workflows/test.yml
name: 测试

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    - name: 设置Python
      uses: actions/setup-python@v4
      with:
        python-version: 3.11
    - name: 安装依赖
      run: pip install -r requirements.txt
    - name: 运行测试
      env:
        CI: true  # 自动跳过集成测试
      run: pytest -v -m "not ci_skip" rpc/douyin/tests/
```

### 其他CI系统

对于其他CI系统，只需设置 `CI=true` 环境变量即可自动跳过集成测试。

## 测试最佳实践

### 1. 编写新测试

```python
import pytest
from rpc.douyin import AsyncDouyinAPI, async_douyin_api

class TestNewFeature:
    """新功能测试"""
    
    @pytest.mark.unit
    def test_unit_functionality(self):
        """单元测试 - 不依赖外部服务"""
        # 测试代码逻辑
        pass
    
    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_integration_functionality(self):
        """集成测试 - 测试组件交互"""
        # 使用预配置的客户端测试请求构建、参数增强等
        api = AsyncDouyinAPI()
        # 测试代码
        pass
    
    @pytest.mark.real_api
    @pytest.mark.ci_skip
    @pytest.mark.asyncio
    async def test_real_api_functionality(self, real_cookies):
        """真实API测试 - 仅本地开发"""
        # 使用预配置的异步客户端进行真实API调用测试
        result = await async_douyin_api.search_videos(keyword="test")
        assert result is not None
        pass
```

### 2. 测试数据管理

- 使用fixtures提供测试数据
- 敏感数据通过环境变量传递
- 模拟数据放在 `mock/` 目录下
- 定期更新测试用的cookies和ID

### 3. 错误处理

- 集成测试应该能处理网络异常
- 真实API测试应该能处理认证失败
- 使用适当的异常断言

### 4. 性能考虑

- 真实API测试添加 `@pytest.mark.slow` 标记
- 设置合理的超时时间
- 避免过度的并发测试

## 故障排除

### 常见问题

1. **集成测试在CI中运行**
   - 确保设置了 `CI=true` 环境变量
   - 检查pytest标记是否正确

2. **真实API测试失败**
   - 检查cookies是否过期
   - 验证网络连接
   - 确认API服务状态

3. **测试运行缓慢**
   - 使用 `-m "unit"` 只运行单元测试
   - 检查网络连接质量
   - 调整超时设置

### 调试技巧

```bash
# 显示详细输出
pytest -v -s rpc/douyin/tests/

# 只运行失败的测试
pytest --lf rpc/douyin/tests/

# 进入调试模式
pytest --pdb rpc/douyin/tests/

# 生成覆盖率报告
pytest -v rpc/douyin/tests/ --cov=rpc.douyin --cov-report=html
```

## 贡献指南

1. 新功能必须包含相应的测试
2. 测试覆盖率应保持在80%以上
3. 集成测试不应依赖特定的外部数据
4. 真实API测试应该能优雅处理失败情况
5. 提交前运行完整的测试套件

## 联系方式

如有问题或建议，请提交Issue或联系维护团队。
