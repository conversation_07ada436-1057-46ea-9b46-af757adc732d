"""
Douyin 真实API集成测试

测试与真实 Douyin API 的集成功能
需要有效的认证信息才能运行
"""

import pytest
from loguru import logger

from rpc.douyin import (
    AsyncDouyinAPI,
    AwemeCommentsRequest,
    SearchInfoRequest,
    UserAwemePostsRequest,
    UserInfoRequest,
    VideoDetailRequest,
    async_douyin_api,
)
from rpc.douyin.exceptions import DouyinResponseError


@pytest.mark.real_api
@pytest.mark.ci_skip
class TestRealAsyncDouyinAPIIntegration:
    """真实API集成测试"""

    @pytest.fixture(scope="class")
    def api_client(self):
        """创建API客户端"""
        # 使用预配置的客户端
        client = async_douyin_api.async_client
        api = AsyncDouyinAPI(client=client)
        yield api

    @pytest.fixture(scope="class")
    def async_api_client(self):
        """创建异步API客户端"""
        # 使用预配置的客户端
        client = async_douyin_api.async_client
        api = AsyncDouyinAPI(client=client)
        yield api

    def test_search_videos_real(self, api_client):
        """测试搜索视频（真实API）"""
        logger.info("开始测试搜索视频")
        try:
            request = SearchInfoRequest(keyword="美食", count="10")
            response = api_client.search_videos(request)
            logger.info(f"视频搜索成功: {response}")
            assert response is not None
            assert response.data
            assert len(response.data) > 0
        except DouyinResponseError as e:
            logger.warning(f"搜索视频失败，可能需要有效的cookies: {e}")
            pytest.skip("需要有效的认证信息")
        except Exception as e:
            logger.error(f"搜索视频查询失败: {e}")
            raise

    @pytest.mark.asyncio
    async def test_search_videos_async_real(self, async_api_client):
        """测试异步搜索视频（真实API）"""
        logger.info("开始测试异步搜索视频")
        try:
            request = SearchInfoRequest(keyword="美食", count="10")
            response = await async_api_client.search_videos(request)
            logger.info(f"异步视频搜索成功: {response}")
            assert response is not None
            assert response.data
            assert len(response.data) > 0
        except DouyinResponseError as e:
            logger.warning(f"异步搜索视频失败: {e}")
            pytest.skip("需要有效的认证信息")
        except Exception as e:
            logger.error(f"异步搜索视频查询失败: {e}")
            raise

    def test_get_video_detail_real(self, api_client):
        """测试获取视频详情（真实API）"""
        logger.info("开始测试获取视频详情")
        # 首先搜索一个视频以获取aweme_id
        try:
            search_request = SearchInfoRequest(keyword="风景", count="1")
            search_response = api_client.search_videos(search_request)
            assert search_response.data and search_response.data[0].aweme_info.aweme_id
            aweme_id = search_response.data[0].aweme_info.aweme_id
            logger.info(f"将使用 aweme_id: {aweme_id} 测试视频详情")

            request = VideoDetailRequest(aweme_id=aweme_id)
            response = api_client.get_video_detail(request)
            logger.info(f"视频详情获取成功: {response}")
            assert response is not None
            assert response.aweme_detail.aweme_id == aweme_id
        except DouyinResponseError as e:
            logger.warning(f"获取视频详情失败: {e}")
            pytest.skip("需要有效的认证信息或有效的aweme_id")
        except Exception as e:
            logger.error(f"获取视频详情失败: {e}")
            raise

    def test_get_video_comments_real(self, api_client):
        """测试获取视频评论（真实API）"""
        logger.info("开始测试获取视频评论")
        try:
            search_request = SearchInfoRequest(keyword="萌宠", count="1")
            search_response = api_client.search_videos(search_request)
            assert search_response.data and search_response.data[0].aweme_info.aweme_id
            aweme_id = search_response.data[0].aweme_info.aweme_id
            logger.info(f"将使用 aweme_id: {aweme_id} 测试视频评论")

            request = AwemeCommentsRequest(aweme_id=aweme_id, count=10, cursor=0)
            response = api_client.get_video_comments(request)
            logger.info(f"视频评论获取成功: {response}")
            assert response is not None
            assert response.comments is not None
        except DouyinResponseError as e:
            logger.warning(f"获取视频评论失败: {e}")
            pytest.skip("需要有效的认证信息或有效的aweme_id")
        except Exception as e:
            logger.error(f"获取视频评论失败: {e}")
            raise

    def test_get_user_info_real(self, api_client):
        """测试获取用户信息（真实API）"""
        logger.info("开始测试获取用户信息")
        # 使用一个已知的sec_user_id，例如人民日报
        sec_user_id = "MS4wLjABAAAA5TC24KXO0x_fedk3Yp3a2uc1n5y5VGmDOR2B0L_Yp_A"
        try:
            request = UserInfoRequest(sec_user_id=sec_user_id)
            response = api_client.get_user_info(request)
            logger.info(f"用户信息获取成功: {response}")
            assert response is not None
            assert response.user.sec_uid == sec_user_id
        except DouyinResponseError as e:
            logger.warning(f"获取用户信息失败: {e}")
            pytest.skip("需要有效的认证信息或有效的sec_user_id")
        except Exception as e:
            logger.error(f"获取用户信息失败: {e}")
            raise

    def test_get_user_videos_real(self, api_client):
        """测试获取用户视频列表（真实API）"""
        logger.info("开始测试获取用户视频列表")
        sec_user_id = "MS4wLjABAAAA5TC24KXO0x_fedk3Yp3a2uc1n5y5VGmDOR2B0L_Yp_A"  # 人民日报
        try:
            request = UserAwemePostsRequest(sec_user_id=sec_user_id, count=5, max_cursor=0)
            response = api_client.get_user_videos(request)
            logger.info(f"用户视频列表获取成功: {response}")
            assert response is not None
            assert response.aweme_list is not None
        except DouyinResponseError as e:
            logger.warning(f"获取用户视频列表失败: {e}")
            pytest.skip("需要有效的认证信息或有效的sec_user_id")
        except Exception as e:
            logger.error(f"获取用户视频列表失败: {e}")
            raise

    def test_pong_real(self, api_client):
        """测试连接性检查（真实API）"""
        logger.info("开始测试连接性检查")
        try:
            result = api_client.pong()
            logger.info(f"连接性检查结果: {result}")
            assert isinstance(result, dict)
        except Exception as e:
            logger.error(f"连接性检查失败: {e}")
            raise
