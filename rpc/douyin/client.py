"""
抖音客户端模块

基于 rpc.common 的三步增强模式创建预配置的抖音客户端
"""

import httpx
from loguru import logger

from models.enums import Platform
from rpc.common.transport import DynamicProxyTransport
from rpc.providers.account_provider import DefaultAccountProvider
from rpc.providers.proxy_provider import DefaultProxyProvider

from .config import DouyinConfig
from .enhancer import DouyinRequestEnhancer


class DouyinClientManager:
    """
    抖音客户端管理器

    提供客户端生命周期管理和便捷的创建方法
    """

    def __init__(self):
        self._clients = {}
        self._async_clients = {}

    def create_direct_cookies_client(self, config: DouyinConfig = None) -> httpx.Client:
        return self.create_common_client(config)

    def create_async_client(self, config: DouyinConfig = None) -> httpx.AsyncClient:
        return self.create_common_client(config, account_provider=DefaultAccountProvider())

    def create_common_client(
        self, config: DouyinConfig = None, account_provider=None, proxy_provider=None, enhancer=None, **kwargs
    ) -> httpx.AsyncClient:
        """
        创建异步抖音客户端

        Args:
            config: 抖音配置
            account_provider: 账户提供者
            proxy_provider: 代理提供者
            enhancer: 请求增强器
            **kwargs: 传递给 httpx.AsyncClient 的其他参数

        Returns:
            配置好的 httpx.AsyncClient 实例
        """
        config = config or DouyinConfig()
        account_provider = account_provider
        proxy_provider = proxy_provider or DefaultProxyProvider()
        enhancer = enhancer or DouyinRequestEnhancer(config)

        # 创建动态传输层
        transport = DynamicProxyTransport(
            platform=Platform.DOUYIN,
            account_provider=account_provider,
            proxy_provider=proxy_provider,
            request_enhancer=enhancer,
        )

        # 设置默认参数
        client_kwargs = {
            "transport": transport,
            "timeout": config.timeout,
            "verify": config.verify_ssl,
            "follow_redirects": config.follow_redirects,
            "headers": config.default_headers,
        }
        client_kwargs.update(kwargs)

        client = httpx.AsyncClient(**client_kwargs)

        # 缓存客户端
        client_id = id(client)
        self._async_clients[client_id] = {
            "client": client,
            "config": config,
            "enhancer": enhancer,
            "transport": transport,
        }

        logger.info(f"抖音异步客户端创建成功: {client_id}")
        return client

    def close_client(self, client: httpx.Client):
        """关闭同步客户端"""
        client_id = id(client)
        if client_id in self._clients:
            client.close()
            del self._clients[client_id]
            logger.info(f"抖音同步客户端已关闭: {client_id}")

    async def close_async_client(self, client: httpx.AsyncClient):
        """关闭异步客户端"""
        client_id = id(client)
        if client_id in self._async_clients:
            await client.aclose()
            del self._async_clients[client_id]
            logger.info(f"抖音异步客户端已关闭: {client_id}")

    def close_all(self):
        """关闭所有同步客户端"""
        for client_info in self._clients.values():
            try:
                client_info["client"].close()
            except Exception as e:
                logger.error(f"关闭同步客户端失败: {e}")
        self._clients.clear()
        logger.info("所有抖音同步客户端已关闭")

    async def close_all_async(self):
        """关闭所有异步客户端"""
        for client_info in self._async_clients.values():
            try:
                await client_info["client"].aclose()
            except Exception as e:
                logger.error(f"关闭异步客户端失败: {e}")
        self._async_clients.clear()
        logger.info("所有抖音异步客户端已关闭")


# 创建全局客户端管理器
client_manager = DouyinClientManager()

logger.info("抖音客户端管理器已初始化，所有客户端将通过 client_manager 按需创建")
