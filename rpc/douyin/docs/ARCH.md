# rpc/douyin 模块架构文档

`rpc/douyin` 模块遵循 `rpc/common` 中定义的 **“三步增强模式”**，构建了一个职责清晰、可扩展、易于维护的抖音 RPC 模块。

## 1. 核心理念与目标

本模块遵循 `rpc/common/example.py` 中展示的设计模式：

1. **职责分离**:
   * **账户管理 (`AccountProvider`)**: 专门负责提供和管理抖音账号的 Cookies 和身份信息。
   * **代理管理 (`ProxyProvider`)**: 专门负责提供代理 IP。
   * **请求增强 (`RequestEnhancer`)**: 专门负责处理抖音特有的请求参数，如 `X-Bogus` 等签名算法。
   * **API 调用**: 业务逻辑代码只负责调用 API，无需关心底层实现细节。

2. **业务方自治**:
   * 创建一个抖音专用的、预配置好的 `httpx.Client` (`douyin_client`)。
   * 所有与抖音相关的业务都将复用这个客户端实例。

3. **目标**:
   * 提供一个统一、稳定的抖音 API 调用客户端。
   * 代码结构清晰，符合单一职责原则。
   * 提高代码的可测试性和可维护性。
   * 为未来接入更多平台提供一个标准的、可复制的模式。

## 2. 目录结构

```text
rpc/
├── common/          # (已存在) 存放通用 RPC 组件
│   ├── __init__.py
│   ├── base.py
│   └── example.py
└── douyin/          # 抖音 RPC 模块
    ├── __init__.py  # 导出 douyin_client，方便业务方使用
    ├── client.py    # 负责组装和初始化抖音专用客户端
    ├── enhancer.py  # 存放抖音请求增强器 (DouyinRequestEnhancer)
    ├── api.py       # 存放具体的业务 API 实现
    └── docs/
        └── INTRO.md # (本文档)
```

## 3. UML 设计

### 3.1. 类图 (Class Diagram)

这张图展示了新模块中各个组件之间的关系。

```mermaid
classDiagram
    direction LR

    class httpx.Client {
        +transport
        +get()
        +post()
    }

    class DynamicProxyTransport {
        +platform
        +account_provider
        +proxy_provider
        +request_enhancer
        +handle_request()
    }

    class RequestEnhancer {
        <<Interface>>
        +enhance_request(request)
    }

    class AccountProvider {
        <<Interface>>
        +get_account(platform)
    }

    class ProxyProvider {
        <<Interface>>
        +get_proxy()
    }

    class DouyinRequestEnhancer {
        +enhance_request(request)
    }

    class DatabaseAccountProvider {
        +get_account(platform)
    }

    class DefaultProxyProvider {
        +get_proxy()
    }

    class DouyinAPI {
        -client: httpx.Client
        +get_video_data()
        +get_author_data()
    }

    httpx.Client o-- DynamicProxyTransport
    DynamicProxyTransport o-- RequestEnhancer
    DynamicProxyTransport o-- AccountProvider
    DynamicProxyTransport o-- ProxyProvider

    RequestEnhancer <|-- DouyinRequestEnhancer
    AccountProvider <|-- DatabaseAccountProvider
    ProxyProvider <|-- DefaultProxyProvider

    DouyinAPI o-- httpx.Client

    namespace rpc.common {
        class DynamicProxyTransport
        class RequestEnhancer
        class AccountProvider
        class ProxyProvider
        class DatabaseAccountProvider
        class DefaultProxyProvider
    }

    namespace rpc.douyin {
        class DouyinRequestEnhancer
        class DouyinAPI
    }
```

* **核心**: `DynamicProxyTransport` 像一个调度中心，它组合了 `AccountProvider`、`ProxyProvider` 和 `RequestEnhancer`。
* **实现**: 我们为抖音创建了一个具体的 `DouyinRequestEnhancer`，并复用通用的 `DatabaseAccountProvider` 和 `DefaultProxyProvider`。
* **使用**: `DouyinAPI` (业务代码) 将通过一个预配置的 `httpx.Client` 实例来发起请求，完全无需感知底层的复杂性。

### 3.2. 序列图 (Sequence Diagram)

这张图展示了一次完整的 API 调用流程。

```mermaid
sequenceDiagram
    participant BusinessLogic as 业务逻辑
    participant DouyinAPI as douyin.api
    participant douyin_client as douyin.client
    participant DynamicProxyTransport as common.transport
    participant DouyinRequestEnhancer as douyin.enhancer
    participant DatabaseAccountProvider as common.account
    participant DefaultProxyProvider as common.proxy

    BusinessLogic->>DouyinAPI: 调用 get_video_data(params)
    DouyinAPI->>douyin_client: client.get(url, params=params)
    douyin_client->>DynamicProxyTransport: handle_request(request)

    DynamicProxyTransport->>DatabaseAccountProvider: get_account("douyin")
    DatabaseAccountProvider-->>DynamicProxyTransport: 返回 AccountInfo (含 cookies)

    DynamicProxyTransport->>DefaultProxyProvider: get_proxy()
    DefaultProxyProvider-->>DynamicProxyTransport: 返回 Proxy Info (或 None)

    DynamicProxyTransport->>DouyinRequestEnhancer: enhance_request(request)
    Note right of DouyinRequestEnhancer: 1. 计算 X-Bogus<br/>2. 添加其他必要 headers
    DouyinRequestEnhancer-->>DynamicProxyTransport: 完成请求增强

    DynamicProxyTransport->>Internet: 发送最终的 HTTP 请求
    Internet-->>DynamicProxyTransport: 返回 HTTP 响应
    DynamicProxyTransport-->>douyin_client: 返回 Response
    douyin_client-->>DouyinAPI: 返回 Response
    DouyinAPI-->>BusinessLogic: 返回处理后的数据
```

## 4. 流程图 (Flowchart)

这个流程图更直观地展示了请求处理的每一步。

```mermaid
graph TD
    A[开始: 业务代码调用 API] --> B{使用预配置的<br>douyin_client 发起请求};
    B --> C[请求进入 DynamicProxyTransport];
    C --> D{获取账户信息<br>(DatabaseAccountProvider)};
    D --> E{获取代理信息<br>(DefaultProxyProvider)};
    E --> F{增强请求<br>(DouyinRequestEnhancer)};
    F --> G[组合最终请求<br>(URL, Headers, Cookies, Proxy)];
    G --> H{发送 HTTP 请求到抖音服务器};
    H --> I[接收响应];
    I --> J[返回响应给业务代码];
    J --> K[结束];

    subgraph "rpc.douyin"
        A
        J
        K
    end

    subgraph "rpc.common & httpx"
        B
        C
        D
        E
        F
        G
        H
        I
    end
```

## 5. 如何使用

所有业务代码都应该通过导入预配置的客户端 `douyin_client` 来与抖音 API 进行交互。

**示例**:

```python
# 在你的业务逻辑文件中
from rpc.douyin import douyin_client
from rpc.douyin.api import get_user_profile  # 假设 api.py 中有此函数

async def fetch_douyin_user(user_id: str):
    """
    获取抖音用户信息的示例
    """
    # 直接调用封装好的 API 函数
    user_data = await get_user_profile(user_id)
    print(user_data)

    # 或者直接使用 client
    # 无需关心 cookie、proxy 和签名
    response = await douyin_client.get(
        "https://www.douyin.com/aweme/v1/web/user/profile/other/",
        params={"sec_user_id": user_id}
    )
    response.raise_for_status()
    print(response.json())

```

这种方式极大地简化了业务代码，使其只专注于业务逻辑本身。
