# 抖音HTML页面请求处理模块设计文档

## 概述

本模块专门处理抖音HTML页面的请求，包括精选页面和视频分享页面。该模块将集成账号Cookie管理和IP代理功能，返回HTML内容供后续解析使用。

## 设计目标

1. **专门处理HTML请求**：区别于API接口请求，专注于获取HTML页面内容
2. **集成Cookie管理**：自动从数据库获取和管理抖音账号的Cookie
3. **支持IP代理**：集成代理池功能，提高请求成功率
4. **灵活的URL处理**：支持多种抖音URL格式
5. **错误处理和重试**：具备完善的异常处理和重试机制

## 功能特性

### 支持的URL类型

1. **精选页面URL**：`https://www.douyin.com/jingxuan?modal_id={aweme_id}`
2. **移动端分享URL**：`https://m.douyin.com/share/video/{aweme_id}`

### 核心功能

1. **HTML内容获取**：获取完整的HTML页面内容
2. **Cookie轮换**：自动轮换可用的账号Cookie
3. **代理支持**：集成IP代理池功能
4. **请求头伪装**：模拟真实浏览器请求头
5. **反爬虫应对**：处理验证码、限流等反爬虫机制

## 模块结构

```
rpc/douyin/html_handler/
├── __init__.py                 # 模块初始化和导出
├── client.py                   # HTML请求客户端
├── config.py                   # 配置管理
├── exceptions.py               # 异常定义
├── schemas.py                  # 请求/响应模型
├── utils.py                    # 工具函数
└── tests/
    ├── __init__.py
    ├── test_client.py          # 客户端测试
    ├── test_integration.py     # 集成测试
    └── test_mock.py            # Mock测试
```

## 核心组件设计

### 1. 配置管理 (config.py)

```python
from dataclasses import dataclass
from typing import Optional, Dict, List

@dataclass
class DouyinHTMLConfig:
    \"\"\"抖音HTML请求配置\"\"\"
    request_timeout: int = 30
    max_retries: int = 3
    retry_delay: float = 1.0
    enable_proxy: bool = True
    enable_cookie_rotation: bool = True
    default_headers: Dict[str, str] = None
    supported_domains: List[str] = None
    
    def __post_init__(self):
        if self.default_headers is None:
            self.default_headers = {
                'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
            }
        
        if self.supported_domains is None:
            self.supported_domains = [
                'www.douyin.com',
                'm.douyin.com',
                'v.douyin.com'
            ]
```

### 2. 请求/响应模型 (schemas.py)

```python
from typing import Optional, Dict, Any
from pydantic import BaseModel, HttpUrl, validator
from enum import Enum

class URLType(str, Enum):
    \"\"\"URL类型枚举\"\"\"
    JINGXUAN = \"jingxuan\"
    MOBILE_SHARE = \"mobile_share\"
    PC_VIDEO = \"pc_video\"
    USER_PROFILE = \"user_profile\"

class HTMLRequest(BaseModel):
    \"\"\"HTML请求模型\"\"\"
    url: HttpUrl
    url_type: URLType
    aweme_id: Optional[str] = None
    sec_uid: Optional[str] = None
    use_proxy: bool = True
    custom_headers: Optional[Dict[str, str]] = None
    timeout: Optional[int] = None
    
    @validator('aweme_id')
    def validate_aweme_id(cls, v, values):
        url_type = values.get('url_type')
        if url_type in [URLType.JINGXUAN, URLType.MOBILE_SHARE, URLType.PC_VIDEO] and not v:
            raise ValueError(f\"aweme_id is required for {url_type}\")
        return v

class HTMLResponse(BaseModel):
    \"\"\"HTML响应模型\"\"\"
    success: bool
    html_content: Optional[str] = None
    status_code: int
    headers: Dict[str, str]
    url: str
    final_url: Optional[str] = None  # 重定向后的最终URL
    response_time: float
    proxy_used: Optional[str] = None
    cookie_used: Optional[str] = None
    error_message: Optional[str] = None
    retry_count: int = 0

class JingxuanRequest(BaseModel):
    \"\"\"精选页面请求\"\"\"
    aweme_id: str
    use_proxy: bool = True
    custom_headers: Optional[Dict[str, str]] = None

class MobileShareRequest(BaseModel):
    \"\"\"移动端分享页面请求\"\"\"
    aweme_id: str
    use_proxy: bool = True
    custom_headers: Optional[Dict[str, str]] = None
```

### 3. 异常定义 (exceptions.py)

```python
class DouyinHTMLError(Exception):
    \"\"\"抖音HTML请求基础异常\"\"\"
    pass

class InvalidURLError(DouyinHTMLError):
    \"\"\"无效URL异常\"\"\"
    pass

class ProxyError(DouyinHTMLError):
    \"\"\"代理异常\"\"\"
    pass

class CookieError(DouyinHTMLError):
    \"\"\"Cookie异常\"\"\"
    pass

class RequestTimeoutError(DouyinHTMLError):
    \"\"\"请求超时异常\"\"\"
    pass

class AntiCrawlerError(DouyinHTMLError):
    \"\"\"反爬虫异常\"\"\"
    pass

class RateLimitError(DouyinHTMLError):
    \"\"\"限流异常\"\"\"
    pass
```

### 4. HTML请求客户端 (client.py)

```python
import asyncio
import time
from typing import Optional, Dict, List
import httpx
from urllib.parse import urlparse, parse_qs

from .config import DouyinHTMLConfig
from .schemas import HTMLRequest, HTMLResponse, URLType, JingxuanRequest, MobileShareRequest
from .exceptions import *
from ..exceptions import DouyinClientError

class DouyinHTMLClient:
    \"\"\"抖音HTML请求客户端\"\"\"
    
    def __init__(self, config: Optional[DouyinHTMLConfig] = None):
        self.config = config or DouyinHTMLConfig()
        self._session: Optional[httpx.AsyncClient] = None
    
    async def __aenter__(self):
        await self._ensure_session()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self._session:
            await self._session.aclose()
    
    async def _ensure_session(self):
        \"\"\"确保HTTP会话存在\"\"\"
        if not self._session:
            self._session = httpx.AsyncClient(
                timeout=httpx.Timeout(self.config.request_timeout),
                follow_redirects=True,
                headers=self.config.default_headers
            )
    
    def _parse_url_type(self, url: str) -> tuple[URLType, Optional[str], Optional[str]]:
        \"\"\"解析URL类型和提取ID\"\"\"
        parsed = urlparse(url)
        domain = parsed.netloc
        path = parsed.path
        query = parse_qs(parsed.query)
        
        # 精选页面
        if domain == 'www.douyin.com' and path == '/jingxuan':
            modal_id = query.get('modal_id', [None])[0]
            return URLType.JINGXUAN, modal_id, None
        
        # 移动端分享页面
        elif domain == 'm.douyin.com' and path.startswith('/share/video/'):
            aweme_id = path.split('/')[-1]
            return URLType.MOBILE_SHARE, aweme_id, None
        
        # PC端视频页面
        elif domain == 'www.douyin.com' and path.startswith('/video/'):
            aweme_id = path.split('/')[-1]
            return URLType.PC_VIDEO, aweme_id, None
        
        # 用户主页
        elif domain == 'www.douyin.com' and path.startswith('/user/'):
            sec_uid = path.split('/')[-1]
            return URLType.USER_PROFILE, None, sec_uid
        
        else:
            raise InvalidURLError(f\"Unsupported URL format: {url}\")
    
    async def _get_account_cookie(self) -> Optional[str]:
        \"\"\"从数据库获取可用的账号Cookie\"\"\"
        try:
            from models.enums import Platform
            from models.system.crawler import CrawlerCookiesAccount
            
            account = await CrawlerCookiesAccount.filter(
                platform_name=Platform.DOUYIN, 
                status=0
            ).first()
            
            if account and account.cookies:
                return account.cookies
            return None
        except Exception as e:
            raise CookieError(f\"Failed to get account cookie: {e}\")
    
    async def _get_proxy(self) -> Optional[Dict[str, str]]:
        \"\"\"获取代理配置\"\"\"
        try:
            # 这里集成代理池功能
            # 返回格式: {\"http://\": \"proxy_url\", \"https://\": \"proxy_url\"}
            # 具体实现需要根据现有的代理池模块来适配
            pass
        except Exception as e:
            raise ProxyError(f\"Failed to get proxy: {e}\")
    
    async def fetch_html(self, request: HTMLRequest) -> HTMLResponse:
        \"\"\"获取HTML内容\"\"\"
        start_time = time.time()
        retry_count = 0
        last_error = None
        
        # 获取Cookie
        cookie = await self._get_account_cookie() if self.config.enable_cookie_rotation else None
        
        # 获取代理
        proxy = await self._get_proxy() if (request.use_proxy and self.config.enable_proxy) else None
        
        await self._ensure_session()
        
        for attempt in range(self.config.max_retries + 1):
            try:
                # 准备请求头
                headers = self.config.default_headers.copy()
                if request.custom_headers:
                    headers.update(request.custom_headers)
                
                # 添加Cookie
                if cookie:
                    headers['Cookie'] = cookie
                
                # 发送请求
                response = await self._session.get(
                    str(request.url),
                    headers=headers,
                    proxies=proxy,
                    timeout=request.timeout or self.config.request_timeout
                )
                
                response_time = time.time() - start_time
                
                # 检查反爬虫
                if self._is_anti_crawler_response(response):
                    raise AntiCrawlerError(\"Detected anti-crawler protection\")
                
                # 检查限流
                if response.status_code == 429:
                    raise RateLimitError(\"Rate limit exceeded\")
                
                return HTMLResponse(
                    success=True,
                    html_content=response.text,
                    status_code=response.status_code,
                    headers=dict(response.headers),
                    url=str(request.url),
                    final_url=str(response.url),
                    response_time=response_time,
                    proxy_used=proxy.get('http://') if proxy else None,
                    cookie_used=cookie[:50] + \"...\" if cookie else None,
                    retry_count=retry_count
                )
                
            except Exception as e:
                last_error = e
                retry_count += 1
                
                if attempt < self.config.max_retries:
                    await asyncio.sleep(self.config.retry_delay * (attempt + 1))
                    continue
                else:
                    break
        
        # 返回失败响应
        response_time = time.time() - start_time
        return HTMLResponse(
            success=False,
            status_code=0,
            headers={},
            url=str(request.url),
            response_time=response_time,
            error_message=str(last_error),
            retry_count=retry_count
        )
    
    def _is_anti_crawler_response(self, response: httpx.Response) -> bool:
        \"\"\"检测是否遇到反爬虫保护\"\"\"
        # 检查状态码
        if response.status_code in [403, 418, 429]:
            return True
        
        # 检查响应内容
        content = response.text.lower()
        anti_crawler_keywords = [
            'captcha', '验证码', 'robot', '机器人',
            'blocked', '封禁', 'forbidden', '禁止'
        ]
        
        return any(keyword in content for keyword in anti_crawler_keywords)
    
    async def fetch_jingxuan_page(self, request: JingxuanRequest) -> HTMLResponse:
        \"\"\"获取精选页面HTML\"\"\"
        url = f\"https://www.douyin.com/jingxuan?modal_id={request.aweme_id}\"
        html_request = HTMLRequest(
            url=url,
            url_type=URLType.JINGXUAN,
            aweme_id=request.aweme_id,
            use_proxy=request.use_proxy,
            custom_headers=request.custom_headers
        )
        return await self.fetch_html(html_request)
    
    async def fetch_mobile_share_page(self, request: MobileShareRequest) -> HTMLResponse:
        \"\"\"获取移动端分享页面HTML\"\"\"
        url = f\"https://m.douyin.com/share/video/{request.aweme_id}\"
        html_request = HTMLRequest(
            url=url,
            url_type=URLType.MOBILE_SHARE,
            aweme_id=request.aweme_id,
            use_proxy=request.use_proxy,
            custom_headers=request.custom_headers
        )
        return await self.fetch_html(html_request)

# 创建默认客户端实例
html_client = DouyinHTMLClient()
```

### 5. 工具函数 (utils.py)

```python
import re
from typing import Optional, Dict, Any
from urllib.parse import urlparse, parse_qs

def extract_aweme_id_from_url(url: str) -> Optional[str]:
    \"\"\"从URL中提取aweme_id\"\"\"
    # 精选页面
    if 'jingxuan' in url:
        parsed = urlparse(url)
        query = parse_qs(parsed.query)
        return query.get('modal_id', [None])[0]
    
    # 移动端分享和PC端视频
    pattern = r'/(?:share/)?video/(\\d+)'
    match = re.search(pattern, url)
    return match.group(1) if match else None

def extract_sec_uid_from_url(url: str) -> Optional[str]:
    \"\"\"从URL中提取sec_uid\"\"\"
    pattern = r'/user/([^/?]+)'
    match = re.search(pattern, url)
    return match.group(1) if match else None

def build_jingxuan_url(aweme_id: str) -> str:
    \"\"\"构建精选页面URL\"\"\"
    return f\"https://www.douyin.com/jingxuan?modal_id={aweme_id}\"

def build_mobile_share_url(aweme_id: str) -> str:
    \"\"\"构建移动端分享URL\"\"\"
    return f\"https://m.douyin.com/share/video/{aweme_id}\"

def build_pc_video_url(aweme_id: str) -> str:
    \"\"\"构建PC端视频URL\"\"\"
    return f\"https://www.douyin.com/video/{aweme_id}\"

def parse_cookie_string(cookie_str: str) -> Dict[str, str]:
    \"\"\"解析Cookie字符串\"\"\"
    cookies = {}
    for item in cookie_str.split(';'):
        if '=' in item:
            key, value = item.strip().split('=', 1)
            cookies[key] = value
    return cookies

def format_response_summary(response: 'HTMLResponse') -> str:
    \"\"\"格式化响应摘要\"\"\"
    status = \"成功\" if response.success else \"失败\"
    proxy_info = f\" (代理: {response.proxy_used})\" if response.proxy_used else \"\"
    retry_info = f\" (重试: {response.retry_count}次)\" if response.retry_count > 0 else \"\"
    
    return f\"请求{status}: {response.status_code} - {response.response_time:.2f}s{proxy_info}{retry_info}\"
```

## 使用示例

### 基础使用

```python
from rpc.douyin.html_handler import DouyinHTMLClient, JingxuanRequest, MobileShareRequest

# 创建客户端
async def example_usage():
    async with DouyinHTMLClient() as client:
        # 获取精选页面
        jingxuan_request = JingxuanRequest(aweme_id=\"**********\")
        jingxuan_response = await client.fetch_jingxuan_page(jingxuan_request)
        
        if jingxuan_response.success:
            logger.info(f\"精选页面HTML长度: {len(jingxuan_response.html_content)}\")
        
        # 获取移动端分享页面
        mobile_request = MobileShareRequest(aweme_id=\"**********\")
        mobile_response = await client.fetch_mobile_share_page(mobile_request)
        
        if mobile_response.success:
            logger.info(f\"移动端页面HTML长度: {len(mobile_response.html_content)}\")
```

### 高级使用

```python
from rpc.douyin.html_handler import DouyinHTMLClient, HTMLRequest, URLType
from rpc.douyin.html_handler.config import DouyinHTMLConfig

async def advanced_usage():
    # 自定义配置
    config = DouyinHTMLConfig(
        request_timeout=45,
        max_retries=5,
        enable_proxy=True,
        enable_cookie_rotation=True
    )
    
    async with DouyinHTMLClient(config) as client:
        # 通用HTML请求
        request = HTMLRequest(
            url=\"https://www.douyin.com/jingxuan?modal_id=**********\",
            url_type=URLType.JINGXUAN,
            aweme_id=\"**********\",
            use_proxy=True,
            custom_headers={
                \"Referer\": \"https://www.douyin.com/\",
                \"X-Requested-With\": \"XMLHttpRequest\"
            }
        )
        
        response = await client.fetch_html(request)
        
        if response.success:
            logger.info(f\"成功获取HTML: {len(response.html_content)} 字符\")
            logger.info(f\"响应时间: {response.response_time:.2f}s\")
            logger.info(f\"使用代理: {response.proxy_used}\")
        else:
            logger.error(f\"请求失败: {response.error_message}\")
```

## 集成方案

### 与现有控制器集成

在 `controllers/trendinsight.py` 中的使用示例：

```python
from rpc.douyin.html_handler import DouyinHTMLClient, JingxuanRequest, MobileShareRequest

class TrendInsightController:
    def __init__(self):
        self.html_client = DouyinHTMLClient()
    
    async def _fetch_single_video_detail_html(self, aweme_id: str) -> Optional[str]:
        \"\"\"通过HTML页面获取视频详情\"\"\"
        try:
            async with self.html_client as client:
                # 优先尝试移动端页面（通常更稳定）
                mobile_request = MobileShareRequest(aweme_id=aweme_id)
                mobile_response = await client.fetch_mobile_share_page(mobile_request)
                
                if mobile_response.success:
                    return mobile_response.html_content
                
                # fallback到精选页面
                jingxuan_request = JingxuanRequest(aweme_id=aweme_id)
                jingxuan_response = await client.fetch_jingxuan_page(jingxuan_request)
                
                if jingxuan_response.success:
                    return jingxuan_response.html_content
                
                return None
                
        except Exception as e:
            self.logger.error(f\"HTML获取失败: {e}\")
            return None
```

## 测试策略

### 单元测试

```python
# tests/test_client.py
import pytest
from unittest.mock import AsyncMock, patch
from rpc.douyin.html_handler import DouyinHTMLClient, JingxuanRequest

@pytest.mark.asyncio
async def test_fetch_jingxuan_page():
    client = DouyinHTMLClient()
    request = JingxuanRequest(aweme_id=\"**********\")
    
    with patch.object(client, '_get_account_cookie', return_value=\"test_cookie\"):
        with patch.object(client._session, 'get') as mock_get:
            mock_response = AsyncMock()
            mock_response.text = \"<html>test</html>\"
            mock_response.status_code = 200
            mock_response.headers = {}
            mock_response.url = \"https://www.douyin.com/jingxuan?modal_id=**********\"
            mock_get.return_value = mock_response
            
            response = await client.fetch_jingxuan_page(request)
            
            assert response.success is True
            assert response.html_content == \"<html>test</html>\"
```

### 集成测试

```python
# tests/test_integration.py
import pytest
from rpc.douyin.html_handler import DouyinHTMLClient, JingxuanRequest

@pytest.mark.asyncio
@pytest.mark.integration
async def test_real_jingxuan_request():
    \"\"\"真实环境集成测试\"\"\"
    client = DouyinHTMLClient()
    request = JingxuanRequest(aweme_id=\"**********\", use_proxy=False)
    
    async with client:
        response = await client.fetch_jingxuan_page(request)
        
        # 根据实际情况调整断言
        assert response.status_code in [200, 302, 403]
        if response.success:
            assert len(response.html_content) > 0
            assert \"douyin\" in response.html_content.lower()
```

## 部署和监控

### 配置管理

建议通过环境变量或配置文件管理关键参数：

```python
# config.py 扩展
import os
from typing import Optional

class DouyinHTMLConfig:
    def __init__(self):
        self.request_timeout = int(os.getenv('DOUYIN_HTML_TIMEOUT', '30'))
        self.max_retries = int(os.getenv('DOUYIN_HTML_MAX_RETRIES', '3'))
        self.enable_proxy = os.getenv('DOUYIN_HTML_ENABLE_PROXY', 'true').lower() == 'true'
        self.enable_cookie_rotation = os.getenv('DOUYIN_HTML_ENABLE_COOKIE_ROTATION', 'true').lower() == 'true'
```

### 日志和监控

```python
import logging
from datetime import datetime

class DouyinHTMLClient:
    def __init__(self, config: Optional[DouyinHTMLConfig] = None):
        self.config = config or DouyinHTMLConfig()
        self.logger = logging.getLogger(__name__)
    
    async def fetch_html(self, request: HTMLRequest) -> HTMLResponse:
        request_id = f\"html_req_{int(time.time())}\"
        self.logger.info(f\"[{request_id}] Starting HTML request: {request.url}\")
        
        try:
            response = await self._do_fetch_html(request)
            
            self.logger.info(f\"[{request_id}] Request completed: \"
                           f\"success={response.success}, \"
                           f\"status={response.status_code}, \"
                           f\"time={response.response_time:.2f}s\")
            
            return response
            
        except Exception as e:
            self.logger.error(f\"[{request_id}] Request failed: {e}\")
            raise
```

## 维护和扩展

### 扩展点

1. **新URL类型支持**：在 `URLType` 枚举和 `_parse_url_type` 方法中添加
2. **新的反爬虫检测**：在 `_is_anti_crawler_response` 方法中扩展
3. **代理池集成**：在 `_get_proxy` 方法中实现具体的代理获取逻辑
4. **Cookie管理策略**：在 `_get_account_cookie` 方法中实现更复杂的轮换策略

### 性能优化

1. **连接池复用**：使用 httpx 的连接池功能
2. **缓存机制**：对相同URL的请求进行短期缓存
3. **并发控制**：限制同时发起的请求数量
4. **资源清理**：确保HTTP连接正确关闭

这个设计文档提供了一个完整的抖音HTML请求处理模块的架构和实现方案，具备良好的扩展性和维护性。