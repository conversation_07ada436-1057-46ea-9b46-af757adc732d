# 抖音 (Do<PERSON><PERSON>) RPC 模块

本模块提供了一个现代、稳定且易于使用的抖音（Douyin） API 客户端。它基于 `rpc/common` 中定义的 **“三步增强模式”** 构建，旨在为开发者提供最简洁的调用体验。

与 `ARCH.md` 中深入探讨的内部架构和重构计划不同，本文档专注于 **如何使用** 本模块与抖音 API 进行交互。

## 🚀 核心特性

- **极简调用**: 无需手动管理 Cookies、代理或请求签名（如 `msToken`, `X-Bogus`），模块全自动处理。
- **架构先进**: 采用“三步增强模式”，实现职责分离，稳定可靠。
- **功能全面**: 支持抖音的核心 API。
- **异步优先**: 基于 `httpx` 构建，提供高性能的异步接口。
- **类型安全**: 完整的类型注解和 `Pydantic` 模型，提升开发体验。
- **开发者友好**: 提供统一的错误处理和详细的日志。

## 目录结构

以下是 `rpc/douyin` 模块的核心文件和目录结构：

```
.
├── __init__.py
├── api.py              # 封装了具体的业务 API 函数
├── client.py           # 定义了核心的 douyin_client
├── config.py           # 模块的配置文件
├── docs/               # 文档目录
│   ├── ARCH.md         # 架构设计文档
│   └── README.md       # 本文档
├── enhancer.py         # 实现了“三步增强模式”中的增强器
├── examples/           # 使用示例
├── exceptions.py       # 自定义异常
├── integration_tests/  # 集成测试
├── models.py           # Pydantic 模型
├── signer/             # 请求签名逻辑
├── tests/              # 单元测试
├── utils.py            # 工具函数
└── verify_params.py    # 参数校验逻辑
```

## 快速开始

所有与抖音的交互都应通过预配置的客户端 `douyin_client` 进行，它封装了所有底层的复杂性。

### 使用示例

以下示例展示了如何轻松地获取指定关键词的视频列表。

```python
import asyncio
from rpc.douyin.client import douyin_client

KEYWORD = "在这里替换为你想搜索的关键词"

async def fetch_video_search():
    """
    获取抖音视频搜索结果的示例。
    """
    if not KEYWORD or "在这里替换" in KEYWORD:
        print("请在代码中设置有效的 KEYWORD。")
        return

    try:
        # 定义请求参数
        params = {
            "keyword": KEYWORD,
            "count": 10,
        }

        # 使用全局客户端发起请求
        response = await douyin_client.get(
            "https://www.douyin.com/aweme/v1/web/search/item/",
            params=params
        )

        response.raise_for_status()
        data = response.json()
        video_list = data.get("data", {}).get("aweme_list", [])

        if video_list:
            print(f"成功获取到 {len(video_list)} 条视频数据：")
            for video in video_list[:3]:
                print(f"  - 视频标题: {video.get('desc')}")
                print(f"    作者: {video.get('author', {}).get('nickname')}")
        else:
            print("未能从响应中找到视频数据。")
            print("响应内容:", data)

    except Exception as e:
        print(f"请求失败: {e}")

if __name__ == "__main__":
    asyncio.run(fetch_video_search())
```

## API 调用方式

推荐使用全局的 `douyin_client` 实例，它已经完成了所有必要的配置。

### 直接使用 `douyin_client`

你可以像使用普通的 `httpx.AsyncClient` 一样使用 `douyin_client`，所有请求都会被自动增强。

```python
from rpc.douyin.client import douyin_client

async def get_user_info(sec_user_id: str):
    params = {
        "sec_user_id": sec_user_id,
    }
    response = await douyin_client.get(
        "https://www.douyin.com/aweme/v1/web/user/profile/other/",
        params=params
    )
    return response.json()
```

### 使用封装的 API 函数 (如果 `api.py` 中已定义)

为了进一步简化调用，可以在 `rpc/douyin/api.py` 中封装具体的业务函数。

```python
# rpc/douyin/api.py
from .client import douyin_client

async def search_videos_by_keyword(keyword: str, count: int = 10):
    """获取视频搜索结果"""
    params = {"keyword": keyword, "count": count}
    response = await douyin_client.get(
        "https://www.douyin.com/aweme/v1/web/search/item/",
        params=params
    )
    response.raise_for_status()
    return response.json().get("data", {}).get("aweme_list", [])

# 在业务代码中调用
# from rpc.douyin.api import search_videos_by_keyword
# videos = await search_videos_by_keyword("美食")
```

## 错误处理

本模块定义了自定义异常，继承自 `rpc.common.base.RPCError`。在调用时，建议使用 `try...except` 块来捕获潜在的错误。

```python
import httpx
from rpc.common.base import RPCError

try:
    # ... 调用 API ...
    response.raise_for_status()
except httpx.HTTPStatusError as e:
    print(f"HTTP 错误: {e.response.status_code} - {e.response.text}")
except RPCError as e:
    print(f"RPC 模块内部错误: {e}")
except Exception as e:
    print(f"未知错误: {e}")
```

## 深入了解架构

如果你对本模块的内部实现、设计模式或未来的重构计划感兴趣，请参阅架构设计文档：
- **[./ARCH.md](./ARCH.md)**
