"""
抖音HTML请求处理模块配置

定义抖音HTML页面请求的配置参数
"""

import os
from dataclasses import dataclass, field
from typing import Dict, List

from .constants import DouyinURLConstants


@dataclass
class DouyinHTMLConfig:
    """抖音HTML请求配置"""

    # 请求配置
    request_timeout: int = 30
    max_retries: int = 3
    retry_delay: float = 1.0
    retry_backoff_factor: float = 2.0

    # 功能开关
    enable_proxy: bool = True
    enable_cookie_rotation: bool = True
    enable_anti_crawler_detection: bool = True

    # 请求头配置
    default_headers: Dict[str, str] = field(
        default_factory=lambda: {
            "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
            "Accept-Encoding": "gzip, deflate, br",
            "Connection": "keep-alive",
            "Upgrade-Insecure-Requests": "1",
            "Sec-Fetch-Dest": "document",
            "Sec-Fetch-Mode": "navigate",
            "Sec-Fetch-Site": "none",
            "Sec-Fetch-User": "?1",
            "Cache-Control": "max-age=0",
        }
    )

    # 支持的域名
    supported_domains: List[str] = field(default_factory=lambda: list(DouyinURLConstants.SUPPORTED_DOMAINS))

    # 反爬虫检测关键词
    anti_crawler_keywords: List[str] = field(
        default_factory=lambda: [
            "captcha",
            "验证码",
            "robot",
            "机器人",
            "blocked",
            "封禁",
            "forbidden",
            "禁止",
            "access denied",
            "拒绝访问",
            "too many requests",
            "请求过于频繁",
            "rate limit",
            "限流",
        ]
    )

    # 环境变量配置
    @classmethod
    def from_env(cls) -> "DouyinHTMLConfig":
        """从环境变量创建配置"""
        return cls(
            request_timeout=int(os.getenv("DOUYIN_HTML_TIMEOUT", "30")),
            max_retries=int(os.getenv("DOUYIN_HTML_MAX_RETRIES", "3")),
            retry_delay=float(os.getenv("DOUYIN_HTML_RETRY_DELAY", "1.0")),
            enable_proxy=os.getenv("DOUYIN_HTML_ENABLE_PROXY", "true").lower() == "true",
            enable_cookie_rotation=os.getenv("DOUYIN_HTML_ENABLE_COOKIE_ROTATION", "true").lower() == "true",
            enable_anti_crawler_detection=os.getenv("DOUYIN_HTML_ENABLE_ANTI_CRAWLER_DETECTION", "true").lower()
            == "true",
        )

    def to_dict(self) -> Dict:
        """转换为字典"""
        return {
            "request_timeout": self.request_timeout,
            "max_retries": self.max_retries,
            "retry_delay": self.retry_delay,
            "retry_backoff_factor": self.retry_backoff_factor,
            "enable_proxy": self.enable_proxy,
            "enable_cookie_rotation": self.enable_cookie_rotation,
            "enable_anti_crawler_detection": self.enable_anti_crawler_detection,
            "supported_domains": self.supported_domains,
            "anti_crawler_keywords": self.anti_crawler_keywords,
        }

    def update_headers(self, custom_headers: Dict[str, str]) -> Dict[str, str]:
        """更新请求头"""
        headers = self.default_headers.copy()
        if custom_headers:
            headers.update(custom_headers)
        return headers

    def is_supported_domain(self, domain: str) -> bool:
        """检查是否为支持的域名"""
        return domain in self.supported_domains

    def get_retry_delay(self, attempt: int) -> float:
        """获取重试延迟时间"""
        return self.retry_delay * (self.retry_backoff_factor**attempt)


# 创建默认配置实例
default_config = DouyinHTMLConfig()
