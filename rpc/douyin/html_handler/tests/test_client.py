"""
抖音HTML客户端单元测试
"""

from unittest.mock import AsyncMock, MagicMock, patch

import pytest

from rpc.douyin.html_handler import (
    DouyinHTMLClient,
    DouyinHTMLConfig,
    InvalidURLError,
    JingxuanRequest,
    MobileShareRequest,
)


class TestDouyinHTMLClient:
    """DouyinHTMLClient测试类"""

    @pytest.fixture
    def client(self):
        """创建测试客户端"""
        config = DouyinHTMLConfig(enable_proxy=False, enable_cookie_rotation=False, enable_anti_crawler_detection=False)
        return DouyinHTMLClient(config)

    @pytest.fixture
    def mock_response(self):
        """创建模拟响应"""
        response = MagicMock()
        response.text = "<html><head><title>抖音</title></head><body>测试内容</body></html>"
        response.status_code = 200
        response.headers = {"Content-Type": "text/html"}
        response.url = "https://www.douyin.com/jingxuan?modal_id=**********"
        return response

    @pytest.mark.asyncio
    async def test_client_context_manager(self, client):
        """测试客户端上下文管理器"""
        async with client as c:
            assert c._session is not None

        # 退出后会话应该被关闭
        assert client._session is None

    @pytest.mark.asyncio
    async def test_fetch_jingxuan_page_success(self, client, mock_response):
        """测试成功获取精选页面"""
        request = JingxuanRequest(aweme_id="**********")

        with patch.object(client, "_get_account_cookie", return_value=None):
            with patch.object(client, "_get_proxy", return_value=None):
                with patch("httpx.AsyncClient") as mock_client_class:
                    mock_client = AsyncMock()
                    mock_client.get.return_value = mock_response
                    mock_client_class.return_value = mock_client

                    client._session = mock_client

                    response = await client.fetch_jingxuan_page(request)

                    assert response.success is True
                    assert response.status_code == 200
                    assert "抖音" in response.html_content
                    assert response.aweme_id == "**********"

    @pytest.mark.asyncio
    async def test_fetch_mobile_share_page_success(self, client, mock_response):
        """测试成功获取移动端分享页面"""
        request = MobileShareRequest(aweme_id="**********")
        mock_response.url = "https://m.douyin.com/share/video/**********"

        with patch.object(client, "_get_account_cookie", return_value=None):
            with patch.object(client, "_get_proxy", return_value=None):
                with patch("httpx.AsyncClient") as mock_client_class:
                    mock_client = AsyncMock()
                    mock_client.get.return_value = mock_response
                    mock_client_class.return_value = mock_client

                    client._session = mock_client

                    response = await client.fetch_mobile_share_page(request)

                    assert response.success is True
                    assert response.status_code == 200
                    assert "抖音" in response.html_content

    @pytest.mark.asyncio
    async def test_fetch_by_url_success(self, client, mock_response):
        """测试根据URL自动识别类型"""
        url = "https://www.douyin.com/jingxuan?modal_id=**********"

        with patch.object(client, "_get_account_cookie", return_value=None):
            with patch.object(client, "_get_proxy", return_value=None):
                with patch("httpx.AsyncClient") as mock_client_class:
                    mock_client = AsyncMock()
                    mock_client.get.return_value = mock_response
                    mock_client_class.return_value = mock_client

                    client._session = mock_client

                    response = await client.fetch_by_url(url)

                    assert response.success is True
                    assert response.status_code == 200

    @pytest.mark.asyncio
    async def test_fetch_by_url_invalid_url(self, client):
        """测试无效URL"""
        url = "https://invalid-domain.com/test"

        with pytest.raises(InvalidURLError):
            await client.fetch_by_url(url)

    @pytest.mark.asyncio
    async def test_get_account_cookie_disabled(self, client):
        """测试禁用Cookie轮换时的行为"""
        client.config.enable_cookie_rotation = False

        cookie = await client._get_account_cookie()
        assert cookie is None

    @pytest.mark.asyncio
    async def test_get_proxy_disabled(self, client):
        """测试禁用代理时的行为"""
        client.config.enable_proxy = False

        proxy = await client._get_proxy()
        assert proxy is None

    def test_is_anti_crawler_response_disabled(self, client, mock_response):
        """测试禁用反爬虫检测时的行为"""
        client.config.enable_anti_crawler_detection = False

        result = client._is_anti_crawler_response(mock_response)
        assert result is False

    def test_is_anti_crawler_response_by_status_code(self, client):
        """测试通过状态码检测反爬虫"""
        client.config.enable_anti_crawler_detection = True

        mock_response = MagicMock()
        mock_response.status_code = 403

        result = client._is_anti_crawler_response(mock_response)
        assert result is True

    def test_is_anti_crawler_response_by_content(self, client):
        """测试通过内容检测反爬虫"""
        client.config.enable_anti_crawler_detection = True

        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.text = "请输入验证码以继续访问"

        result = client._is_anti_crawler_response(mock_response)
        assert result is True


class TestDouyinHTMLConfig:
    """DouyinHTMLConfig测试类"""

    def test_default_config(self):
        """测试默认配置"""
        config = DouyinHTMLConfig()

        assert config.request_timeout == 30
        assert config.max_retries == 3
        assert config.enable_proxy is True
        assert config.enable_cookie_rotation is True
        assert len(config.supported_domains) > 0
        assert "www.douyin.com" in config.supported_domains

    def test_from_env(self):
        """测试从环境变量创建配置"""
        with patch.dict(
            "os.environ",
            {"DOUYIN_HTML_TIMEOUT": "45", "DOUYIN_HTML_MAX_RETRIES": "5", "DOUYIN_HTML_ENABLE_PROXY": "false"},
        ):
            config = DouyinHTMLConfig.from_env()

            assert config.request_timeout == 45
            assert config.max_retries == 5
            assert config.enable_proxy is False

    def test_update_headers(self):
        """测试更新请求头"""
        config = DouyinHTMLConfig()
        custom_headers = {"X-Custom": "test"}

        headers = config.update_headers(custom_headers)

        assert "User-Agent" in headers
        assert headers["X-Custom"] == "test"

    def test_is_supported_domain(self):
        """测试域名支持检查"""
        config = DouyinHTMLConfig()

        assert config.is_supported_domain("www.douyin.com") is True
        assert config.is_supported_domain("invalid.com") is False

    def test_get_retry_delay(self):
        """测试重试延迟计算"""
        config = DouyinHTMLConfig(retry_delay=1.0, retry_backoff_factor=2.0)

        assert config.get_retry_delay(0) == 1.0
        assert config.get_retry_delay(1) == 2.0
        assert config.get_retry_delay(2) == 4.0
