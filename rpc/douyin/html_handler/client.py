"""
抖音HTML请求客户端

提供抖音HTML页面请求的核心功能，集成现有的provider和enhancer架构
"""

import asyncio
import time
from typing import Dict, Optional

import httpx
from loguru import logger

from models.enums import Platform
from rpc.common.transport import DynamicProxyTransport
from rpc.providers.account_provider import DefaultAccountProvider
from rpc.providers.proxy_provider import DefaultProxyProvider

from .config import DouyinHTMLConfig
from .enhancer import DouyinHTMLRequestEnhancer
from .exceptions import (
    AntiCrawlerError,
    InvalidURLError,
    NetworkError,
    RateLimitError,
    RequestTimeoutError,
)
from .schemas import (
    HTMLRequest,
    HTMLResponse,
    JingxuanRequest,
    MobileShareRequest,
    PCVideoRequest,
    URLType,
    UserProfileRequest,
)
from .utils import detect_anti_crawler_patterns, generate_request_id, parse_url_info


class DouyinHTMLClient:
    """抖音HTML请求客户端"""

    def __init__(
        self,
        config: Optional[DouyinHTMLConfig] = None,
        account_provider: Optional[DefaultAccountProvider] = None,
        proxy_provider: Optional[DefaultProxyProvider] = None,
        request_enhancer: Optional[DouyinHTMLRequestEnhancer] = None,
    ):
        """
        初始化客户端

        Args:
            config: 配置对象
            account_provider: 账户提供者
            proxy_provider: 代理提供者
            request_enhancer: 请求增强器
        """
        self.config = config or DouyinHTMLConfig()
        self.account_provider = account_provider or DefaultAccountProvider()
        self.proxy_provider = proxy_provider or DefaultProxyProvider()

        # 初始化请求增强器
        if request_enhancer is None:
            self.request_enhancer = DouyinHTMLRequestEnhancer(self.config)
        else:
            self.request_enhancer = request_enhancer

        self._session: Optional[httpx.AsyncClient] = None
        self.logger = logger.bind(component="DouyinHTMLClient")

    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self._ensure_session()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self._session:
            await self._session.aclose()
            self._session = None

    async def _ensure_session(self):
        """确保HTTP会话存在"""
        if not self._session:
            timeout = httpx.Timeout(
                connect=self.config.request_timeout,
                read=self.config.request_timeout,
                write=self.config.request_timeout,
                pool=self.config.request_timeout,
            )

            # 创建动态传输层
            transport = DynamicProxyTransport(
                platform=Platform.DOUYIN,
                account_provider=self.account_provider,
                proxy_provider=self.proxy_provider,
                request_enhancer=self.request_enhancer,
            )

            self._session = httpx.AsyncClient(
                transport=transport,
                timeout=timeout,
                follow_redirects=True,
                headers=self.config.default_headers,
                verify=True,
            )

            self.logger.debug("HTTP会话已创建（使用动态传输层）")

    async def _get_account_info(self):
        """
        获取账户信息（通过provider）

        Returns:
            账户信息或None
        """
        try:
            if not self.config.enable_cookie_rotation:
                return None

            account_info = await self.account_provider.get_account_async(Platform.DOUYIN)
            if account_info and account_info.cookies:
                self.logger.debug("通过provider获取到账号信息")
                return account_info

            self.logger.warning("未通过provider获取到账号信息")
            return None

        except Exception as e:
            self.logger.error(f"通过provider获取账号信息失败: {e}")
            return None

    async def _get_proxy_info(self):
        """
        获取代理信息（通过provider）

        Returns:
            代理信息或None
        """
        try:
            if not self.config.enable_proxy:
                return None

            proxy_info = await self.proxy_provider.get_proxy_async()
            if proxy_info:
                self.logger.debug("通过provider获取到代理信息")
                return proxy_info

            self.logger.debug("未通过provider获取到代理信息")
            return None

        except Exception as e:
            self.logger.error(f"通过provider获取代理信息失败: {e}")
            return None

    def _is_anti_crawler_response(self, response: httpx.Response) -> bool:
        """
        检测是否遇到反爬虫保护

        Args:
            response: HTTP响应

        Returns:
            是否检测到反爬虫
        """
        if not self.config.enable_anti_crawler_detection:
            return False

        # 检查状态码
        if response.status_code in [403, 418, 429]:
            return True

        # 检查响应内容
        try:
            content = response.text
            detected, keyword = detect_anti_crawler_patterns(content, self.config.anti_crawler_keywords)

            if detected:
                self.logger.warning(f"检测到反爬虫保护，关键词: {keyword}")
                return True

        except Exception as e:
            self.logger.warning(f"反爬虫检测失败: {e}")

        return False

    async def fetch_html(self, request: HTMLRequest) -> HTMLResponse:
        """
        获取HTML内容

        Args:
            request: HTML请求对象

        Returns:
            HTML响应对象
        """
        request_id = generate_request_id()
        start_time = time.time()
        retry_count = 0
        last_error = None

        self.logger.info(f"[{request_id}] 开始HTML请求: {request.url}")

        await self._ensure_session()

        for attempt in range(self.config.max_retries + 1):
            try:
                # 准备请求头（自定义头会被enhancer处理）
                headers = {}
                if request.custom_headers:
                    headers.update(request.custom_headers)

                # 发送请求（账户、代理、参数增强都由DynamicProxyTransport处理）
                response = await self._session.get(
                    str(request.url), headers=headers, timeout=request.timeout or self.config.request_timeout
                )

                response_time = time.time() - start_time

                # # 检查反爬虫
                # if self._is_anti_crawler_response(response):
                #     raise AntiCrawlerError(
                #         "检测到反爬虫保护",
                #         status_code=response.status_code,
                #         url=str(request.url),
                #         aweme_id=request.aweme_id
                #     )

                # 检查限流
                if response.status_code == 429:
                    retry_after = response.headers.get("Retry-After")
                    raise RateLimitError(
                        "请求频率限制",
                        status_code=response.status_code,
                        retry_after=int(retry_after) if retry_after else None,
                        url=str(request.url),
                        aweme_id=request.aweme_id,
                    )

                # 构建成功响应
                html_response = HTMLResponse(
                    success=True,
                    html_content=response.text,
                    status_code=response.status_code,
                    headers=dict(response.headers),
                    url=str(request.url),
                    final_url=str(response.url),
                    response_time=response_time,
                    proxy_used=None,  # 代理信息由transport管理，这里不再显示
                    cookie_used=None,  # Cookie信息由transport管理，这里不再显示
                    retry_count=retry_count,
                    request_id=request_id,
                    timestamp=start_time,
                )

                self.logger.info(f"[{request_id}] {html_response.get_summary()}")
                return html_response

            except (AntiCrawlerError, RateLimitError):
                # 这些异常不重试
                raise

            except httpx.TimeoutException as e:
                last_error = RequestTimeoutError(
                    f"请求超时: {e}",
                    timeout_duration=request.timeout or self.config.request_timeout,
                    url=str(request.url),
                    aweme_id=request.aweme_id,
                )

            except httpx.NetworkError as e:
                last_error = NetworkError(
                    f"网络错误: {e}",
                    network_error_type=type(e).__name__,
                    original_exception=e,
                    url=str(request.url),
                    aweme_id=request.aweme_id,
                )

            except Exception as e:
                last_error = NetworkError(
                    f"未知错误: {e}",
                    network_error_type="UnknownError",
                    original_exception=e,
                    url=str(request.url),
                    aweme_id=request.aweme_id,
                )

            retry_count += 1

            if attempt < self.config.max_retries:
                delay = self.config.get_retry_delay(attempt)
                self.logger.warning(f"[{request_id}] 请求失败，{delay}秒后重试 (第{attempt + 1}次): {last_error}")
                await asyncio.sleep(delay)
            else:
                self.logger.error(f"[{request_id}] 请求最终失败: {last_error}")
                break

        # 返回失败响应
        response_time = time.time() - start_time
        return HTMLResponse(
            success=False,
            status_code=0,
            headers={},
            url=str(request.url),
            response_time=response_time,
            error_message=str(last_error),
            retry_count=retry_count,
            request_id=request_id,
            timestamp=start_time,
        )

    async def fetch_jingxuan_page(self, request: JingxuanRequest) -> HTMLResponse:
        """
        获取精选页面HTML

        Args:
            request: 精选页面请求

        Returns:
            HTML响应对象
        """
        url = f"https://www.douyin.com/jingxuan?modal_id={request.aweme_id}"
        html_request = HTMLRequest(
            url=url,
            url_type=URLType.JINGXUAN,
            aweme_id=request.aweme_id,
            use_proxy=request.use_proxy,
            custom_headers=request.custom_headers,
            timeout=request.timeout,
        )
        return await self.fetch_html(html_request)

    async def fetch_mobile_share_page(self, request: MobileShareRequest) -> HTMLResponse:
        """
        获取移动端分享页面HTML

        Args:
            request: 移动端分享页面请求

        Returns:
            HTML响应对象
        """
        url = f"https://m.douyin.com/share/video/{request.aweme_id}"
        html_request = HTMLRequest(
            url=url,
            url_type=URLType.MOBILE_SHARE,
            aweme_id=request.aweme_id,
            use_proxy=request.use_proxy,
            custom_headers=request.custom_headers,
            timeout=request.timeout,
        )
        return await self.fetch_html(html_request)

    async def fetch_pc_video_page(self, request: PCVideoRequest) -> HTMLResponse:
        """
        获取PC端视频页面HTML

        Args:
            request: PC端视频页面请求

        Returns:
            HTML响应对象
        """
        url = f"https://www.douyin.com/video/{request.aweme_id}"
        html_request = HTMLRequest(
            url=url,
            url_type=URLType.PC_VIDEO,
            aweme_id=request.aweme_id,
            use_proxy=request.use_proxy,
            custom_headers=request.custom_headers,
            timeout=request.timeout,
        )
        return await self.fetch_html(html_request)

    async def fetch_user_profile_page(self, request: UserProfileRequest) -> HTMLResponse:
        """
        获取用户主页HTML

        Args:
            request: 用户主页请求

        Returns:
            HTML响应对象
        """
        url = f"https://www.douyin.com/user/{request.sec_uid}"
        html_request = HTMLRequest(
            url=url,
            url_type=URLType.USER_PROFILE,
            sec_uid=request.sec_uid,
            use_proxy=request.use_proxy,
            custom_headers=request.custom_headers,
            timeout=request.timeout,
        )
        return await self.fetch_html(html_request)

    async def fetch_by_url(
        self, url: str, use_proxy: bool = True, custom_headers: Optional[Dict[str, str]] = None
    ) -> HTMLResponse:
        """
        根据URL自动识别类型并获取HTML

        Args:
            url: 抖音URL
            use_proxy: 是否使用代理
            custom_headers: 自定义请求头

        Returns:
            HTML响应对象

        Raises:
            InvalidURLError: 无效的URL格式
        """
        try:
            url_type, aweme_id, sec_uid = parse_url_info(url)

            html_request = HTMLRequest(
                url=url,
                url_type=url_type,
                aweme_id=aweme_id,
                sec_uid=sec_uid,
                use_proxy=use_proxy,
                custom_headers=custom_headers,
            )

            return await self.fetch_html(html_request)

        except ValueError as e:
            raise InvalidURLError(str(e), url=url)


# 创建默认客户端实例
html_client = DouyinHTMLClient()
