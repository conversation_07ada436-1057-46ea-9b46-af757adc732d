"""
抖音HTML请求处理模型定义

定义请求和响应的Pydantic模型
"""

from enum import Enum
from typing import Dict, Optional

from pydantic import BaseModel, HttpUrl, field_validator


class URLType(str, Enum):
    """URL类型枚举"""

    JINGXUAN = "jingxuan"
    MOBILE_SHARE = "mobile_share"
    PC_VIDEO = "pc_video"
    USER_PROFILE = "user_profile"


class HTMLRequest(BaseModel):
    """HTML请求模型"""

    url: HttpUrl
    url_type: URLType
    aweme_id: Optional[str] = None
    sec_uid: Optional[str] = None
    use_proxy: bool = True
    custom_headers: Optional[Dict[str, str]] = None
    timeout: Optional[int] = None

    @field_validator("aweme_id")
    @classmethod
    def validate_aweme_id(cls, v, info):
        """验证aweme_id"""
        if info.data and "url_type" in info.data:
            url_type = info.data["url_type"]
            if url_type in [URLType.JINGXUAN, URLType.MOBILE_SHARE, URLType.PC_VIDEO] and not v:
                raise ValueError(f"aweme_id is required for {url_type}")
        return v

    @field_validator("sec_uid")
    @classmethod
    def validate_sec_uid(cls, v, info):
        """验证sec_uid"""
        if info.data and "url_type" in info.data:
            url_type = info.data["url_type"]
            if url_type == URLType.USER_PROFILE and not v:
                raise ValueError(f"sec_uid is required for {url_type}")
        return v


class HTMLResponse(BaseModel):
    """HTML响应模型"""

    success: bool
    html_content: Optional[str] = None
    status_code: int
    headers: Dict[str, str]
    url: str
    final_url: Optional[str] = None  # 重定向后的最终URL
    response_time: float
    proxy_used: Optional[str] = None
    cookie_used: Optional[str] = None
    error_message: Optional[str] = None
    retry_count: int = 0

    # 额外的调试信息
    request_id: Optional[str] = None
    timestamp: Optional[float] = None

    def get_summary(self) -> str:
        """获取响应摘要"""
        status = "成功" if self.success else "失败"
        proxy_info = f" (代理: {self.proxy_used})" if self.proxy_used else ""
        retry_info = f" (重试: {self.retry_count}次)" if self.retry_count > 0 else ""

        return f"请求{status}: {self.status_code} - {self.response_time:.2f}s{proxy_info}{retry_info}"

    def is_html_valid(self) -> bool:
        """检查HTML内容是否有效"""
        if not self.html_content:
            return False

        # 基本的HTML有效性检查
        html_lower = self.html_content.lower()
        return "<html" in html_lower or "<!doctype html" in html_lower or "<head" in html_lower or "<body" in html_lower

    def get_content_length(self) -> int:
        """获取内容长度"""
        return len(self.html_content) if self.html_content else 0


class JingxuanRequest(BaseModel):
    """精选页面请求"""

    aweme_id: str
    use_proxy: bool = True
    custom_headers: Optional[Dict[str, str]] = None
    timeout: Optional[int] = None

    @field_validator("aweme_id")
    @classmethod
    def validate_aweme_id(cls, v):
        """验证aweme_id格式"""
        if not v or not v.strip():
            raise ValueError("aweme_id cannot be empty")

        # 检查是否为数字字符串
        if not v.isdigit():
            raise ValueError("aweme_id must be a numeric string")

        return v.strip()


class MobileShareRequest(BaseModel):
    """移动端分享页面请求"""

    aweme_id: str
    use_proxy: bool = True
    custom_headers: Optional[Dict[str, str]] = None
    timeout: Optional[int] = None

    @field_validator("aweme_id")
    @classmethod
    def validate_aweme_id(cls, v):
        """验证aweme_id格式"""
        if not v or not v.strip():
            raise ValueError("aweme_id cannot be empty")

        # 检查是否为数字字符串
        if not v.isdigit():
            raise ValueError("aweme_id must be a numeric string")

        return v.strip()


class PCVideoRequest(BaseModel):
    """PC端视频页面请求"""

    aweme_id: str
    use_proxy: bool = True
    custom_headers: Optional[Dict[str, str]] = None
    timeout: Optional[int] = None

    @field_validator("aweme_id")
    @classmethod
    def validate_aweme_id(cls, v):
        """验证aweme_id格式"""
        if not v or not v.strip():
            raise ValueError("aweme_id cannot be empty")

        # 检查是否为数字字符串
        if not v.isdigit():
            raise ValueError("aweme_id must be a numeric string")

        return v.strip()


class UserProfileRequest(BaseModel):
    """用户主页请求"""

    sec_uid: str
    use_proxy: bool = True
    custom_headers: Optional[Dict[str, str]] = None
    timeout: Optional[int] = None

    @field_validator("sec_uid")
    @classmethod
    def validate_sec_uid(cls, v):
        """验证sec_uid格式"""
        if not v or not v.strip():
            raise ValueError("sec_uid cannot be empty")

        return v.strip()


class BatchHTMLRequest(BaseModel):
    """批量HTML请求"""

    requests: list[HTMLRequest]
    max_concurrent: int = 5
    delay_between_requests: float = 1.0
    stop_on_first_error: bool = False

    @field_validator("requests")
    @classmethod
    def validate_requests(cls, v):
        """验证请求列表"""
        if not v:
            raise ValueError("requests cannot be empty")

        if len(v) > 100:  # 限制批量请求数量
            raise ValueError("too many requests, maximum 100 allowed")

        return v

    @field_validator("max_concurrent")
    @classmethod
    def validate_max_concurrent(cls, v):
        """验证并发数"""
        if v < 1 or v > 20:
            raise ValueError("max_concurrent must be between 1 and 20")
        return v


class BatchHTMLResponse(BaseModel):
    """批量HTML响应"""

    responses: list[HTMLResponse]
    total_requests: int
    successful_requests: int
    failed_requests: int
    total_time: float
    average_response_time: float

    def get_success_rate(self) -> float:
        """获取成功率"""
        if self.total_requests == 0:
            return 0.0
        return self.successful_requests / self.total_requests

    def get_summary(self) -> str:
        """获取批量请求摘要"""
        success_rate = self.get_success_rate() * 100
        return (
            f"批量请求完成: {self.total_requests}个请求, "
            f"成功{self.successful_requests}个, 失败{self.failed_requests}个, "
            f"成功率{success_rate:.1f}%, 总耗时{self.total_time:.2f}s"
        )
