"""
抖音HTML请求增强器

为抖音HTML页面请求添加特定的参数和请求头
"""

from typing import Optional

import httpx
from loguru import logger

from rpc.common.providers import RequestEnhancer

from .config import DouyinHTMLConfig


class DouyinHTMLRequestEnhancer(RequestEnhancer):
    """
    抖音HTML请求增强器

    专门为抖音HTML页面请求添加特定的参数和请求头，
    与API请求的增强器不同，这个增强器专注于HTML页面访问
    """

    def __init__(self, config: Optional[DouyinHTMLConfig] = None):
        """
        初始化HTML请求增强器

        Args:
            config: HTML配置对象
        """
        self.config = config or DouyinHTMLConfig()
        self.logger = logger.bind(component="DouyinHTMLRequestEnhancer")

        self.logger.debug("DouyinHTMLRequestEnhancer 初始化完成")

    def enhance_request(self, request: httpx.Request) -> None:
        """
        增强HTML请求，添加抖音HTML页面访问特定的参数

        Args:
            request: httpx.Request 对象，会被直接修改
        """
        try:
            self.logger.debug(f"开始增强HTML请求: {request.method} {request.url}")

            # 1. 确保请求头符合HTML页面访问的要求
            self._enhance_headers(request)

            # 2. 根据URL类型添加特定参数
            self._enhance_url_params(request)

            # 3. 添加防检测参数
            self._add_anti_detection_params(request)

            self.logger.debug(f"HTML请求增强完成: {request.url}")

        except Exception as e:
            self.logger.error(f"HTML请求增强失败: {e}")
            # 不抛出异常，避免影响请求流程

    def _enhance_headers(self, request: httpx.Request) -> None:
        """
        增强请求头，确保符合浏览器访问HTML页面的特征

        Args:
            request: httpx.Request 对象
        """
        # 确保Accept头适合HTML页面
        if "Accept" not in request.headers:
            request.headers["Accept"] = (
                "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7"
            )

        # 确保有合适的User-Agent
        if "User-Agent" not in request.headers:
            request.headers["User-Agent"] = self.config.default_headers.get(
                "User-Agent",
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            )

        # 添加浏览器特征头
        browser_headers = {
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
            "Accept-Encoding": "gzip, deflate, br",
            "Connection": "keep-alive",
            "Upgrade-Insecure-Requests": "1",
            "Sec-Fetch-Dest": "document",
            "Sec-Fetch-Mode": "navigate",
            "Sec-Fetch-Site": "none",
            "Sec-Fetch-User": "?1",
            "Cache-Control": "max-age=0",
        }

        for key, value in browser_headers.items():
            if key not in request.headers:
                request.headers[key] = value

        # 根据URL设置Referer
        self._set_referer(request)

    def _set_referer(self, request: httpx.Request) -> None:
        """
        根据请求URL设置合适的Referer

        Args:
            request: httpx.Request 对象
        """
        url_str = str(request.url)

        # 如果已经有Referer，不覆盖
        if "Referer" in request.headers:
            return

        # 根据不同的页面类型设置Referer
        if "jingxuan" in url_str:
            request.headers["Referer"] = "https://www.douyin.com/"
        elif "m.douyin.com" in url_str:
            request.headers["Referer"] = "https://m.douyin.com/"
        elif "www.douyin.com" in url_str:
            request.headers["Referer"] = "https://www.douyin.com/"

    def _enhance_url_params(self, request: httpx.Request) -> None:
        """
        根据URL类型添加特定参数

        Args:
            request: httpx.Request 对象
        """
        url_str = str(request.url)
        params = dict(request.url.params)

        # 为精选页面添加特定参数
        if "jingxuan" in url_str:
            # 精选页面可能需要的参数
            if "enter_from" not in params:
                params["enter_from"] = "web_search_result"
            if "enter_method" not in params:
                params["enter_method"] = "click"

        # 为移动端页面添加特定参数
        elif "m.douyin.com" in url_str:
            # 移动端页面可能需要的参数
            if "enter_from" not in params:
                params["enter_from"] = "web_share"

        # 更新URL参数
        if params != dict(request.url.params):
            request.url = request.url.copy_with(params=params)

    def _add_anti_detection_params(self, request: httpx.Request) -> None:
        """
        添加防检测参数

        Args:
            request: httpx.Request 对象
        """
        # 添加一些常见的浏览器参数
        params = dict(request.url.params)

        # 添加时间戳相关参数（如果URL中没有的话）
        import time

        current_time = int(time.time())

        # 某些页面可能需要时间戳参数
        if "jingxuan" in str(request.url) or "video" in str(request.url):
            if "_t" not in params and "timestamp" not in params:
                # 只在特定情况下添加时间戳，避免过度修改URL
                pass

        # 更新URL参数（如果有变化）
        if params != dict(request.url.params):
            request.url = request.url.copy_with(params=params)

    async def enhance_request_async(self, request: httpx.Request) -> None:
        """
        异步增强请求

        Args:
            request: httpx.Request 对象
        """
        # HTML请求增强通常不需要异步操作，直接调用同步方法
        self.enhance_request(request)


# 创建默认的HTML请求增强器实例
default_html_enhancer = DouyinHTMLRequestEnhancer()
