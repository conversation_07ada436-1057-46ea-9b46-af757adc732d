"""
fetch_jingxuan_page 快速测试示例

这是一个简化版本的示例，用于快速测试 fetch_jingxuan_page 方法。
适合开发调试时使用。
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parents[4]
sys.path.insert(0, str(project_root))

from log import logger

from rpc.douyin.html_handler.client import DouyinHTMLClient
from rpc.douyin.html_handler.schemas import JingxuanRequest


async def quick_test():
    """快速测试函数"""
    # 请修改这个aweme_id为实际的抖音视频ID
    TEST_AWEME_ID = "7123456789012345678"

    logger.info("🧪 快速测试 fetch_jingxuan_page")
    logger.info(f"测试 aweme_id: {TEST_AWEME_ID}")
    logger.info("-" * 40)

    try:
        # 创建客户端和请求
        async with DouyinHTMLClient() as client:
            request = JingxuanRequest(aweme_id=TEST_AWEME_ID)

            logger.info("📡 发送请求...")
            response = await client.fetch_jingxuan_page(request)

            # 输出结果
            if response.success:
                logger.info("✅ 请求成功!")
                logger.info(f"   状态码: {response.status_code}")
                logger.info(f"   响应时间: {response.response_time:.2f}秒")
                logger.info(f"   HTML长度: {len(response.html_content or '') :,}字符")
                logger.info(f"   重试次数: {response.retry_count}")

                # 检查是否包含关键数据
                if response.html_content:
                    if "window._SSR_HYDRATED_DATA" in response.html_content:
                        logger.info("   ✅ 包含SSR数据")
                    else:
                        logger.warning("   ⚠️  未检测到SSR数据")

                    # 输出HTML前500字符用于检查
                    logger.info(f"   HTML预览: {response.html_content[:500]}...")

            else:
                logger.error("❌ 请求失败")
                logger.error(f"   错误: {response.error_message}")
                logger.error(f"   状态码: {response.status_code}")

    except ValueError as e:
        logger.error(f"❌ 参数错误: {e}")
    except Exception as e:
        logger.error(f"❌ 异常: {e}")
        import traceback

        logger.error(traceback.format_exc())


if __name__ == "__main__":
    logger.info("=" * 50)
    logger.info("DouyinHTMLClient 快速测试")
    logger.info("=" * 50)

    # 提示用户修改测试ID
    logger.warning("⚠️  请在代码中修改 TEST_AWEME_ID 为实际的抖音视频ID")
    logger.info("   可以从抖音分享链接中获取，格式类似: 7123456789012345678")
    logger.info("")

    asyncio.run(quick_test())