# DouyinHTMLClient 示例代码

这个目录包含了 `DouyinHTMLClient` 的示例代码，特别是针对 `fetch_jingxuan_page` 方法的使用示例。

## 文件说明

### 📄 `fetch_jingxuan_example.py`
完整的示例代码，包含多个使用场景：

- **基本使用示例**: 最简单的调用方式
- **自定义配置示例**: 如何配置客户端参数
- **错误处理示例**: 各种异常情况的处理
- **批量请求示例**: 并发请求多个精选页面
- **响应分析示例**: 深入分析响应内容

### 📄 `quick_test.py`
快速测试脚本，适合开发调试时使用：

- 简化的测试流程
- 快速验证功能是否正常
- 输出关键信息用于调试

## 使用方法

### 1. 运行完整示例

```bash
cd /path/to/project
python rpc/douyin/html_handler/examples/fetch_jingxuan_example.py
```

### 2. 运行快速测试

```bash
cd /path/to/project
python rpc/douyin/html_handler/examples/quick_test.py
```

⚠️ **注意**: 在运行前，请修改代码中的 `TEST_AWEME_ID` 为实际的抖音视频ID。

## 获取真实的 aweme_id

可以通过以下方式获取真实的抖音视频ID：

1. **从分享链接获取**:
   - 抖音分享链接格式: `https://v.douyin.com/xxxxxxx/`
   - 打开后重定向到: `https://www.douyin.com/video/7123456789012345678`
   - 其中 `7123456789012345678` 就是 aweme_id

2. **从精选页面URL获取**:
   - 精选页面格式: `https://www.douyin.com/jingxuan?modal_id=7123456789012345678`
   - 其中 `modal_id` 参数值就是 aweme_id

## 示例输出

成功请求的输出示例：
```
✅ 请求成功!
状态码: 200
响应时间: 1.23秒
HTML长度: 45,678字符
最终URL: https://www.douyin.com/jingxuan?modal_id=7123456789012345678
重试次数: 0
✅ 包含SSR数据
```

失败请求的输出示例：
```
❌ 请求失败: 检测到反爬虫保护
状态码: 403
```

## 配置选项

可以通过 `DouyinHTMLConfig` 自定义客户端行为：

```python
from rpc.douyin.html_handler.config import DouyinHTMLConfig

config = DouyinHTMLConfig(
    request_timeout=30,              # 请求超时时间（秒）
    max_retries=5,                   # 最大重试次数
    enable_proxy=True,               # 是否启用代理
    enable_cookie_rotation=True,     # 是否启用Cookie轮换
    enable_anti_crawler_detection=True,  # 是否启用反爬虫检测
)
```

## 请求参数

`JingxuanRequest` 支持的参数：

```python
from rpc.douyin.html_handler.schemas import JingxuanRequest

request = JingxuanRequest(
    aweme_id="7123456789012345678",  # 必需：抖音视频ID
    use_proxy=True,                  # 可选：是否使用代理
    custom_headers={                 # 可选：自定义请求头
        "User-Agent": "...",
        "Accept-Language": "zh-CN,zh;q=0.9",
    },
    timeout=20                       # 可选：请求超时时间
)
```

## 常见问题

### Q: 为什么请求失败？
A: 可能的原因：
- aweme_id 格式不正确（必须是数字字符串）
- 视频不存在或已删除
- 触发了反爬虫保护
- 网络连接问题

### Q: 如何处理反爬虫？
A: 可以尝试：
- 启用代理 (`use_proxy=True`)
- 启用Cookie轮换 (`enable_cookie_rotation=True`)
- 自定义User-Agent
- 增加请求间隔

### Q: 如何提高成功率？
A: 建议：
- 使用真实的User-Agent
- 启用代理和Cookie轮换
- 适当设置重试次数
- 控制请求频率

## 依赖说明

这些示例依赖于以下组件：
- `DouyinHTMLClient`: 主要的HTML客户端
- `DouyinHTMLConfig`: 配置管理
- `JingxuanRequest`: 请求模型
- `HTMLResponse`: 响应模型
- 各种异常类: 错误处理

确保这些组件已正确实现和配置。