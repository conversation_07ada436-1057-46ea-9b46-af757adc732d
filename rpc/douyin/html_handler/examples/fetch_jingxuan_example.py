"""
fetch_jingxuan_page 方法示例代码

演示如何使用 DouyinHTMLClient 获取精选页面的HTML内容。
此示例包含基本用法、错误处理和配置自定义选项。
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parents[4]
sys.path.insert(0, str(project_root))

from log import logger

from rpc.douyin.html_handler.client import DouyinHTMLClient
from rpc.douyin.html_handler.config import DouyinHTMLConfig
from rpc.douyin.html_handler.exceptions import (
    AntiCrawlerError,
    NetworkError,
    RateLimitError,
    RequestTimeoutError,
)
from rpc.douyin.html_handler.schemas import JingxuanRequest


async def basic_example():
    """基本使用示例"""
    logger.info("=== 基本使用示例 ===")

    # 创建客户端（使用默认配置）
    async with DouyinHTMLClient() as client:
        # 创建精选页面请求
        request = JingxuanRequest(aweme_id="7123743056825716009")

        try:
            # 获取HTML响应
            response = await client.fetch_jingxuan_page(request)

            if response.success:
                logger.info("✅ 请求成功!")
                logger.info(f"状态码: {response.status_code}")
                logger.info(f"响应时间: {response.response_time:.2f}秒")
                logger.info(f"HTML长度: {len(response.html_content) if response.html_content else 0}字符")
                logger.info(f"最终URL: {response.final_url}")
                logger.info(f"重试次数: {response.retry_count}")

                # 打印HTML片段（前200字符）
                if response.html_content:
                    html_preview = response.html_content[:200]
                    logger.info(f"HTML预览: {html_preview}...")
            else:
                logger.error(f"❌ 请求失败: {response.error_message}")

        except Exception as e:
            logger.error(f"❌ 异常: {e}")


async def save_html_example():
    """保存HTML示例 - 将返回的HTML内容保存到.temp文件夹"""
    logger.info("=== 保存HTML示例 ===")

    # 创建客户端（使用默认配置）
    async with DouyinHTMLClient() as client:
        # 创建精选页面请求
        request = JingxuanRequest(aweme_id="7123743056825716009")

        try:
            # 获取HTML响应
            response = await client.fetch_jingxuan_page(request)

            if response.success:
                logger.info("✅ 请求成功!")
                logger.info(f"状态码: {response.status_code}")
                logger.info(f"响应时间: {response.response_time:.2f}秒")
                logger.info(f"HTML长度: {len(response.html_content) if response.html_content else 0}字符")
                logger.info(f"最终URL: {response.final_url}")
                logger.info(f"重试次数: {response.retry_count}")

                # 保存HTML内容到.temp文件夹
                if response.html_content:
                    # 创建.temp文件夹（如果不存在）
                    temp_dir = Path(project_root) / ".temp"
                    temp_dir.mkdir(exist_ok=True)

                    # 生成文件名（包含aweme_id和时间戳）
                    from datetime import datetime

                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    filename = f"jingxuan_{request.aweme_id}_{timestamp}.html"
                    filepath = temp_dir / filename

                    # 写入HTML内容
                    with open(filepath, "w", encoding="utf-8") as f:
                        f.write(response.html_content)

                    logger.info(f"📁 HTML已保存到: {filepath}")
                    logger.info(f"📊 文件大小: {filepath.stat().st_size:,} 字节")

                    # 打印HTML片段（前200字符）
                    html_preview = response.html_content[:200]
                    logger.info(f"HTML预览: {html_preview}...")
                else:
                    logger.warning("⚠️ 没有HTML内容可保存")
            else:
                logger.error(f"❌ 请求失败: {response.error_message}")

        except Exception as e:
            logger.error(f"❌ 异常: {e}")


async def custom_config_example():
    """自定义配置示例"""
    logger.info("\n=== 自定义配置示例 ===")

    # 创建自定义配置
    config = DouyinHTMLConfig(
        request_timeout=30,  # 增加超时时间
        max_retries=5,  # 增加重试次数
        enable_proxy=True,  # 启用代理
        enable_cookie_rotation=True,  # 启用Cookie轮换
        enable_anti_crawler_detection=True,  # 启用反爬虫检测
    )

    # 创建带自定义配置的客户端
    async with DouyinHTMLClient(config=config) as client:
        # 创建请求，禁用代理和自定义请求头
        request = JingxuanRequest(
            aweme_id="7123456789012345678",
            use_proxy=False,  # 这个请求不使用代理
            custom_headers={
                "User-Agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15",
                "Accept-Language": "zh-CN,zh;q=0.9",
            },
            timeout=20,  # 自定义超时时间
        )

        try:
            response = await client.fetch_jingxuan_page(request)

            if response.success:
                logger.info("✅ 自定义配置请求成功!")
                logger.info(f"请求ID: {response.request_id}")
                logger.info(f"响应摘要: {response.get_summary()}")
            else:
                logger.error(f"❌ 自定义配置请求失败: {response.error_message}")

        except Exception as e:
            logger.error(f"❌ 异常: {e}")


async def error_handling_example():
    """错误处理示例"""
    logger.info("\n=== 错误处理示例 ===")

    async with DouyinHTMLClient() as client:
        # 使用可能触发错误的aweme_id
        test_cases = [
            ("", "空aweme_id"),
            ("invalid", "无效aweme_id格式"),
            ("7000000000000000000", "可能不存在的aweme_id"),
        ]

        for aweme_id, description in test_cases:
            logger.info(f"\n测试 {description}: {aweme_id}")

            try:
                request = JingxuanRequest(aweme_id=aweme_id)
                response = await client.fetch_jingxuan_page(request)

                if response.success:
                    logger.info(f"✅ {description} 请求成功")
                else:
                    logger.error(f"❌ {description} 请求失败: {response.error_message}")

            except ValueError as e:
                logger.error(f"❌ 验证错误: {e}")
            except AntiCrawlerError as e:
                logger.error(f"❌ 反爬虫检测: {e}")
            except RateLimitError as e:
                logger.error(f"❌ 限流错误: {e}, 等待时间: {e.retry_after}秒")
            except NetworkError as e:
                logger.error(f"❌ 网络错误: {e}")
            except RequestTimeoutError as e:
                logger.error(f"❌ 超时错误: {e}")
            except Exception as e:
                logger.error(f"❌ 未知错误: {e}")


async def batch_request_example():
    """批量请求示例"""
    logger.info("\n=== 批量请求示例 ===")

    # 示例aweme_id列表
    aweme_ids = [
        "7123456789012345678",
        "7123456789012345679",
        "7123456789012345680",
    ]

    async with DouyinHTMLClient() as client:
        tasks = []

        # 创建批量任务
        for aweme_id in aweme_ids:
            request = JingxuanRequest(aweme_id=aweme_id)
            task = client.fetch_jingxuan_page(request)
            tasks.append(task)

        # 并发执行
        logger.info(f"开始批量请求 {len(tasks)} 个精选页面...")
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # 处理结果
        success_count = 0
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"❌ aweme_id {aweme_ids[i]} 异常: {result}")
            elif result.success:
                logger.info(f"✅ aweme_id {aweme_ids[i]} 成功: {result.get_summary()}")
                success_count += 1
            else:
                logger.error(f"❌ aweme_id {aweme_ids[i]} 失败: {result.error_message}")

        logger.info(f"\n批量请求完成: {success_count}/{len(aweme_ids)} 成功")


async def response_analysis_example():
    """响应分析示例"""
    logger.info("\n=== 响应分析示例 ===")

    async with DouyinHTMLClient() as client:
        request = JingxuanRequest(aweme_id="7123456789012345678")

        try:
            response = await client.fetch_jingxuan_page(request)

            if response.success:
                logger.info("✅ 响应分析:")
                logger.info(f"  - 请求ID: {response.request_id}")
                logger.info(f"  - 时间戳: {response.timestamp}")
                logger.info(f"  - 状态码: {response.status_code}")
                logger.info(f"  - 原始URL: {response.url}")
                logger.info(f"  - 最终URL: {response.final_url}")
                logger.info(f"  - 响应时间: {response.response_time:.3f}秒")
                logger.info(f"  - 重试次数: {response.retry_count}")

                # 分析响应头
                if response.headers:
                    logger.info("  - 重要响应头:")
                    important_headers = ["content-type", "content-length", "server", "set-cookie"]
                    for header in important_headers:
                        value = response.headers.get(header.lower())
                        if value:
                            logger.info(f"    {header}: {value[:100]}{'...' if len(str(value)) > 100 else ''}")

                # 分析HTML内容
                if response.html_content:
                    html_length = len(response.html_content)
                    logger.info(f"  - HTML内容长度: {html_length:,} 字符")

                    # 检查关键内容
                    keywords = ["window._SSR_HYDRATED_DATA", "videoObject", "authorObject"]
                    found_keywords = []
                    for keyword in keywords:
                        if keyword in response.html_content:
                            found_keywords.append(keyword)

                    if found_keywords:
                        logger.info(f"  - 找到关键数据: {', '.join(found_keywords)}")
                    else:
                        logger.info("  - 未找到关键数据标识")

                logger.info(f"  - 响应摘要: {response.get_summary()}")
            else:
                logger.error(f"❌ 响应分析失败: {response.error_message}")

        except Exception as e:
            logger.error(f"❌ 响应分析异常: {e}")


async def main():
    """主函数 - 运行所有示例"""
    logger.info("🚀 DouyinHTMLClient fetch_jingxuan_page 示例")
    logger.info("=" * 50)

    try:
        # 运行所有示例
        # await basic_example()
        await save_html_example()
        # await custom_config_example()
        # await error_handling_example()
        # await batch_request_example()
        # await response_analysis_example()

        logger.info("\n" + "=" * 50)
        logger.info("✅ 所有示例运行完成")

    except KeyboardInterrupt:
        logger.error("\n❌ 用户中断")
    except Exception as e:
        logger.error(f"\n❌ 运行示例时发生错误: {e}")
        logger.exception("示例运行异常")


if __name__ == "__main__":
    # 配置日志
    logger.remove()
    logger.add(
        sys.stderr,
        level="INFO",
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
    )

    # 运行示例
    asyncio.run(main())
