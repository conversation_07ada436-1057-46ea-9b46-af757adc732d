"""
抖音URL常量定义

统一管理所有抖音相关的URL模板、域名和路径常量
确保URL字符串的单一数据源，便于维护和更新
"""

from typing import Final


class DouyinURLConstants:
    """抖音URL相关常量集合"""

    # 域名常量
    DOMAIN_WWW: Final[str] = "www.douyin.com"
    DOMAIN_MOBILE: Final[str] = "m.douyin.com"
    DOMAIN_SHORT: Final[str] = "v.douyin.com"

    # 协议
    PROTOCOL: Final[str] = "https"

    # URL模板
    JINGXUAN_TEMPLATE: Final[str] = "https://www.douyin.com/jingxuan?modal_id={aweme_id}"
    MOBILE_SHARE_TEMPLATE: Final[str] = "https://m.douyin.com/share/video/{aweme_id}"
    PC_VIDEO_TEMPLATE: Final[str] = "https://www.douyin.com/video/{aweme_id}"
    USER_PROFILE_TEMPLATE: Final[str] = "https://www.douyin.com/user/{sec_uid}"

    # 路径常量
    JINGXUAN_PATH: Final[str] = "/jingxuan"
    MOBILE_SHARE_PATH: Final[str] = "/share/video/"
    PC_VIDEO_PATH: Final[str] = "/video/"
    USER_PROFILE_PATH: Final[str] = "/user/"

    # 查询参数
    JINGXUAN_MODAL_ID_PARAM: Final[str] = "modal_id"

    # 支持的域名列表
    SUPPORTED_DOMAINS: Final[tuple] = (DOMAIN_WWW, DOMAIN_MOBILE, DOMAIN_SHORT)

    # 完整URL前缀
    WWW_BASE_URL: Final[str] = f"{PROTOCOL}://{DOMAIN_WWW}"
    MOBILE_BASE_URL: Final[str] = f"{PROTOCOL}://{DOMAIN_MOBILE}"
    SHORT_BASE_URL: Final[str] = f"{PROTOCOL}://{DOMAIN_SHORT}"


class DouyinURLPatterns:
    """抖音URL正则表达式模式"""

    # aweme_id 模式 (通常是10-25位数字)
    AWEME_ID_PATTERN: Final[str] = r"\d{10,25}"

    # sec_uid 模式 (通常以MS4wLjABAAAA开头)
    SEC_UID_PATTERN: Final[str] = r"[A-Za-z0-9_-]{10,}"

    # 完整URL匹配模式
    JINGXUAN_URL_PATTERN: Final[str] = rf"https://www\.douyin\.com/jingxuan\?.*modal_id=({AWEME_ID_PATTERN})"
    MOBILE_SHARE_URL_PATTERN: Final[str] = rf"https://m\.douyin\.com/share/video/({AWEME_ID_PATTERN})"
    PC_VIDEO_URL_PATTERN: Final[str] = rf"https://www\.douyin\.com/video/({AWEME_ID_PATTERN})"
    USER_PROFILE_URL_PATTERN: Final[str] = rf"https://www\.douyin\.com/user/({SEC_UID_PATTERN})"


class DouyinURLValidation:
    """抖音URL验证相关常量"""

    # aweme_id 有效性验证
    MIN_AWEME_ID_LENGTH: Final[int] = 10
    MAX_AWEME_ID_LENGTH: Final[int] = 25

    # sec_uid 有效性验证
    MIN_SEC_UID_LENGTH: Final[int] = 10
    SEC_UID_PREFIX_COMMON: Final[str] = "MS4wLjABAAAA"

    # URL 最大长度限制
    MAX_URL_LENGTH: Final[int] = 2048


# 便捷访问的模块级常量
JINGXUAN_URL_TEMPLATE = DouyinURLConstants.JINGXUAN_TEMPLATE
MOBILE_SHARE_URL_TEMPLATE = DouyinURLConstants.MOBILE_SHARE_TEMPLATE
PC_VIDEO_URL_TEMPLATE = DouyinURLConstants.PC_VIDEO_TEMPLATE
USER_PROFILE_URL_TEMPLATE = DouyinURLConstants.USER_PROFILE_TEMPLATE

SUPPORTED_DOMAINS = DouyinURLConstants.SUPPORTED_DOMAINS
