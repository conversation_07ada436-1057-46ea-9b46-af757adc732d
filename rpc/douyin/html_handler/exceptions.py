"""
抖音HTML请求处理异常定义

定义HTML请求处理过程中可能出现的各种异常
"""

from typing import Any, Dict, Optional

from ..exceptions import DouyinClientError


class DouyinHTMLError(DouyinClientError):
    """抖音HTML请求基础异常"""

    def __init__(
        self,
        message: str,
        status_code: Optional[int] = None,
        response_data: Optional[Dict[str, Any]] = None,
        url: Optional[str] = None,
        aweme_id: Optional[str] = None,
        **kwargs,
    ):
        super().__init__(message, status_code, response_data)
        self.url = url
        self.aweme_id = aweme_id
        self.extra_data = kwargs

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "error_type": self.__class__.__name__,
            "message": self.message,
            "status_code": self.status_code,
            "url": self.url,
            "aweme_id": self.aweme_id,
            "response_data": self.response_data,
            "extra_data": self.extra_data,
        }


class InvalidURLError(DouyinHTMLError):
    """无效URL异常"""

    def __init__(self, message: str, url: Optional[str] = None, **kwargs):
        super().__init__(message, url=url, **kwargs)


class ProxyError(DouyinHTMLError):
    """代理异常"""

    def __init__(self, message: str, proxy_url: Optional[str] = None, **kwargs):
        super().__init__(message, **kwargs)
        self.proxy_url = proxy_url

    def to_dict(self) -> Dict[str, Any]:
        result = super().to_dict()
        result["proxy_url"] = self.proxy_url
        return result


class CookieError(DouyinHTMLError):
    """Cookie异常"""

    def __init__(self, message: str, cookie_info: Optional[str] = None, **kwargs):
        super().__init__(message, **kwargs)
        self.cookie_info = cookie_info

    def to_dict(self) -> Dict[str, Any]:
        result = super().to_dict()
        result["cookie_info"] = self.cookie_info
        return result


class RequestTimeoutError(DouyinHTMLError):
    """请求超时异常"""

    def __init__(self, message: str, timeout_duration: Optional[float] = None, **kwargs):
        super().__init__(message, **kwargs)
        self.timeout_duration = timeout_duration

    def to_dict(self) -> Dict[str, Any]:
        result = super().to_dict()
        result["timeout_duration"] = self.timeout_duration
        return result


class AntiCrawlerError(DouyinHTMLError):
    """反爬虫异常"""

    def __init__(
        self, message: str, detection_reason: Optional[str] = None, suggested_action: Optional[str] = None, **kwargs
    ):
        super().__init__(message, **kwargs)
        self.detection_reason = detection_reason
        self.suggested_action = suggested_action

    def to_dict(self) -> Dict[str, Any]:
        result = super().to_dict()
        result.update(
            {
                "detection_reason": self.detection_reason,
                "suggested_action": self.suggested_action,
            }
        )
        return result


class RateLimitError(DouyinHTMLError):
    """限流异常"""

    def __init__(self, message: str, retry_after: Optional[int] = None, limit_type: Optional[str] = None, **kwargs):
        super().__init__(message, **kwargs)
        self.retry_after = retry_after
        self.limit_type = limit_type

    def to_dict(self) -> Dict[str, Any]:
        result = super().to_dict()
        result.update(
            {
                "retry_after": self.retry_after,
                "limit_type": self.limit_type,
            }
        )
        return result


class HTMLParsingError(DouyinHTMLError):
    """HTML解析异常"""

    def __init__(self, message: str, html_snippet: Optional[str] = None, parsing_stage: Optional[str] = None, **kwargs):
        super().__init__(message, **kwargs)
        self.html_snippet = html_snippet[:500] if html_snippet else None  # 限制长度
        self.parsing_stage = parsing_stage

    def to_dict(self) -> Dict[str, Any]:
        result = super().to_dict()
        result.update(
            {
                "html_snippet": self.html_snippet,
                "parsing_stage": self.parsing_stage,
            }
        )
        return result


class NetworkError(DouyinHTMLError):
    """网络错误异常"""

    def __init__(
        self,
        message: str,
        network_error_type: Optional[str] = None,
        original_exception: Optional[Exception] = None,
        **kwargs,
    ):
        super().__init__(message, **kwargs)
        self.network_error_type = network_error_type
        self.original_exception = str(original_exception) if original_exception else None

    def to_dict(self) -> Dict[str, Any]:
        result = super().to_dict()
        result.update(
            {
                "network_error_type": self.network_error_type,
                "original_exception": self.original_exception,
            }
        )
        return result


# 错误恢复建议映射
RECOVERY_SUGGESTIONS = {
    InvalidURLError: "请检查URL格式是否正确，确保是支持的抖音URL类型",
    ProxyError: "请检查代理配置，或尝试使用其他代理服务器",
    CookieError: "请检查Cookie是否有效，或尝试获取新的Cookie",
    RequestTimeoutError: "请检查网络连接，或增加超时时间",
    AntiCrawlerError: "建议更换User-Agent、使用代理或降低请求频率",
    RateLimitError: "请降低请求频率，或等待一段时间后重试",
    HTMLParsingError: "页面结构可能已变更，需要更新解析逻辑",
    NetworkError: "请检查网络连接状态",
}


def get_recovery_suggestion(error: DouyinHTMLError) -> str:
    """获取错误恢复建议"""
    return RECOVERY_SUGGESTIONS.get(type(error), "请联系技术支持")


def format_error_message(error: DouyinHTMLError) -> str:
    """格式化错误消息"""
    parts = [f"[{error.__class__.__name__}] {error.message}"]

    if error.url:
        parts.append(f"URL: {error.url}")

    if error.aweme_id:
        parts.append(f"视频ID: {error.aweme_id}")

    if error.status_code:
        parts.append(f"状态码: {error.status_code}")

    return " | ".join(parts)
