"""
抖音HTML请求处理模块

专门处理抖音HTML页面的请求，包括精选页面和视频分享页面。
该模块集成账号Cookie管理和IP代理功能，返回HTML内容供后续解析使用。
"""

# 导入客户端
from .client import DouyinHTMLClient, html_client

# 导入配置
from .config import DouyinHTMLConfig, default_config

# 导入增强器
from .enhancer import DouyinHTMLRequestEnhancer, default_html_enhancer

# 导入异常
from .exceptions import (
    AntiCrawlerError,
    CookieError,
    DouyinHTMLError,
    HTMLParsingError,
    InvalidURLError,
    NetworkError,
    ProxyError,
    RateLimitError,
    RequestTimeoutError,
    format_error_message,
    get_recovery_suggestion,
)

# 导入模型
from .schemas import (
    BatchHTMLRequest,
    BatchHTMLResponse,
    HTMLRequest,
    HTMLResponse,
    JingxuanRequest,
    MobileShareRequest,
    PCVideoRequest,
    URLType,
    UserProfileRequest,
)

# 导入工具函数
from .utils import (
    build_jingxuan_url,
    build_mobile_share_url,
    build_pc_video_url,
    build_user_profile_url,
    detect_anti_crawler_patterns,
    extract_aweme_id_from_url,
    extract_sec_uid_from_url,
    format_cookie_dict,
    format_response_summary,
    generate_request_id,
    is_douyin_url,
    is_valid_aweme_id,
    is_valid_sec_uid,
    mask_cookie_for_logging,
    parse_cookie_string,
    parse_url_info,
    sanitize_html_content,
)

__all__ = [
    # 配置
    "DouyinHTMLConfig",
    "default_config",
    # 异常
    "DouyinHTMLError",
    "InvalidURLError",
    "ProxyError",
    "CookieError",
    "RequestTimeoutError",
    "AntiCrawlerError",
    "RateLimitError",
    "HTMLParsingError",
    "NetworkError",
    "get_recovery_suggestion",
    "format_error_message",
    # 模型
    "URLType",
    "HTMLRequest",
    "HTMLResponse",
    "JingxuanRequest",
    "MobileShareRequest",
    "PCVideoRequest",
    "UserProfileRequest",
    "BatchHTMLRequest",
    "BatchHTMLResponse",
    # 客户端
    "DouyinHTMLClient",
    "html_client",
    # 增强器
    "DouyinHTMLRequestEnhancer",
    "default_html_enhancer",
    # 工具函数
    "extract_aweme_id_from_url",
    "extract_sec_uid_from_url",
    "parse_url_info",
    "build_jingxuan_url",
    "build_mobile_share_url",
    "build_pc_video_url",
    "build_user_profile_url",
    "parse_cookie_string",
    "format_cookie_dict",
    "mask_cookie_for_logging",
    "format_response_summary",
    "is_valid_aweme_id",
    "is_valid_sec_uid",
    "generate_request_id",
    "is_douyin_url",
    "sanitize_html_content",
    "detect_anti_crawler_patterns",
]

# 版本信息
__version__ = "1.0.0"
__author__ = "MediaCrawlerPro Team"
__description__ = "抖音HTML页面请求处理模块"
