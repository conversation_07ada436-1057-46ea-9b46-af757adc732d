"""
抖音URL统一管理器

提供高级的URL构建、解析和验证功能
所有URL相关操作的统一入口，确保一致性和易维护性
"""

import re
from typing import Optional, Tuple
from urllib.parse import urlparse

from .constants import DouyinURLConstants, DouyinURLPatterns
from .schemas import URLType
from .utils import (
    build_jingxuan_url,
    build_mobile_share_url,
    build_pc_video_url,
    build_user_profile_url,
    extract_aweme_id_from_url,
    extract_sec_uid_from_url,
    is_valid_aweme_id,
    is_valid_sec_uid,
    parse_url_info,
)


class DouyinURLManager:
    """
    抖音URL统一管理器

    提供所有URL相关操作的统一接口，包括：
    - URL构建和生成
    - URL解析和验证
    - ID提取和格式检查
    """

    @staticmethod
    def build_jingxuan_url(aweme_id: str) -> str:
        """
        构建精选页面URL

        Args:
            aweme_id: 视频ID

        Returns:
            精选页面URL

        Raises:
            ValueError: aweme_id无效时
        """
        if not is_valid_aweme_id(aweme_id):
            raise ValueError(f"Invalid aweme_id: {aweme_id}")
        return build_jingxuan_url(aweme_id)

    @staticmethod
    def build_mobile_share_url(aweme_id: str) -> str:
        """
        构建移动端分享URL

        Args:
            aweme_id: 视频ID

        Returns:
            移动端分享URL

        Raises:
            ValueError: aweme_id无效时
        """
        if not is_valid_aweme_id(aweme_id):
            raise ValueError(f"Invalid aweme_id: {aweme_id}")
        return build_mobile_share_url(aweme_id)

    @staticmethod
    def build_pc_video_url(aweme_id: str) -> str:
        """
        构建PC端视频URL

        Args:
            aweme_id: 视频ID

        Returns:
            PC端视频URL

        Raises:
            ValueError: aweme_id无效时
        """
        if not is_valid_aweme_id(aweme_id):
            raise ValueError(f"Invalid aweme_id: {aweme_id}")
        return build_pc_video_url(aweme_id)

    @staticmethod
    def build_user_profile_url(sec_uid: str) -> str:
        """
        构建用户主页URL

        Args:
            sec_uid: 用户sec_uid

        Returns:
            用户主页URL

        Raises:
            ValueError: sec_uid无效时
        """
        if not is_valid_sec_uid(sec_uid):
            raise ValueError(f"Invalid sec_uid: {sec_uid}")
        return build_user_profile_url(sec_uid)

    @staticmethod
    def parse_aweme_id_from_url(url: str) -> Optional[str]:
        """
        从任意抖音URL中提取aweme_id

        Args:
            url: 抖音URL

        Returns:
            aweme_id或None
        """
        return extract_aweme_id_from_url(url)

    @staticmethod
    def parse_sec_uid_from_url(url: str) -> Optional[str]:
        """
        从抖音用户主页URL中提取sec_uid

        Args:
            url: 抖音用户主页URL

        Returns:
            sec_uid或None
        """
        return extract_sec_uid_from_url(url)

    @staticmethod
    def get_url_type(url: str) -> Optional[URLType]:
        """
        判断URL类型

        Args:
            url: URL字符串

        Returns:
            URLType枚举值或None
        """
        try:
            url_type, _, _ = parse_url_info(url)
            return url_type
        except ValueError:
            return None

    @staticmethod
    def parse_url(url: str) -> Tuple[Optional[URLType], Optional[str], Optional[str]]:
        """
        完整解析URL信息

        Args:
            url: URL字符串

        Returns:
            (url_type, aweme_id, sec_uid)
        """
        try:
            return parse_url_info(url)
        except ValueError:
            return None, None, None

    @staticmethod
    def validate_aweme_id(aweme_id: str) -> bool:
        """
        验证aweme_id格式

        Args:
            aweme_id: 视频ID

        Returns:
            是否有效
        """
        return is_valid_aweme_id(aweme_id)

    @staticmethod
    def validate_sec_uid(sec_uid: str) -> bool:
        """
        验证sec_uid格式

        Args:
            sec_uid: 用户sec_uid

        Returns:
            是否有效
        """
        return is_valid_sec_uid(sec_uid)

    @staticmethod
    def is_douyin_url(url: str) -> bool:
        """
        检查是否为抖音域名的URL

        Args:
            url: URL字符串

        Returns:
            是否为抖音URL
        """
        try:
            parsed = urlparse(url)
            return parsed.netloc in DouyinURLConstants.SUPPORTED_DOMAINS
        except Exception:
            return False

    @staticmethod
    def extract_all_aweme_ids(text: str) -> list[str]:
        """
        从文本中提取所有aweme_id

        Args:
            text: 包含URL的文本

        Returns:
            aweme_id列表
        """
        aweme_ids = []

        # 使用正则表达式匹配各种URL格式
        patterns = [
            DouyinURLPatterns.JINGXUAN_URL_PATTERN,
            DouyinURLPatterns.MOBILE_SHARE_URL_PATTERN,
            DouyinURLPatterns.PC_VIDEO_URL_PATTERN,
        ]

        for pattern in patterns:
            matches = re.findall(pattern, text)
            aweme_ids.extend(matches)

        # 去重并验证
        unique_ids = []
        for aweme_id in set(aweme_ids):
            if DouyinURLManager.validate_aweme_id(aweme_id):
                unique_ids.append(aweme_id)

        return unique_ids

    @classmethod
    def get_all_url_variants(cls, aweme_id: str) -> dict[str, str]:
        """
        获取指定aweme_id的所有URL变体

        Args:
            aweme_id: 视频ID

        Returns:
            包含各种URL格式的字典

        Raises:
            ValueError: aweme_id无效时
        """
        if not cls.validate_aweme_id(aweme_id):
            raise ValueError(f"Invalid aweme_id: {aweme_id}")

        return {
            "jingxuan": cls.build_jingxuan_url(aweme_id),
            "mobile_share": cls.build_mobile_share_url(aweme_id),
            "pc_video": cls.build_pc_video_url(aweme_id),
        }

    @staticmethod
    def normalize_url(url: str) -> Optional[str]:
        """
        标准化URL格式

        Args:
            url: 原始URL

        Returns:
            标准化后的URL或None
        """
        aweme_id = DouyinURLManager.parse_aweme_id_from_url(url)
        if aweme_id:
            # 默认标准化为精选页面URL
            return DouyinURLManager.build_jingxuan_url(aweme_id)

        sec_uid = DouyinURLManager.parse_sec_uid_from_url(url)
        if sec_uid:
            return DouyinURLManager.build_user_profile_url(sec_uid)

        return None


# 便捷访问的模块级实例
url_manager = DouyinURLManager()
