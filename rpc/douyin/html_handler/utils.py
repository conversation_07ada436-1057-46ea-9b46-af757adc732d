"""
抖音HTML请求处理工具函数

提供URL解析、<PERSON><PERSON>处理等实用工具函数
"""

import re
import time
from typing import Dict, Optional, Tuple
from urllib.parse import parse_qs, urlparse

from .constants import DouyinURLConstants, DouyinURLValidation
from .schemas import HTMLResponse, URLType


def extract_aweme_id_from_url(url: str) -> Optional[str]:
    """
    从URL中提取aweme_id

    Args:
        url: 抖音URL

    Returns:
        aweme_id或None
    """
    # 精选页面: https://www.douyin.com/jingxuan?modal_id=7123456789
    if "jingxuan" in url:
        parsed = urlparse(url)
        query = parse_qs(parsed.query)
        return query.get("modal_id", [None])[0]

    # 移动端分享和PC端视频:
    # https://m.douyin.com/share/video/7123456789
    # https://www.douyin.com/video/7123456789
    pattern = r"/(?:share/)?video/(\d+)"
    match = re.search(pattern, url)
    return match.group(1) if match else None


def extract_sec_uid_from_url(url: str) -> Optional[str]:
    """
    从URL中提取sec_uid

    Args:
        url: 抖音用户主页URL

    Returns:
        sec_uid或None
    """
    # 用户主页: https://www.douyin.com/user/MS4wLjABAAAA...
    pattern = r"/user/([^/?]+)"
    match = re.search(pattern, url)
    return match.group(1) if match else None


def parse_url_info(url: str) -> Tuple[URLType, Optional[str], Optional[str]]:
    """
    解析URL信息，返回URL类型和相关ID

    Args:
        url: 抖音URL

    Returns:
        (url_type, aweme_id, sec_uid)

    Raises:
        ValueError: 不支持的URL格式
    """
    parsed = urlparse(url)
    domain = parsed.netloc
    path = parsed.path
    query = parse_qs(parsed.query)

    # 精选页面
    if domain == DouyinURLConstants.DOMAIN_WWW and path == DouyinURLConstants.JINGXUAN_PATH:
        modal_id = query.get(DouyinURLConstants.JINGXUAN_MODAL_ID_PARAM, [None])[0]
        return URLType.JINGXUAN, modal_id, None

    # 移动端分享页面
    elif domain == DouyinURLConstants.DOMAIN_MOBILE and path.startswith(DouyinURLConstants.MOBILE_SHARE_PATH):
        aweme_id = path.split("/")[-1]
        return URLType.MOBILE_SHARE, aweme_id, None

    # PC端视频页面
    elif domain == DouyinURLConstants.DOMAIN_WWW and path.startswith(DouyinURLConstants.PC_VIDEO_PATH):
        aweme_id = path.split("/")[-1]
        return URLType.PC_VIDEO, aweme_id, None

    # 用户主页
    elif domain == DouyinURLConstants.DOMAIN_WWW and path.startswith(DouyinURLConstants.USER_PROFILE_PATH):
        sec_uid = path.split("/")[-1]
        return URLType.USER_PROFILE, None, sec_uid

    else:
        raise ValueError(f"Unsupported URL format: {url}")


def build_jingxuan_url(aweme_id: str) -> str:
    """
    构建精选页面URL

    Args:
        aweme_id: 视频ID

    Returns:
        精选页面URL
    """
    return DouyinURLConstants.JINGXUAN_TEMPLATE.format(aweme_id=aweme_id)


def build_mobile_share_url(aweme_id: str) -> str:
    """
    构建移动端分享URL

    Args:
        aweme_id: 视频ID

    Returns:
        移动端分享URL
    """
    return DouyinURLConstants.MOBILE_SHARE_TEMPLATE.format(aweme_id=aweme_id)


def build_pc_video_url(aweme_id: str) -> str:
    """
    构建PC端视频URL

    Args:
        aweme_id: 视频ID

    Returns:
        PC端视频URL
    """
    return DouyinURLConstants.PC_VIDEO_TEMPLATE.format(aweme_id=aweme_id)


def build_user_profile_url(sec_uid: str) -> str:
    """
    构建用户主页URL

    Args:
        sec_uid: 用户sec_uid

    Returns:
        用户主页URL
    """
    return DouyinURLConstants.USER_PROFILE_TEMPLATE.format(sec_uid=sec_uid)


def parse_cookie_string(cookie_str: str) -> Dict[str, str]:
    """
    解析Cookie字符串为字典

    Args:
        cookie_str: Cookie字符串

    Returns:
        Cookie字典
    """
    cookies = {}
    if not cookie_str:
        return cookies

    for item in cookie_str.split(";"):
        item = item.strip()
        if "=" in item:
            key, value = item.split("=", 1)
            cookies[key.strip()] = value.strip()

    return cookies


def format_cookie_dict(cookies: Dict[str, str]) -> str:
    """
    将Cookie字典格式化为字符串

    Args:
        cookies: Cookie字典

    Returns:
        Cookie字符串
    """
    if not cookies:
        return ""

    return "; ".join([f"{key}={value}" for key, value in cookies.items()])


def mask_cookie_for_logging(cookie_str: str, max_length: int = 50) -> str:
    """
    为日志记录遮蔽Cookie敏感信息

    Args:
        cookie_str: Cookie字符串
        max_length: 最大显示长度

    Returns:
        遮蔽后的Cookie字符串
    """
    if not cookie_str:
        return "None"

    if len(cookie_str) <= max_length:
        return cookie_str

    return cookie_str[:max_length] + "..."


def format_response_summary(response: HTMLResponse) -> str:
    """
    格式化响应摘要

    Args:
        response: HTML响应对象

    Returns:
        格式化的摘要字符串
    """
    return response.get_summary()


def is_valid_aweme_id(aweme_id: str) -> bool:
    """
    验证aweme_id是否有效

    Args:
        aweme_id: 视频ID

    Returns:
        是否有效
    """
    if not aweme_id:
        return False

    # 检查是否为数字字符串且长度合理
    return (
        aweme_id.isdigit()
        and DouyinURLValidation.MIN_AWEME_ID_LENGTH <= len(aweme_id) <= DouyinURLValidation.MAX_AWEME_ID_LENGTH
    )


def is_valid_sec_uid(sec_uid: str) -> bool:
    """
    验证sec_uid是否有效

    Args:
        sec_uid: 用户sec_uid

    Returns:
        是否有效
    """
    if not sec_uid:
        return False

    # sec_uid通常以MS4wLjABAAAA开头且长度合理
    return len(sec_uid) > DouyinURLValidation.MIN_SEC_UID_LENGTH and not sec_uid.isdigit()


def generate_request_id() -> str:
    """
    生成请求ID

    Returns:
        唯一的请求ID
    """
    return f"html_req_{int(time.time() * 1000)}"


def calculate_retry_delay(attempt: int, base_delay: float = 1.0, backoff_factor: float = 2.0) -> float:
    """
    计算重试延迟时间

    Args:
        attempt: 重试次数
        base_delay: 基础延迟时间
        backoff_factor: 退避因子

    Returns:
        延迟时间（秒）
    """
    return base_delay * (backoff_factor**attempt)


def extract_domain_from_url(url: str) -> Optional[str]:
    """
    从URL中提取域名

    Args:
        url: URL字符串

    Returns:
        域名或None
    """
    try:
        parsed = urlparse(url)
        return parsed.netloc
    except Exception:
        return None


def is_douyin_url(url: str) -> bool:
    """
    检查是否为抖音URL

    Args:
        url: URL字符串

    Returns:
        是否为抖音URL
    """
    domain = extract_domain_from_url(url)
    if not domain:
        return False

    return domain in DouyinURLConstants.SUPPORTED_DOMAINS


def sanitize_html_content(html_content: str, max_length: int = 1000000) -> str:
    """
    清理HTML内容

    Args:
        html_content: HTML内容
        max_length: 最大长度

    Returns:
        清理后的HTML内容
    """
    if not html_content:
        return ""

    # 限制长度
    if len(html_content) > max_length:
        html_content = html_content[:max_length]

    return html_content.strip()


def detect_anti_crawler_patterns(html_content: str, keywords: list[str]) -> Tuple[bool, Optional[str]]:
    """
    检测反爬虫模式

    Args:
        html_content: HTML内容
        keywords: 反爬虫关键词列表

    Returns:
        (是否检测到反爬虫, 匹配的关键词)
    """
    if not html_content:
        return False, None

    content_lower = html_content.lower()

    for keyword in keywords:
        if keyword.lower() in content_lower:
            return True, keyword

    return False, None
