"""
抖音 API 客户端模块

提供抖音相关 API 的异步客户端实现
支持新架构的三步增强模式和传统客户端模式
"""

# 导入配置
from .config import DouyinConfig

# 导入异常
from .exceptions import (
    DouyinAuthenticationError,
    DouyinClientError,
    DouyinRateLimitError,
    DouyinRequestError,
    DouyinResponseError,
    DouyinTimeoutError,
    DouyinValidationError,
)

# 导入新架构组件
from .client import DouyinClientManager, client_manager

# 导入增强器
from .enhancer import DouyinRequestEnhancer

# 导入 API 接口 (需要放在 client 之后以避免循环导入)
from .api import AsyncDouyinAPI, async_douyin_api

from .schemas import (  # 签名相关; 原有模型; 搜索相关; 评论相关; 用户相关; 收藏夹
    AwemeCommentsRequest,
    AwemeCommentsResponse,
    CollectVideoListRequest,
    CollectVideoListResponse,
    DouyinSignRequest,
    DouyinSignResponse,
    SearchInfoRequest,
    SearchInfoResponse,
    UserAwemePostsRequest,
    UserAwemePostsResponse,
    UserInfoRequest,
    UserInfoResponse,
    VideoAuthor,
    VideoDetailRequest,
    VideoDetailResponse,
    VideoInfo,
    VideoStatistics,
)
from .signer import (
    AbstractDouyinSign,
    DouyinJavascriptSign,
    DouyinSignFactory,
    DouyinSignLogic,
)
from .verify_params import (
    CommonVerifyParams,
    TokenManager,
    VerifyFpManager,
    VerifyParamsGenerator,
)

__all__ = [
    # 配置
    "DouyinConfig",
    # 异常类
    "DouyinClientError",
    "DouyinRequestError",
    "DouyinResponseError",
    "DouyinTimeoutError",
    "DouyinRateLimitError",
    "DouyinAuthenticationError",
    "DouyinValidationError",
    # 新架构客户端
    "client_manager",
    "DouyinClientManager",
    # 增强器
    "DouyinRequestEnhancer",
    # API 接口
    "AsyncDouyinAPI",
    "async_douyin_api",
    # 签名相关
    "DouyinSignRequest",
    "DouyinSignResponse",
    "AbstractDouyinSign",
    "DouyinJavascriptSign",
    "DouyinSignFactory",
    "DouyinSignLogic",
    # 原有模型
    "CollectVideoListRequest",
    "CollectVideoListResponse",
    "VideoAuthor",
    "VideoInfo",
    "VideoStatistics",
    # 搜索相关
    "SearchInfoRequest",
    "SearchInfoResponse",
    "VideoDetailRequest",
    "VideoDetailResponse",
    # 评论相关
    "AwemeCommentsRequest",
    "AwemeCommentsResponse",
    # 用户相关
    "UserInfoRequest",
    "UserInfoResponse",
    "UserAwemePostsRequest",
    "UserAwemePostsResponse",
    # 验证参数生成
    "VerifyParamsGenerator",
    "CommonVerifyParams",
    "TokenManager",
    "VerifyFpManager",
]
