"""
抖音 API 客户端配置

定义抖音 API 客户端的配置参数
"""

from typing import Dict, Optional

from pydantic import BaseModel, Field


class DouyinConfig(BaseModel):
    """抖音 API 客户端配置"""

    # 基础配置
    base_url: str = Field(default="https://www.douyin.com", description="抖音 API 基础 URL")

    # 超时配置
    timeout: float = Field(default=30.0, description="请求超时时间（秒）")

    connect_timeout: float = Field(default=10.0, description="连接超时时间（秒）")

    read_timeout: float = Field(default=30.0, description="读取超时时间（秒）")

    # 重试配置
    max_retries: int = Field(default=3, description="最大重试次数")

    retry_delay: float = Field(default=1.0, description="重试延迟时间（秒）")

    retry_backoff_factor: float = Field(default=2.0, description="重试退避因子")

    # HTTP 配置
    verify_ssl: bool = Field(default=True, description="是否验证 SSL 证书")

    follow_redirects: bool = Field(default=True, description="是否跟随重定向")

    # 默认请求头
    default_headers: Dict[str, str] = Field(
        default_factory=lambda: {
            "accept": "application/json, text/plain, */*",
            "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-US;q=0.7",
            "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "referer": "https://www.douyin.com/",
            "sec-ch-ua": '"Google Chrome";v="137", "Chromium";v="137", "Not?A_Brand";v="24"',
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": '"macOS"',
            "sec-fetch-dest": "empty",
            "sec-fetch-mode": "cors",
            "sec-fetch-site": "same-origin",
        },
        description="默认请求头",
    )

    # Cookie 配置
    default_cookies: Optional[Dict[str, str]] = Field(default=None, description="默认 Cookie")

    # 日志配置
    enable_logging: bool = Field(default=True, description="是否启用日志记录")

    log_level: str = Field(default="INFO", description="日志级别")

    log_requests: bool = Field(default=True, description="是否记录请求日志")

    log_responses: bool = Field(default=False, description="是否记录响应日志")

    # 代理配置
    proxy: Optional[str] = Field(default=None, description="代理服务器地址")

    # 限流配置
    rate_limit_enabled: bool = Field(default=False, description="是否启用限流")

    rate_limit_calls: int = Field(default=100, description="限流调用次数")

    rate_limit_period: int = Field(default=60, description="限流时间窗口（秒）")

    # 算签配置
    enable_sign: bool = Field(default=True, description="是否启用算签功能")

    use_javascript_sign: bool = Field(default=True, description="是否使用 JavaScript 算签（推荐）")

    sign_js_file_path: Optional[str] = Field(default=None, description="JavaScript 算签文件路径")

    sign_cache_enabled: bool = Field(default=True, description="是否启用算签缓存")

    sign_cache_ttl: int = Field(default=300, description="算签缓存过期时间（秒）")

    sign_max_retries: int = Field(default=3, description="算签最大重试次数")

    sign_retry_delay: float = Field(default=1.0, description="算签重试延迟时间（秒）")

    class Config:
        """Pydantic 配置"""

        extra = "forbid"  # 禁止额外字段
        validate_assignment = True  # 启用赋值验证
