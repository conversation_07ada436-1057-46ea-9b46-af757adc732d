# -*- coding: utf-8 -*-
"""
抖音验证参数生成器

基于 MediaCrawlerPro-Python 项目的参数生成逻辑实现
用于自动生成 verify_fp、ms_token、webid、uifid 等参数
"""

import asyncio
import random
import time

import httpx
from loguru import logger

from .exceptions import DouyinClientError
from .schemas.base import CommonVerifyParams

# ==================== 常量定义 ====================

# 抖音固定 User-Agent
DOUYIN_FIXED_USER_AGENT = "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"

# MS Token 请求相关
DOUYIN_MS_TOKEN_REQ_URL = "https://mssdk.bytedance.com/web/common"
DOUYIN_MS_TOKEN_REQ_STR_DATA = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz"

# WebID 请求相关
DOUYIN_WEBID_REQ_URL = "https://mcs.zijieapi.com/webid?aid=6383&sdk_version=5.1.18_zip&device_platform=web"


# ==================== 工具函数 ====================


def get_current_timestamp() -> int:
    """获取当前时间戳（13位毫秒）"""
    return int(time.time() * 1000)


def get_random_str(random_len: int = 12) -> str:
    """生成随机字符串"""
    chars = "AaBbCcDdEeFfGgHhIiJjKkLlMmNnOoPpQqRrSsTtUuVvWwXxYyZz0123456789"
    length = len(chars) - 1
    random_str = ""
    for i in range(random_len):
        random_str += chars[random.randint(0, length)]
    return random_str


def get_web_id() -> str:
    """
    生成随机的 webid
    基于 MediaCrawlerPro-Python 项目的算法
    """

    def e(t):
        if t is not None:
            return str(t ^ (int(16 * random.random()) >> (t // 4)))
        else:
            return "".join(
                [
                    str(int(1e7)),
                    "-",
                    str(int(1e3)),
                    "-",
                    str(int(4e3)),
                    "-",
                    str(int(8e3)),
                    "-",
                    str(int(1e11)),
                ]
            )

    web_id = "".join(e(int(x)) if x in "018" else x for x in e(None))
    return web_id.replace("-", "")[:19]


# ==================== 数据模型 ====================


# ==================== 核心类 ====================


class TokenManager:
    """
    Token 管理器
    基于 MediaCrawlerPro-Python 项目的 TokenManager 实现
    """

    def __init__(self, user_agent: str = DOUYIN_FIXED_USER_AGENT):
        self._user_agent = user_agent

    async def gen_real_msToken(self) -> str:
        """
        生成真实的 msToken
        通过调用抖音官方接口获取
        """
        async with httpx.AsyncClient() as client:
            post_data = {
                "magic": 538969122,
                "version": 1,
                "dataType": 8,
                "strData": DOUYIN_MS_TOKEN_REQ_STR_DATA,
                "tspFromClient": get_current_timestamp(),
                "url": 0,
            }
            headers = {
                "Content-Type": "application/json; charset=utf-8",
                "User-Agent": self._user_agent,
            }

            try:
                response = await client.post(DOUYIN_MS_TOKEN_REQ_URL, json=post_data, headers=headers, timeout=10.0)
                response.raise_for_status()

                # 从响应的 cookies 中提取 msToken
                ms_token = None
                for cookie in response.cookies:
                    if cookie.name == "msToken":
                        ms_token = cookie.value
                        break

                if not ms_token:
                    raise Exception("响应中未找到 msToken")

                if len(ms_token) not in [120, 128]:
                    raise Exception(f"获取msToken内容不符合要求: {ms_token}")

                return ms_token

            except Exception as e:
                logger.warning(f"生成真实 msToken 失败: {e}")
                raise

    @classmethod
    def gen_fake_msToken(cls) -> str:
        """
        生成假的 msToken
        当无法获取真实 msToken 时使用
        """
        false_ms_token = get_random_str(126) + "=="
        return false_ms_token

    async def get_msToken(self) -> str:
        """
        获取 msToken
        只获取真实的 msToken，不使用假的
        """
        return await self.gen_real_msToken()

    async def gen_webid(self) -> str:
        """
        生成个性化追踪 webid
        通过调用抖音官方接口获取
        """
        async with httpx.AsyncClient() as client:
            post_data = {
                "app_id": 6383,
                "referer": "https://www.douyin.com/",
                "url": "https://www.douyin.com/",
                "user_agent": self._user_agent,
                "user_unique_id": "",
            }
            headers = {
                "User-Agent": self._user_agent,
                "Content-Type": "application/json; charset=UTF-8",
                "Referer": "https://www.douyin.com/",
            }

            try:
                response = await client.post(DOUYIN_WEBID_REQ_URL, json=post_data, headers=headers, timeout=10.0)
                response.raise_for_status()

                result = response.json()
                webid = result.get("web_id")

                if not webid:
                    raise Exception("响应中未找到 web_id")

                return webid

            except Exception as e:
                logger.warning(f"生成真实 webid 失败: {e}, 返回随机 webid")
                return get_web_id()


class VerifyFpManager:
    """
    验证指纹管理器
    基于 MediaCrawlerPro-Python 项目的 VerifyFpManager 实现
    """

    @classmethod
    def gen_verify_fp(cls) -> str:
        """
        生成 verifyFp 参数
        基于时间戳和随机数生成
        """
        base_str = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz"
        t = len(base_str)
        milliseconds = int(round(time.time() * 1000))

        # 转换为 base36
        base36 = ""
        while milliseconds > 0:
            remainder = milliseconds % 36
            if remainder < 10:
                base36 = str(remainder) + base36
            else:
                base36 = chr(ord("a") + remainder - 10) + base36
            milliseconds = int(milliseconds / 36)

        r = base36
        o = [""] * 36
        o[8] = o[13] = o[18] = o[23] = "_"
        o[14] = "4"

        for i in range(36):
            if not o[i]:
                n = 0 or int(random.random() * t)
                if i == 19:
                    n = 3 & n | 8
                o[i] = base_str[n]

        return "verify_" + r + "_" + "".join(o)

    @classmethod
    def gen_s_v_web_id(cls) -> str:
        """生成 s_v_web_id 参数"""
        return cls.gen_verify_fp()

    @classmethod
    def gen_uifid(cls) -> str:
        """生成 uifid 参数"""
        return get_random_str(32)


# ==================== 主要接口 ====================


class VerifyParamsGenerator:
    """
    验证参数生成器
    统一的参数生成接口
    """

    def __init__(self, user_agent: str = DOUYIN_FIXED_USER_AGENT):
        self.user_agent = user_agent
        self.token_manager = TokenManager(user_agent)

    async def get_common_verify_params(self) -> CommonVerifyParams:
        """
        获取通用验证参数
        生成所有必需的验证参数
        """
        logger.info("[VerifyParamsGenerator] 开始生成通用验证参数")

        try:
            # 并发生成各种参数
            ms_token_task = self.token_manager.get_msToken()
            webid_task = self.token_manager.gen_webid()

            # 等待异步任务完成
            ms_token, webid = await asyncio.gather(ms_token_task, webid_task)

            # 生成其他参数
            verify_fp = VerifyFpManager.gen_verify_fp()
            s_v_web_id = VerifyFpManager.gen_s_v_web_id()
            uifid = VerifyFpManager.gen_uifid()

            logger.info(
                f"[VerifyParamsGenerator] 参数生成完成: "
                f"ms_token={ms_token[:20]}..., "
                f"webid={webid}, "
                f"verify_fp={verify_fp[:20]}..., "
                f"s_v_web_id={s_v_web_id[:20]}..., "
                f"uifid={uifid[:20]}..."
            )

            return CommonVerifyParams(
                ms_token=ms_token, webid=webid, verify_fp=verify_fp, s_v_web_id=s_v_web_id, uifid=uifid
            )

        except Exception as e:
            logger.error(f"[VerifyParamsGenerator] 生成验证参数失败: {e}")
            raise DouyinClientError(f"生成验证参数失败: {e}")

    def get_fake_verify_params(self) -> CommonVerifyParams:
        """
        获取假的验证参数
        用于测试或备用场景
        """
        logger.info("[VerifyParamsGenerator] 生成假的验证参数")

        return CommonVerifyParams(
            ms_token=TokenManager.gen_fake_msToken(),
            webid=get_web_id(),
            verify_fp=VerifyFpManager.gen_verify_fp(),
            s_v_web_id=VerifyFpManager.gen_s_v_web_id(),
            uifid=VerifyFpManager.gen_uifid(),
        )
