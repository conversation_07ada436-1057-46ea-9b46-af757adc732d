# -*- coding: utf-8 -*-
"""
抖音客户端工具类

提供签名客户端等工具功能
"""

import time
from typing import Any, Dict, Optional

from log import logger

from .schemas import DouyinSignRequest
from .signer import DouyinSignFactory, DouyinSignLogic


class DouyinSignClient:
    """
    抖音签名客户端

    适配器类，桥接新的签名逻辑和原有的客户端接口
    """

    def __init__(
        self,
        use_javascript: bool = True,
        js_file_path: Optional[str] = None,
        enable_cache: bool = True,
        cache_ttl: int = 300,
        max_retries: int = 3,
        retry_delay: float = 1.0,
    ):
        """
        初始化签名客户端

        Args:
            use_javascript: 是否使用 JavaScript 签名（已移除 Playwright 支持）
            js_file_path: JavaScript 文件路径
            enable_cache: 是否启用缓存
            cache_ttl: 缓存过期时间（秒）
            max_retries: 最大重试次数
            retry_delay: 重试延迟（秒）
        """
        # Playwright 支持已被移除，强制使用 JavaScript
        if not use_javascript:
            logger.warning("Playwright 签名已被移除，强制使用 JavaScript 签名")
            use_javascript = True

        self.use_javascript = use_javascript
        self.enable_cache = enable_cache
        self.cache_ttl = cache_ttl
        self.max_retries = max_retries
        self.retry_delay = retry_delay

        # 创建签名逻辑实例（仅支持 JavaScript）
        kwargs = {}
        if js_file_path:
            kwargs["js_file_path"] = js_file_path

        self._sign_logic = DouyinSignLogic(sign_type=DouyinSignFactory.JAVASCRIPT_SIGN, **kwargs)

        # 统计信息
        self._stats = {
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "cache_hits": 0,
            "cache_misses": 0,
            "last_request_time": None,
        }

        # 简单的内存缓存
        self._cache = {}

        logger.info("DouyinSignClient 初始化完成，使用 JavaScript 签名")

    async def sign(self, uri: str, query_params: str, user_agent: str, cookies: str = "") -> str:
        """
        生成签名

        Args:
            uri: 请求的 URI
            query_params: 查询参数字符串
            user_agent: User-Agent
            cookies: Cookies 字符串

        Returns:
            a_bogus 签名字符串
        """
        self._stats["total_requests"] += 1
        self._stats["last_request_time"] = time.time()

        try:
            # 检查缓存
            cache_key = None
            if self.enable_cache:
                cache_key = self._generate_cache_key(uri, query_params, user_agent, cookies)
                cached_result = self._get_from_cache(cache_key)
                if cached_result:
                    self._stats["cache_hits"] += 1
                    self._stats["successful_requests"] += 1
                    return cached_result
                else:
                    self._stats["cache_misses"] += 1

            # 创建签名请求
            request = DouyinSignRequest(uri=uri, query_params=query_params, user_agent=user_agent, cookies=cookies)

            # 生成签名
            response = await self._sign_logic.sign(request)

            # 缓存结果
            if self.enable_cache and cache_key:
                self._set_to_cache(cache_key, response.a_bogus)

            self._stats["successful_requests"] += 1
            return response.a_bogus

        except Exception as e:
            self._stats["failed_requests"] += 1
            logger.error(f"签名生成失败: {e}")
            raise

    def _generate_cache_key(self, uri: str, query_params: str, user_agent: str, cookies: str) -> str:
        """生成缓存键"""
        import hashlib

        content = f"{uri}|{query_params}|{user_agent}|{cookies}"
        return hashlib.md5(content.encode()).hexdigest()

    def _get_from_cache(self, cache_key: str) -> Optional[str]:
        """从缓存获取结果"""
        if cache_key in self._cache:
            cached_data = self._cache[cache_key]
            if time.time() - cached_data["timestamp"] < self.cache_ttl:
                return cached_data["value"]
            else:
                # 缓存过期，删除
                del self._cache[cache_key]
        return None

    def _set_to_cache(self, cache_key: str, value: str) -> None:
        """设置缓存"""
        self._cache[cache_key] = {"value": value, "timestamp": time.time()}

    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return self._stats.copy()

    def clear_cache(self) -> None:
        """清空缓存"""
        self._cache.clear()
        logger.info("签名缓存已清空")

    def get_cache_size(self) -> int:
        """获取缓存大小"""
        return len(self._cache)

    def cleanup_expired_cache(self) -> int:
        """清理过期缓存"""
        current_time = time.time()
        expired_keys = []

        for key, data in self._cache.items():
            if current_time - data["timestamp"] >= self.cache_ttl:
                expired_keys.append(key)

        for key in expired_keys:
            del self._cache[key]

        if expired_keys:
            logger.info(f"清理了 {len(expired_keys)} 个过期缓存项")

        return len(expired_keys)
