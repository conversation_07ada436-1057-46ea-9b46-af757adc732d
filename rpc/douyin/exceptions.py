"""
抖音 API 客户端异常定义

定义抖音 API 客户端相关的异常类
"""

from typing import Any, Dict, Optional


class DouyinClientError(Exception):
    """抖音客户端基础异常"""

    def __init__(
        self,
        message: str,
        status_code: Optional[int] = None,
        response_data: Optional[Dict[str, Any]] = None,
    ):
        self.message = message
        self.status_code = status_code
        self.response_data = response_data
        super().__init__(self.message)


class DouyinRequestError(DouyinClientError):
    """抖音请求异常"""

    pass


class DouyinResponseError(DouyinClientError):
    """抖音响应异常"""

    pass


class DouyinTimeoutError(DouyinClientError):
    """抖音请求超时异常"""

    pass


class DouyinRateLimitError(DouyinClientError):
    """抖音限流异常"""

    pass


class DouyinAuthenticationError(DouyinClientError):
    """抖音认证异常"""

    pass


class DouyinValidationError(DouyinClientError):
    """抖音参数验证异常"""

    pass
