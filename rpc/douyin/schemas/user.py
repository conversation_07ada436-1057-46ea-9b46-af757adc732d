"""
抖音用户数据模型
"""

from typing import Any, Dict, List, Optional, Union

from pydantic import BaseModel, Field, field_validator, model_validator

from .base import VideoAuthor


class UserInfoRequest(BaseModel):
    """获取用户信息请求参数"""

    sec_user_id: str = Field(..., description="加密用户ID")


class UserInfoResponse(BaseModel):
    """获取用户信息响应"""

    status_code: int = Field(..., description="状态码")
    user: Optional[VideoAuthor] = Field(None, description="用户信息")

    class Config:
        extra = "allow"


class UserAwemePostsRequest(BaseModel):
    """获取用户作品列表请求参数"""

    sec_user_id: str = Field(..., description="加密用户ID")
    max_cursor: int = Field(0, description="分页游标")
    count: int = Field(20, description="每页数量")


class UserAwemePostsResponse(BaseModel):
    """获取用户作品列表响应"""

    status_code: int = Field(..., description="状态码")
    aweme_list: List[Dict[str, Any]] = Field([], description="作品列表")
    has_more: bool = Field(False, description="是否有更多")
    max_cursor: int = Field(0, description="下一个分页游標")

    class Config:
        extra = "allow"


class SelfAwemeCollectionRequest(BaseModel):
    """获取用户收藏夹列表请求参数"""

    cursor: int = Field(0, description="分页游标")
    count: int = Field(20, description="每页数量")


class CollectionInfo(BaseModel):
    """收藏夹信息"""

    collects_id: Union[int, str] = Field(..., description="收藏夹ID")
    collects_name: Optional[str] = Field("", description="收藏夹名称")
    cover: Optional[Dict[str, Any]] = Field(None, description="封面")
    total_number: Optional[int] = Field(0, description="收藏数量", alias="count")
    collects_id_str: Optional[str] = Field(None, description="收藏夹ID字符串")
    app_id: Optional[int] = Field(None, description="应用ID")
    create_time: Optional[int] = Field(None, description="创建时间")
    follow_status: Optional[int] = Field(None, description="关注状态")
    followed_count: Optional[int] = Field(None, description="关注数")
    is_normal_status: Optional[bool] = Field(None, description="是否正常状态")
    item_type: Optional[int] = Field(None, description="项目类型")
    last_collect_time: Optional[int] = Field(None, description="最后收藏时间")
    play_count: Optional[int] = Field(None, description="播放数")
    states: Optional[int] = Field(None, description="状态")
    status: Optional[int] = Field(None, description="状态码")
    system_type: Optional[int] = Field(None, description="系统类型")
    user_id: Optional[Union[int, str]] = Field(None, description="用户ID")
    user_id_str: Optional[str] = Field(None, description="用户ID字符串")
    user_info: Optional[Dict[str, Any]] = Field(None, description="用户信息")
    collects_cover: Optional[Dict[str, Any]] = Field(None, description="收藏夹封面")
    sicily_collects_cover_list: Optional[Any] = Field(None, description="西西里收藏夹封面列表")

    class Config:
        extra = "allow"
        populate_by_name = True  # 允许使用字段名和别名

    @field_validator("collects_name", mode="before")
    @classmethod
    def validate_collects_name(cls, v):
        """处理收藏夹名称为空或缺失的情况"""
        if v is None:
            return ""
        return str(v)

    @field_validator("total_number", mode="before")
    @classmethod
    def validate_total_number(cls, v):
        """处理收藏数量为空或缺失的情况"""
        if v is None:
            return 0
        try:
            return int(v)
        except (ValueError, TypeError):
            return 0

    @model_validator(mode="after")
    def ensure_required_fields(self):
        """确保必需字段有有效值"""
        if not self.collects_name:
            self.collects_name = ""
        if self.total_number is None:
            self.total_number = 0
        return self

    @property
    def name(self) -> str:
        """获取收藏夹名称（向后兼容）"""
        return self.collects_name or ""

    @property
    def count(self) -> int:
        """获取收藏数量（向后兼容）"""
        return self.total_number or 0


class SelfAwemeCollectionResponse(BaseModel):
    """获取用户收藏夹列表响应"""

    status_code: int = Field(..., description="状态码")
    collects_list: List[CollectionInfo] = Field(default_factory=list, description="收藏夹列表")
    cursor: int = Field(0, description="游标")
    has_more: bool = Field(False, description="是否有更多")

    @field_validator("collects_list", mode="before")
    @classmethod
    def validate_collects_list(cls, v):
        """处理 collects_list 为 null 的情况，转换为空数组，并过滤无效的收藏夹项"""
        if v is None:
            return []

        if not isinstance(v, list):
            return []

        # 过滤和修复收藏夹项
        valid_collections = []
        for item in v:
            if not isinstance(item, dict):
                continue

            # 确保必需字段存在，如果不存在则设置默认值
            if "collects_id" not in item:
                continue  # 没有ID的收藏夹跳过

            # 设置默认值
            item.setdefault("collects_name", "")
            item.setdefault("name", "")  # 处理别名

            # 处理 total_number 字段和其别名 count
            if "total_number" not in item and "count" not in item:
                item["total_number"] = 0
            elif "total_number" not in item and "count" in item:
                item["total_number"] = item["count"]

            valid_collections.append(item)

        return valid_collections

    class Config:
        extra = "allow"  # 允许额外字段

    @property
    def has_more_collections(self) -> bool:
        """是否有更多收藏夹"""
        return self.has_more


class QueryUserRequest(BaseModel):
    """查询用户请求参数"""

    # 这个接口可能不需要特定参数，或需要通过headers传递
    pass


class QueryUserResponse(BaseModel):
    """查询用户响应"""

    id: str = Field(..., description="用户会话ID")
    create_time: str = Field(..., description="创建时间")
    last_time: str = Field(..., description="最后活动时间")
    user_uid: str = Field(..., description="用户UID")
    user_uid_type: int = Field(..., description="用户UID类型")
    firebase_instance_id: str = Field(..., description="Firebase实例ID")
    user_agent: str = Field(..., description="用户代理字符串")
    browser_name: str = Field(..., description="浏览器名称")

    class Config:
        extra = "allow"
