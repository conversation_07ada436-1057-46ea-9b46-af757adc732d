"""
抖音评论数据模型
"""

from typing import Any, Dict, List

from pydantic import BaseModel, Field


class AwemeCommentsRequest(BaseModel):
    """获取视频评论请求参数"""

    aweme_id: str = Field(..., description="视频ID")
    cursor: int = Field(0, description="分页游标")
    count: int = Field(20, description="每页数量")
    item_type: int = Field(0, description="项目类型")


class AwemeCommentsResponse(BaseModel):
    """获取视频评论响应"""

    status_code: int = Field(..., description="状态码")
    comments: List[Dict[str, Any]] = Field([], description="评论列表")
    has_more: bool = Field(False, description="是否有更多")
    cursor: int = Field(0, description="下一个分页游标")

    class Config:
        extra = "allow"
