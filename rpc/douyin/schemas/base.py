"""
基础数据模型
"""

from typing import Optional

from pydantic import BaseModel, Field


class VideoAuthor(BaseModel):
    """视频作者信息（基于实际API响应结构）"""

    # 核心必需字段
    uid: str = Field(default="", description="用户ID")
    nickname: str = Field(default="", description="昵称")
    sec_uid: str = Field(default="", description="安全用户ID")
    unique_id: str = Field(default="", description="用户唯一ID")

    # 其他所有字段都设为可选，以适应API响应的变化
    class Config:
        extra = "allow"  # 允许额外字段


class CommonVerifyParams(BaseModel):
    """通用验证参数模型"""

    ms_token: str = Field(..., title="ms_token", description="ms_token")
    webid: str = Field(..., title="webid", description="webid")
    verify_fp: str = Field(..., title="verify_fp", description="verify_fp")
    s_v_web_id: str = Field(..., title="s_v_web_id", description="s_v_web_id")
    uifid: Optional[str] = Field(None, title="uifid", description="uifid")
