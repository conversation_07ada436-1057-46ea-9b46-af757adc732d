"""
抖音视频数据模型
"""

from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field

from .base import VideoAuthor


class VideoDetailRequest(BaseModel):
    """获取视频详情请求参数"""

    aweme_id: str = Field(..., description="视频ID")


class AwemeControl(BaseModel):
    """视频控制信息"""

    class Config:
        extra = "allow"  # 允许额外字段


class MusicInfo(BaseModel):
    """音乐信息"""

    id: Optional[int] = Field(None, description="音乐ID")
    id_str: Optional[str] = Field(None, description="音乐ID字符串")
    title: Optional[str] = Field(None, description="音乐标题")
    author: Optional[str] = Field(None, description="音乐作者")
    album: Optional[str] = Field(None, description="专辑")
    duration: Optional[int] = Field(None, description="时长")

    class Config:
        extra = "allow"  # 允许额外字段


class VideoDetail(BaseModel):
    """视频详细信息"""

    height: Optional[int] = Field(None, description="视频高度")
    width: Optional[int] = Field(None, description="视频宽度")
    ratio: Optional[str] = Field(None, description="视频比例")
    format: Optional[str] = Field(None, description="视频格式")

    class Config:
        extra = "allow"  # 允许额外字段


class VideoStatus(BaseModel):
    """视频状态信息"""

    is_delete: Optional[bool] = Field(None, description="是否删除")
    allow_share: Optional[bool] = Field(None, description="是否允许分享")
    is_prohibited: Optional[bool] = Field(None, description="是否被禁止")
    private_status: Optional[int] = Field(None, description="私密状态")
    in_reviewing: Optional[bool] = Field(None, description="是否在审核中")

    class Config:
        extra = "allow"  # 允许额外字段


class VideoStatistics(BaseModel):
    """视频统计信息（基于实际API响应结构）"""

    # 必需字段
    comment_count: int = Field(default=0, description="评论数")
    digg_count: int = Field(default=0, description="点赞数")
    play_count: int = Field(default=0, description="播放数")
    share_count: int = Field(default=0, description="分享数")

    # 可选字段（根据实际API响应调整）
    collect_count: Optional[int] = Field(None, description="收藏数")
    exposure_count: Optional[int] = Field(None, description="曝光数")
    live_watch_count: Optional[int] = Field(None, description="直播观看数")
    recommend_count: Optional[int] = Field(None, description="推荐数")
    digest: Optional[str] = Field(None, description="摘要")

    # 可选字段
    admire_count: Optional[int] = Field(None, description="赞赏数")
    flame_amount_simple_str: Optional[str] = Field(None, description="火焰金额简单字符串")
    flame_income_count: Optional[int] = Field(None, description="火焰收入数")
    flame_income_count_str: Optional[str] = Field(None, description="火焰收入数字符串")
    flame_send_count: Optional[int] = Field(None, description="火焰发送数")
    friend_digg_count: Optional[int] = Field(None, description="朋友点赞数")

    class Config:
        extra = "allow"  # 允许额外字段

    mutual_friend_comment_count: Optional[int] = Field(None, description="共同朋友评论数")
    mutual_friend_digg_count: Optional[int] = Field(None, description="共同朋友点赞数")


class AwemeDetail(BaseModel):
    """视频详情信息（基于实际API响应结构）"""

    # 核心必需字段
    aweme_id: str = Field(default="", description="视频ID")
    desc: str = Field(default="", description="视频描述")
    create_time: int = Field(default=0, description="创建时间")
    author: Optional[VideoAuthor] = Field(default=None, description="作者信息")
    author_user_id: int = Field(default=0, description="作者用户ID")
    aweme_type: int = Field(default=0, description="视频类型")
    collect_stat: int = Field(default=0, description="收藏状态")

    # 控制信息
    aweme_control: Optional[AwemeControl] = Field(default=None, description="视频控制")

    # 统计信息
    statistics: Optional[VideoStatistics] = Field(default=None, description="统计信息")

    # 音乐信息
    music: Optional[MusicInfo] = Field(default=None, description="音乐信息")

    # 视频详细信息
    video: Optional[VideoDetail] = Field(default=None, description="视频详细信息")

    # 视频状态
    status: Optional[VideoStatus] = Field(default=None, description="视频状态")

    # 其他常见字段
    duration: Optional[int] = Field(default=None, description="视频时长")
    share_url: Optional[str] = Field(default=None, description="分享链接")
    user_digged: Optional[int] = Field(default=None, description="用户是否点赞")
    is_ads: Optional[bool] = Field(default=None, description="是否为广告")

    # 其他所有字段都设为可选，以适应API响应的变化
    class Config:
        extra = "allow"  # 允许额外字段


class VideoInfo(BaseModel):
    """视频信息（基于实际API响应结构）"""

    # 基础必需信息
    aweme_id: str = Field(default="", description="视频ID")
    desc: str = Field(default="", description="视频描述")
    create_time: int = Field(default=0, description="创建时间")
    author: Optional[VideoAuthor] = Field(default=None, description="作者信息")
    author_user_id: int = Field(default=0, description="作者用户ID")
    aweme_type: int = Field(default=0, description="视频类型")
    collect_stat: int = Field(default=0, description="收藏状态")

    # 控制信息
    aweme_control: Optional[AwemeControl] = Field(default=None, description="视频控制")

    # 统计信息
    statistics: Optional[VideoStatistics] = Field(default=None, description="统计信息")

    # 音乐信息
    music: Optional[MusicInfo] = Field(default=None, description="音乐信息")

    # 视频详细信息
    video: Optional[VideoDetail] = Field(default=None, description="视频详细信息")

    # 视频状态
    status: Optional[VideoStatus] = Field(default=None, description="视频状态")

    # 其他常见字段
    duration: Optional[int] = Field(default=None, description="视频时长")
    share_url: Optional[str] = Field(default=None, description="分享链接")
    user_digged: Optional[int] = Field(default=None, description="用户是否点赞")
    is_ads: Optional[bool] = Field(default=None, description="是否为广告")

    # 其他所有字段都设为可选，以适应API响应的变化
    class Config:
        extra = "allow"  # 允许额外字段


class VideoDetailResponse(BaseModel):
    """获取视频详情响应"""

    status_code: int = Field(..., description="状态码")
    aweme_detail: Optional[AwemeDetail] = Field(None, description="视频详情")

    class Config:
        extra = "allow"


class CollectVideoListRequest(BaseModel):
    """抖音收藏视频列表请求参数"""

    # 业务参数
    collects_id: str = Field(..., description="收藏夹ID")
    cursor: int = Field(default=0, description="游标，用于分页")
    count: int = Field(default=10, description="每页数量")


class LogPb(BaseModel):
    """日志PB信息"""

    class Config:
        extra = "allow"  # 允许额外字段


class ExtraInfo(BaseModel):
    """额外信息"""

    class Config:
        extra = "allow"  # 允许额外字段


class CollectVideoListResponse(BaseModel):
    """抖音收藏视频列表响应"""

    # 基础响应字段
    status_code: int = Field(..., description="状态码")
    aweme_list: Optional[List[VideoInfo]] = Field(default_factory=list, description="视频列表")
    cursor: int = Field(default=0, description="游标")
    has_more: int = Field(default=0, description="是否有更多（0=无，1=有）")
    extra: Optional[ExtraInfo] = Field(default=None, description="额外信息")
    log_pb: Optional[LogPb] = Field(default=None, description="日志PB")
    sec_uid: str = Field(default="", description="安全用户ID")
    uid: str = Field(default="", description="用户ID")

    # 可选字段
    time_list: Optional[List[Dict[str, Any]]] = Field(None, description="时间列表")
    request_item_cursor: Optional[int] = Field(None, description="请求项游标")
