#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 Playwright 逻辑清除

验证 Playwright 相关逻辑已被正确清除，并且系统能够优雅地处理向后兼容性
"""

import asyncio

from log import logger

from rpc.douyin import DouyinSignFactory, DouyinSignLogic, DouyinSignRequest
from rpc.douyin.utils import DouyinSignClient


async def test_playwright_removal():
    """测试 Playwright 功能移除"""
    logger.info("=== 测试 Playwright 功能移除 ===")

    results = []

    # 1. 测试 DouyinPlaywrightSign 类已被删除
    logger.info("\n1. 测试 DouyinPlaywrightSign 类已被删除:")
    try:
        # 尝试导入 DouyinPlaywrightSign，应该失败
        from rpc.douyin import DouyinPlaywrightSign

        logger.info("  ❌ 意外成功 - DouyinPlaywrightSign 应该已被删除")
        results.append(False)

    except ImportError:
        logger.info("  ✅ 正确抛出 ImportError: DouyinPlaywrightSign 已被删除")
        results.append(True)
    except Exception as e:
        logger.warning(f"  ⚠️  意外错误: {str(e)[:80]}...")
        results.append(False)

    request = DouyinSignRequest(
        uri="/aweme/v1/web/aweme/detail/",
        query_params="device_platform=webapp&aid=6383",
        user_agent="Mozilla/5.0 (Test)",
        cookies="test=cookie",
    )

    # 2. 测试工厂模式使用 playwright 类型
    logger.info("\n2. 测试工厂模式使用 'playwright' 类型:")
    try:
        signer = DouyinSignFactory.get_sign("playwright")
        logger.info(f"  ✅ 工厂返回 JavaScript 实例: {type(signer).__name__}")

        # 尝试使用这个实例，应该正常工作（因为返回的是 JavaScript 实现）
        try:
            response = await signer.sign(request)
            logger.info(f"  ✅ 签名生成成功: {response.a_bogus[:50]}...")
            results.append(True)
        except Exception as e:
            logger.error(f"  ❌ 签名生成失败: {str(e)[:80]}...")
            results.append(False)

    except Exception as e:
        logger.error(f"  ❌ 工厂创建失败: {str(e)[:80]}...")
        results.append(False)

    # 3. 测试 DouyinSignLogic 使用 playwright 类型
    logger.info("\n3. 测试 DouyinSignLogic 使用 'playwright' 类型:")
    try:
        sign_logic = DouyinSignLogic(sign_type="playwright")
        logger.info("  ✅ DouyinSignLogic 创建成功（应该自动切换到 JavaScript）")

        # 检查实际使用的签名类型
        if sign_logic.sign_type == DouyinSignFactory.JAVASCRIPT_SIGN:
            logger.info("  ✅ 自动切换到 JavaScript 签名")
            results.append(True)
        else:
            logger.error(f"  ❌ 签名类型未正确切换: {sign_logic.sign_type}")
            results.append(False)

    except Exception as e:
        logger.error(f"  ❌ DouyinSignLogic 创建失败: {str(e)[:80]}...")
        results.append(False)

    # 4. 测试 DouyinSignClient 使用 use_javascript=False
    logger.info("\n4. 测试 DouyinSignClient 使用 use_javascript=False:")
    try:
        sign_client = DouyinSignClient(use_javascript=False)
        logger.info("  ✅ DouyinSignClient 创建成功（应该强制使用 JavaScript）")

        # 检查是否强制使用了 JavaScript
        if sign_client.use_javascript:
            logger.info("  ✅ 强制切换到 JavaScript 签名")
            results.append(True)
        else:
            logger.error("  ❌ 未强制切换到 JavaScript")
            results.append(False)

    except Exception as e:
        logger.error(f"  ❌ DouyinSignClient 创建失败: {str(e)[:80]}...")
        results.append(False)

    # 5. 测试 JavaScript 签名仍然正常工作
    logger.info("\n5. 测试 JavaScript 签名仍然正常工作:")
    try:
        js_logic = DouyinSignLogic(sign_type=DouyinSignFactory.JAVASCRIPT_SIGN)
        response = await js_logic.sign(request)
        logger.info(f"  ✅ JavaScript 签名正常: {response.a_bogus[:50]}...")
        results.append(True)
    except Exception as e:
        logger.error(f"  ❌ JavaScript 签名失败: {str(e)[:80]}...")
        results.append(False)

    return results


async def main():
    """主函数"""
    logger.info("Playwright 逻辑清除测试")
    logger.info("=" * 60)
    logger.info("测试目标:")
    logger.info("  - Playwright 相关功能已被移除")
    logger.info("  - 向后兼容性得到保持")
    logger.info("  - JavaScript 签名仍然正常工作")
    logger.info("  - 优雅的错误处理和自动切换")
    logger.info("=" * 60)

    results = await test_playwright_removal()

    logger.info("\n" + "=" * 60)
    logger.info("测试结果汇总:")
    test_names = [
        "DouyinPlaywrightSign 类已删除",
        "工厂模式 'playwright' 类型",
        "DouyinSignLogic 自动切换",
        "DouyinSignClient 强制切换",
        "JavaScript 签名正常工作",
    ]

    for i, (name, result) in enumerate(zip(test_names, results)):
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"  {i+1}. {name}: {status}")

    success_count = sum(results)
    total_count = len(results)
    logger.info(f"\n总体结果: {success_count}/{total_count} 项测试通过")

    if success_count == total_count:
        logger.info("🎉 所有测试通过！Playwright 逻辑已成功清除")
        logger.info("✅ 向后兼容性保持良好")
        logger.info("✅ JavaScript 签名功能正常")
    else:
        logger.warning("⚠️  部分测试失败，请检查实现")

    logger.info("\n📝 清除总结:")
    logger.info("  - DouyinPlaywrightSign 类已完全删除")
    logger.info("  - 工厂模式支持 'playwright' 类型但返回 JavaScript 实现")
    logger.info("  - DouyinSignLogic 自动将 'playwright' 切换为 'javascript'")
    logger.info("  - DouyinSignClient 强制使用 JavaScript 签名")
    logger.info("  - 所有 Playwright 依赖已被移除")


if __name__ == "__main__":
    asyncio.run(main())
