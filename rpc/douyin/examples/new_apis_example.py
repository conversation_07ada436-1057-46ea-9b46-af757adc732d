"""
新增 API 接口使用示例

展示所有新增接口的使用方法
"""

import asyncio

from log import logger

from rpc.douyin import (  # 搜索相关; 评论相关; 用户相关; 异常
    AwemeCommentsRequest,
    DouyinResponseError,
    SearchInfoRequest,
    SubCommentsRequest,
    UserAwemePostsRequest,
    UserInfoRequest,
    VideoDetailRequest,
    async_douyin_api,
)


async def search_example():
    """搜索接口示例"""
    logger.info("🔍 搜索接口示例")
    logger.info("-" * 40)

    # 使用预配置的客户端
    client = async_douyin_api.async_client
    try:
        # 关键词搜索
        search_request = SearchInfoRequest(
            keyword="美食",
            search_channel="aweme_general",
            offset=0,
            count="10",
        )

        cookies = "your_cookie_string"

        logger.info("正在搜索关键词: 美食")
        response = await client.search_info_by_keyword(search_request, cookies)

        logger.info(f"✅ 搜索成功，找到 {len(response.video_list)} 个视频")
        for i, video in enumerate(response.video_list[:3], 1):
            author_name = video.author.nickname if video.author else "未知作者"
            logger.info(f"  {i}. {video.desc[:50]}... (作者: {author_name})")

        # 获取视频详情
        if response.video_list:
            video_id = response.video_list[0].aweme_id
            detail_request = VideoDetailRequest(aweme_id=video_id)

            logger.info(f"\n正在获取视频详情: {video_id}")
            detail_response = await client.get_video_by_id(detail_request, cookies)

            if detail_response.aweme_detail is not None:
                video_detail = detail_response.aweme_detail
                logger.info("✅ 视频详情获取成功")
                logger.info(f"  标题: {video_detail.desc}")
                author_name = video_detail.author.nickname if video_detail.author else "未知作者"
                logger.info(f"  作者: {author_name}")
                if video_detail.statistics:
                    logger.info(f"  点赞数: {video_detail.statistics.digg_count}")
                    logger.info(f"  评论数: {video_detail.statistics.comment_count}")
            else:
                logger.warning("⚠️ 视频详情为空，可能视频不存在或无访问权限")

    except DouyinResponseError as e:
        logger.error(f"❌ 搜索失败: {e}")
    except Exception as e:
        logger.error(f"❌ 未知错误: {e}")


async def comments_example():
    """评论接口示例"""
    logger.info("\n💬 评论接口示例")
    logger.info("-" * 40)

    # 使用预配置的客户端
    client = async_douyin_api.async_client
    try:
        # 获取视频评论
        comments_request = AwemeCommentsRequest(
            aweme_id="your_video_id",
            cursor=0,
            count=10,
        )

        cookies = "your_cookie_string"

        logger.info("正在获取视频评论...")
        response = await client.get_aweme_comments(comments_request, cookies)

        logger.info(f"✅ 评论获取成功，共 {len(response.comments)} 条评论")
        for i, comment in enumerate(response.comments[:3], 1):
            logger.info(f"  {i}. {comment.user.nickname}: {comment.text[:50]}...")
            logger.info(f"     点赞数: {comment.digg_count}, 回复数: {comment.reply_comment_total}")

        # 获取子评论
        if response.comments and response.comments[0].reply_comment_total > 0:
            parent_comment = response.comments[0]
            sub_comments_request = SubCommentsRequest(
                item_id="your_video_id",
                comment_id=parent_comment.cid,
            )

            logger.info(f"\n正在获取评论 {parent_comment.cid} 的子评论...")
            sub_response = await client.get_sub_comments(sub_comments_request, cookies)

            logger.info(f"✅ 子评论获取成功，共 {len(sub_response.comments)} 条子评论")
            for i, sub_comment in enumerate(sub_response.comments[:2], 1):
                logger.info(f"    └─ {i}. {sub_comment.user.nickname}: {sub_comment.text[:30]}...")

    except DouyinResponseError as e:
        logger.error(f"❌ 评论获取失败: {e}")
    except Exception as e:
        logger.error(f"❌ 未知错误: {e}")


async def user_example():
    """用户接口示例"""
    logger.info("\n👤 用户接口示例")
    logger.info("-" * 40)

    # 使用预配置的客户端
    client = async_douyin_api.async_client
    try:
        # 获取用户信息
        user_request = UserInfoRequest(
            sec_user_id="your_sec_user_id",
        )

        cookies = "your_cookie_string"

        logger.info("正在获取用户信息...")
        response = await client.get_user_info(user_request, cookies)

        user = response.user
        logger.info("✅ 用户信息获取成功")
        logger.info(f"  昵称: {user.nickname}")
        logger.info(f"  个性签名: {user.signature}")
        logger.info(f"  粉丝数: {user.follower_count}")
        logger.info(f"  关注数: {user.following_count}")
        logger.info(f"  作品数: {user.aweme_count}")
        logger.info(f"  获赞总数: {user.total_favorited}")

        # 获取用户视频列表
        posts_request = UserAwemePostsRequest(
            sec_user_id="your_sec_user_id",
            max_cursor=0,
            count=10,
        )

        logger.info("\n正在获取用户视频列表...")
        posts_response = await client.get_user_aweme_posts(posts_request, cookies)

        logger.info(f"✅ 用户视频获取成功，共 {len(posts_response.aweme_list)} 个视频")
        for i, video in enumerate(posts_response.aweme_list[:3], 1):
            logger.info(f"  {i}. {video.desc[:50]}...")
            if video.statistics:
                logger.info(f"     点赞数: {video.statistics.digg_count}")

    except DouyinResponseError as e:
        logger.error(f"❌ 用户信息获取失败: {e}")
    except Exception as e:
        logger.error(f"❌ 未知错误: {e}")


async def pong_example():
    """测试连接示例"""
    logger.info("\n🔧 测试连接示例")
    logger.info("-" * 40)

    # 使用预配置的客户端
    client = async_douyin_api.async_client
    try:
        logger.info("正在测试连接...")

        # pong 方法现在需要真实参数
        # result = await client.pong(
        #     webid="your_webid",
        #     msToken="your_msToken",
        #     a_bogus="your_a_bogus",
        #     verifyFp="your_verifyFp",
        #     fp="your_fp",
        #     cookies="your_cookies"
        # )

        # 这里只演示方法存在性
        assert hasattr(client, "pong"), "pong 方法不存在"
        logger.info("✅ pong 方法存在")
        logger.info("   注意：pong 方法需要真实参数才能测试接口可用性")
        logger.info("   返回值：bool (True=可用, False=不可用)")

    except Exception as e:
        logger.error(f"❌ 连接测试失败: {e}")


async def main():
    """运行所有示例"""
    logger.info("🚀 新增 API 接口使用示例")
    logger.info("=" * 60)

    logger.info("\n⚠️  注意事项:")
    logger.info("1. 请将示例中的占位符替换为真实参数")
    logger.info("2. 从浏览器获取有效的 Cookie 和参数")
    logger.info("3. 参考 USAGE.md 获取详细的参数获取方法")
    logger.info("4. 注意遵守抖音的使用条款和频率限制")

    # 运行测试连接示例（不需要真实参数）
    await pong_example()

    logger.info("\n" + "=" * 60)
    logger.info("📝 其他示例需要真实参数才能运行")
    logger.info("请参考以下方法获取参数:")
    logger.info("- 查看 docs/USAGE.md 了解参数获取步骤")
    logger.info("- 运行 examples/debug_example.py 进行参数验证")
    logger.info("- 查看 examples/simple_example.py 了解基础用法")

    # 如果有真实参数，可以取消注释以下行来运行其他示例
    # await search_example()
    # await comments_example()
    # await user_example()


if __name__ == "__main__":
    asyncio.run(main())
