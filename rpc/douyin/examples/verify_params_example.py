# -*- coding: utf-8 -*-
"""
验证参数生成示例

演示如何使用新的验证参数生成功能
"""

import asyncio

from log import logger

from rpc.douyin import (
    TokenManager,
    VerifyFpManager,
    VerifyParamsGenerator,
    async_douyin_api,
)


async def example_basic_verify_params():
    """基础验证参数生成示例"""
    logger.info("=== 基础验证参数生成示例 ===")

    # 创建验证参数生成器
    generator = VerifyParamsGenerator()

    try:
        # 生成真实的验证参数
        logger.info("正在生成真实验证参数...")
        params = await generator.get_common_verify_params()

        logger.info("生成成功:")
        logger.info(f"  ms_token: {params.ms_token[:20]}...")
        logger.info(f"  webid: {params.webid}")
        logger.info(f"  verify_fp: {params.verify_fp[:30]}...")
        logger.info(f"  s_v_web_id: {params.s_v_web_id[:30]}...")
        logger.info(f"  uifid: {params.uifid[:20] if params.uifid else 'None'}...")

    except Exception as e:
        logger.error(f"生成真实参数失败: {e}")

        # 使用假参数作为备用
        logger.info("使用假参数作为备用...")
        fake_params = generator.get_fake_verify_params()

        logger.info("假参数生成成功:")
        logger.info(f"  ms_token: {fake_params.ms_token[:20]}...")
        logger.info(f"  webid: {fake_params.webid}")
        logger.info(f"  verify_fp: {fake_params.verify_fp[:30]}...")


async def example_individual_components():
    """单独组件使用示例"""
    logger.info("\n=== 单独组件使用示例 ===")

    # 1. TokenManager 示例
    logger.info("1. TokenManager 示例:")
    token_manager = TokenManager()

    try:
        ms_token = await token_manager.get_msToken()
        logger.info(f"   ms_token: {ms_token[:20]}...")
    except Exception as e:
        logger.error(f"   获取 ms_token 失败: {e}")

    try:
        webid = await token_manager.gen_webid()
        logger.info(f"   webid: {webid}")
    except Exception as e:
        logger.error(f"   获取 webid 失败: {e}")

    # 2. VerifyFpManager 示例
    logger.info("\n2. VerifyFpManager 示例:")
    verify_fp = VerifyFpManager.gen_verify_fp()
    s_v_web_id = VerifyFpManager.gen_s_v_web_id()
    uifid = VerifyFpManager.gen_uifid()

    logger.info(f"   verify_fp: {verify_fp[:30]}...")
    logger.info(f"   s_v_web_id: {s_v_web_id[:30]}...")
    logger.info(f"   uifid: {uifid[:20]}...")


async def example_client_integration():
    """客户端集成示例"""
    logger.info("\n=== 客户端集成示例 ===")

    # 使用预配置的客户端
    client = async_douyin_api.async_client
    logger.info("客户端初始化成功")

    # 获取验证参数
    try:
        verify_params = await client.get_verify_params()
        logger.info("客户端验证参数获取成功:")
        logger.info(f"  webid: {verify_params.webid}")
        logger.info(f"  ms_token: {verify_params.ms_token[:20]}...")

    except Exception as e:
        logger.error(f"获取验证参数失败: {e}")

        # 使用假参数
        fake_params = client.get_fake_verify_params()
        logger.info("使用假参数:")
        logger.info(f"  webid: {fake_params.webid}")
        logger.info(f"  ms_token: {fake_params.ms_token[:20]}...")


async def example_auto_api_call():
    """自动参数生成的 API 调用示例"""
    logger.info("\n=== 自动参数生成的 API 调用示例 ===")

    # 使用预配置的客户端

    # 模拟 Cookie（实际使用时需要真实的 Cookie）
    test_cookies = {
        "sessionid": "test_session_id",
        "tt_webid": "test_tt_webid",
        "passport_csrf_token": "test_csrf_token",
    }

    client = async_douyin_api.client
    try:
        # 使用自动参数生成的方法
        logger.info("正在调用自动参数生成的 API...")
        result = await client.query_user_self_info_auto(cookies=test_cookies)

        logger.info("API 调用成功!")
        logger.info(f"响应状态: {result.get('status_code', 'unknown')}")

    except Exception as e:
        logger.error(f"API 调用失败: {e}")
        logger.info("这是预期的，因为使用的是测试 Cookie")


async def example_parameter_caching():
    """参数缓存示例"""
    logger.info("\n=== 参数缓存示例 ===")

    # 使用预配置的客户端
    client = async_douyin_api.client
    logger.info("第一次获取验证参数...")
    try:
        params1 = await client.get_verify_params()
        logger.info(f"第一次获取: webid={params1.webid}")

        logger.info("第二次获取验证参数（应该使用缓存）...")
        params2 = await client.get_verify_params()
        logger.info(f"第二次获取: webid={params2.webid}")

        # 验证是否使用了缓存
        if params1.webid == params2.webid:
            logger.info("✓ 缓存工作正常")
        else:
            logger.info("✗ 缓存可能未工作")

        logger.info("强制刷新参数...")
        params3 = await client.get_verify_params(force_refresh=True)
        logger.info(f"强制刷新后: webid={params3.webid}")

    except Exception as e:
        logger.error(f"参数缓存测试失败: {e}")


async def example_error_handling():
    """错误处理示例"""
    logger.info("\n=== 错误处理示例 ===")

    # 模拟网络错误的情况
    from unittest.mock import patch

    generator = VerifyParamsGenerator()

    # 模拟网络错误
    with patch("httpx.AsyncClient") as mock_client:
        mock_client.side_effect = Exception("模拟网络错误")

        try:
            params = await generator.get_common_verify_params()
            logger.info("不应该到达这里")
        except Exception as e:
            logger.error(f"捕获到预期的错误: {e}")

            # 使用假参数作为降级方案
            fake_params = generator.get_fake_verify_params()
            logger.info(f"降级到假参数: webid={fake_params.webid}")


async def main():
    """主函数"""
    logger.info("抖音验证参数生成功能演示")
    logger.info("=" * 50)

    # 运行所有示例
    await example_basic_verify_params()
    await example_individual_components()
    await example_client_integration()
    await example_auto_api_call()
    await example_parameter_caching()
    await example_error_handling()

    logger.info("\n演示完成!")


if __name__ == "__main__":
    asyncio.run(main())
