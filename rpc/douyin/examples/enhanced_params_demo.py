# -*- coding: utf-8 -*-
"""
增强请求参数功能演示

演示重命名后的 _enhance_request_params 方法的功能，
该方法能够自动添加三类参数：
1. 通用参数 (Common Parameters)
2. 验证参数 (Verify Parameters)  
3. 算签参数 (Sign Parameters)
"""

import asyncio
from unittest.mock import patch

from log import logger

from rpc.douyin import DouyinConfig, async_douyin_api
from rpc.douyin.schemas.base import CommonVerifyParams


async def demo_enhanced_params_method():
    """演示增强请求参数方法的功能"""
    logger.info("=== _enhance_request_params 方法功能演示 ===")

    config = DouyinConfig(enable_logging=True, log_level="DEBUG", enable_sign=True)  # 使用 DEBUG 级别查看详细日志

    # 模拟验证参数
    mock_verify_params = CommonVerifyParams(
        ms_token="enhanced_ms_token_123456789",
        webid="enhanced_webid_987654321",
        verify_fp="verify_enhanced_fp_abcdef123",
        s_v_web_id="verify_enhanced_s_v_web_id_456789",
        uifid="enhanced_uifid_ghijkl012",
    )

    client = async_douyin_api.async_client
    async with client:
        logger.info("✓ 客户端初始化完成")

        with patch.object(client, "get_verify_params", return_value=mock_verify_params):
            if client._sign_client:
                with patch.object(client._sign_client, "sign", return_value="enhanced_signature_xyz123"):
                    logger.info("✓ Mock 设置完成")

                    # 直接测试 _enhance_request_params 方法
                    logger.info("\n--- 直接调用 _enhance_request_params 方法 ---")

                    original_params = {"user_id": "test_user_123", "action": "query_info"}

                    headers = {"user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36"}

                    cookies = {"sessionid": "test_session_123", "tt_webid": "test_tt_webid_456"}

                    logger.info(f"📥 原始参数: {original_params}")
                    logger.info(f"📥 原始参数数量: {len(original_params)}")

                    # 调用增强方法
                    enhanced_params = await client._enhance_request_params(
                        url="/aweme/v1/web/query/user/", params=original_params, headers=headers, cookies=cookies
                    )

                    logger.info(f"\n📤 增强后参数数量: {len(enhanced_params)}")
                    logger.info("\n📋 参数分类详情:")
                    logger.info("=" * 60)

                    # 分类显示参数
                    common_param_keys = [
                        "device_platform",
                        "aid",
                        "channel",
                        "publish_video_strategy_type",
                        "update_version_code",
                        "pc_client_type",
                        "version_code",
                        "version_name",
                        "cookie_enabled",
                        "screen_width",
                        "screen_height",
                        "browser_language",
                        "browser_platform",
                        "browser_name",
                        "browser_version",
                        "browser_online",
                        "engine_name",
                        "engine_version",
                        "os_name",
                        "os_version",
                        "cpu_core_num",
                        "device_memory",
                        "platform",
                        "downlink",
                        "effective_type",
                        "round_trip_time",
                    ]

                    verify_param_keys = ["webid", "msToken", "verifyFp", "fp", "uifid"]
                    sign_param_keys = ["a_bogus"]
                    original_param_keys = list(original_params.keys())

                    logger.info("🔧 通用参数 (Common Parameters):")
                    common_count = 0
                    for key in common_param_keys:
                        if key in enhanced_params:
                            logger.info(f"  ✓ {key}: {enhanced_params[key]}")
                            common_count += 1
                    logger.info(f"  📊 通用参数总数: {common_count}")

                    logger.info("\n🔐 验证参数 (Verify Parameters):")
                    verify_count = 0
                    for key in verify_param_keys:
                        if key in enhanced_params:
                            value = enhanced_params[key]
                            if len(str(value)) > 30:
                                value = str(value)[:30] + "..."
                            logger.info(f"  ✓ {key}: {value}")
                            verify_count += 1
                    logger.info(f"  📊 验证参数总数: {verify_count}")

                    logger.info("\n✍️ 算签参数 (Sign Parameters):")
                    sign_count = 0
                    for key in sign_param_keys:
                        if key in enhanced_params:
                            logger.info(f"  ✓ {key}: {enhanced_params[key]}")
                            sign_count += 1
                    logger.info(f"  📊 算签参数总数: {sign_count}")

                    logger.info("\n📝 原始参数 (Original Parameters):")
                    original_count = 0
                    for key in original_param_keys:
                        if key in enhanced_params:
                            logger.info(f"  ✓ {key}: {enhanced_params[key]}")
                            original_count += 1
                    logger.info(f"  📊 原始参数总数: {original_count}")

                    logger.info("\n📊 总体统计:")
                    logger.info(f"  总参数数量: {len(enhanced_params)}")
                    logger.info(f"  通用参数: {common_count}")
                    logger.info(f"  验证参数: {verify_count}")
                    logger.info(f"  算签参数: {sign_count}")
                    logger.info(f"  原始参数: {original_count}")
                    logger.info(
                        f"  其他参数: {len(enhanced_params) - common_count - verify_count - sign_count - original_count}"
                    )


async def demo_parameter_enhancement_flow():
    """演示参数增强流程"""
    logger.info("\n=== 参数增强流程演示 ===")

    config = DouyinConfig(enable_logging=True, log_level="INFO")

    client = async_douyin_api.async_client
    async with client:
        logger.info("\n--- 步骤1: 仅通用参数 ---")

        # 测试仅添加通用参数的情况
        base_params = {"test_param": "test_value"}
        common_params = client._build_request_params(base_params)

        logger.info(f"基础参数: {len(base_params)} 个")
        logger.info(f"添加通用参数后: {len(common_params)} 个")
        logger.info(f"新增参数数量: {len(common_params) - len(base_params)}")

        logger.info("\n--- 步骤2: 添加验证参数 ---")

        mock_verify_params = CommonVerifyParams(
            ms_token="flow_demo_ms_token",
            webid="flow_demo_webid",
            verify_fp="flow_demo_verify_fp",
            s_v_web_id="flow_demo_s_v_web_id",
            uifid="flow_demo_uifid",
        )

        with patch.object(client, "get_verify_params", return_value=mock_verify_params):
            # 模拟添加验证参数
            params_with_verify = common_params.copy()
            params_with_verify.update(
                {
                    "webid": mock_verify_params.webid,
                    "msToken": mock_verify_params.ms_token,
                    "verifyFp": mock_verify_params.verify_fp,
                    "fp": mock_verify_params.verify_fp,
                    "uifid": mock_verify_params.uifid,
                }
            )

            logger.info(f"添加验证参数后: {len(params_with_verify)} 个")
            logger.info(f"新增验证参数数量: {len(params_with_verify) - len(common_params)}")

            logger.info("\n--- 步骤3: 添加算签参数 ---")

            if client._sign_client:
                with patch.object(client._sign_client, "sign", return_value="flow_demo_signature"):
                    params_with_sign = params_with_verify.copy()
                    params_with_sign["a_bogus"] = "flow_demo_signature"

                    logger.info(f"添加算签参数后: {len(params_with_sign)} 个")
                    logger.info(f"新增算签参数数量: {len(params_with_sign) - len(params_with_verify)}")

                    logger.info("\n🎯 完整流程总结:")
                    logger.info(f"  原始参数: {len(base_params)} 个")
                    logger.info(f"  → 通用参数: +{len(common_params) - len(base_params)} 个")
                    logger.info(f"  → 验证参数: +{len(params_with_verify) - len(common_params)} 个")
                    logger.info(f"  → 算签参数: +{len(params_with_sign) - len(params_with_verify)} 个")
                    logger.info(f"  = 最终参数: {len(params_with_sign)} 个")


async def demo_method_naming_clarity():
    """演示方法命名的清晰性"""
    logger.info("\n=== 方法命名清晰性演示 ===")

    logger.info("📝 方法重命名说明:")
    logger.info("=" * 50)
    logger.info("旧方法名: _add_sign_to_params")
    logger.info("  ❌ 误导性: 暗示只添加签名参数")
    logger.info("  ❌ 不准确: 实际上添加三类参数")
    logger.info()
    logger.info("新方法名: _enhance_request_params")
    logger.info("  ✅ 准确性: 明确表示增强请求参数")
    logger.info("  ✅ 全面性: 涵盖所有参数类型")
    logger.info("  ✅ 清晰性: 方法功能一目了然")
    logger.info()
    logger.info("📋 方法功能说明:")
    logger.info("1. 🔧 添加通用参数 (Common Parameters)")
    logger.info("   - device_platform, aid, channel 等基础参数")
    logger.info("   - 通过 _build_request_params() 方法添加")
    logger.info()
    logger.info("2. 🔐 添加验证参数 (Verify Parameters)")
    logger.info("   - webid, msToken, verifyFp, fp, uifid 等身份验证参数")
    logger.info("   - 通过 get_verify_params() 方法获取并添加")
    logger.info()
    logger.info("3. ✍️ 添加算签参数 (Sign Parameters)")
    logger.info("   - a_bogus 等签名参数")
    logger.info("   - 通过 _sign_client.sign() 方法生成并添加")
    logger.info()
    logger.info("🎯 参数优先级:")
    logger.info("  用户提供的参数 > 自动生成的参数")
    logger.info("  确保用户的自定义参数不会被覆盖")


async def main():
    """主函数"""
    logger.info("🚀 增强请求参数功能演示")
    logger.info("=" * 60)
    logger.info("演示重命名后的 _enhance_request_params 方法")
    logger.info("该方法能够自动添加所有必需的请求参数")
    logger.info("=" * 60)

    try:
        await demo_enhanced_params_method()
        await demo_parameter_enhancement_flow()
        await demo_method_naming_clarity()

        logger.info("\n" + "=" * 60)
        logger.info("🎉 演示完成！")
        logger.info("\n📝 总结:")
        logger.info("✅ 方法重命名提高了代码可读性")
        logger.info("✅ 文档更新准确描述了方法功能")
        logger.info("✅ 日志信息更清晰地反映了处理过程")
        logger.info("✅ 方法功能保持完整，无任何功能损失")

    except Exception as e:
        logger.error(f"\n❌ 演示过程中出现错误: {e}")
        import traceback

        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
