"""
抖音 API 客户端使用示例

演示如何使用抖音 API 客户端获取收藏视频列表
"""

import asyncio

from log import logger
from rpc.douyin import CollectVideoListRequest, DouyinConfig, async_douyin_api


async def example_get_collect_videos():
    """获取收藏视频列表示例"""

    # 配置客户端
    config = DouyinConfig(
        timeout=30.0,
        max_retries=3,
        enable_logging=True,
        log_requests=True,
        log_responses=False,  # 响应数据可能很大，建议关闭
    )

    # 使用客户端
    client = async_douyin_api.async_client
    try:
        # 构建请求参数
        request = CollectVideoListRequest(
            collects_id="7495633625980131112",  # 收藏夹ID
            cursor=0,
            count=10,
            webid="7515479047155254819",
            uifid="7457f5ef2178e63069f24974fa04cbf9321b3fa7afdb44208071bc08e7f7084fdec4affe6c9400d15eaddd8ff3b64d14c735cab26aa9036f7251b11b3d69039604ab487e442c3433dc7853a3c5a888eaaf76bba74db9720ad0e9745d2e03363cccaf2007ff52a75138033259087bd0ccabcf8b40237b896087580933a1ecb389f2f834b2983cacb8f4f1423c7ee4b63d9a8ce2a94838412966a9e425756b09e6",
            msToken="tawjmnjZf9BfOvlMg0A6-qgcJlkpSIrHtEWyr2lc-KNrlXe4HhFs_XIAj94DbUmPTKPqgsMSTimJaOwj48DfMsWia9lUKovfTETCgeyf7Zxy-N-3wdoIyBDNxvgOUcjgyUGScBYMmqaKkKmZAa5IEs8lHuO9r4hs8gLGluJ8zMLjLKOPciw%3D",
            a_bogus="mysfhzSwYNmccdFSmKpDt3cUMS2%2FNTSy-lixWoaTHOYbGXUPoYPykPaCcxwodUxJu8p0hoAHfDalYnVbKzXTZKCpumkDSOXRQU599W8LgqqXPUssDqmxC0XzLwBKlQiLe%2FCjEA8RAssrIVdAVrV%2Fld2aS5ToQcDdWNMjDMLy7EWgfWSkhn3wO9Ddx6aC0-nU",
            verifyFp="verify_mckr40mp_XNxX8Elu_EIaw_4as5_8mTS_F47HgvTuh94m",
            fp="verify_mckr40mp_XNxX8Elu_EIaw_4as5_8mTS_F47HgvTuh94m",
        )

        # 设置 Cookie（支持字符串和字典两种格式）

        # 方式一：使用字符串格式（推荐，直接从浏览器复制）
        cookies = "UIFID_TEMP=7457f5ef2178e63069f24974fa04cbf9321b3fa7afdb44208071bc08e7f7084f44268a63da6486779858d819c2bf0a284b20f7c0a93bab48780171b672930a89df9ce37b46f62f404af466a5ab86f43f; fpk1=U2FsdGVkX1+QTIeEYthMRRM9wQIGvq+McrtWCsHqvETK4N57O8QilzFeZW6BfnPMDZbCGnjGkwgIRODv+0mvLQ==; fpk2=8381c048a9d70230af13a12a76663dc4; UIFID=7457f5ef2178e63069f24974fa04cbf9321b3fa7afdb44208071bc08e7f7084fdec4affe6c9400d15eaddd8ff3b64d14c735cab26aa9036f7251b11b3d69039604ab487e442c3433dc7853a3c5a888eaaf76bba74db9720ad0e9745d2e03363cccaf2007ff52a75138033259087bd0ccabcf8b40237b896087580933a1ecb389f2f834b2983cacb8f4f1423c7ee4b63d9a8ce2a94838412966a9e425756b09e6; bd_ticket_guard_client_web_domain=2; hevc_supported=true; is_staff_user=false; SelfTabRedDotControl=%5B%5D; __security_mc_1_s_sdk_cert_key=6342a58d-498e-8e7a; SearchMultiColumnLandingAbVer=2; SEARCH_RESULT_LIST_TYPE=%22multi%22; passport_csrf_token=28036da03200e6a091b8da4339613534; passport_csrf_token_default=28036da03200e6a091b8da4339613534; xgplayer_user_id=75253824406; d_ticket=2c5dd49063a1aabbe93df14cdae3e2f433f95; passport_mfa_token=CjURYNKpyd1q7uPQpQDQR%2BMe3kA9p2BOAMK%2Fdvg%2FbDgw5TtolpFLTXqQfXQhW9sObOwqaiAuMhpKCjzfJfXDnwxsFHCSuGR6NNpqSxNUgFTkoJb%2BQ7Nf8Xe8m7UG%2BX562HPkGRWxxDC4N3kHVNpAKrRXQ5LAeooQm8TyDRj2sdFsIAIiAQNvxs9D; __security_server_data_status=1; my_rd=2; login_time=1749050184259; passport_assist_user=CkFshGd02WMHnDIk6TOe7LfJpay9_OYNdJ5HBNpgIWxZ0YaRBvOvJjxgAY3ilVzGRvfWscMgxmFXVvRDWVkcI2mJmhpKCjwAAAAAAAAAAAAATxNsD4LlBv2bX2b6Cj16zpS_VMEONe5RExK-rzkStpdRwyoj3vYCxChNEub_PRR8tNIQm5rzDRiJr9ZUIAEiAQNvxs9D; n_mh=XN-wT6okQcwIAv5AmXX_N01iHgwgFD2o_XPxJxRQouM; sso_uid_tt=f382b873f43d6695959fe02ec7c4a5a4; sso_uid_tt_ss=f382b873f43d6695959fe02ec7c4a5a4; toutiao_sso_user=391dae2309a77075fb5cd94410027f2c; toutiao_sso_user_ss=391dae2309a77075fb5cd94410027f2c; sid_ucp_sso_v1=1.0.0-KGE1NjE1OGRkMmFhY2RhMWRjM2JiNWRjMWZjM2I5MjFhNzlhZjVmYmIKIQjYtOCTq_SNAxDSxoHCBhjvMSAMOAZA9AdIBhoCbGYiIDM5MWRhZTIzMDlhNzcwNzVmYjVjZDk0NDEwMDI3ZjJj; ssid_ucp_sso_v1=1.0.0-KGE1NjE1OGRkMmFhY2RhMWRjM2JiNWRjMWZjM2I5MjFhNzlhZjVmYmIKIQjYtOCTq_SNAxDSxoHCBhjvMSAMOAZA9AdIBhoCaGwiIGYyNTA1NTM0ZjM1NzM2ZjU2YjY2ZGVjMzlkODViOTUy; stream_player_status_params=%22%7B%5C%22is_auto_play%5C%22%3A0%2C%5C%22is_full_screen%5C%22%3A0%2C%5C%22is_full_webscreen%5C%22%3A0%2C%5C%22is_mute%5C%22%3A0%2C%5C%22is_speed%5C%22%3A1%2C%5C%22is_visible%5C%22%3A0%7D%22; __ac_signature=_02B4Z6wo00f01.70DBwAAIDC1.CSYeiRu.P-1AiAAJfiC3bW8KgKG6WIuDwiK-xLR.kIBJ1s6-oGOSCeyuxwsHSh4htPZA2WMdmiMaTUSrSjy1QigoIh05SFzTjNM3NJsVbK4DQbM4pebvGP03; download_guide=%223%2F20250702%2F0%22; __ac_nonce=0686557670078915fbf2c; IsDouyinActive=true; stream_recommend_feed_params=%22%7B%5C%22cookie_enabled%5C%22%3Atrue%2C%5C%22screen_width%5C%22%3A1512%2C%5C%22screen_height%5C%22%3A982%2C%5C%22browser_online%5C%22%3Atrue%2C%5C%22cpu_core_num%5C%22%3A12%2C%5C%22device_memory%5C%22%3A8%2C%5C%22downlink%5C%22%3A10%2C%5C%22effective_type%5C%22%3A%5C%224g%5C%22%2C%5C%22round_trip_time%5C%22%3A50%7D%22; home_can_add_dy_2_desktop=%221%22; bd_ticket_guard_client_data=eyJiZC10aWNrZXQtZ3VhcmQtdmVyc2lvbiI6MiwiYmQtdGlja2V0LWd1YXJkLWl0ZXJhdGlvbi12ZXJzaW9uIjoxLCJiZC10aWNrZXQtZ3VhcmQtcmVlLXB1YmxpYy1rZXkiOiJCRUFXZzYzRGo1b2pqQjV0Y08rdWlEeUdERTVZdTBQKzBBK3BncDVVR2sxQ24yUFNjd3pibHVtcXVuY2lFczlBeUp5NDNtQVBaWEl2NHdUQzBRWTZtdkE9IiwiYmQtdGlja2V0LWd1YXJkLXdlYi12ZXJzaW9uIjoyfQ%3D%3D; FOLLOW_LIVE_POINT_INFO=%22MS4wLjABAAAADY7u-UqfGogzrFtNghKD7Rgnz1QnWBJUrBA4AYrCX20zcMMCg1d8xoslUJiVTQyP%2F1751472000000%2F0%2F0%2F1751472590712%22; FOLLOW_NUMBER_YELLOW_POINT_INFO=%22MS4wLjABAAAADY7u-UqfGogzrFtNghKD7Rgnz1QnWBJUrBA4AYrCX20zcMMCg1d8xoslUJiVTQyP%2F1751472000000%2F0%2F0%2F1751473190712%22; passport_fe_beating_status=true; odin_tt=391b5f747276ceb93ba20dc086c5963085d102afcff15df887c6d74ced4a7ef822ee937fdfcd2c154ff86065d4054c7e"

        # 方式二：使用字典格式
        # cookies = {
        #     "sessionid": "your_session_id",
        #     "tt_webid": "your_tt_webid",
        #     "passport_csrf_token": "your_csrf_token",
        #     "uid_tt": "your_uid",
        #     "sid_guard": "your_sid_guard",
        # }

        # 获取收藏视频列表
        response = await client.get_collect_video_list(request, cookies)

        logger.info(f"状态码: {response.status_code}")
        logger.info(f"视频数量: {len(response.aweme_list)}")
        logger.info(f"是否有更多: {response.has_more}")
        logger.info(f"最大游标: {response.max_cursor}")

        # 打印视频信息
        for i, video in enumerate(response.aweme_list):
            logger.info(f"\n视频 {i + 1}:")
            logger.info(f"  ID: {video.aweme_id}")
            logger.info(f"  描述: {video.desc}")
            logger.info(f"  作者: {video.author.nickname}")
            logger.info(f"  点赞数: {video.statistics.digg_count}")
            logger.info(f"  评论数: {video.statistics.comment_count}")
            logger.info(f"  分享数: {video.statistics.share_count}")
            logger.info(f"  播放数: {video.statistics.play_count}")
            logger.info(f"  分享链接: {video.share_url}")

    except Exception as e:
        logger.error(f"获取收藏视频列表失败: {str(e)}")


async def example_get_collect_videos_with_pagination():
    """分页获取收藏视频列表示例"""

    config = DouyinConfig(
        timeout=30.0,
        max_retries=3,
        enable_logging=True,
    )

    client = async_douyin_api.async_client
    try:
        # 分页获取收藏视频列表
        responses = await client.get_collect_video_list_with_pagination(
            collects_id="7495633625980131112",
            count=10,
            max_pages=3,
        )

        total_videos = 0
        for page, response in enumerate(responses):
            logger.info(f"\n第 {page + 1} 页:")
            logger.info(f"  视频数量: {len(response.aweme_list)}")
            logger.info(f"  是否有更多: {response.has_more}")
            total_videos += len(response.aweme_list)

        logger.info(f"\n总共获取到 {total_videos} 个视频")

    except Exception as e:
        logger.error(f"分页获取收藏视频列表失败: {str(e)}")


if __name__ == "__main__":
    # 运行示例
    logger.info("=== 获取收藏视频列表示例 ===")
    asyncio.run(example_get_collect_videos())

    logger.info("\n=== 分页获取收藏视频列表示例 ===")
    asyncio.run(example_get_collect_videos_with_pagination())
