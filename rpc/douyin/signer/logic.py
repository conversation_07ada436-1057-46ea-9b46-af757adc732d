# -*- coding: utf-8 -*-
"""
抖音签名逻辑实现

迁移自 MediaCrawlerPro-SignSrv 项目的 logic/douyin/douyin_logic.py
提供抖音请求签名生成功能，支持 Playwright 和 JavaScript 两种方式
"""

import os
from abc import ABC, abstractmethod
from typing import Optional

import execjs
from tenacity import RetryError, retry, stop_after_attempt, wait_fixed

from log import logger

from ..schemas import DouyinSignRequest, DouyinSignResponse


class AbstractDouyinSign(ABC):
    """抖音签名抽象基类"""

    @abstractmethod
    async def sign(self, req_data: DouyinSignRequest, force_init: bool = False) -> DouyinSignResponse:
        """
        生成签名

        Args:
            req_data: 签名请求参数
            force_init: 是否强制重新初始化

        Returns:
            签名响应结果
        """
        raise NotImplementedError


class DouyinJavascriptSign(AbstractDouyinSign):
    """基于 JavaScript 的抖音签名实现"""

    def __init__(self, js_file_path: Optional[str] = None):
        """
        初始化 JavaScript 签名器

        Args:
            js_file_path: JavaScript 文件路径，如果为 None 则使用默认路径
        """
        if js_file_path is None:
            # 使用相对于当前文件的默认路径
            current_dir = os.path.dirname(os.path.abspath(__file__))
            js_file_path = os.path.join(current_dir, "js", "douyin.js")

        if not os.path.exists(js_file_path):
            raise FileNotFoundError(f"JavaScript 文件不存在: {js_file_path}")

        try:
            with open(js_file_path, encoding="utf-8") as f:
                js_content = f.read()
            self.douyin_sign_obj = execjs.compile(js_content)
        except Exception as e:
            logger.error(f"加载 JavaScript 文件失败: {e}")
            raise

    @retry(stop=stop_after_attempt(3), wait=wait_fixed(0.5))
    async def sign(self, req: DouyinSignRequest, force_init: bool = False) -> DouyinSignResponse:
        """
        抖音请求签名 JavaScript 版本，如果发生异常默认重试3次，每次间隔500ms

        Args:
            req: 签名请求参数
            force_init: 是否强制重新初始化（此实现中忽略）

        Returns:
            签名响应结果
        """
        try:
            # 根据 URI 选择不同的签名函数
            sign_js_name = "sign_reply" if "/reply" in req.uri else "sign_datail"

            # 调用 JavaScript 函数生成签名
            a_bogus = self.douyin_sign_obj.call(sign_js_name, req.query_params, req.user_agent)

            # 确保 a_bogus 是字符串类型，避免字节类型导致的连接错误
            if isinstance(a_bogus, bytes):
                a_bogus = a_bogus.decode("utf-8")
            elif a_bogus is not None:
                a_bogus = str(a_bogus)
            else:
                a_bogus = ""

            return DouyinSignResponse(a_bogus=a_bogus)

        except Exception as e:
            logger.error(f"JavaScript 签名生成失败: {e}")
            raise


class DouyinSignFactory:
    """抖音签名工厂类"""

    # 签名类型常量
    JAVASCRIPT_SIGN = "javascript"
    # PLAYWRIGHT_SIGN 已被移除

    @staticmethod
    def get_sign(sign_type: str, **kwargs) -> AbstractDouyinSign:
        """
        获取签名实现实例

        Args:
            sign_type: 签名类型（目前仅支持 "javascript"）
            **kwargs: 额外参数，传递给具体实现的构造函数

        Returns:
            签名实现实例

        Raises:
            NotImplementedError: 不支持的签名类型
        """
        if sign_type == DouyinSignFactory.JAVASCRIPT_SIGN:
            return DouyinJavascriptSign(**kwargs)
        elif sign_type == "playwright":
            # 为了向后兼容，记录警告并返回 JavaScript 实现
            logger.warning("Playwright 签名已被移除，自动使用 JavaScript 签名")
            return DouyinJavascriptSign(**kwargs)
        else:
            raise NotImplementedError(f"不支持的签名类型: {sign_type}。目前仅支持 'javascript'。")


class DouyinSignLogic:
    """抖音签名业务逻辑类"""

    def __init__(self, sign_type: str = DouyinSignFactory.JAVASCRIPT_SIGN, **kwargs):
        """
        初始化签名逻辑

        Args:
            sign_type: 签名类型，默认使用 JavaScript 签名（推荐）
            **kwargs: 额外参数，传递给签名实现
        """
        # 如果使用了已移除的 playwright 类型，记录警告并使用 javascript
        if sign_type == "playwright":
            logger.warning("Playwright 签名已被移除，自动切换到 JavaScript 签名")
            self.sign_type = DouyinSignFactory.JAVASCRIPT_SIGN
        else:
            self.sign_type = sign_type

        self.sign_server = DouyinSignFactory.get_sign(self.sign_type, **kwargs)

    async def sign(self, req_data: DouyinSignRequest) -> DouyinSignResponse:
        """
        生成签名

        Args:
            req_data: 签名请求参数

        Returns:
            签名响应结果
        """
        try:
            return await self.sign_server.sign(req_data)
        except RetryError as e:
            logger.warning(f"签名生成重试失败，尝试强制重新初始化: {e}")
            return await self.sign_server.sign(req_data, force_init=True)
