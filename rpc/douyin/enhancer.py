"""
抖音请求增强器

基于 rpc.common.RequestEnhancer 实现的抖音专用请求增强器
负责为抖音 API 请求添加签名、验证参数等
"""

import asyncio
import time
from typing import Any, Dict, Optional
from urllib.parse import urlencode

import httpx
from loguru import logger

from rpc.common.providers import RequestEnhancer

from .config import DouyinConfig
from .schemas.base import CommonVerifyParams
from .utils import DouyinSignClient
from .verify_params import VerifyParamsGenerator


class CommonParams:
    """通用参数类"""

    def __init__(self):
        self.device_platform = "webapp"
        self.aid = "6383"
        self.channel = "channel_pc_web"
        self.pc_client_type = "1"
        self.version_code = "170400"
        self.version_name = "17.4.0"
        self.cookie_enabled = "true"
        self.screen_width = "1920"
        self.screen_height = "1080"
        self.browser_language = "zh-CN"
        self.browser_platform = "MacIntel"
        self.browser_name = "Mozilla"
        self.browser_version = "5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        self.browser_online = "true"
        self.engine_name = "Blink"
        self.engine_version = "120.0.0.0"
        self.os_name = "Mac OS"
        self.os_version = "10.15.7"
        self.cpu_core_num = "8"
        self.device_memory = "8"
        self.platform = "PC"
        self.downlink = "10"
        self.effective_type = "4g"
        self.round_trip_time = "50"

    def to_dict(self) -> Dict[str, str]:
        """转换为字典格式"""
        return {
            "device_platform": self.device_platform,
            "aid": self.aid,
            "channel": self.channel,
            "pc_client_type": self.pc_client_type,
            "version_code": self.version_code,
            "version_name": self.version_name,
            "cookie_enabled": self.cookie_enabled,
            "screen_width": self.screen_width,
            "screen_height": self.screen_height,
            "browser_language": self.browser_language,
            "browser_platform": self.browser_platform,
            "browser_name": self.browser_name,
            "browser_version": self.browser_version,
            "browser_online": self.browser_online,
            "engine_name": self.engine_name,
            "engine_version": self.engine_version,
            "os_name": self.os_name,
            "os_version": self.os_version,
            "cpu_core_num": self.cpu_core_num,
            "device_memory": self.device_memory,
            "platform": self.platform,
            "downlink": self.downlink,
            "effective_type": self.effective_type,
            "round_trip_time": self.round_trip_time,
        }


class DouyinRequestEnhancer(RequestEnhancer):
    """
    抖音请求增强器

    为抖音平台的请求添加特定的参数，如 a_bogus、验证参数等
    """

    def __init__(self, config: Optional[DouyinConfig] = None):
        """
        初始化抖音请求增强器

        Args:
            config: 抖音配置对象
        """
        self.config = config or DouyinConfig()

        # 初始化签名客户端
        self._sign_client: Optional[DouyinSignClient] = None
        if self.config.enable_sign:
            self._init_sign_client()

        # 初始化通用参数
        self._common_params = CommonParams()

        # 初始化验证参数生成器
        self._verify_params_generator = VerifyParamsGenerator(
            user_agent=self.config.default_headers.get("user-agent", "")
        )
        self._cached_verify_params: Optional[CommonVerifyParams] = None
        self._verify_params_cache_time: Optional[float] = None

        logger.info("DouyinRequestEnhancer 初始化完成")

    def _init_sign_client(self):
        """初始化签名客户端"""
        try:
            self._sign_client = DouyinSignClient(
                use_javascript=self.config.use_javascript_sign,
                js_file_path=self.config.sign_js_file_path,
                enable_cache=self.config.sign_cache_enabled,
                cache_ttl=self.config.sign_cache_ttl,
                max_retries=self.config.sign_max_retries,
                retry_delay=self.config.sign_retry_delay,
            )
            logger.info("签名客户端初始化成功")
        except Exception as e:
            logger.error(f"签名客户端初始化失败: {e}")
            self._sign_client = None

    def enhance_request(self, request: httpx.Request) -> None:
        """
        增强请求，添加抖音特定的参数

        Args:
            request: httpx.Request 对象，会被直接修改
        """
        # 使用同步版本的内部方法
        self._enhance_request_sync(request)

    def _enhance_request_sync(self, request: httpx.Request) -> None:
        """
        同步版本的请求增强方法

        Args:
            request: httpx.Request 对象，会被直接修改
        """
        try:
            # 1. 确保验证参数已生成（同步版本）
            self._ensure_verify_params_sync()

            # 2. 获取当前 URL 参数
            params = dict(request.url.params)

            # 3. 添加通用参数
            common_params = self._common_params.to_dict()
            for key, value in common_params.items():
                if key not in params:  # 不覆盖用户提供的参数
                    params[key] = value

            # 4. 添加验证参数
            if self._cached_verify_params:
                verify_params = {
                    "webid": self._cached_verify_params.webid,
                    "msToken": self._cached_verify_params.ms_token,
                    "verifyFp": self._cached_verify_params.verify_fp,
                    "s_v_web_id": self._cached_verify_params.s_v_web_id,
                }

                if self._cached_verify_params.uifid:
                    verify_params["uifid"] = self._cached_verify_params.uifid

                for key, value in verify_params.items():
                    if key not in params:  # 不覆盖用户提供的参数
                        params[key] = value

            # 5. 生成签名（同步版本）
            self._add_signature_sync(request, params)

            # 6. 更新请求的 URL
            request.url = request.url.copy_with(params=params)

            # 7. 添加抖音特有的请求头
            self._add_headers(request)

            logger.debug(f"抖音请求增强完成: {request.url}")

        except Exception as e:
            logger.error(f"抖音请求增强失败: {e}")
            # 不抛出异常，让请求继续进行

    def _ensure_verify_params_sync(self):
        """确保验证参数已生成（同步版本）"""
        current_time = time.time()

        # 检查缓存是否过期（5分钟过期）
        if (
            self._cached_verify_params is None
            or self._verify_params_cache_time is None
            or current_time - self._verify_params_cache_time > 300
        ):

            try:
                # 在同步环境中运行异步方法
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    # 如果事件循环正在运行，使用假参数
                    logger.warning("在运行中的事件循环中生成验证参数，使用假参数")
                    self._cached_verify_params = self._get_fake_verify_params()
                else:
                    # 事件循环存在但未运行
                    self._cached_verify_params = loop.run_until_complete(
                        self._verify_params_generator.get_common_verify_params()
                    )
            except RuntimeError:
                # 没有事件循环，创建新的
                try:
                    self._cached_verify_params = asyncio.run(self._verify_params_generator.get_common_verify_params())
                except Exception as e:
                    logger.warning(f"生成验证参数失败，使用假参数: {e}")
                    self._cached_verify_params = self._get_fake_verify_params()
            except Exception as e:
                logger.warning(f"生成验证参数失败，使用假参数: {e}")
                self._cached_verify_params = self._get_fake_verify_params()

            self._verify_params_cache_time = current_time
            logger.debug("验证参数已更新")

    def _get_fake_verify_params(self) -> CommonVerifyParams:
        """获取假的验证参数"""
        import random
        import string

        def random_string(length: int) -> str:
            return "".join(random.choices(string.ascii_letters + string.digits, k=length))

        return CommonVerifyParams(
            ms_token=random_string(128),
            webid=str(random.randint(1000000000000000000, 9999999999999999999)),
            verify_fp=f"verify_{random_string(8)}_{random_string(32)}",
            s_v_web_id=f"verify_{random_string(8)}_{random_string(32)}",
            uifid=random_string(32),
        )

    def _add_signature_sync(self, request: httpx.Request, params: Dict[str, Any]):
        """添加签名参数（同步版本）"""
        if not self._sign_client:
            return

        try:
            # 获取请求路径
            uri = request.url.path
            if request.url.query:
                uri += "?" + request.url.query

            # 获取 User-Agent
            user_agent = request.headers.get("user-agent", request.headers.get("User-Agent", ""))
            if not user_agent:
                user_agent = self.config.default_headers.get("user-agent", "")

            # 获取 cookies
            cookies = ""
            if "Cookie" in request.headers:
                cookies = request.headers["Cookie"]

            # 构造查询参数字符串
            query_params = urlencode(params)

            # 在同步环境中运行异步签名方法
            try:
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    # 如果事件循环正在运行，跳过签名
                    logger.warning("在运行中的事件循环中跳过签名生成")
                    return
                else:
                    # 事件循环存在但未运行
                    a_bogus = loop.run_until_complete(self._sign_client.sign(uri, query_params, user_agent, cookies))
            except RuntimeError:
                # 没有事件循环，创建新的
                try:
                    a_bogus = asyncio.run(self._sign_client.sign(uri, query_params, user_agent, cookies))
                except Exception as e:
                    logger.warning(f"签名生成失败: {e}")
                    return

            # 添加签名参数
            params["a_bogus"] = a_bogus
            logger.debug("签名参数添加成功")

        except Exception as e:
            logger.warning(f"添加签名参数失败: {e}")

    def _add_headers(self, request: httpx.Request):
        """添加抖音特有的请求头"""
        # 添加默认请求头
        default_headers = {
            "Accept": "application/json, text/plain, */*",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
            "Accept-Encoding": "gzip, deflate, br",
            "Connection": "keep-alive",
            "Referer": "https://www.douyin.com/",
            "Origin": "https://www.douyin.com",
            "Sec-Fetch-Dest": "empty",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Site": "same-origin",
        }

        # 只添加不存在的请求头
        for key, value in default_headers.items():
            if key not in request.headers:
                request.headers[key] = value

    # ==================== 异步版本方法 ====================

    async def enhance_request_async(self, request: httpx.Request) -> None:
        """
        异步增强请求，添加抖音特定的参数

        Args:
            request: httpx.Request 对象，会被直接修改
        """
        try:
            # 1. 确保验证参数已生成
            await self._ensure_verify_params()

            # 2. 获取当前 URL 参数
            params = dict(request.url.params)

            # 3. 添加通用参数
            common_params = self._common_params.to_dict()
            for key, value in common_params.items():
                if key not in params:  # 不覆盖用户提供的参数
                    params[key] = value

            # 4. 添加验证参数
            if self._cached_verify_params:
                verify_params = {
                    "webid": self._cached_verify_params.webid,
                    "msToken": self._cached_verify_params.ms_token,
                    "verifyFp": self._cached_verify_params.verify_fp,
                    "s_v_web_id": self._cached_verify_params.s_v_web_id,
                }

                if self._cached_verify_params.uifid:
                    verify_params["uifid"] = self._cached_verify_params.uifid

                for key, value in verify_params.items():
                    if key not in params:  # 不覆盖用户提供的参数
                        params[key] = value

            # 5. 生成签名
            await self._add_signature(request, params)

            # 6. 更新请求的 URL
            request.url = request.url.copy_with(params=params)

            # 7. 添加抖音特有的请求头
            self._add_headers(request)

            logger.debug(f"抖音请求增强完成: {request.url}")

        except Exception as e:
            logger.error(f"抖音请求增强失败: {e}")
            # 不抛出异常，让请求继续进行

    async def _ensure_verify_params(self):
        """确保验证参数已生成"""
        current_time = time.time()

        # 检查缓存是否过期（5分钟过期）
        if (
            self._cached_verify_params is None
            or self._verify_params_cache_time is None
            or current_time - self._verify_params_cache_time > 300
        ):

            try:
                self._cached_verify_params = await self._verify_params_generator.get_common_verify_params()
                self._verify_params_cache_time = current_time
                logger.debug("验证参数已更新")
            except Exception as e:
                logger.warning(f"生成验证参数失败，使用假参数: {e}")
                self._cached_verify_params = self._get_fake_verify_params()
                self._verify_params_cache_time = current_time

    async def _add_signature(self, request: httpx.Request, params: Dict[str, Any]):
        """添加签名参数"""
        if not self._sign_client:
            return

        try:
            # 获取请求路径
            uri = request.url.path
            if request.url.query:
                uri += "?" + request.url.query

            # 获取 User-Agent
            user_agent = request.headers.get("user-agent", request.headers.get("User-Agent", ""))
            if not user_agent:
                user_agent = self.config.default_headers.get("user-agent", "")

            # 获取 cookies
            cookies = ""
            if "Cookie" in request.headers:
                cookies = request.headers["Cookie"]

            # 确保所有参数值都是字符串类型，避免字节类型导致的拼接错误
            str_params = {}
            for key, value in params.items():
                if isinstance(value, bytes):
                    str_params[key] = value.decode("utf-8")
                elif value is not None:
                    str_params[key] = str(value)
                else:
                    str_params[key] = ""

            # 构造查询参数字符串
            query_params = urlencode(str_params)

            # 生成签名
            a_bogus = await self._sign_client.sign(uri, query_params, user_agent, cookies)

            # 确保签名也是字符串类型
            if isinstance(a_bogus, bytes):
                a_bogus = a_bogus.decode("utf-8")
            elif a_bogus is not None:
                a_bogus = str(a_bogus)
            else:
                a_bogus = ""

            # 添加签名参数
            params["a_bogus"] = a_bogus
            logger.debug("签名参数添加成功")

        except Exception as e:
            logger.warning(f"添加签名参数失败: {e}")

    # ==================== 工具方法 ====================

    def get_stats(self) -> Dict[str, Any]:
        """获取增强器统计信息"""
        stats = {
            "config": {
                "enable_sign": self.config.enable_sign,
                "use_javascript_sign": self.config.use_javascript_sign,
                "sign_cache_enabled": self.config.sign_cache_enabled,
            },
            "verify_params": {
                "cached": self._cached_verify_params is not None,
                "cache_time": self._verify_params_cache_time,
            },
            "sign_client": {
                "initialized": self._sign_client is not None,
            },
        }

        # 添加签名客户端统计
        if self._sign_client and hasattr(self._sign_client, "get_stats"):
            stats["sign_client"]["stats"] = self._sign_client.get_stats()

        return stats

    def clear_cache(self):
        """清空缓存"""
        self._cached_verify_params = None
        self._verify_params_cache_time = None

        if self._sign_client and hasattr(self._sign_client, "clear_cache"):
            self._sign_client.clear_cache()

        logger.info("增强器缓存已清空")

    async def close(self):
        """关闭增强器，清理资源"""
        try:
            if self._sign_client and hasattr(self._sign_client, "close"):
                await self._sign_client.close()

            self.clear_cache()
            logger.info("DouyinRequestEnhancer 资源已清理")
        except Exception as e:
            logger.error(f"清理 DouyinRequestEnhancer 资源失败: {e}")
