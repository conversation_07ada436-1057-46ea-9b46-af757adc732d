# RPC Common 模块更新日志

## v2.0.0 - 平台客户端 + 请求增强器模式 (2025-01-11)

### 🎉 重大更新

基于最新的文档要求，完全重构了 `rpc/common` 模块，实现了**平台客户端 + 请求增强器模式**。

### ✨ 新增功能

#### 1. RequestEnhancer 接口
- 新增 `RequestEnhancer` 抽象基类，用于业务特定的请求增强
- 新增 `DefaultRequestEnhancer` 默认实现
- 支持自定义业务逻辑，如添加签名、token 等

#### 2. 三步增强流程
重新设计了请求处理流程，按照以下顺序增强请求：
1. **第一步 (账户)**: 注入平台账户信息（如 Cookies）
2. **第二步 (业务参数)**: 注入业务方自定义的请求参数（如 msToken）
3. **第三步 (代理)**: 注入代理 IP

#### 3. 平台客户端模式
- `DynamicProxyTransport` 现在在初始化时就确定平台归属
- 每个平台可以创建自己的专用客户端实例
- 不再需要在请求的 `extensions` 中传递平台信息

### 🔧 API 变更

#### DynamicProxyTransport 构造函数
```python
# 旧版本
transport = DynamicProxyTransport(
    account_provider=account_provider,
    proxy_provider=proxy_provider
)

# 新版本
transport = DynamicProxyTransport(
    platform=Platform.DOUYIN,  # 🆕 必需参数
    account_provider=account_provider,
    proxy_provider=proxy_provider,
    request_enhancer=request_enhancer  # 🆕 必需参数
)
```

#### 请求发送方式
```python
# 旧版本
response = client.get(
    "https://example.com",
    extensions={"platform": "douyin"}  # 需要指定平台
)

# 新版本
response = client.get("https://example.com")  # 平台已在初始化时确定
```

### 📦 新增组件

#### DouyinRequestEnhancer 示例
```python
class DouyinRequestEnhancer(RequestEnhancer):
    def enhance_request(self, request: httpx.Request) -> None:
        # 添加抖音特有的参数
        params = dict(request.url.params)
        params["msToken"] = f"douyin_ms_token_{int(time.time())}"
        params["X-Bogus"] = "dfpbgj..."
        request.url = request.url.copy_with(params=params)
        
        # 添加抖音特有的请求头
        request.headers["X-Platform"] = "douyin"
```

#### XiaohongshuRequestEnhancer 示例
```python
class XiaohongshuRequestEnhancer(RequestEnhancer):
    def enhance_request(self, request: httpx.Request) -> None:
        # 添加小红书特有的参数和签名
        params = dict(request.url.params)
        timestamp = str(int(time.time()))
        params["t"] = timestamp
        params["sign"] = hashlib.md5(f"timestamp={timestamp}".encode()).hexdigest()[:16]
        request.url = request.url.copy_with(params=params)
```

### 🏗️ 架构优势

1. **业务方自治**: 每个平台团队管理自己的客户端和增强逻辑
2. **职责分离**: 账户、代理、业务参数各司其职
3. **高度灵活**: RequestEnhancer 可以自定义任何业务逻辑
4. **易于测试**: 每个组件都可以独立测试
5. **完美复用**: 基础设施组件可以被所有业务方共享

### 📝 文件变更

- `providers.py`: 新增 `RequestEnhancer` 和 `DefaultRequestEnhancer`
- `transport.py`: 重构 `DynamicProxyTransport`，实现三步增强流程
- `__init__.py`: 更新导出列表，包含新的接口
- `example.py`: 新增完整的使用示例和业务方实现示例
- `README.md`: 更新文档，反映新的架构和使用方式
- `docs/dynamic_transport_usage.md`: 详细的使用文档和 UML 图

### 🧪 测试验证

所有示例代码都已通过测试，包括：
- 基本使用示例
- 高级使用示例（业务方自治模式）
- 业务团队使用示例（抖音团队）

### 🔄 迁移指南

如果您正在使用旧版本的 `DynamicProxyTransport`，请按照以下步骤迁移：

1. 为您的平台创建 `RequestEnhancer` 实现
2. 更新 `DynamicProxyTransport` 的构造函数调用
3. 移除请求中的 `extensions={"platform": "..."}` 参数
4. 测试新的三步增强流程

### 📚 相关文档

- [详细使用文档](docs/dynamic_transport_usage.md)
- [完整示例代码](example.py)
- [README 文档](README.md)
