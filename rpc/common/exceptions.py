"""
统一异常定义模块

定义所有 RPC 客户端的基础异常类
"""

from typing import Any, Dict, Optional


class BaseRPCError(Exception):
    """RPC 客户端基础异常类"""

    def __init__(
        self, message: str, status_code: Optional[int] = None, response_data: Optional[Dict[str, Any]] = None, **kwargs
    ):
        super().__init__(message)
        self.message = message
        self.status_code = status_code
        self.response_data = response_data
        self.extra_data = kwargs


class AuthenticationError(BaseRPCError):
    """认证失败异常"""

    pass


class RateLimitError(BaseRPCError):
    """请求频率限制异常"""

    pass


class RequestError(BaseRPCError):
    """请求错误异常"""

    pass


class ResponseError(BaseRPCError):
    """响应错误异常"""

    pass


class ValidationError(BaseRPCError):
    """数据验证错误异常"""

    pass


class TimeoutError(BaseRPCError):
    """请求超时异常"""

    pass


class ProxyError(BaseRPCError):
    """代理错误异常"""

    pass


class CookieError(BaseRPCError):
    """Cookie 错误异常"""

    pass
