"""
代理提供者测试

测试 DefaultProxyProvider 的功能
"""

import os
from unittest.mock import AsyncMock, MagicMock, patch

import pytest

from rpc.common.providers import ProxyProvider
from rpc.providers import DefaultProxyProvider


@pytest.mark.unit
class TestDefaultProxyProvider:
    """DefaultProxyProvider 单元测试类"""

    def test_inheritance(self):
        """测试继承关系"""
        provider = DefaultProxyProvider()

        # 验证继承关系
        assert isinstance(provider, ProxyProvider)

    @pytest.mark.asyncio
    async def test_initialization_without_api_key(self):
        """测试没有 API Key 时的初始化"""
        provider = DefaultProxyProvider()

        # 确保环境变量为空
        with patch.dict(os.environ, {}, clear=True):
            await provider._initialize_proxy_pool()

            # 验证代理池未初始化
            assert provider._proxy_pool is None
            assert provider._initialized is True

    @pytest.mark.asyncio
    async def test_initialization_with_api_key(self):
        """测试有 API Key 时的初始化"""
        provider = DefaultProxyProvider()

        # 模拟环境变量
        with patch.dict(
            os.environ, {"KUAIDAILI_DPS_SECRET_ID": "test_secret_id", "KUAIDAILI_DPS_SIGNATURE": "test_signature"}
        ):
            # 模拟 pkg.proxy 模块
            mock_pool = AsyncMock()
            mock_pool.load_proxies = AsyncMock()
            mock_pool.get_proxy_count.return_value = 10

            with patch("pkg.proxy.create_ip_pool", return_value=mock_pool):
                await provider._initialize_proxy_pool()

                # 验证代理池已初始化
                assert provider._proxy_pool is not None
                assert provider._initialized is True

                # 验证调用了加载代理
                mock_pool.load_proxies.assert_called_once_with(count=20)

    @pytest.mark.asyncio
    async def test_initialization_import_error(self):
        """测试 pkg.proxy 模块导入失败的情况"""
        provider = DefaultProxyProvider()

        with patch.dict(os.environ, {"KUAIDAILI_DPS_SECRET_ID": "test_key"}):
            with patch("pkg.proxy.create_ip_pool", side_effect=ImportError("Module not found")):
                await provider._initialize_proxy_pool()

                # 验证代理池未初始化
                assert provider._proxy_pool is None
                assert provider._initialized is True

    def test_get_proxy_sync_without_pool(self):
        """测试同步获取代理（没有代理池）"""
        provider = DefaultProxyProvider()

        # 没有初始化代理池
        result = provider.get_proxy()

        # 应该返回 None
        assert result is None

    @pytest.mark.asyncio
    async def test_get_proxy_async_without_pool(self):
        """测试异步获取代理（没有代理池）"""
        provider = DefaultProxyProvider()

        # 确保没有 API Key
        with patch.dict(os.environ, {}, clear=True):
            result = await provider.get_proxy_async()

            # 应该返回 None
            assert result is None

    @pytest.mark.asyncio
    async def test_get_proxy_async_with_pool(self):
        """测试异步获取代理（有代理池）"""
        provider = DefaultProxyProvider()

        # 模拟代理对象
        mock_proxy = MagicMock()
        mock_proxy.to_dict.return_value = {
            "http://": "http://proxy.example.com:8080",
            "https://": "http://proxy.example.com:8080",
        }

        # 模拟代理池
        mock_pool = AsyncMock()
        mock_pool.get_proxy.return_value = mock_proxy

        provider._proxy_pool = mock_pool
        provider._initialized = True

        result = await provider.get_proxy_async()

        # 验证返回了代理信息
        assert result is not None
        assert "http://" in result
        assert "https://" in result

        # 验证调用了代理池的方法
        mock_pool.get_proxy.assert_called_once()
        mock_proxy.to_dict.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_proxy_async_no_available_proxy(self):
        """测试异步获取代理（代理池返回 None）"""
        provider = DefaultProxyProvider()

        # 模拟代理池返回 None
        mock_pool = AsyncMock()
        mock_pool.get_proxy.return_value = None

        provider._proxy_pool = mock_pool
        provider._initialized = True

        result = await provider.get_proxy_async()

        # 应该返回 None
        assert result is None

    @pytest.mark.asyncio
    async def test_get_proxy_async_exception(self):
        """测试异步获取代理时发生异常"""
        provider = DefaultProxyProvider()

        # 模拟代理池抛出异常
        mock_pool = AsyncMock()
        mock_pool.get_proxy.side_effect = Exception("Network error")

        provider._proxy_pool = mock_pool
        provider._initialized = True

        result = await provider.get_proxy_async()

        # 应该返回 None
        assert result is None

    @pytest.mark.asyncio
    async def test_close_without_pool(self):
        """测试关闭资源（没有代理池）"""
        provider = DefaultProxyProvider()

        # 应该不会抛出异常
        await provider.close()

    @pytest.mark.asyncio
    async def test_close_with_pool(self):
        """测试关闭资源（有代理池）"""
        provider = DefaultProxyProvider()

        # 模拟代理池
        mock_ip_provider = AsyncMock()
        mock_pool = MagicMock()
        mock_pool.ip_provider = mock_ip_provider

        provider._proxy_pool = mock_pool

        await provider.close()

        # 验证调用了关闭方法
        mock_ip_provider.close.assert_called_once()

    @pytest.mark.asyncio
    async def test_close_exception(self):
        """测试关闭资源时发生异常"""
        provider = DefaultProxyProvider()

        # 模拟代理池关闭时抛出异常
        mock_ip_provider = AsyncMock()
        mock_ip_provider.close.side_effect = Exception("Close error")
        mock_pool = MagicMock()
        mock_pool.ip_provider = mock_ip_provider

        provider._proxy_pool = mock_pool

        # 应该不会抛出异常
        await provider.close()


@pytest.mark.integration
class TestDefaultProxyProviderIntegration:
    """DefaultProxyProvider 集成测试类"""

    @pytest.mark.asyncio
    async def test_full_workflow_without_real_api(self):
        """测试完整工作流程（不使用真实 API）"""
        provider = DefaultProxyProvider()

        try:
            # 模拟环境变量
            with patch.dict(
                os.environ, {"KUAIDAILI_DPS_SECRET_ID": "test_key", "KUAIDAILI_DPS_SIGNATURE": "test_secret"}
            ):
                # 模拟整个代理获取流程
                mock_proxy = MagicMock()
                mock_proxy.to_dict.return_value = {
                    "http://": "http://127.0.0.1:8080",
                    "https://": "http://127.0.0.1:8080",
                }

                mock_pool = AsyncMock()
                mock_pool.load_proxies = AsyncMock()
                mock_pool.get_proxy_count.return_value = 5
                mock_pool.get_proxy.return_value = mock_proxy

                with patch("pkg.proxy.create_ip_pool", return_value=mock_pool):
                    # 1. 初始化
                    await provider._initialize_proxy_pool()
                    assert provider._initialized is True

                    # 2. 获取代理
                    proxy_info = await provider.get_proxy_async()
                    assert proxy_info is not None
                    assert "http://" in proxy_info

                    # 3. 再次获取代理
                    proxy_info2 = await provider.get_proxy_async()
                    assert proxy_info2 is not None

        finally:
            # 4. 清理资源
            await provider.close()


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
