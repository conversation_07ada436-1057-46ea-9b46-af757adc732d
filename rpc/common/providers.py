"""
This module defines common provider implementations for the dynamic transport layer.
"""

import logging
from abc import ABC, abstractmethod
from typing import TYPE_CHECKING, Dict, Optional, TypedDict

import httpx

from pkg.proxy.models import IpInfoModel

if TYPE_CHECKING:
    from models.system.crawler import CrawlerCookiesAccount

logger = logging.getLogger(__name__)


class AccountInfo(TypedDict, total=False):
    """账户信息类型定义"""

    cookies: Dict[str, str]
    headers: Dict[str, str]
    user_agent: Optional[str]
    account_model: Optional["CrawlerCookiesAccount"]


class AccountProvider(ABC):
    """
    账户信息提供者的抽象基类

    负责为不同平台提供账户信息，如 Cookies、Headers 等。
    简化后的版本不再需要账号释放功能。
    """

    @abstractmethod
    def get_account(self, platform: str) -> AccountInfo:
        """
        获取指定平台的账户信息

        Args:
            platform: 平台名称，如 'douyin', 'xiaohongshu' 等

        Returns:
            AccountInfo 类型的字典，包含：
            - cookies: Dict[str, str] - Cookie 信息
            - headers: Dict[str, str] - 请求头信息
            - user_agent: Optional[str] - 用户代理字符串
            - account_model: Optional[CrawlerCookiesAccount] - 原始账户模型（可选）

        Raises:
            NotImplementedError: 子类必须实现此方法
            ValueError: 当平台参数无效时
        """
        pass

    async def get_account_async(self, platform: str) -> AccountInfo:
        """
        异步获取指定平台的账户信息
        """
        return self.get_account(platform)


class ProxyProvider(ABC):
    """
    代理信息提供者的抽象基类

    负责提供代理 IP 信息，用于网络请求的代理设置。
    """

    @abstractmethod
    def get_proxy(self) -> Optional[IpInfoModel]:
        """
        获取代理信息（同步方法）

        Returns:
            IpInfoModel 实例，包含代理信息：
            - 成功时返回: IpInfoModel 对象
            - 无可用代理时返回: None

        Raises:
            NotImplementedError: 子类必须实现此方法
        """
        pass

    async def get_proxy_async(self) -> Optional[IpInfoModel]:
        """
        异步获取代理信息
        """
        return self.get_proxy()


class RequestEnhancer(ABC):
    """
    请求增强器的抽象基类

    负责为请求添加业务特定的参数，如平台特有的签名、token 等。
    这是一个用于增强请求的接口，允许业务方自定义请求处理逻辑。
    """

    @abstractmethod
    def enhance_request(self, request: httpx.Request) -> None:
        """
        增强请求，添加业务特定的参数

        Args:
            request: httpx.Request 对象，会被直接修改

        Note:
            此方法应该直接修改传入的 request 对象，而不是返回新的对象。
            可以修改 request 的 URL 参数、Headers、Body 等任何属性。

        Raises:
            NotImplementedError: 子类必须实现此方法
        """
        pass

    async def enhance_request_async(self, request: httpx.Request) -> None:
        """
        异步增强请求，添加业务特定的参数
        """
        self.enhance_request(request)


class DefaultRequestEnhancer(RequestEnhancer):
    """
    默认的请求增强器实现

    不对请求进行任何修改，用于测试和占位。
    业务方应该实现自己的 RequestEnhancer 来添加特定的业务逻辑。
    """

    def enhance_request(self, request: httpx.Request) -> None:
        """
        默认的请求增强实现（不做任何修改）

        Args:
            request: httpx.Request 对象
        """
        # 默认实现不对请求进行任何修改
        _ = request  # 忽略未使用的参数
