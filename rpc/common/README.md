# RPC Common - 动态代理传输层组件 (平台客户端 + 请求增强器模式)

本模块提供了基于 httpx 的动态代理传输层实现，采用**平台客户端 + 请求增强器模式**，支持在发送 HTTP 请求前动态完成三项任务：

1. **注入平台账户信息**（如 Cookies）
2. **注入业务方自定义的请求参数**（如 msToken）
3. **注入代理 IP**

此方案遵循"组合优于继承"的设计原则，将**通用能力（账户、代理）**与**业务特有逻辑（自定义参数）**清晰地分离开来，实现了高度的灵活性和自治性。

## 核心组件

### 1. DynamicProxyTransport
动态代理传输层，是整个方案的核心组件。通过组合的方式集成 AccountProvider、ProxyProvider 和 RequestEnhancer，按照三步增强流程处理请求。

### 2. AccountProvider (抽象基类)
账户信息提供者的抽象基类，定义了获取平台账户信息的接口规范。

### 3. ProxyProvider (抽象基类)
代理信息提供者的抽象基类，定义了获取代理信息的接口规范。

### 4. RequestEnhancer (抽象基类) 🆕
请求增强器的抽象基类，负责为请求添加业务特定的参数，如平台特有的签名、token 等。

### 5. DefaultAccountProvider
默认的账户信息提供者实现，暂时返回空的账户信息，用于测试和占位。

### 6. DefaultProxyProvider
默认的代理信息提供者实现，暂时返回 None，表示不使用代理。

### 7. DefaultRequestEnhancer 🆕
默认的请求增强器实现，不对请求进行任何修改，用于测试和占位。

### 8. AccountInfo (类型定义)
强类型的账户信息结构，包含：
- `cookies`: Dict[str, str] - Cookie 信息
- `headers`: Dict[str, str] - 请求头信息
- `user_agent`: Optional[str] - 用户代理字符串
- `account_model`: Optional[CrawlerCookiesAccount] - 原始账户模型

## 强类型支持

本模块使用 `AccountInfo` TypedDict 提供强类型支持，确保账户信息的类型安全。所有 AccountProvider 实现都必须返回 `AccountInfo` 类型的数据。

```python
from rpc.common import AccountInfo
from models.system.crawler import CrawlerCookiesAccount

# 创建强类型的账户信息
account_info = AccountInfo(
    cookies={"sessionid": "abc123"},
    headers={"X-Platform": "douyin"},
    user_agent="Mozilla/5.0...",
    account_model=crawler_account_instance  # CrawlerCookiesAccount 实例
)
```

## 三步增强流程

1. **第一步 (账户)**: 调用 `AccountProvider.get_account(platform)` 获取账户信息并注入请求
2. **第二步 (业务参数)**: 调用 `RequestEnhancer.enhance_request(request)` 添加业务特定参数
3. **第三步 (代理)**: 调用 `ProxyProvider.get_proxy()` 获取代理 IP 并应用到请求上

## 快速开始

### 基本使用 (新的平台客户端模式)

```python
import httpx
from models.enums import Platform
from rpc.common import (
    DynamicProxyTransport,
    DefaultAccountProvider,
    DefaultProxyProvider,
    DefaultRequestEnhancer
)

# 1. 创建提供者实例
account_provider = DefaultAccountProvider()
proxy_provider = DefaultProxyProvider()
request_enhancer = DefaultRequestEnhancer()  # 默认增强器，不做任何修改

# 2. 创建动态传输层 (指定平台)
transport = DynamicProxyTransport(
    platform=Platform.DOUYIN,  # 🆕 平台在初始化时确定
    account_provider=account_provider,
    proxy_provider=proxy_provider,
    request_enhancer=request_enhancer  # 🆕 请求增强器
)

# 3. 创建 httpx 客户端
client = httpx.Client(transport=transport)

# 4. 发送请求 (不再需要在 extensions 中指定平台)
response = client.get("https://example.com")

client.close()
```

### 业务方自定义实现 (抖音团队示例)

```python
import httpx
import time
from models.enums import Platform
from rpc.common import (
    AccountProvider, ProxyProvider, RequestEnhancer,
    DynamicProxyTransport, AccountInfo
)

# 1. 业务方实现自己的请求增强器
class DouyinRequestEnhancer(RequestEnhancer):
    def enhance_request(self, request: httpx.Request) -> None:
        # 获取当前 URL 参数
        params = dict(request.url.params)

        # 添加抖音特有的参数
        params["msToken"] = f"douyin_ms_token_{int(time.time())}"
        params["X-Bogus"] = "dfpbgj..."  # 实际项目中这里应该是真实的签名算法
        params["device_platform"] = "webapp"
        params["aid"] = "6383"

        # 更新请求的 URL
        request.url = request.url.copy_with(params=params)

        # 添加抖音特有的请求头
        request.headers["X-Platform"] = "douyin"
        request.headers["Referer"] = "https://www.douyin.com/"

# 2. 业务方创建自己的客户端
class DouyinService:
    def __init__(self, account_provider, proxy_provider):
        # 抖音业务方自己的增强器
        self.enhancer = DouyinRequestEnhancer()

        # 创建抖音专用的传输层
        self.transport = DynamicProxyTransport(
            platform=Platform.DOUYIN,
            account_provider=account_provider,
            proxy_provider=proxy_provider,
            request_enhancer=self.enhancer
        )

        # 创建长生命周期的客户端
        self.client = httpx.Client(transport=self.transport)

    def get_user_info(self, user_id: str):
        """获取用户信息"""
        return self.client.get(f"https://api.douyin.com/user/{user_id}")

    def close(self):
        self.client.close()

# 3. 使用业务方服务
# 共享的基础设施 (由基础设施团队提供)
shared_account_provider = MyAccountProvider()
shared_proxy_provider = MyProxyProvider()

# 抖音团队创建自己的服务实例
douyin_service = DouyinService(shared_account_provider, shared_proxy_provider)

# 发送业务请求
response = douyin_service.get_user_info("123456")
douyin_service.close()
```

## 方案优势

### 1. 业务方自治 🎯
- 每个业务团队可以完全控制其客户端的生命周期和请求增强逻辑
- 无需影响其他团队，实现真正的业务自治

### 2. 极致的灵活性 🔧
- `RequestEnhancer` 提供了一个干净、可组合的切面，用于注入任何业务相关的请求修改逻辑
- 无论是修改 URL、Headers 还是 Body，都无需触及核心 `Transport`

### 3. 完美的职责分离 📦
- `AccountProvider`: 负责**通用认证**
- `ProxyProvider`: 负责**通用网络代理**
- `RequestEnhancer`: 负责**业务特定参数**
- `Transport`: 负责**编排**上述所有逻辑，保持自身干净

### 4. 易于测试 🧪
- 可以独立地测试每个 `Enhancer` 和 `Provider`，确保业务逻辑的正确性
- 支持单元测试，无需依赖外部服务

### 5. 高度可复用 ♻️
- 共享的基础设施组件（账户、代理）可以被所有业务方复用
- 业务特定的增强器可以在同一平台的不同场景中复用

## 扩展指南

### 实现自定义 AccountProvider

```python
import json
from rpc.common import AccountProvider, AccountInfo
from models.system.crawler import CrawlerCookiesAccount

class DatabaseAccountProvider(AccountProvider):
    async def get_account_async(self, platform: str) -> AccountInfo:
        """异步获取账户信息（推荐）"""
        # 查询数据库获取有效账户
        account = await CrawlerCookiesAccount.filter(
            platform_name=platform,
            status=0,  # 有效状态
            is_deleted=False
        ).first()

        if not account:
            return AccountInfo(
                cookies={},
                headers={},
                user_agent=None,
                account_model=None
            )

        # 解析 cookies
        cookies = {}
        if account.cookies:
            try:
                cookies = json.loads(account.cookies)
            except (json.JSONDecodeError, TypeError):
                cookies = self._parse_cookie_string(account.cookies)

        return AccountInfo(
            cookies=cookies,
            headers={},
            user_agent=None,
            account_model=account  # 包含原始模型
        )

    def get_account(self, platform: str) -> AccountInfo:
        """同步接口（兼容性）"""
        import asyncio
        try:
            loop = asyncio.get_event_loop()
            return loop.run_until_complete(self.get_account_async(platform))
        except RuntimeError:
            return asyncio.run(self.get_account_async(platform))

    def _parse_cookie_string(self, cookie_string: str) -> Dict[str, str]:
        """解析 cookie 字符串"""
        cookies = {}
        for item in cookie_string.split(';'):
            if '=' in item:
                key, value = item.split('=', 1)
                cookies[key.strip()] = value.strip()
        return cookies
```

### 实现自定义 ProxyProvider

```python
class PoolProxyProvider(ProxyProvider):
    def __init__(self, proxy_pool_api):
        self.api = proxy_pool_api
    
    def get_proxy(self) -> Optional[Dict[str, str]]:
        # 从代理池 API 获取代理
        proxy = self.api.get_available_proxy()
        if proxy:
            return {"all://": f"http://{proxy.ip}:{proxy.port}"}
        return None
```

## 错误处理

组件内置了基本的错误处理机制：

- 账户信息注入失败时，会记录警告日志但不中断请求
- 代理获取失败时，会自动回退到无代理模式
- 提供详细的日志记录，便于调试和监控

## 注意事项

1. **线程安全**: 当前实现不保证线程安全，在多线程环境中使用时需要注意
2. **资源管理**: 使用完毕后记得调用 `client.close()` 释放资源
3. **代理缓存**: 相同的代理配置会被缓存，避免重复创建传输层实例
4. **平台信息**: 通过请求的 `extensions` 参数传递平台信息

## 示例代码

查看 `example.py` 文件获取完整的使用示例。

## 依赖项

- httpx >= 0.24.0
- Python >= 3.8

## 许可证

本项目遵循 MIT 许可证。
