"""
动态代理传输层实现

本模块实现了 DynamicProxyTransport 类，用于在 httpx 请求发送前
动态注入账户信息和代理配置。
"""

import logging
from typing import Any, Dict

import httpx

from rpc.common.providers import (
    AccountInfo,
    AccountProvider,
    ProxyProvider,
    RequestEnhancer,
)

logger = logging.getLogger(__name__)


class DynamicProxyTransport(httpx.AsyncBaseTransport, httpx.BaseTransport):
    """
    动态代理传输层 (平台客户端 + 请求增强器模式)

    通过组合的方式集成 AccountProvider、ProxyProvider 和 RequestEnhancer，
    在发送请求前动态完成三项任务：
    1. 注入平台账户信息（如 Cookies）
    2. 注入业务方自定义的请求参数（如 msToken）
    3. 注入代理 IP

    此方案遵循"组合优于继承"的设计原则，将通用能力与业务特有逻辑清晰分离。
    """

    def __init__(
        self,
        platform: str,
        account_provider: AccountProvider,
        proxy_provider: ProxyProvider,
        request_enhancer: RequestEnhancer,
        **kwargs: Any,
    ) -> None:
        """
        初始化动态代理传输层

        Args:
            platform: 平台名称，如 'douyin', 'xiaohongshu' 等
            account_provider: 账户信息提供者
            proxy_provider: 代理信息提供者
            request_enhancer: 请求增强器，用于添加业务特定参数
            **kwargs: 传递给底层传输层的其他参数
        """
        super().__init__()
        self.platform = platform
        self.account_provider = account_provider
        self.proxy_provider = proxy_provider
        self.request_enhancer = request_enhancer

        # 缓存不同代理配置的传输层实例
        self._transport_cache: Dict[str, httpx.HTTPTransport] = {}

        # 默认的无代理传输层
        self._default_transport = httpx.HTTPTransport(**kwargs)

        logger.info(f"DynamicProxyTransport 初始化完成 (平台: {platform})")

    def close(self) -> None:
        """
        关闭传输层（同步版本）
        """
        pass

    async def __aenter__(self):
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        pass

    def handle_request(self, request: httpx.Request) -> httpx.Response:
        """
        处理 HTTP 请求 (三步增强流程)

        按照以下顺序增强请求：
        1. 注入平台账户信息（如 Cookies）
        2. 注入业务方自定义的请求参数（如 msToken）
        3. 注入代理 IP

        Args:
            request: HTTP 请求对象

        Returns:
            HTTP 响应对象

        Raises:
            Exception: 请求处理过程中的各种异常
        """
        try:
            logger.debug(f"开始处理请求: {request.method} {request.url} (平台: {self.platform})")

            # 第一步：注入账户信息
            self._inject_account_info(request, self.platform)

            # 第二步：注入业务参数
            self._inject_business_params(request)

            # 第三步：注入代理并选择传输层
            transport = self._get_transport_for_request()

            # 使用选定的传输层发送请求
            logger.debug(f"使用传输层发送请求: {request.method} {request.url}")
            return transport.handle_request(request)

        except Exception as e:
            logger.error(f"请求处理失败: {e}")
            raise

    async def handle_async_request(self, request: httpx.Request) -> httpx.Response:
        """
        处理异步 HTTP 请求 (三步增强流程)

        按照以下顺序增强请求：
        1. 注入平台账户信息（如 Cookies）
        2. 注入业务方自定义的请求参数（如 msToken）
        3. 注入代理 IP

        Args:
            request: HTTP 请求对象

        Returns:
            HTTP 响应对象

        Raises:
            Exception: 请求处理过程中的各种异常
        """
        try:
            logger.debug(f"开始处理异步请求: {request.method} {request.url} (平台: {self.platform})")

            # 第一步：注入账户信息
            await self._inject_account_info_async(request, self.platform)

            # 第二步：注入业务参数
            await self._inject_business_params_async(request)

            # 第三步：注入代理并选择传输层
            transport = await self._get_transport_for_request_async()

            # 使用选定的传输层发送请求
            logger.debug(f"使用传输层发送异步请求: {request.method} {request.url}")
            return await transport.handle_async_request(request)

        except Exception as e:
            logger.error(f"异步请求处理失败: {e}")
            raise

    def _inject_business_params(self, request: httpx.Request) -> None:
        """
        注入业务特定参数

        调用 RequestEnhancer 为请求添加业务特定的参数，
        如平台特有的签名、token 等。

        Args:
            request: HTTP 请求对象
        """
        try:
            logger.debug("开始注入业务参数")
            self.request_enhancer.enhance_request(request)
            logger.debug("业务参数注入完成")

        except Exception as e:
            logger.warning(f"注入业务参数失败: {e}")
            # 继续执行，不因为业务参数注入失败而中断请求

    def _inject_account_info(self, request: httpx.Request, platform: str) -> None:
        """
        向请求中注入账户信息

        Args:
            request: HTTP 请求对象
            platform: 平台名称
        """
        try:
            # 检查账户提供者是否可用
            if self.account_provider is None:
                logger.debug(f"平台 {platform} 没有配置账户提供者，跳过账户信息注入")
                return

            # 获取账户信息
            account_info: AccountInfo = self.account_provider.get_account(platform)

            # 检查账户信息是否有效
            if account_info is None:
                logger.debug(f"平台 {platform} 没有可用账户信息，跳过账户信息注入")
                return

            # 注入 Cookies
            cookies = account_info.get("cookies", {})
            if cookies:
                # 解析现有的 cookie 字符串
                existing_cookies = {}
                cookie_header = request.headers.get("cookie", "")
                if cookie_header:
                    # 解析 cookie 字符串为字典
                    for cookie in cookie_header.split(";"):
                        cookie = cookie.strip()
                        if "=" in cookie:
                            key, value = cookie.split("=", 1)
                            existing_cookies[key.strip()] = value.strip()

                # 合并现有 cookies 和新的 cookies
                existing_cookies.update(cookies)

                # 构建 cookie 字符串
                cookie_str = "; ".join([f"{k}={v}" for k, v in existing_cookies.items()])
                request.headers["cookie"] = cookie_str

                logger.debug(f"已注入 {len(cookies)} 个 cookies")

            # 注入 Headers
            headers = account_info.get("headers", {})
            if headers:
                for key, value in headers.items():
                    request.headers[key] = value

                logger.debug(f"已注入 {len(headers)} 个请求头")

            # 注入 User-Agent
            user_agent = account_info.get("user_agent")
            if user_agent:
                request.headers["user-agent"] = user_agent
                logger.debug("已注入自定义 User-Agent")

            # 记录原始账户模型信息（如果有）
            account_model = account_info.get("account_model")
            if account_model:
                logger.debug(f"使用账户: {account_model.account_name} (ID: {account_model.id})")

        except Exception as e:
            logger.warning(f"注入账户信息失败: {e}")
            # 继续执行，不因为账户信息注入失败而中断请求

    async def _inject_account_info_async(self, request: httpx.Request, platform: str) -> None:
        """
        向请求中注入账户信息 (异步版本)

        Args:
            request: HTTP 请求对象
            platform: 平台名称
        """
        try:
            # 检查账户提供者是否可用
            if self.account_provider is None:
                logger.debug(f"平台 {platform} 没有配置账户提供者，跳过账户信息注入")
                return

            # 获取账户信息
            account_info: AccountInfo = await self.account_provider.get_account_async(platform)

            # 检查账户信息是否有效
            if account_info is None:
                logger.debug(f"平台 {platform} 没有可用账户信息，跳过账户信息注入")
                return

            # 注入 Cookies
            cookies = account_info.get("cookies", {})
            if cookies:
                # 解析现有的 cookie 字符串
                existing_cookies = {}
                cookie_header = request.headers.get("cookie", "")
                if cookie_header:
                    # 解析 cookie 字符串为字典
                    for cookie in cookie_header.split(";"):
                        cookie = cookie.strip()
                        if "=" in cookie:
                            key, value = cookie.split("=", 1)
                            existing_cookies[key.strip()] = value.strip()

                # 合并现有 cookies 和新的 cookies
                existing_cookies.update(cookies)

                # 构建 cookie 字符串
                cookie_str = "; ".join([f"{k}={v}" for k, v in existing_cookies.items()])
                request.headers["cookie"] = cookie_str

                logger.debug(f"已注入 {len(cookies)} 个 cookies")

            # 注入 Headers
            headers = account_info.get("headers", {})
            if headers:
                for key, value in headers.items():
                    request.headers[key] = value

                logger.debug(f"已注入 {len(headers)} 个请求头")

            # 注入 User-Agent
            user_agent = account_info.get("user_agent")
            if user_agent:
                request.headers["user-agent"] = user_agent
                logger.debug("已注入自定义 User-Agent")

            # 记录原始账户模型信息（如果有）
            account_model = account_info.get("account_model")
            if account_model:
                logger.debug(f"使用账户: {account_model.account_name} (ID: {account_model.id})")

        except Exception as e:
            logger.warning(f"注入账户信息失败: {e}")
            # 继续执行，不因为账户信息注入失败而中断请求

    async def _inject_business_params_async(self, request: httpx.Request) -> None:
        """
        注入业务特定参数 (异步版本)

        调用 RequestEnhancer 为请求添加业务特定的参数，
        如平台特有的签名、token 等。

        Args:
            request: HTTP 请求对象
        """
        try:
            logger.debug("开始注入业务参数")
            # 检查 RequestEnhancer 是否有异步方法
            if hasattr(self.request_enhancer, "enhance_request_async"):
                await self.request_enhancer.enhance_request_async(request)
            else:
                # 回退到同步方法
                self.request_enhancer.enhance_request(request)
            logger.debug("业务参数注入完成")

        except Exception as e:
            logger.warning(f"注入业务参数失败: {e}")
            # 继续执行，不因为业务参数注入失败而中断请求

    async def _get_transport_for_request_async(self) -> httpx.AsyncHTTPTransport:
        """
        为请求获取合适的传输层 (异步版本)

        Returns:
            异步 HTTP 传输层实例
        """
        try:
            # 获取代理信息
            if hasattr(self.proxy_provider, "get_proxy_async"):
                proxy_info = await self.proxy_provider.get_proxy_async()
            else:
                proxy_info = self.proxy_provider.get_proxy()

            if proxy_info is None:
                logger.debug("没有可用代理，使用默认传输层")
                # 创建默认的异步传输层
                if not hasattr(self, "_default_async_transport"):
                    self._default_async_transport = httpx.AsyncHTTPTransport()
                return self._default_async_transport

            # 构建代理 URL - 使用 IpInfoModel 的 to_proxy_url 方法
            if hasattr(proxy_info, "to_proxy_url"):
                proxy_url = proxy_info.to_proxy_url()
            elif hasattr(proxy_info, "ip") and hasattr(proxy_info, "port"):
                # 处理 IpInfoModel 对象但没有 to_proxy_url 方法的情况
                protocol = getattr(proxy_info, "protocol", "http")
                if (
                    hasattr(proxy_info, "user")
                    and proxy_info.user
                    and hasattr(proxy_info, "password")
                    and proxy_info.password
                ):
                    proxy_url = (
                        f"{protocol}://{proxy_info.user}:{proxy_info.password}@{proxy_info.ip}:{proxy_info.port}"
                    )
                else:
                    proxy_url = f"{protocol}://{proxy_info.ip}:{proxy_info.port}"
            else:
                # 兼容字符串格式的代理信息
                proxy_url = f"http://{proxy_info}"

            # 检查缓存
            cache_key = f"async_{proxy_url}"
            if cache_key not in self._transport_cache:
                logger.debug(f"创建新的异步代理传输层: {proxy_url}")
                self._transport_cache[cache_key] = httpx.AsyncHTTPTransport(proxy=proxy_url)

            return self._transport_cache[cache_key]

        except Exception as e:
            logger.warning(f"获取代理传输层失败: {e}")
            # 回退到默认传输层
            if not hasattr(self, "_default_async_transport"):
                self._default_async_transport = httpx.AsyncHTTPTransport()
            return self._default_async_transport

    def _get_transport_for_request(self) -> httpx.HTTPTransport:
        """
        为请求获取合适的传输层

        Returns:
            HTTP 传输层实例
        """
        try:
            # 获取代理信息
            proxy_info = self.proxy_provider.get_proxy()

            if not proxy_info:
                logger.debug("未获取到代理信息，使用默认传输层")
                return self._default_transport

            # 构建代理 URL - 使用 IpInfoModel 的 to_proxy_url 方法
            if hasattr(proxy_info, "to_proxy_url"):
                proxy_url = proxy_info.to_proxy_url()
            elif hasattr(proxy_info, "ip") and hasattr(proxy_info, "port"):
                # 处理 IpInfoModel 对象但没有 to_proxy_url 方法的情况
                protocol = getattr(proxy_info, "protocol", "http")
                if (
                    hasattr(proxy_info, "user")
                    and proxy_info.user
                    and hasattr(proxy_info, "password")
                    and proxy_info.password
                ):
                    proxy_url = (
                        f"{protocol}://{proxy_info.user}:{proxy_info.password}@{proxy_info.ip}:{proxy_info.port}"
                    )
                else:
                    proxy_url = f"{protocol}://{proxy_info.ip}:{proxy_info.port}"
            else:
                # 兼容字符串格式的代理信息
                proxy_url = f"http://{proxy_info}"

            # 生成代理缓存键
            cache_key = f"sync_{proxy_url}"

            # 检查缓存中是否已有该代理的传输层
            if cache_key not in self._transport_cache:
                logger.debug(f"创建新的代理传输层: {proxy_url}")
                self._transport_cache[cache_key] = httpx.HTTPTransport(proxy=proxy_url)

            return self._transport_cache[cache_key]

        except Exception as e:
            logger.warning(f"获取代理传输层失败: {e}，使用默认传输层")
            return self._default_transport

    def close(self) -> None:
        """
        关闭传输层，清理资源
        """
        logger.info("关闭 DynamicProxyTransport")

        # 关闭账号提供者（简化后的版本不再需要释放账号）
        try:
            if hasattr(self.account_provider, "close"):
                # 检查是否是异步方法
                import asyncio
                import inspect

                if inspect.iscoroutinefunction(self.account_provider.close):
                    # 异步关闭
                    try:
                        loop = asyncio.get_event_loop()
                        if loop.is_running():
                            # 如果事件循环正在运行，创建任务
                            asyncio.create_task(self.account_provider.close())
                            logger.info("已创建异步任务来关闭账号提供者")
                        else:
                            # 事件循环未运行，直接运行
                            loop.run_until_complete(self.account_provider.close())
                            logger.info("账号提供者已关闭")
                    except RuntimeError:
                        # 没有事件循环，创建新的
                        asyncio.run(self.account_provider.close())
                        logger.info("账号提供者已关闭")
                else:
                    # 同步关闭
                    self.account_provider.close()
                    logger.info("账号提供者已关闭")
            else:
                logger.debug("账号提供者没有 close 方法")
        except Exception as e:
            logger.error(f"关闭账号提供者失败: {e}")

        # 关闭代理提供者
        try:
            if hasattr(self.proxy_provider, "close"):
                # 检查是否是异步方法
                import inspect

                if inspect.iscoroutinefunction(self.proxy_provider.close):
                    # 异步关闭
                    try:
                        loop = asyncio.get_event_loop()
                        if loop.is_running():
                            # 如果事件循环正在运行，创建任务
                            asyncio.create_task(self.proxy_provider.close())
                            logger.info("已创建异步任务来关闭代理提供者")
                        else:
                            # 事件循环未运行，直接运行
                            loop.run_until_complete(self.proxy_provider.close())
                            logger.info("代理提供者已关闭")
                    except RuntimeError:
                        # 没有事件循环，创建新的
                        asyncio.run(self.proxy_provider.close())
                        logger.info("代理提供者已关闭")
                else:
                    # 同步关闭
                    self.proxy_provider.close()
                    logger.info("代理提供者已关闭")
            else:
                logger.debug("代理提供者没有 close 方法")
        except Exception as e:
            logger.error(f"关闭代理提供者失败: {e}")

        # 关闭默认传输层
        self._default_transport.close()

        # 关闭默认异步传输层（如果存在）
        if hasattr(self, "_default_async_transport"):
            try:
                import asyncio

                if asyncio.iscoroutinefunction(self._default_async_transport.aclose):
                    # 异步关闭
                    try:
                        loop = asyncio.get_event_loop()
                        if loop.is_running():
                            asyncio.create_task(self._default_async_transport.aclose())
                        else:
                            loop.run_until_complete(self._default_async_transport.aclose())
                    except RuntimeError:
                        asyncio.run(self._default_async_transport.aclose())
                else:
                    # 同步关闭
                    self._default_async_transport.close()
            except Exception as e:
                logger.error(f"关闭默认异步传输层失败: {e}")

        # 关闭所有缓存的代理传输层
        for cache_key, transport in self._transport_cache.items():
            try:
                if cache_key.startswith("async_"):
                    # 异步传输层
                    try:
                        import asyncio

                        if hasattr(transport, "aclose") and asyncio.iscoroutinefunction(transport.aclose):
                            try:
                                loop = asyncio.get_event_loop()
                                if loop.is_running():
                                    asyncio.create_task(transport.aclose())
                                else:
                                    loop.run_until_complete(transport.aclose())
                            except RuntimeError:
                                asyncio.run(transport.aclose())
                        else:
                            transport.close()
                    except Exception as e:
                        logger.error(f"关闭异步传输层失败 {cache_key}: {e}")
                else:
                    # 同步传输层
                    transport.close()
            except Exception as e:
                logger.error(f"关闭传输层失败 {cache_key}: {e}")

        self._transport_cache.clear()

        super().close()

    async def aclose(self) -> None:
        """
        异步关闭传输层，清理资源
        """
        import asyncio

        logger.info("异步关闭 DynamicProxyTransport")

        # 关闭账号提供者（简化后的版本不再需要释放账号）
        try:
            if hasattr(self.account_provider, "close"):
                if hasattr(self.account_provider, "aclose"):
                    # 优先使用异步关闭方法
                    await self.account_provider.aclose()
                    logger.info("账号提供者已异步关闭")
                elif asyncio.iscoroutinefunction(self.account_provider.close):
                    # 使用异步 close 方法
                    await self.account_provider.close()
                    logger.info("账号提供者已异步关闭")
                else:
                    # 同步关闭
                    self.account_provider.close()
                    logger.info("账号提供者已关闭")
            else:
                logger.debug("账号提供者没有 close 方法")
        except Exception as e:
            logger.error(f"异步关闭账号提供者失败: {e}")

        # 关闭代理提供者
        try:
            if hasattr(self.proxy_provider, "close"):
                if hasattr(self.proxy_provider, "aclose"):
                    # 优先使用异步关闭方法
                    await self.proxy_provider.aclose()
                    logger.info("代理提供者已异步关闭")
                elif asyncio.iscoroutinefunction(self.proxy_provider.close):
                    # 使用异步 close 方法
                    await self.proxy_provider.close()
                    logger.info("代理提供者已异步关闭")
                else:
                    # 同步关闭
                    self.proxy_provider.close()
                    logger.info("代理提供者已关闭")
            else:
                logger.debug("代理提供者没有 close 方法")
        except Exception as e:
            logger.error(f"异步关闭代理提供者失败: {e}")

        # 关闭默认传输层
        if hasattr(self._default_transport, "aclose"):
            await self._default_transport.aclose()
        else:
            self._default_transport.close()

        # 关闭默认异步传输层（如果存在）
        if hasattr(self, "_default_async_transport"):
            try:
                if hasattr(self._default_async_transport, "aclose"):
                    await self._default_async_transport.aclose()
                else:
                    self._default_async_transport.close()
            except Exception as e:
                logger.error(f"关闭默认异步传输层失败: {e}")

        # 关闭所有缓存的代理传输层
        for cache_key, transport in self._transport_cache.items():
            try:
                if cache_key.startswith("async_"):
                    # 异步传输层
                    if hasattr(transport, "aclose"):
                        await transport.aclose()
                    else:
                        transport.close()
                else:
                    # 同步传输层
                    if hasattr(transport, "aclose"):
                        await transport.aclose()
                    else:
                        transport.close()
            except Exception as e:
                logger.error(f"关闭传输层失败 {cache_key}: {e}")

        self._transport_cache.clear()
