#!/usr/bin/env python3
"""
代理提供者使用示例

展示如何使用 DefaultProxyProvider 来获取代理信息
"""

import asyncio

from loguru import logger

from rpc.providers import DefaultProxyProvider


async def basic_usage_example():
    """基本使用示例"""
    logger.info("=== 代理提供者基本使用示例 ===")

    # 创建代理提供者
    proxy_provider = DefaultProxyProvider()

    try:
        # 初始化代理池（异步）
        await proxy_provider._initialize_proxy_pool()

        # 获取代理信息（异步方法）
        proxy_info = await proxy_provider.get_proxy_async()

        if proxy_info:
            logger.info(f"✅ 成功获取代理: {proxy_info}")

            # 展示如何在 httpx 中使用
            logger.info("📝 在 httpx 中使用代理的示例:")
            logger.info("   import httpx")
            logger.info(f"   async with httpx.AsyncClient(proxies={proxy_info}) as client:")
            logger.info("       response = await client.get('https://httpbin.org/ip')")

        else:
            logger.warning("⚠️ 未获取到代理信息")

        # 尝试获取多个代理
        logger.info("\n=== 获取多个代理示例 ===")
        for i in range(3):
            proxy = await proxy_provider.get_proxy_async()
            if proxy:
                logger.info(f"代理 {i+1}: {proxy}")
            else:
                logger.warning(f"代理 {i+1}: 无可用代理")

    except Exception as e:
        logger.error(f"❌ 示例执行失败: {e}")
        import traceback

        traceback.print_exc()
    finally:
        # 清理资源
        await proxy_provider.close()


async def integration_example():
    """集成使用示例 - 在实际 HTTP 请求中使用代理"""
    logger.info("\n=== 代理集成使用示例 ===")

    proxy_provider = DefaultProxyProvider()

    try:
        # 初始化代理池
        await proxy_provider._initialize_proxy_pool()

        # 获取代理
        proxy_info = await proxy_provider.get_proxy_async()

        if proxy_info:
            # 使用代理发送 HTTP 请求
            import httpx

            logger.info(f"🌐 使用代理发送请求: {proxy_info}")

            async with httpx.AsyncClient(proxies=proxy_info, timeout=10.0) as client:
                try:
                    # 测试代理是否工作
                    response = await client.get("https://httpbin.org/ip")
                    if response.status_code == 200:
                        data = response.json()
                        logger.info(f"✅ 代理请求成功，IP: {data.get('origin', 'unknown')}")
                    else:
                        logger.warning(f"⚠️ 代理请求失败，状态码: {response.status_code}")

                except Exception as e:
                    logger.error(f"❌ 代理请求异常: {e}")
        else:
            logger.warning("⚠️ 无可用代理，使用直连")

            # 不使用代理的请求
            async with httpx.AsyncClient(timeout=10.0) as client:
                try:
                    response = await client.get("https://httpbin.org/ip")
                    if response.status_code == 200:
                        data = response.json()
                        logger.info(f"✅ 直连请求成功，IP: {data.get('origin', 'unknown')}")
                except Exception as e:
                    logger.error(f"❌ 直连请求异常: {e}")

    except Exception as e:
        logger.error(f"❌ 集成示例失败: {e}")
    finally:
        await proxy_provider.close()


async def configuration_example():
    """配置示例"""
    logger.info("\n=== 代理配置示例 ===")

    # 检查 Dynaconf 配置
    from settings.config import settings

    api_key = settings.get("KUAIDAILI_DPS_SECRET_ID")
    api_secret = settings.get("KUAIDAILI_DPS_SIGNATURE")

    if api_key:
        logger.info("✅ 检测到快代理配置:")
        logger.info(f"   API Key: {api_key[:8]}...")
        if api_secret:
            logger.info(f"   API Secret: {api_secret[:8]}...")
        else:
            logger.info("   API Secret: 未设置")
    else:
        logger.warning("⚠️ 未检测到快代理配置")
        logger.info("💡 设置环境变量以启用代理:")
        logger.info("   export KUAIDAILI_DPS_SECRET_ID=your_secret_id")
        logger.info("   export KUAIDAILI_DPS_SIGNATURE=your_signature")


async def main():
    """主函数"""
    logger.info("🚀 代理提供者示例程序启动")

    try:
        # 配置示例
        await configuration_example()

        # 基本使用示例
        await basic_usage_example()

        # 集成使用示例
        await integration_example()

        logger.info("\n🎉 所有示例执行完成！")

    except KeyboardInterrupt:
        logger.warning("\n⚠️ 程序被用户中断")
    except Exception as e:
        logger.error(f"\n❌ 程序执行失败: {e}")
        import traceback

        traceback.print_exc()


if __name__ == "__main__":
    # 运行示例
    asyncio.run(main())
