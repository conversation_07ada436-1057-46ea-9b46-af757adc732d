# DynamicProxyTransport 使用文档 (平台客户端 + 请求增强器模式)

## 1. 简介

本文档旨在说明如何使用 `DynamicProxyTransport` 方案，通过组合（Composition）的方式，为每个平台（如抖音、小红书）创建专用的 `httpx.Client` 实例。每个客户端在初始化时就已确定平台归属，并在请求发出前动态地完成三项任务：

1.  注入平台账户信息（如 Cookies）。
2.  注入业务方自定义的请求参数（如 `msToken`）。
3.  注入代理 IP。

此方案遵循“组合优于继承”的设计原则，将**通用能力（账户、代理）**与**业务特有逻辑（自定义参数）**清晰地分离开来，实现了高度的灵活性和自治性。

## 2. 核心组件 UML 图

下图展示了本方案中各个核心组件的关系。

```mermaid
classDiagram
    class httpx.Client {
        +send(request)
    }
    class DynamicProxyTransport {
        -platform: str
        -account_provider: AccountProvider
        -proxy_provider: ProxyProvider
        -request_enhancer: RequestEnhancer
        +handle_request(request)
    }
    class AccountProvider {
        <<interface>>
        +get_account(platform) dict
    }
    class ProxyProvider {
        <<interface>>
        +get_proxy() dict
    }
    class RequestEnhancer {
        <<interface>>
        +enhance_request(request)
    }
    class DbAccountProvider {
        +get_account(platform) dict
    }
    class PoolProxyProvider {
        +get_proxy() dict
    }
    class DouyinRequestEnhancer {
        +enhance_request(request)
    }

    httpx.Client o-- DynamicProxyTransport : uses
    DynamicProxyTransport o-- AccountProvider : has a
    DynamicProxyTransport o-- ProxyProvider : has a
    DynamicProxyTransport o-- RequestEnhancer : has a
    DbAccountProvider ..|> AccountProvider : implements
    PoolProxyProvider ..|> ProxyProvider : implements
    DouyinRequestEnhancer ..|> RequestEnhancer : implements
```

-   **`RequestEnhancer`**: 新增的核心接口，负责为请求添加业务特有的参数。
-   **`DouyinRequestEnhancer`**: `RequestEnhancer` 的一个具体实现，专门处理抖音业务的请求增强逻辑。
-   **`DynamicProxyTransport`**: 现在组合了三个接口，负责编排整个请求处理流程。

## 3. 请求处理流程

1.  **初始化阶段 (业务方自治)**:
    -   共享的 `DbAccountProvider` 和 `PoolProxyProvider` 实例由基础设施提供。
    -   抖音业务方自行创建其专用的 `DouyinRequestEnhancer` 实例。
    -   抖音业务方使用平台名称 `"douyin"`、共享的 `provider` 和自己的 `enhancer` 来实例化一个 `DynamicProxyTransport`。
    -   最后，业务方用这个 `transport` 创建一个属于自己的、长生命周期的 `httpx.Client` 实例。

2.  **发起请求**:
    -   业务代码调用 `douyin_client.get(url)`。

3.  **Transport 层处理 (三步增强)**:
    -   `DynamicProxyTransport.handle_request` 方法被调用。
    -   **第一步 (账户)**: 调用 `self.account_provider.get_account("douyin")` 获取账户信息并注入请求。
    -   **第二步 (业务参数)**: 调用 `self.request_enhancer.enhance_request(request)`，`DouyinRequestEnhancer` 会为请求添加 `msToken` 等抖音特有的参数。
    -   **第三步 (代理)**: 调用 `self.proxy_provider.get_proxy()` 获取代理 IP 并应用到请求上。

4.  **执行与响应**:
    -   经过三步增强后，请求被底层的 `httpx.HTTPTransport` 发出。
    -   响应沿调用链返回。

## 4. 完整使用示例

```python
# rpc/base/custom_transport.py (需要更新)
# ... (AccountProvider, ProxyProvider 定义不变) ...
class RequestEnhancer(Protocol):
    """一个用于增强请求的接口，负责添加业务特定参数。"""
    def enhance_request(self, request: httpx.Request) -> None:
        ...

class DynamicProxyTransport(httpx.BaseTransport):
    def __init__(
        self,
        platform: str,
        account_provider: AccountProvider,
        proxy_provider: ProxyProvider,
        request_enhancer: RequestEnhancer,
        # ...
    ):
        self.platform = platform
        self.account_provider = account_provider
        self.proxy_provider = proxy_provider
        self.request_enhancer = request_enhancer
        # ... (transport cache logic) ...

    def handle_request(self, request: httpx.Request) -> httpx.Response:
        # 1. 注入账户信息
        account_info = self.account_provider.get_account(self.platform)
        # ... (update cookies/headers) ...

        # 2. 注入业务参数
        self.request_enhancer.enhance_request(request)

        # 3. 注入代理
        proxy_config = self.proxy_provider.get_proxy()
        # ... (select transport with or without proxy) ...
        
        return transport.handle_request(request)

# rpc/douyin/enhancer.py (抖音业务方实现)
from rpc.base.custom_transport import RequestEnhancer
import httpx
import time

class DouyinRequestEnhancer(RequestEnhancer):
    def enhance_request(self, request: httpx.Request) -> None:
        print("抖音增强器：添加 msToken 和 X-Bogus...")
        # 示例：为 URL 添加 msToken 和 X-Bogus
        params = request.url.params
        params = params.set("msToken", f"douyin_ms_token_{int(time.time())}")
        params = params.set("X-Bogus", "dfpbgj...")
        
        # 更新请求的 URL
        request.url = request.url.copy_with(params=params)


# rpc/douyin/service.py (抖音业务方使用)
import httpx
from models.enums import Platform
# from rpc.base.providers import DbAccountProvider, PoolProxyProvider
# from rpc.douyin.enhancer import DouyinRequestEnhancer
# from rpc.base.custom_transport import DynamicProxyTransport

# 共享的 Provider 实例
account_provider = DbAccountProvider()
proxy_provider = PoolProxyProvider()

# 业务方自己的 Enhancer
douyin_enhancer = DouyinRequestEnhancer()

# 业务方初始化自己的 Transport 和 Client
douyin_transport = DynamicProxyTransport(
    platform=Platform.DOUYIN,
    account_provider=account_provider,
    proxy_provider=proxy_provider,
    request_enhancer=douyin_enhancer
)
douyin_client = httpx.Client(transport=douyin_transport)

# 使用客户端
try:
    response = douyin_client.get("https://httpbin.org/anything")
    print("\n--- httpbin.org 收到的请求详情 ---")
    print(f"URL: {response.json()['url']}")
finally:
    douyin_client.close()

```

## 5. 方案优势

-   **业务方自治**: 每个业务团队可以完全控制其客户端的生命周期和请求增强逻辑，而无需影响其他团队。
-   **极致的灵活性**: `RequestEnhancer` 提供了一个干净、可组合的切面，用于注入任何业务相关的请求修改逻辑，无论是修改 URL、Headers 还是 Body，都无需触及核心 `Transport`。
-   **完美的职责分离**:
    -   `AccountProvider`: 负责**通用认证**。
    -   `ProxyProvider`: 负责**通用网络代理**。
    -   `RequestEnhancer`: 负责**业务特定参数**。
    -   `Transport`: 负责**编排**上述所有逻辑，保持自身干净。
-   **易于测试**: 可以独立地测试每个 `Enhancer` 和 `Provider`，确保业务逻辑的正确性。
