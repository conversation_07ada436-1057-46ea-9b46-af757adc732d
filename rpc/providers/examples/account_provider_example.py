#!/usr/bin/env python3
"""
账号提供者使用示例

展示如何使用 DefaultAccountProvider 来获取账号信息
"""

import asyncio

from loguru import logger

from rpc.providers import DefaultAccountProvider


async def basic_usage_example():
    """基本使用示例"""
    logger.info("=== 账号提供者基本使用示例 ===")

    # 创建账号提供者
    account_provider = DefaultAccountProvider()

    try:
        # 获取抖音账号信息（异步方法）
        logger.info("🔍 获取抖音账号信息...")
        account_info = await account_provider.get_account_async("douyin")

        if account_info:
            logger.info("✅ 成功获取账号信息:")
            logger.info(f"   Cookies: {len(account_info.get('cookies', {}))} 个")
            logger.info(f"   Headers: {len(account_info.get('headers', {}))} 个")
            logger.info(f"   User-Agent: {account_info.get('user_agent', 'N/A')[:50]}...")

            # 展示如何在 httpx 中使用
            logger.info("📝 在 httpx 中使用账号信息的示例:")
            logger.info("   import httpx")
            logger.info("   async with httpx.AsyncClient(")
            logger.info("       cookies=account_info['cookies'],")
            logger.info("       headers=account_info['headers']")
            logger.info("   ) as client:")
            logger.info("       response = await client.get('https://www.douyin.com')")

            # 简化后的版本不再需要释放账号
            if account_info.get("account_model"):
                logger.info("ℹ️ 账号信息包含原始模型，但简化后的版本不再需要释放账号")

        else:
            logger.warning("⚠️ 未获取到账号信息")

        # 尝试获取其他平台的账号
        logger.info("\n=== 获取其他平台账号示例 ===")
        platforms = ["xiaohongshu", "weibo", "bilibili"]

        for platform in platforms:
            logger.info(f"🔍 获取 {platform} 账号信息...")
            account = await account_provider.get_account_async(platform)
            if account:
                logger.info(f"✅ {platform}: 获取成功")
            else:
                logger.warning(f"⚠️ {platform}: 无可用账号")

    except Exception as e:
        logger.error(f"❌ 示例执行失败: {e}")
        import traceback

        traceback.print_exc()
    finally:
        # 清理资源
        await account_provider.close()


async def account_stats_example():
    """账号统计示例"""
    logger.info("\n=== 账号统计信息示例 ===")

    account_provider = DefaultAccountProvider()

    try:
        platforms = ["douyin", "xiaohongshu", "weibo"]

        for platform in platforms:
            logger.info(f"📊 获取 {platform} 账号统计...")
            stats = await account_provider.get_account_stats_async(platform)

            if stats:
                logger.info(f"✅ {platform} 统计信息:")
                logger.info(f"   总账号数: {stats.get('total_count', 0)}")
                logger.info(f"   可用账号: {stats.get('available_count', 0)}")
                logger.info(f"   使用中账号: {stats.get('in_use_count', 0)}")
                logger.info(f"   不可用账号: {stats.get('unavailable_count', 0)}")
            else:
                logger.warning(f"⚠️ {platform}: 无法获取统计信息")

    except Exception as e:
        logger.error(f"❌ 统计示例失败: {e}")
    finally:
        await account_provider.close()


async def integration_example():
    """集成使用示例 - 在实际 HTTP 请求中使用账号"""
    logger.info("\n=== 账号集成使用示例 ===")

    account_provider = DefaultAccountProvider()

    try:
        # 获取账号
        account_info = await account_provider.get_account_async("douyin")

        if account_info and account_info.get("cookies"):
            # 使用账号发送 HTTP 请求
            import httpx

            logger.info("🌐 使用账号发送请求...")

            async with httpx.AsyncClient(
                cookies=account_info["cookies"], headers=account_info["headers"], timeout=10.0
            ) as client:
                try:
                    # 测试请求（使用 httpbin 进行测试）
                    response = await client.get("https://httpbin.org/cookies")
                    if response.status_code == 200:
                        data = response.json()
                        logger.info(f"✅ 请求成功，返回的 cookies: {data.get('cookies', {})}")
                    else:
                        logger.warning(f"⚠️ 请求失败，状态码: {response.status_code}")

                except Exception as e:
                    logger.error(f"❌ 请求异常: {e}")

            # 简化后的版本不再需要释放账号
            if account_info.get("account_model"):
                logger.info("ℹ️ 简化后的版本不再需要释放账号")
        else:
            logger.warning("⚠️ 无可用账号，跳过集成测试")

    except Exception as e:
        logger.error(f"❌ 集成示例失败: {e}")
    finally:
        await account_provider.close()


async def account_lifecycle_example():
    """账号生命周期管理示例"""
    logger.info("\n=== 账号生命周期管理示例 ===")

    account_provider = DefaultAccountProvider()

    try:
        # 1. 获取多个平台的账号
        platforms = ["douyin", "xiaohongshu"]
        accounts = {}

        for platform in platforms:
            logger.info(f"🔍 获取 {platform} 账号...")
            account = await account_provider.get_account_async(platform)
            if account:
                accounts[platform] = account
                logger.info(f"✅ {platform}: 账号获取成功")
            else:
                logger.warning(f"⚠️ {platform}: 无可用账号")

        # 2. 模拟使用账号
        logger.info("⏳ 模拟使用账号...")
        await asyncio.sleep(1)

        # 3. 简化后的版本不再需要手动释放账号
        for platform, account in accounts.items():
            if account.get("account_model"):
                logger.info(f"ℹ️ {platform} 账号可以共享使用，无需释放")

        # 4. 获取统计信息
        for platform in platforms:
            stats = await account_provider.get_account_stats_async(platform)
            if stats:
                logger.info(f"📊 {platform} 当前统计: 可用 {stats.get('available_count', 0)} 个")

    except Exception as e:
        logger.error(f"❌ 生命周期示例失败: {e}")
    finally:
        # 5. 清理所有资源（简化后的版本不再需要释放账号）
        await account_provider.close()
        logger.info("🧹 所有资源已清理")


async def error_handling_example():
    """错误处理示例"""
    logger.info("\n=== 错误处理示例 ===")

    account_provider = DefaultAccountProvider()

    try:
        # 测试无效平台名称
        try:
            await account_provider.get_account_async("")
        except ValueError as e:
            logger.info(f"✅ 正确捕获无效平台错误: {e}")

        try:
            await account_provider.get_account_async(None)
        except ValueError as e:
            logger.info(f"✅ 正确捕获空平台错误: {e}")

        # 测试不存在的平台
        result = await account_provider.get_account_async("nonexistent_platform")
        if result is None:
            logger.info("✅ 正确处理不存在的平台")
        else:
            logger.info("ℹ️ 返回了默认账号信息")

    except Exception as e:
        logger.error(f"❌ 错误处理示例失败: {e}")
    finally:
        await account_provider.close()


async def main():
    """主函数"""
    logger.info("🚀 账号提供者示例程序启动")

    try:
        # 基本使用示例
        await basic_usage_example()

        # 账号统计示例
        await account_stats_example()

        # 集成使用示例
        await integration_example()

        # 账号生命周期管理示例
        await account_lifecycle_example()

        # 错误处理示例
        await error_handling_example()

        logger.info("\n🎉 所有示例执行完成！")

    except KeyboardInterrupt:
        logger.warning("\n⚠️ 程序被用户中断")
    except Exception as e:
        logger.error(f"\n❌ 程序执行失败: {e}")
        import traceback

        traceback.print_exc()


if __name__ == "__main__":
    # 运行示例
    asyncio.run(main())
