"""
代理提供者实现

本模块包含代理信息提供者的具体实现，支持从 pkg/proxy 模块获取代理信息。
"""

from typing import Optional

from loguru import logger

from pkg.proxy.models import IpInfoModel

# 导入基础接口
from rpc.common.providers import ProxyProvider


class DefaultProxyProvider(ProxyProvider):
    """
    默认的代理信息提供者实现

    使用 pkg/proxy 模块获取代理信息。
    支持从快代理等代理提供商获取代理 IP。
    """

    def __init__(self, proxy_source: Optional[str] = None):
        """初始化代理提供者"""
        self._proxy_pool = None
        self._initialized = False
        self._proxy_source = proxy_source

    async def _initialize_proxy_pool(self):
        """初始化代理池"""
        if self._initialized:
            return

        try:
            # 动态导入 pkg.proxy 模块
            from pkg.proxy import create_ip_pool

            # 如果指定了代理源，则使用它
            if self._proxy_source:
                logger.info(f"使用指定的代理源: {self._proxy_source}")
                # 这里可以根据 self._proxy_source 的格式来解析和创建代理池
                # 例如，如果它是一个 URL，我们可以从该 URL 获取代理
                # 此处为示例，假设 self._proxy_source 是 kuaidaili
                if self._proxy_source == "kuaidaili":
                    from settings.config import settings

                    secret_id = settings.get("KUAIDAILI_DPS_SECRET_ID")
                    signature = settings.get("KUAIDAILI_DPS_SIGNATURE")
                    if not secret_id:
                        logger.error("代理源设置为 'kuaidaili'，但未找到 KUAIDAILI_DPS_SECRET_ID")
                        return
                else:
                    logger.error(f"不支持的代理源: {self._proxy_source}")
                    return
            else:
                # 否则，回退到从 settings 加载
                from settings.config import settings

                secret_id = settings.get("KUAIDAILI_DPS_SECRET_ID")
                signature = settings.get("KUAIDAILI_DPS_SIGNATURE")

            if secret_id:
                logger.info("正在初始化代理池...")
                self._proxy_pool = create_ip_pool(
                    provider_type="kuaidaili",
                    api_key=secret_id,
                    api_secret=signature or "",
                    cache_ttl=3600,  # 缓存1小时
                )

                # 预加载一些代理
                from settings.config import settings

                proxy_count = settings.get("proxy_pool_default_count", 2)
                await self._proxy_pool.load_proxies(count=proxy_count)
                logger.info(f"代理池初始化完成，已加载 {self._proxy_pool.get_proxy_count()} 个代理")
            else:
                logger.warning("未设置 KUAIDAILI_DPS_SECRET_ID 环境变量，将不使用代理")

        except ImportError:
            logger.warning("pkg.proxy 模块不可用，将不使用代理")
        except Exception as e:
            logger.error(f"初始化代理池失败: {e}")
        finally:
            self._initialized = True

    def get_proxy(self) -> Optional[IpInfoModel]:
        """
        获取代理信息（同步方法）

        Returns:
            IpInfoModel 实例，包含代理信息，
            如果没有可用代理则返回 None
        """
        # 如果代理池未初始化，返回 None
        if not self._proxy_pool:
            return None

        try:
            # 同步获取代理
            import asyncio

            loop = asyncio.get_event_loop()
            if loop.is_running():
                # 如果事件循环正在运行，创建任务
                logger.warning("代理池需要异步调用，当前同步接口返回 None")
                return None
            else:
                # 事件循环未运行，可以直接运行
                proxy = loop.run_until_complete(self._proxy_pool.get_proxy())
                if proxy:
                    logger.debug(f"获取到代理: {proxy}")
                    return proxy
                else:
                    logger.warning("代理池中没有可用代理")
                    return None
        except Exception as e:
            logger.error(f"获取代理失败: {e}")
            return None
