"""
账号提供者实现

本模块包含账号信息提供者的具体实现，支持从 pkg/crawler_account 模块获取真实账号信息。
"""

import json
from abc import ABC, abstractmethod
from typing import TYPE_CHECKING, Dict, Optional

from loguru import logger

# 导入基础接口
from rpc.common.providers import AccountInfo, AccountProvider

if TYPE_CHECKING:
    from models.system.crawler import CrawlerCookiesAccount


class AsyncAccountProvider(ABC):
    """
    异步账号信息提供者的抽象基类

    负责提供账号信息，用于网络请求的认证设置。
    支持异步操作，适用于需要从数据库或远程服务获取账号的场景。
    简化后的版本不再需要账号释放功能。
    """

    @abstractmethod
    async def get_account_async(self, platform: str) -> Optional[AccountInfo]:
        """
        异步获取账号信息

        Args:
            platform: 平台名称，如 'douyin', 'xiaohongshu' 等

        Returns:
            AccountInfo 类型的字典，包含账号信息，
            如果没有可用账号则返回 None

        Raises:
            NotImplementedError: 子类必须实现此方法
        """
        pass

    @abstractmethod
    async def close(self) -> None:
        """
        关闭账号提供者，清理资源

        Raises:
            NotImplementedError: 子类必须实现此方法
        """
        pass


class DefaultAccountProvider(AccountProvider, AsyncAccountProvider):
    """
    默认的账号信息提供者实现

    使用 pkg/crawler_account 模块获取真实账号信息。
    支持从数据库获取可用账号，并提供完整的账号生命周期管理。
    """

    def __init__(self):
        """初始化账号提供者"""
        self._account_manager = None
        self._initialized = False
        self._current_accounts: Dict[str, "CrawlerCookiesAccount"] = {}

    async def _initialize_account_manager(self):
        """初始化账号管理器"""
        if self._initialized:
            return

        try:
            # 首先确保 Tortoise ORM 已初始化
            await self._ensure_tortoise_initialized()

            # 添加 pkg/crawler_account 目录到 Python 路径
            import os
            import sys

            crawler_account_path = os.path.join(os.path.dirname(__file__), "../../pkg/crawler_account")
            if crawler_account_path not in sys.path:
                sys.path.insert(0, crawler_account_path)

            # 直接导入
            from account_manager import AccountManager

            # 在调试模式下启用 SQL 日志
            from settings.config import settings

            enable_sql_logging = settings.DEBUG

            self._account_manager = AccountManager(enable_sql_logging=enable_sql_logging)
            logger.info("账号管理器初始化完成")

        except ImportError:
            logger.warning("pkg.crawler_account 模块不可用，将使用默认账号信息")
        except Exception as e:
            logger.error(f"初始化账号管理器失败: {e}")
        finally:
            self._initialized = True

    async def _ensure_tortoise_initialized(self):
        """确保 Tortoise ORM 已初始化"""
        try:
            from tortoise import Tortoise

            # 检查是否已经初始化
            if Tortoise._inited:
                logger.debug("Tortoise ORM 已初始化")
                return

            # 如果未初始化，尝试初始化
            logger.info("正在初始化 Tortoise ORM...")

            # 导入配置
            import os
            import sys

            # 添加项目根目录到路径
            project_root = os.path.join(os.path.dirname(__file__), "../..")
            if project_root not in sys.path:
                sys.path.insert(0, project_root)

            from settings.config import settings

            await Tortoise.init(config=settings.tortoise_orm)
            logger.info("Tortoise ORM 初始化完成")

            # 在调试模式下启用 SQL 日志
            if settings.DEBUG:
                try:
                    from settings.sql_logging import enable_sql_logging

                    enable_sql_logging()
                except ImportError:
                    logger.warning("无法导入 SQL 日志配置模块")

        except Exception as e:
            logger.error(f"Tortoise ORM 初始化失败: {e}")
            raise

    def _convert_cookies_to_dict(self, cookies_str: str) -> Dict[str, str]:
        """
        将 cookies 字符串转换为字典

        支持两种格式：
        1. JSON 格式：{\"sessionid\": \"abc123\", \"csrftoken\": \"def456\"}
        2. HTTP Cookie 格式：sessionid=abc123; csrftoken=def456

        Args:
            cookies_str: cookies 字符串（JSON 格式或 HTTP Cookie 格式）

        Returns:
            cookies 字典
        """
        if not cookies_str:
            return {}

        cookies_str = cookies_str.strip()

        # 检查是否为 JSON 格式
        if cookies_str.startswith("{") and cookies_str.endswith("}"):
            try:
                return json.loads(cookies_str)
            except json.JSONDecodeError:
                logger.warning(f"无法将字符串解析为 JSON，将尝试其他格式: {cookies_str}")

        # 尝试解析 HTTP Cookie 格式
        try:
            return self._parse_http_cookie_string(cookies_str)
        except Exception as e:
            logger.error(f"转换 cookies 时发生错误: {e}")
            return {}

    def _parse_http_cookie_string(self, cookie_string: str) -> Dict[str, str]:
        """
        解析 HTTP Cookie 字符串格式

        Args:
            cookie_string: HTTP Cookie 字符串，如 "sessionid=abc123; csrftoken=def456"

        Returns:
            cookies 字典
        """
        cookies = {}
        if not cookie_string:
            return cookies

        for item in cookie_string.split(";"):
            item = item.strip()
            if "=" in item:
                key, value = item.split("=", 1)
                cookies[key.strip()] = value.strip()
            else:
                # 处理没有值的 cookie（如某些标志性 cookie）
                if item:
                    cookies[item] = ""

        return cookies

    def _generate_headers(self, platform: str, user_agent: Optional[str] = None) -> Dict[str, str]:
        """
        生成请求头信息

        Args:
            platform: 平台名称
            user_agent: 用户代理字符串

        Returns:
            请求头字典
        """
        headers = {
            "Accept": "application/json, text/plain, */*",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
            "Accept-Encoding": "gzip, deflate, br",
            "Connection": "keep-alive",
            "Upgrade-Insecure-Requests": "1",
        }

        # 根据平台设置特定的请求头
        if platform.lower() == "douyin":
            headers.update(
                {
                    "Referer": "https://www.douyin.com/",
                    "Origin": "https://www.douyin.com",
                }
            )
        elif platform.lower() == "xiaohongshu":
            headers.update(
                {
                    "Referer": "https://www.xiaohongshu.com/",
                    "Origin": "https://www.xiaohongshu.com",
                }
            )

        # 设置 User-Agent
        if user_agent:
            headers["User-Agent"] = user_agent
        else:
            # 默认 User-Agent
            headers["User-Agent"] = (
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) "
                "AppleWebKit/537.36 (KHTML, like Gecko) "
                "Chrome/120.0.0.0 Safari/537.36"
            )

        return headers

    def _convert_account_to_info(self, account: "CrawlerCookiesAccount") -> AccountInfo:
        """
        将 CrawlerCookiesAccount 模型转换为 AccountInfo

        Args:
            account: 爬虫账号模型

        Returns:
            AccountInfo 格式的账号信息
        """
        # 转换 cookies
        cookies = self._convert_cookies_to_dict(account.cookies)

        # 生成请求头
        headers = self._generate_headers(account.platform_name)

        # 提取 User-Agent（如果 cookies 中有的话）
        user_agent = cookies.get("User-Agent") or headers.get("User-Agent")

        return AccountInfo(cookies=cookies, headers=headers, user_agent=user_agent, account_model=account)

    def get_account(self, platform: str) -> AccountInfo:
        """
        获取指定平台的账号信息（同步方法，内部调用异步方法）

        Args:
            platform: 平台名称

        Returns:
            AccountInfo 格式的账号信息

        Note:
            这是一个同步包装方法，内部调用异步的 get_account_async 方法。
            在可能的情况下，建议直接使用 get_account_async 方法。
        """
        import asyncio

        try:
            # 尝试在现有事件循环中运行
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # 如果事件循环正在运行，我们不能使用 run_until_complete
                # 这种情况下返回默认账号信息
                logger.warning(f"在运行中的事件循环中调用同步方法，返回默认账号信息 - platform: {platform}")
                return AccountInfo(
                    cookies={}, headers=self._generate_headers(platform), user_agent=None, account_model=None
                )
            else:
                # 事件循环存在但未运行，可以使用 run_until_complete
                result = loop.run_until_complete(self.get_account_async(platform))
                return result or AccountInfo(
                    cookies={}, headers=self._generate_headers(platform), user_agent=None, account_model=None
                )
        except RuntimeError:
            # 没有事件循环，创建新的
            result = asyncio.run(self.get_account_async(platform))
            return result or AccountInfo(
                cookies={}, headers=self._generate_headers(platform), user_agent=None, account_model=None
            )

    async def get_account_async(self, platform: str) -> Optional[AccountInfo]:
        """
        异步获取账号信息

        Args:
            platform: 平台名称

        Returns:
            AccountInfo 格式的账号信息，如果没有可用账号则返回 None
        """
        if not platform or not isinstance(platform, str):
            raise ValueError("平台名称必须是非空字符串")

        # 确保账号管理器已初始化
        await self._initialize_account_manager()

        if not self._account_manager:
            logger.warning(f"账号管理器不可用，返回默认账号信息 - platform: {platform}")
            return AccountInfo(
                cookies={}, headers=self._generate_headers(platform), user_agent=None, account_model=None
            )

        try:
            # 从账号管理器获取可用账号
            account = await self._account_manager.get_available_account(platform)

            if account:
                # 记录当前使用的账号
                self._current_accounts[platform] = account

                # 转换为 AccountInfo 格式
                account_info = self._convert_account_to_info(account)

                logger.info(f"成功获取账号 - platform: {platform}, account: {account.account_name}")
                return account_info
            else:
                logger.warning(f"没有可用账号 - platform: {platform}")
                return None

        except Exception as e:
            logger.error(f"获取账号失败 - platform: {platform}, error: {e}")
            return None

    async def get_account_stats_async(self, platform: str) -> Optional[Dict]:
        """
        异步获取账号统计信息

        Args:
            platform: 平台名称

        Returns:
            账号统计信息字典
        """
        # 确保账号管理器已初始化
        await self._initialize_account_manager()

        if not self._account_manager:
            return None

        try:
            stats = await self._account_manager.get_platform_account_stats(platform)
            logger.debug(f"获取账号统计 - platform: {platform}, stats: {stats}")
            return stats
        except Exception as e:
            logger.error(f"获取账号统计失败 - platform: {platform}, error: {e}")
            return None

    async def close(self):
        """关闭账号提供者，清理资源"""
        # 简化后的版本不再需要释放账号，只需清理记录
        try:
            # 清空当前账号记录
            self._current_accounts.clear()
            logger.info("账号提供者资源已清理")

        except Exception as e:
            logger.error(f"清理账号提供者资源失败: {e}")
