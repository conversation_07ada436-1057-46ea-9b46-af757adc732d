"""
账号提供者测试

测试 DefaultAccountProvider 的功能
"""

from unittest.mock import AsyncMock, MagicMock

import pytest

from rpc.common.providers import AccountProvider
from rpc.providers import AsyncAccountProvider, DefaultAccountProvider


@pytest.mark.unit
class TestDefaultAccountProvider:
    """DefaultAccountProvider 单元测试类"""

    def test_inheritance(self):
        """测试继承关系"""
        provider = DefaultAccountProvider()

        # 验证继承关系
        assert isinstance(provider, AccountProvider)
        assert isinstance(provider, AsyncAccountProvider)

    @pytest.mark.asyncio
    async def test_initialization_without_account_manager(self):
        """测试没有账号管理器时的初始化"""
        provider = DefaultAccountProvider()

        # 直接设置初始化状态来模拟导入失败
        provider._account_manager = None
        provider._initialized = True

        # 验证账号管理器未初始化
        assert provider._account_manager is None
        assert provider._initialized is True

    @pytest.mark.asyncio
    async def test_initialization_with_account_manager(self):
        """测试有账号管理器时的初始化"""
        provider = DefaultAccountProvider()

        # 模拟账号管理器
        mock_manager = AsyncMock()

        # 直接设置账号管理器来模拟成功初始化
        provider._account_manager = mock_manager
        provider._initialized = True

        # 验证账号管理器已初始化
        assert provider._account_manager is not None
        assert provider._initialized is True

    def test_convert_cookies_to_dict_valid_json(self):
        """测试有效 JSON cookies 转换"""
        provider = DefaultAccountProvider()

        cookies_str = '{"sessionid": "abc123", "csrftoken": "xyz789"}'
        result = provider._convert_cookies_to_dict(cookies_str)

        expected = {"sessionid": "abc123", "csrftoken": "xyz789"}
        assert result == expected

    def test_convert_cookies_to_dict_invalid_json(self):
        """测试无效 JSON cookies 转换"""
        provider = DefaultAccountProvider()

        cookies_str = "invalid json"
        result = provider._convert_cookies_to_dict(cookies_str)

        assert result == {}

    def test_convert_cookies_to_dict_empty_string(self):
        """测试空字符串 cookies 转换"""
        provider = DefaultAccountProvider()

        result = provider._convert_cookies_to_dict("")
        assert result == {}

    def test_generate_headers_douyin(self):
        """测试抖音平台请求头生成"""
        provider = DefaultAccountProvider()

        headers = provider._generate_headers("douyin")

        assert "Referer" in headers
        assert headers["Referer"] == "https://www.douyin.com/"
        assert headers["Origin"] == "https://www.douyin.com"
        assert "User-Agent" in headers

    def test_generate_headers_xiaohongshu(self):
        """测试小红书平台请求头生成"""
        provider = DefaultAccountProvider()

        headers = provider._generate_headers("xiaohongshu")

        assert "Referer" in headers
        assert headers["Referer"] == "https://www.xiaohongshu.com/"
        assert headers["Origin"] == "https://www.xiaohongshu.com"

    def test_generate_headers_custom_user_agent(self):
        """测试自定义 User-Agent"""
        provider = DefaultAccountProvider()

        custom_ua = "Custom User Agent"
        headers = provider._generate_headers("douyin", custom_ua)

        assert headers["User-Agent"] == custom_ua

    def test_convert_account_to_info(self):
        """测试账号模型转换为 AccountInfo"""
        provider = DefaultAccountProvider()

        # 模拟账号模型
        mock_account = MagicMock()
        mock_account.cookies = '{"sessionid": "test123"}'
        mock_account.platform_name = "douyin"
        mock_account.account_name = "test_account"

        result = provider._convert_account_to_info(mock_account)

        assert isinstance(result, dict)
        assert "cookies" in result
        assert "headers" in result
        assert "user_agent" in result
        assert "account_model" in result
        assert result["cookies"]["sessionid"] == "test123"
        assert result["account_model"] == mock_account

    @pytest.mark.asyncio
    async def test_get_account_async_invalid_platform(self):
        """测试异步获取账号（无效平台）"""
        provider = DefaultAccountProvider()

        with pytest.raises(ValueError):
            await provider.get_account_async("")

        with pytest.raises(ValueError):
            await provider.get_account_async(None)

    @pytest.mark.asyncio
    async def test_get_account_async_without_manager(self):
        """测试异步获取账号（没有账号管理器）"""
        provider = DefaultAccountProvider()

        # 直接设置没有账号管理器
        provider._account_manager = None
        provider._initialized = True

        result = await provider.get_account_async("douyin")

        assert result is not None
        assert result["cookies"] == {}
        assert result["account_model"] is None

    @pytest.mark.asyncio
    async def test_get_account_async_with_manager_success(self):
        """测试异步获取账号（有账号管理器，成功）"""
        provider = DefaultAccountProvider()

        # 模拟账号模型
        mock_account = MagicMock()
        mock_account.id = 1
        mock_account.account_name = "test_account"
        mock_account.platform_name = "douyin"
        mock_account.cookies = '{"sessionid": "test123"}'

        # 模拟账号管理器
        mock_manager = AsyncMock()
        mock_manager.get_available_account.return_value = mock_account

        # 直接设置账号管理器
        provider._account_manager = mock_manager
        provider._initialized = True

        result = await provider.get_account_async("douyin")

        assert result is not None
        assert result["cookies"]["sessionid"] == "test123"
        assert result["account_model"] == mock_account
        assert "douyin" in provider._current_accounts

    @pytest.mark.asyncio
    async def test_get_account_async_with_manager_no_account(self):
        """测试异步获取账号（有账号管理器，无可用账号）"""
        provider = DefaultAccountProvider()

        # 模拟账号管理器返回 None
        mock_manager = AsyncMock()
        mock_manager.get_available_account.return_value = None

        # 直接设置账号管理器
        provider._account_manager = mock_manager
        provider._initialized = True

        result = await provider.get_account_async("douyin")

        assert result is None

    @pytest.mark.asyncio
    async def test_get_account_async_with_manager_exception(self):
        """测试异步获取账号（账号管理器抛出异常）"""
        provider = DefaultAccountProvider()

        # 模拟账号管理器抛出异常
        mock_manager = AsyncMock()
        mock_manager.get_available_account.side_effect = Exception("Database error")

        # 直接设置账号管理器
        provider._account_manager = mock_manager
        provider._initialized = True

        result = await provider.get_account_async("douyin")

        assert result is None

    @pytest.mark.asyncio
    async def test_get_account_stats_async(self):
        """测试异步获取账号统计"""
        provider = DefaultAccountProvider()

        # 模拟统计数据
        mock_stats = {
            "platform_name": "douyin",
            "total_count": 10,
            "available_count": 5,
            "in_use_count": 3,
            "unavailable_count": 2,
        }

        # 模拟账号管理器
        mock_manager = AsyncMock()
        mock_manager.get_platform_account_stats.return_value = mock_stats

        # 直接设置账号管理器
        provider._account_manager = mock_manager
        provider._initialized = True

        result = await provider.get_account_stats_async("douyin")

        assert result == mock_stats

    @pytest.mark.asyncio
    async def test_close(self):
        """测试关闭资源"""
        provider = DefaultAccountProvider()

        # 模拟当前使用的账号
        mock_account1 = MagicMock()
        mock_account1.id = 1
        mock_account1.account_name = "account1"

        mock_account2 = MagicMock()
        mock_account2.id = 2
        mock_account2.account_name = "account2"

        provider._current_accounts = {"douyin": mock_account1, "xiaohongshu": mock_account2}

        await provider.close()

        # 验证清空了当前账号记录（简化后的版本不再需要释放账号）
        assert len(provider._current_accounts) == 0


@pytest.mark.integration
class TestDefaultAccountProviderIntegration:
    """DefaultAccountProvider 集成测试类"""

    @pytest.mark.asyncio
    async def test_full_workflow_without_real_manager(self):
        """测试完整工作流程（不使用真实账号管理器）"""
        provider = DefaultAccountProvider()

        try:
            # 模拟账号模型
            mock_account = MagicMock()
            mock_account.id = 1
            mock_account.account_name = "test_account"
            mock_account.platform_name = "douyin"
            mock_account.cookies = '{"sessionid": "test123", "csrftoken": "abc456"}'

            # 模拟账号管理器
            mock_manager = AsyncMock()
            mock_manager.get_available_account.return_value = mock_account
            mock_manager.get_platform_account_stats.return_value = {
                "platform_name": "douyin",
                "total_count": 5,
                "available_count": 3,
            }

            # 直接设置账号管理器
            provider._account_manager = mock_manager
            provider._initialized = True

            # 1. 获取账号
            account_info = await provider.get_account_async("douyin")
            assert account_info is not None
            assert account_info["cookies"]["sessionid"] == "test123"
            assert account_info["account_model"] == mock_account

            # 2. 获取统计信息
            stats = await provider.get_account_stats_async("douyin")
            assert stats["total_count"] == 5

            # 3. 简化后的版本不再需要释放账号
            # 账号可以被多个客户端共享使用

        finally:
            # 4. 清理资源
            await provider.close()


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
