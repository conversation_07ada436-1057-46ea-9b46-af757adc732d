# RPC 提供者模块

本模块包含各种 RPC 服务的提供者实现，提供统一的接口和实现，便于在不同的 RPC 客户端中使用。

## 📁 模块结构

```
rpc/providers/
├── __init__.py              # 模块导出
├── proxy_provider.py        # 代理提供者实现
├── account_provider.py      # 账号提供者实现
├── tests/                   # 测试文件
│   └── test_account_provider.py
├── examples/                # 使用示例
│   └── account_provider_example.py
└── README.md               # 本文档
```

## 🔄 迁移说明

### 从 `rpc/common/providers.py` 迁移

为了更好的模块化组织，我们将代理相关的实现从 `rpc/common/providers.py` 迁移到了 `rpc/providers/` 目录：

**迁移的类：**

- `DefaultProxyProvider` - 默认代理提供者实现
- `AsyncAccountProvider` - 异步账号提供者抽象基类
- `DefaultAccountProvider` - 默认账号提供者实现

**保留在原位置的类：**

- `ProxyProvider` - 同步代理提供者抽象基类（保持向后兼容）
- `AccountProvider` - 账户提供者抽象基类（保持向后兼容）
- `RequestEnhancer` - 请求增强器抽象基类

### 向后兼容性

为了确保现有代码无需修改，我们在 `rpc/common/providers.py` 中保留了导入：

```python
# 旧的导入方式（仍然有效）
from rpc.common.providers import DefaultProxyProvider

# 新的推荐导入方式
from rpc.providers import DefaultProxyProvider
```

## 🚀 代理提供者



### DefaultProxyProvider

默认的代理提供者实现，集成了 `pkg/proxy` 模块：

**特性：**
- 🔄 使用 `pkg/proxy` 模块获取真实代理
- 🌍 支持快代理等代理提供商
- ⚡ 异步操作，高性能
- 💾 内置缓存机制（1小时）
- 🔧 环境变量配置
- 🛡️ 优雅的错误处理和回退机制

**使用方法：**

```python
from rpc.providers import DefaultProxyProvider

# 创建代理提供者
proxy_provider = DefaultProxyProvider()

try:
    # 异步获取代理
    proxy_info = await proxy_provider.get_proxy_async()
    
    if proxy_info:
        # 在 httpx 中使用代理
        async with httpx.AsyncClient(proxies=proxy_info) as client:
            response = await client.get("https://example.com")
    
finally:
    # 清理资源
    await proxy_provider.close()
```

**环境变量配置：**

```bash
# 快代理配置
export KUAIDAILI_DPS_SECRET_ID=your_secret_id
export KUAIDAILI_DPS_SIGNATURE=your_signature  # 可选
```

## 👤 账号提供者

### AsyncAccountProvider

异步账号信息提供者的抽象基类，定义了异步账号获取接口：

```python
from rpc.providers import AsyncAccountProvider

class MyAccountProvider(AsyncAccountProvider):
    async def get_account_async(self, platform: str) -> Optional[AccountInfo]:
        # 实现异步账号获取逻辑
        pass

    async def close(self) -> None:
        # 实现资源清理逻辑
        pass
```

### DefaultAccountProvider

默认的账号提供者实现，集成了 `pkg/crawler_account` 模块：

**特性：**

- 🔄 使用 `pkg/crawler_account` 模块获取真实账号
- 🌍 支持多平台账号管理（抖音、小红书等）
- ⚡ 异步操作，高性能
- 🔧 自动账号生命周期管理
- 🛡️ 优雅的错误处理和回退机制
- 📊 账号统计和监控功能

**使用方法：**

```python
from rpc.providers import DefaultAccountProvider

# 创建账号提供者
account_provider = DefaultAccountProvider()

try:
    # 异步获取账号
    account_info = await account_provider.get_account_async("douyin")

    if account_info:
        # 在 httpx 中使用账号
        async with httpx.AsyncClient(
            cookies=account_info["cookies"],
            headers=account_info["headers"]
        ) as client:
            response = await client.get("https://www.douyin.com")

        # 简化后的版本不再需要释放账号
        # 账号可以被多个客户端共享使用

finally:
    # 清理资源
    await account_provider.close()
```

## 🧪 测试

### 运行测试

```bash
# 运行所有代理提供者测试
python rpc/common/tests/test_proxy_provider.py

# 或使用 pytest
pytest rpc/common/tests/test_proxy_provider.py -v
```

### 运行示例

```bash
# 运行代理提供者示例
python rpc/common/examples/proxy_provider_example.py
```

## 📊 测试覆盖

- ✅ 继承关系验证
- ✅ 环境变量配置测试
- ✅ 代理池初始化测试
- ✅ 异步代理获取测试
- ✅ 错误处理测试
- ✅ 资源清理测试
- ✅ 向后兼容性测试
- ✅ 完整工作流程集成测试

## 🔧 架构设计

```mermaid
classDiagram
    class ProxyProvider {
        <<interface>>
        +get_proxy() Optional[Dict]
    }
    
    class DefaultProxyProvider {
        -_proxy_pool: ProxyIpPool
        -_initialized: bool
        +__init__()
        +_initialize_proxy_pool()
        +get_proxy() Optional[Dict]
        +get_proxy_async() Optional[Dict]
        +close() None
    }
    
    |> ProxyProvider
    |> "pkg.proxy"
```

## 🎯 优势

1. **模块化组织**：代理相关实现独立成模块
2. **向后兼容**：现有代码无需修改导入路径
3. **功能增强**：集成 `pkg/proxy` 模块获取真实代理
4. **异步支持**：提供完整的异步接口
5. **错误容忍**：优雅处理各种异常情况
6. **易于扩展**：便于添加新的代理提供商
7. **完整测试**：全面的单元测试和集成测试

## 🔮 未来扩展

1. **多提供商支持**：可以扩展支持更多代理提供商
2. **负载均衡**：在多个代理提供商之间进行负载均衡
3. **健康检查**：定期检查代理的可用性
4. **统计监控**：添加代理使用统计和监控功能
5. **配置文件**：支持从配置文件读取代理设置

## 📚 相关文档

- [pkg/proxy 模块文档](../../pkg/proxy/README.md)
- [代理提供者测试](../common/tests/test_proxy_provider.py)
- [使用示例](../common/examples/proxy_provider_example.py)
- [RPC 通用提供者](../common/providers.py)
