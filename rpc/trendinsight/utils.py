# -*- coding: utf-8 -*-
"""
TrendInsight 客户端工具类

提供签名客户端等工具功能
"""

import time
from typing import Any, Dict, Optional

from loguru import logger

from .schemas import TrendInsightSignRequest, TrendInsightSignResponse
from .signer import TrendInsightSignFactory, TrendInsightSignLogic


class TrendInsightSignClient:
    """
    TrendInsight 签名客户端

    使用 TrendInsight 专用的签名算法
    """

    def __init__(
        self,
        use_javascript: bool = True,
        js_file_path: Optional[str] = None,
        enable_cache: bool = True,
        cache_ttl: int = 300,
        max_retries: int = 3,
        retry_delay: float = 1.0,
    ):
        """
        初始化签名客户端

        Args:
            use_javascript: 是否使用 JavaScript 签名
            js_file_path: JavaScript 文件路径
            enable_cache: 是否启用缓存
            cache_ttl: 缓存过期时间（秒）
            max_retries: 最大重试次数
            retry_delay: 重试延迟（秒）
        """
        if not use_javascript:
            logger.warning("强制使用 JavaScript 签名")
            use_javascript = True

        self.use_javascript = use_javascript
        self.enable_cache = enable_cache
        self.cache_ttl = cache_ttl
        self.max_retries = max_retries
        self.retry_delay = retry_delay

        # 创建 TrendInsight 专用签名逻辑实例
        kwargs = {}
        if js_file_path:
            kwargs["js_file_path"] = js_file_path

        self._sign_logic = TrendInsightSignLogic(sign_type=TrendInsightSignFactory.JAVASCRIPT_SIGN, **kwargs)

        # 统计信息
        self._stats = {
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "cache_hits": 0,
            "cache_misses": 0,
            "last_request_time": None,
        }

        # 简单的内存缓存
        self._cache = {}

        logger.info("TrendInsightSignClient 初始化完成，使用 JavaScript 签名")

    async def sign(
        self, uri: str, params: Optional[Dict[str, Any]] = None, cookies: str = ""
    ) -> TrendInsightSignResponse:
        """
        生成签名

        Args:
            uri: 请求的 URI
            params: 请求参数字典
            cookies: Cookies 字符串

        Returns:
            TrendInsight 签名响应（包含 x_bogus, signature, ms_token）
        """
        self._stats["total_requests"] += 1
        self._stats["last_request_time"] = time.time()

        try:
            # 检查缓存
            cache_key = f"{uri}:{params}:{cookies}"
            if self.enable_cache and cache_key in self._cache:
                cached_data = self._cache[cache_key]
                if time.time() - cached_data["timestamp"] < self.cache_ttl:
                    self._stats["cache_hits"] += 1
                    logger.debug(f"签名缓存命中: {uri}")
                    return cached_data["signature"]
                else:
                    # 缓存过期，删除
                    del self._cache[cache_key]

            self._stats["cache_misses"] += 1

            # 构建签名请求
            sign_req = TrendInsightSignRequest(uri=uri, params=params or {}, cookies=cookies)

            # 生成签名（使用 TrendInsight 专用签名逻辑）
            signature_response = await self._sign_logic.sign(sign_req)

            # 缓存结果
            if self.enable_cache:
                self._cache[cache_key] = {"signature": signature_response, "timestamp": time.time()}

            self._stats["successful_requests"] += 1
            logger.debug(f"签名生成成功: {uri}")
            return signature_response

        except Exception as e:
            self._stats["failed_requests"] += 1
            logger.error(f"签名生成失败: {uri}, 错误: {e}")
            raise

    async def trendinsight_sign(self, sign_req: TrendInsightSignRequest) -> TrendInsightSignResponse:
        """
        TrendInsight 签名接口

        Args:
            sign_req: 签名请求参数

        Returns:
            签名响应
        """
        # 处理参数类型
        params = sign_req.params
        if isinstance(params, str):
            # 如果是字符串，尝试解析为字典
            try:
                import json

                params = json.loads(params)
            except (json.JSONDecodeError, TypeError):
                params = {}
        elif params is None:
            params = {}

        return await self.sign(uri=sign_req.uri, params=params, cookies=sign_req.cookies)

    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return self._stats.copy()

    def clear_cache(self):
        """清空缓存"""
        self._cache.clear()
        logger.info("签名缓存已清空")

    def cleanup_expired_cache(self):
        """清理过期缓存"""
        current_time = time.time()
        expired_keys = []

        for key, data in self._cache.items():
            if current_time - data["timestamp"] >= self.cache_ttl:
                expired_keys.append(key)

        for key in expired_keys:
            del self._cache[key]

        if expired_keys:
            logger.debug(f"清理了 {len(expired_keys)} 个过期缓存项")


# TrendInsight 专用签名客户端，不提供 Douyin 兼容性别名
