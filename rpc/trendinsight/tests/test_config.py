"""
TrendInsight 配置类测试
"""

import os
from unittest.mock import patch

from rpc.trendinsight.config import TrendInsightConfig


class TestTrendInsightConfig:
    """TrendInsight 配置类测试"""

    def test_default_config(self):
        """测试默认配置"""
        config = TrendInsightConfig()

        assert config.base_url == "https://trendinsight.oceanengine.com"
        assert config.timeout == 30.0
        assert config.max_retries == 3
        assert config.retry_delay == 1.0
        assert config.verify_ssl is True
        assert config.follow_redirects is True
        assert config.use_javascript_sign is True
        assert config.sign_cache_enabled is True
        assert config.sign_cache_ttl == 300
        assert config.sign_max_retries == 3
        assert config.sign_retry_delay == 1.0

        # 检查默认请求头
        assert config.default_headers is not None
        assert "user-agent" in config.default_headers
        assert "accept" in config.default_headers
        assert "content-type" in config.default_headers

    def test_config_from_dynaconf_dict(self):
        """测试从 dynaconf 字典加载配置"""
        from dynaconf import Dynaconf

        settings = Dynaconf(
            settings_module=False,
            load_dotenv=False,
            TRENDINSIGHT={
                "base_url": "https://test.example.com",
                "timeout": 60.0,
                "max_retries": 5,
                "verify_ssl": False,
                "use_javascript_sign": False,
                "sign_cache_ttl": 600,
            },
        )

        with patch("rpc.trendinsight.config.settings", settings):
            config = TrendInsightConfig.from_dynaconf()

            assert str(config.base_url) == "https://test.example.com/"
            assert config.timeout == 60.0
            assert config.max_retries == 5
            assert config.verify_ssl is False
            assert config.use_javascript_sign is False
            assert config.sign_cache_ttl == 600

    def test_config_from_env_fallback(self):
        """测试从环境变量加载配置作为后备"""
        from dynaconf import Dynaconf

        settings = Dynaconf(settings_module=False, load_dotenv=False)
        settings.set("TRENDINSIGHT_BASE_URL", "https://env.example.com")
        settings.set("TRENDINSIGHT_TIMEOUT", 70.0)

        with patch("rpc.trendinsight.config.settings", settings):
            config = TrendInsightConfig.from_dynaconf()
            assert str(config.base_url) == "https://env.example.com/"
            assert config.timeout == 70.0

    def test_config_to_dict(self):
        """测试配置转换为字典"""
        config = TrendInsightConfig()
        config_dict = config.to_dict()

        assert isinstance(config_dict, dict)
        assert "base_url" in config_dict
        assert "timeout" in config_dict
        assert "max_retries" in config_dict
        assert "default_headers" in config_dict

        # 检查默认请求头是否被复制
        assert isinstance(config_dict["default_headers"], dict)
        assert config_dict["default_headers"] is not config.default_headers

    def test_invalid_env_values(self):
        """测试无效的环境变量值"""
        # 设置无效的环境变量
        os.environ["TRENDINSIGHT_TIMEOUT"] = "invalid"
        os.environ["TRENDINSIGHT_MAX_RETRIES"] = "not_a_number"

        try:
            config = TrendInsightConfig.from_dynaconf()

            # 应该使用默认值
            assert config.timeout == 30.0
            assert config.max_retries == 3

        finally:
            # 清理环境变量
            for key in ["TRENDINSIGHT_TIMEOUT", "TRENDINSIGHT_MAX_RETRIES"]:
                if key in os.environ:
                    del os.environ[key]

    def test_boolean_env_values(self):
        """测试布尔类型环境变量值"""
        from dynaconf import Dynaconf

        test_cases = [
            ("true", True),
            ("1", True),
            ("yes", True),
            ("on", True),
            ("false", False),
            ("0", False),
            ("no", False),
            ("off", False),
        ]

        for env_value, expected in test_cases:
            settings = Dynaconf(settings_module=False, load_dotenv=False)
            settings.set("TRENDINSIGHT_VERIFY_SSL", env_value)

            with patch("rpc.trendinsight.config.settings", settings):
                config = TrendInsightConfig.from_dynaconf()
                assert config.verify_ssl == expected, f"环境变量值 '{env_value}' 应该解析为 {expected}"

    def test_js_file_path_env(self):
        """测试 JavaScript 文件路径环境变量"""
        from dynaconf import Dynaconf

        settings = Dynaconf(settings_module=False, load_dotenv=False)
        test_path = "/path/to/test.js"
        settings.set("TRENDINSIGHT_SIGN_JS_FILE_PATH", test_path)

        with patch("rpc.trendinsight.config.settings", settings):
            config = TrendInsightConfig.from_dynaconf()
            assert config.sign_js_file_path == test_path

    def test_config_immutability(self):
        """测试配置的不可变性"""
        config = TrendInsightConfig()
        original_headers = config.default_headers.copy()

        # 修改返回的字典不应该影响原始配置
        config_dict = config.to_dict()
        config_dict["default_headers"]["test"] = "value"

        assert config.default_headers == original_headers
        assert "test" not in config.default_headers
