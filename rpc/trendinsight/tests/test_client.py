"""
TrendInsight 客户端测试
"""

import httpx
import pytest

from rpc.trendinsight.client import TrendInsightClientManager, client_manager
from rpc.trendinsight.config import TrendInsightConfig


@pytest.mark.integration
class TestClientCreation:
    """客户端创建测试"""

    @pytest.mark.asyncio
    async def test_create_async_client_default(self):
        """测试创建默认异步客户端"""
        client = client_manager.create_async_client()

        assert isinstance(client, httpx.AsyncClient)
        assert client.timeout.connect == 30.0  # 默认超时
        assert client.is_closed is False

        # 清理
        await client_manager.close_async_client(client)

    @pytest.mark.asyncio
    async def test_create_async_client_with_config(self):
        """测试使用自定义配置创建异步客户端"""
        config = TrendInsightConfig()
        config.timeout = 60.0
        config.follow_redirects = False

        client = client_manager.create_async_client(config=config)

        assert isinstance(client, httpx.AsyncClient)
        assert client.timeout.connect == 60.0
        assert client.follow_redirects is False

        # 清理
        await client_manager.close_async_client(client)


@pytest.mark.integration
class TestTrendInsightClientManager:
    """TrendInsight 客户端管理器测试"""

    def test_manager_initialization(self):
        """测试管理器初始化"""
        manager = TrendInsightClientManager()

        assert hasattr(manager, "_clients")
        assert hasattr(manager, "_async_clients")
        assert manager._clients == {}
        assert manager._async_clients == {}

    @pytest.mark.asyncio
    async def test_create_async_client(self):
        """测试创建异步客户端"""
        manager = TrendInsightClientManager()

        # 创建客户端
        client = manager.create_async_client()
        assert isinstance(client, httpx.AsyncClient)

        # 检查客户端是否被缓存
        client_id = id(client)
        assert client_id in manager._async_clients

        # 清理
        await manager.close_async_client(client)

    @pytest.mark.asyncio
    async def test_close_async_client(self):
        """测试关闭异步客户端"""
        manager = TrendInsightClientManager()

        # 创建客户端
        client = manager.create_async_client()
        client_id = id(client)
        assert client_id in manager._async_clients
        assert client.is_closed is False

        # 关闭客户端
        await manager.close_async_client(client)
        assert client_id not in manager._async_clients
        assert client.is_closed is True

    @pytest.mark.asyncio
    async def test_close_all(self):
        """测试关闭所有客户端"""
        manager = TrendInsightClientManager()

        # 创建多个异步客户端
        client1 = manager.create_async_client()
        client2 = manager.create_async_client()

        # 关闭所有客户端
        await manager.close_all()

        assert len(manager._async_clients) == 0
        assert client1.is_closed is True
        assert client2.is_closed is True


@pytest.mark.integration
class TestDefaultClients:
    """默认客户端测试"""

    def test_default_client_manager_exists(self):
        """测试默认客户端管理器存在"""
        assert client_manager is not None
        assert isinstance(client_manager, TrendInsightClientManager)

    @pytest.mark.asyncio
    async def test_client_manager_can_create_clients(self):
        """测试客户端管理器可以创建客户端"""
        # 测试创建异步客户端
        async_client = client_manager.create_async_client()
        assert isinstance(async_client, httpx.AsyncClient)

        # 清理
        await client_manager.close_async_client(async_client)


@pytest.mark.integration
@pytest.mark.slow
class TestClientTransport:
    """客户端传输层测试"""

    @pytest.mark.asyncio
    async def test_client_has_dynamic_transport(self):
        """测试客户端具有动态传输层"""
        client = client_manager.create_async_client()

        # 检查传输层类型
        from rpc.common import DynamicProxyTransport

        assert isinstance(client._transport, DynamicProxyTransport)

        # 清理
        await client_manager.close_async_client(client)

    @pytest.mark.asyncio
    async def test_client_transport_platform(self):
        """测试客户端传输层平台设置"""
        client = client_manager.create_async_client()

        from models.enums import Platform

        assert client._transport.platform == Platform.TRENDINSIGHT

        # 清理
        await client_manager.close_async_client(client)

    @pytest.mark.asyncio
    async def test_client_transport_components(self):
        """测试客户端传输层组件"""
        client = client_manager.create_async_client()

        transport = client._transport

        # 检查组件存在
        assert transport.account_provider is not None
        assert transport.proxy_provider is not None
        assert transport.request_enhancer is not None

        # 检查增强器类型
        from rpc.trendinsight.enhancer import TrendInsightRequestEnhancer

        assert isinstance(transport.request_enhancer, TrendInsightRequestEnhancer)

        # 清理
        await client_manager.close_async_client(client)
