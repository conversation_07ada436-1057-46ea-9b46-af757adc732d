# TrendInsight 测试 Pytest 标记说明

## 📋 概述

为 `rpc/trendinsight_new/tests/test_client.py` 文件中的测试类和测试方法添加了适当的 pytest 标记，以便更好地组织和运行测试。

## 🏷️ 添加的标记

### 类级别标记

| 测试类 | 标记 | 说明 |
|--------|------|------|
| `TestClientCreation` | `@pytest.mark.integration` | 客户端创建测试，需要外部依赖 |
| `TestTrendInsightClientManager` | `@pytest.mark.integration` | 客户端管理器测试，需要外部依赖 |
| `TestDefaultClients` | `@pytest.mark.integration` | 默认客户端测试，需要外部依赖 |
| `TestClientTransport` | `@pytest.mark.integration`<br>`@pytest.mark.slow` | 传输层测试，需要外部依赖且可能较慢 |

### 方法级别标记

| 测试方法 | 标记 | 说明 |
|----------|------|------|
| `test_create_async_douyin_client_default` | `@pytest.mark.asyncio` | 异步客户端创建测试 |
| `test_create_async_douyin_client_with_config` | `@pytest.mark.asyncio` | 异步客户端配置测试 |
| `test_get_async_client_singleton` | `@pytest.mark.asyncio` | 异步客户端单例测试 |
| `test_close_async_client` | `@pytest.mark.asyncio` | 异步客户端关闭测试 |
| `test_close_all_with_async_warning` | `@pytest.mark.asyncio` | 异步客户端警告测试 |

## 🎯 标记说明

### `@pytest.mark.integration`
- **用途**: 标记需要外部依赖的集成测试
- **特点**: 这些测试会创建真实的客户端实例，涉及传输层、增强器等组件
- **运行**: 可以单独运行或跳过这些测试

### `@pytest.mark.asyncio`
- **用途**: 标记异步测试方法
- **特点**: 这些测试使用 `async def` 定义，需要异步运行时支持
- **依赖**: 需要安装 `pytest-asyncio` 插件

### `@pytest.mark.slow`
- **用途**: 标记可能较慢的测试
- **特点**: 这些测试可能涉及复杂的组件初始化或网络操作
- **运行**: 可以在快速测试时跳过这些测试

## 🔧 运行测试命令

### 基本运行
```bash
# 运行所有测试
pytest rpc/trendinsight_new/tests/test_client.py

# 运行测试并显示详细输出
pytest rpc/trendinsight_new/tests/test_client.py -v
```

### 按标记运行
```bash
# 只运行集成测试
pytest rpc/trendinsight_new/tests/test_client.py -m integration

# 只运行异步测试
pytest rpc/trendinsight_new/tests/test_client.py -m asyncio

# 跳过慢速测试
pytest rpc/trendinsight_new/tests/test_client.py -m "not slow"

# 运行集成测试但跳过慢速测试
pytest rpc/trendinsight_new/tests/test_client.py -m "integration and not slow"
```

### 组合条件
```bash
# 运行所有非慢速的集成测试
pytest rpc/trendinsight_new/tests/test_client.py -m "integration and not slow"

# 运行异步集成测试
pytest rpc/trendinsight_new/tests/test_client.py -m "asyncio and integration"
```

## 📊 标记统计

- **总测试类**: 4个
- **集成测试类**: 4个 (100%)
- **慢速测试类**: 1个 (25%)
- **总测试方法**: 15个
- **异步测试方法**: 5个 (33%)

## 🔄 与项目标记的一致性

添加的标记与项目中其他测试文件保持一致：

### 参考文件
- `rpc/trendinsight_new/integration_tests/test_real_api.py`
  - 使用 `@pytest.mark.real_api` 和 `@pytest.mark.ci_skip`
- `rpc/trendinsight_new/integration_tests/test_client_integration.py`
  - 使用 `@pytest.mark.integration`

### 标记约定
- `integration`: 需要外部依赖的测试
- `asyncio`: 异步测试方法
- `slow`: 可能较慢的测试
- `real_api`: 真实API调用测试
- `ci_skip`: CI环境中跳过的测试

## 🛠️ 配置建议

### pytest.ini 配置
可以在项目根目录的 `pytest.ini` 文件中添加标记定义：

```ini
[tool:pytest]
markers =
    integration: marks tests as integration tests (may require external dependencies)
    slow: marks tests as slow (may take longer to run)
    asyncio: marks tests as async tests
    real_api: marks tests that make real API calls
    ci_skip: marks tests to skip in CI environment
    network: marks tests that require network connectivity
```

### 测试运行策略
- **开发阶段**: 运行所有测试确保功能正确
- **快速验证**: 跳过慢速测试 `-m "not slow"`
- **CI环境**: 跳过真实API测试 `-m "not real_api and not ci_skip"`
- **集成验证**: 只运行集成测试 `-m integration`

## ✅ 验证

使用提供的验证脚本确认标记正确添加：

```bash
# 运行验证脚本
python rpc/trendinsight_new/tests/verify_markers.py
```

验证结果显示所有标记都已正确添加，符合项目的测试策略和标记约定。
