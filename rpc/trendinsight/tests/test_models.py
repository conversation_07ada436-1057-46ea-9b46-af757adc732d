"""
TrendInsight 模型测试
"""

import pytest
from pydantic import ValidationError

from rpc.trendinsight.schemas import (
    AuthorDetailRequest,
    DarenSearchRequest,
    DarenSearchResponse,
    DarenUserInfo,
    DateType,
    SearchChannelType,
    SearchSortType,
    TrendInsightSignRequest,
    TrendInsightSignResponse,
    UserInfo,
    UserInfoResponse,
    VideoIndexRequest,
    VideoInfo,
    VideoSearchRequest,
)


class TestSignModels:
    """签名相关模型测试"""

    def test_sign_request_valid(self):
        """测试有效的签名请求"""
        request = TrendInsightSignRequest(uri="/api/test", params={"key": "value"}, cookies="session=123")

        assert request.uri == "/api/test"
        assert request.params == {"key": "value"}
        assert request.cookies == "session=123"

    def test_sign_request_minimal(self):
        """测试最小签名请求"""
        request = TrendInsightSignRequest(uri="/api/test", cookies="session=123")

        assert request.uri == "/api/test"
        assert request.params is None
        assert request.cookies == "session=123"

    def test_sign_response_valid(self):
        """测试有效的签名响应"""
        response = TrendInsightSignResponse(
            x_bogus="test_x_bogus", signature="test_signature", ms_token="test_ms_token"
        )

        assert response.x_bogus == "test_x_bogus"
        assert response.signature == "test_signature"
        assert response.ms_token == "test_ms_token"

    def test_sign_response_missing_fields(self):
        """测试缺少必填字段的签名响应"""
        with pytest.raises(ValidationError):
            TrendInsightSignResponse(x_bogus="test")


class TestEnumTypes:
    """枚举类型测试"""

    def test_search_channel_type(self):
        """测试搜索频道类型枚举"""
        assert SearchChannelType.GENERAL.value == "aweme_general"
        assert SearchChannelType.VIDEO.value == "aweme_video_web"
        assert SearchChannelType.USER.value == "aweme_user_web"
        assert SearchChannelType.LIVE.value == "aweme_live"

    def test_search_sort_type(self):
        """测试搜索排序类型枚举"""
        assert SearchSortType.GENERAL.value == 0
        assert SearchSortType.MOST_LIKE.value == 1
        assert SearchSortType.LATEST.value == 2

    def test_date_type(self):
        """测试日期类型枚举"""
        assert DateType.ALL.value == 0
        assert DateType.RECENT_7_DAYS.value == 1
        assert DateType.RECENT_30_DAYS.value == 2
        assert DateType.RECENT_90_DAYS.value == 3


class TestUserModels:
    """用户相关模型测试"""

    def test_user_info_valid(self):
        """测试有效的用户信息"""
        user_info = UserInfo(
            user_id=123456, screen_name="测试用户", name="Test User", avatar_url="https://example.com/avatar.jpg"
        )

        assert user_info.user_id == 123456
        assert user_info.screen_name == "测试用户"
        assert user_info.name == "Test User"
        assert user_info.avatar_url == "https://example.com/avatar.jpg"

    def test_user_info_nickname_property(self):
        """测试用户信息昵称属性"""
        # 测试 screen_name 优先
        user_info = UserInfo(screen_name="屏幕名", name="姓名")
        assert user_info.nickname == "屏幕名"

        # 测试 name 作为备选
        user_info = UserInfo(name="姓名")
        assert user_info.nickname == "姓名"

        # 测试都为空
        user_info = UserInfo()
        assert user_info.nickname is None

    def test_user_info_response(self):
        """测试用户信息响应"""
        user_data = {"user_id": 123456, "screen_name": "测试用户"}

        response = UserInfoResponse(data=UserInfo(**user_data), message="success")

        assert response.data.user_id == 123456
        assert response.data.screen_name == "测试用户"
        assert response.message == "success"

    def test_user_info_extra_fields(self):
        """测试用户信息额外字段"""
        user_info = UserInfo(user_id=123456, extra_field="extra_value")  # 额外字段应该被允许

        assert user_info.user_id == 123456
        # 额外字段应该可以访问
        assert hasattr(user_info, "extra_field")


class TestDarenModels:
    """达人相关模型测试"""

    def test_daren_search_request(self):
        """测试达人搜索请求"""
        request = DarenSearchRequest(keyword="测试关键词", total=50)

        assert request.keyword == "测试关键词"
        assert request.total == 50

    def test_daren_search_request_default(self):
        """测试达人搜索请求默认值"""
        request = DarenSearchRequest(keyword="测试")
        assert request.total == 30  # 默认值

    def test_daren_user_info(self):
        """测试达人用户信息"""
        daren_info = DarenUserInfo(
            user_id="123456",
            user_name="测试达人",
            item_count="100",
            follow_count="1000",
            like_count="10000",
            aweme_url="https://www.douyin.com/user/MS4wLjABAAAAtest",
        )

        assert daren_info.user_id == "123456"
        assert daren_info.user_name == "测试达人"
        assert daren_info.item_count == "100"

    def test_daren_user_info_properties(self):
        """测试达人用户信息属性"""
        daren_info = DarenUserInfo(
            user_name="测试达人",
            user_head_logo="https://example.com/avatar.jpg",
            follow_count="1000",
            item_count="100",
            aweme_url="https://www.douyin.com/user/MS4wLjABAAAAtest123",
        )

        # 测试属性别名
        assert daren_info.nickname == "测试达人"
        assert daren_info.avatar_url == "https://example.com/avatar.jpg"
        assert daren_info.follower_count == 1000
        assert daren_info.aweme_count == 100
        assert daren_info.sec_user_id == "MS4wLjABAAAAtest123"

    def test_daren_user_info_invalid_counts(self):
        """测试达人用户信息无效计数"""
        daren_info = DarenUserInfo(follow_count="invalid", item_count="not_a_number")

        # 无效值应该返回 None
        assert daren_info.follower_count is None
        assert daren_info.aweme_count is None

    def test_daren_search_response(self):
        """测试达人搜索响应"""
        response_data = {
            "data": {
                "userlist": [{"user_id": "123", "user_name": "达人1"}],
                "BaseResp": {"StatusCode": 0, "StatusMessage": "success"},
            },
            "status": 0,
            "msg": "success",
        }

        response = DarenSearchResponse(**response_data)

        assert response.status == 0
        assert response.msg == "success"
        assert len(response.userlist) == 1
        assert response.userlist[0].user_id == "123"
        assert response.StatusCode == 0
        assert response.StatusMsg == "success"


class TestVideoModels:
    """视频相关模型测试"""

    def test_video_search_request(self):
        """测试视频搜索请求"""
        request = VideoSearchRequest(
            keyword="测试视频", author_ids=["123", "456"], category_id="1", date_type=1, label_type=1, duration_type=1
        )

        assert request.keyword == "测试视频"
        assert request.author_ids == ["123", "456"]
        assert request.category_id == "1"
        assert request.date_type == 1

    def test_video_search_request_defaults(self):
        """测试视频搜索请求默认值"""
        request = VideoSearchRequest(keyword="测试")

        assert request.author_ids is None
        assert request.category_id == "0"
        assert request.date_type == 0
        assert request.label_type == 0
        assert request.duration_type == 0

    def test_video_info(self):
        """测试视频信息"""
        video_info = VideoInfo(
            item_id="video123", title="测试视频标题", author_name="作者名", play_count="1000", like_count="100"
        )

        assert video_info.item_id == "video123"
        assert video_info.title == "测试视频标题"
        assert video_info.author_name == "作者名"
        assert video_info.play_count == "1000"

    def test_author_detail_request(self):
        """测试作者详情请求"""
        request = AuthorDetailRequest(user_id="123456")
        assert request.user_id == "123456"

    def test_video_index_request(self):
        """测试视频指数请求"""
        request = VideoIndexRequest(item_id="video123", start_date="20240101", end_date="20240131")

        assert request.item_id == "video123"
        assert request.start_date == "20240101"
        assert request.end_date == "20240131"
