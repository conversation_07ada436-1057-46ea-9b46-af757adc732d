#!/usr/bin/env python3
"""
简化的 pytest 标记验证脚本
"""

import logging
import re
from pathlib import Path


def verify_markers():
    """验证 test_client.py 中的 pytest 标记"""
    logging.info("🔍 验证 test_client.py 中的 pytest 标记...")

    file_path = Path(__file__).parent / "test_client.py"

    with open(file_path, "r", encoding="utf-8") as f:
        content = f.read()

    # 检查类级别标记
    class_markers = {
        "TestClientCreation": ["integration"],
        "TestTrendInsightClientManager": ["integration"],
        "TestDefaultClients": ["integration"],
        "TestClientTransport": ["integration", "slow"],
    }

    # 检查异步方法标记
    async_methods = [
        "test_create_async_trendinsight_client_default",
        "test_create_async_trendinsight_client_with_config",
        "test_get_async_client_singleton",
        "test_close_async_client",
        "test_close_all_with_async_warning",
    ]

    success = True

    # 验证类标记
    for class_name, expected_markers in class_markers.items():
        class_pattern = rf"class {class_name}:"
        class_match = re.search(class_pattern, content)

        if not class_match:
            logging.error(f"❌ 类 {class_name} 未找到")
            success = False
            continue

        # 检查类前面的标记
        class_start = class_match.start()
        lines_before_class = content[:class_start].split("\n")

        # 获取类定义前的几行
        marker_lines = []
        for line in reversed(lines_before_class[-10:]):  # 检查前10行
            line = line.strip()
            if line.startswith("@pytest.mark."):
                marker_lines.append(line)
            elif line and not line.startswith("#"):
                break

        found_markers = []
        for line in marker_lines:
            match = re.search(r"@pytest\.mark\.(\w+)", line)
            if match:
                found_markers.append(match.group(1))

        if set(found_markers) == set(expected_markers):
            logging.info(f"✅ 类 {class_name} 标记正确: {found_markers}")
        else:
            logging.error(f"❌ 类 {class_name} 标记不匹配:")
            logging.error(f"   期望: {expected_markers}")
            logging.error(f"   实际: {found_markers}")
            success = False

    # 验证异步方法标记
    for method_name in async_methods:
        method_pattern = rf"async def {method_name}\("
        method_match = re.search(method_pattern, content)

        if not method_match:
            logging.error(f"❌ 异步方法 {method_name} 未找到")
            success = False
            continue

        # 检查方法前面是否有 @pytest.mark.asyncio
        method_start = method_match.start()
        lines_before_method = content[:method_start].split("\n")

        has_asyncio_marker = False
        for line in reversed(lines_before_method[-5:]):  # 检查前5行
            if "@pytest.mark.asyncio" in line:
                has_asyncio_marker = True
                break

        if has_asyncio_marker:
            logging.info(f"✅ 异步方法 {method_name} 有 @pytest.mark.asyncio 标记")
        else:
            logging.error(f"❌ 异步方法 {method_name} 缺少 @pytest.mark.asyncio 标记")
            success = False

    return success


def main():
    """主函数"""
    logging.info("🚀 pytest 标记验证")
    logging.info("=" * 50)

    success = verify_markers()

    if success:
        logging.info("\n🎉 所有 pytest 标记都正确添加！")
        logging.info("\n📋 标记总结:")
        logging.info("  类级别标记:")
        logging.info("    - TestClientCreation: @pytest.mark.integration")
        logging.info("    - TestTrendInsightClientManager: @pytest.mark.integration")
        logging.info("    - TestDefaultClients: @pytest.mark.integration")
        logging.info("    - TestClientTransport: @pytest.mark.integration, @pytest.mark.slow")
        logging.info("\n  方法级别标记:")
        logging.info("    - 5个异步方法都有 @pytest.mark.asyncio 标记")

        logging.info("\n🔧 运行测试的命令示例:")
        logging.info("  # 运行所有测试")
        logging.info("  pytest rpc/trendinsight_new/tests/test_client.py")
        logging.info("  # 只运行集成测试")
        logging.info("  pytest rpc/trendinsight_new/tests/test_client.py -m integration")
        logging.info("  # 跳过慢速测试")
        logging.info("  pytest rpc/trendinsight_new/tests/test_client.py -m 'not slow'")
        logging.info("  # 只运行异步测试")
        logging.info("  pytest rpc/trendinsight_new/tests/test_client.py -m asyncio")

        return 0
    else:
        logging.error("\n❌ 部分标记有问题，请检查上述错误信息")
        return 1


if __name__ == "__main__":
    import sys

    sys.exit(main())