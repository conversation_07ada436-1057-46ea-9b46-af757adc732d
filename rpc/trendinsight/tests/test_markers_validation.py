#!/usr/bin/env python3
"""
验证 pytest 标记是否正确添加的测试脚本
"""

import ast
import logging
from pathlib import Path


def extract_pytest_markers_from_file(file_path):
    """从文件中提取 pytest 标记"""
    with open(file_path, "r", encoding="utf-8") as f:
        content = f.read()

    tree = ast.parse(content)
    markers = {}

    for node in ast.walk(tree):
        if isinstance(node, ast.ClassDef):
            class_markers = []
            for decorator in node.decorator_list:
                if isinstance(decorator, ast.Attribute):
                    if (
                        isinstance(decorator.value, ast.Attribute)
                        and isinstance(decorator.value.value, ast.Name)
                        and decorator.value.value.id == "pytest"
                        and decorator.value.attr == "mark"
                    ):
                        class_markers.append(decorator.attr)

            method_markers = {}
            for item in node.body:
                if isinstance(item, ast.FunctionDef) and item.name.startswith("test_"):
                    method_markers_list = []
                    for decorator in item.decorator_list:
                        if isinstance(decorator, ast.Attribute):
                            if (
                                isinstance(decorator.value, ast.Attribute)
                                and isinstance(decorator.value.value, ast.Name)
                                and decorator.value.value.id == "pytest"
                                and decorator.value.attr == "mark"
                            ):
                                method_markers_list.append(decorator.attr)
                    method_markers[item.name] = method_markers_list

            markers[node.name] = {"class_markers": class_markers, "method_markers": method_markers}

    return markers


def validate_test_client_markers():
    """验证 test_client.py 中的标记"""
    logging.info("🔍 验证 test_client.py 中的 pytest 标记...")

    file_path = Path(__file__).parent / "test_client.py"
    markers = extract_pytest_markers_from_file(file_path)

    expected_structure = {
        "TestClientCreation": {
            "class_markers": ["integration"],
            "async_methods": [
                "test_create_async_trendinsight_client_default",
                "test_create_async_trendinsight_client_with_config",
            ],
        },
        "TestTrendInsightClientManager": {
            "class_markers": ["integration"],
            "async_methods": [
                "test_get_async_client_singleton",
                "test_close_async_client",
                "test_close_all_with_async_warning",
            ],
        },
        "TestDefaultClients": {"class_markers": ["integration"], "async_methods": []},
        "TestClientTransport": {"class_markers": ["integration", "slow"], "async_methods": []},
    }

    success = True

    for class_name, expected in expected_structure.items():
        if class_name not in markers:
            logging.error(f"❌ 类 {class_name} 未找到")
            success = False
            continue

        actual = markers[class_name]

        # 检查类级别标记
        if set(actual["class_markers"]) != set(expected["class_markers"]):
            logging.error(f"❌ 类 {class_name} 标记不匹配:")
            logging.error(f"   期望: {expected['class_markers']}")
            logging.error(f"   实际: {actual['class_markers']}")
            success = False
        else:
            logging.info(f"✅ 类 {class_name} 标记正确: {actual['class_markers']}")

        # 检查异步方法标记
        for method_name in expected["async_methods"]:
            if method_name not in actual["method_markers"]:
                logging.error(f"❌ 方法 {class_name}.{method_name} 未找到")
                success = False
                continue

            method_markers = actual["method_markers"][method_name]
            if "asyncio" not in method_markers:
                logging.error(f"❌ 异步方法 {class_name}.{method_name} 缺少 @pytest.mark.asyncio 标记")
                success = False
            else:
                logging.info(f"✅ 异步方法 {class_name}.{method_name} 有正确的 asyncio 标记")

    return success


def validate_marker_consistency():
    """验证标记的一致性"""
    logging.info("\n🔍 验证标记一致性...")

    # 检查是否与集成测试文件的标记一致
    integration_file = Path(__file__).parent.parent / "integration_tests" / "test_client_integration.py"

    if integration_file.exists():
        integration_markers = extract_pytest_markers_from_file(integration_file)

        # 检查集成测试是否使用了 @pytest.mark.integration
        has_integration_marker = False
        for class_name, data in integration_markers.items():
            if "integration" in data["class_markers"]:
                has_integration_marker = True
                break

        if has_integration_marker:
            logging.info("✅ 标记与集成测试文件一致")
            return True
        else:
            logging.warning("⚠️  集成测试文件中未找到 integration 标记")
            return False
    else:
        logging.warning("⚠️  集成测试文件不存在")
        return False


def main():
    """主函数"""
    logging.info("🚀 pytest 标记验证")
    logging.info("=" * 50)

    # 验证 test_client.py 标记
    client_markers_ok = validate_test_client_markers()

    # 验证标记一致性
    consistency_ok = validate_marker_consistency()

    logging.info("\n📊 验证结果:")
    logging.info(f"  test_client.py 标记: {'✅ 正确' if client_markers_ok else '❌ 有问题'}")
    logging.info(f"  标记一致性: {'✅ 一致' if consistency_ok else '⚠️  需要检查'}")

    if client_markers_ok:
        logging.info("\n🎉 所有 pytest 标记都正确添加！")
        logging.info("\n💡 标记说明:")
        logging.info("  @pytest.mark.integration - 标记需要外部依赖的集成测试")
        logging.info("  @pytest.mark.asyncio - 标记异步测试方法")
        logging.info("  @pytest.mark.slow - 标记可能较慢的测试")

        logging.info("\n🔧 运行测试的命令:")
        logging.info("  # 运行所有测试")
        logging.info("  pytest rpc/trendinsight_new/tests/test_client.py")
        logging.info("  # 只运行集成测试")
        logging.info("  pytest rpc/trendinsight_new/tests/test_client.py -m integration")
        logging.info("  # 跳过慢速测试")
        logging.info("  pytest rpc/trendinsight_new/tests/test_client.py -m 'not slow'")
        logging.info("  # 只运行异步测试")
        logging.info("  pytest rpc/trendinsight_new/tests/test_client.py -m asyncio")

        return 0
    else:
        logging.error("\n❌ 部分标记有问题，请检查上述错误信息")
        return 1


if __name__ == "__main__":
    import sys

    sys.exit(main())
