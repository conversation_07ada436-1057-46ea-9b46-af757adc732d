import json
from pathlib import Path

import pytest

from rpc.trendinsight.schemas import (
    AuthorDetailResponse,
    DarenSearchResponse,
    UserInfoResponse,
    VideoIndexResponse,
    VideoSearchResponse,
)

# 获取 mock 数据目录的绝对路径
MOCK_DATA_DIR = Path(__file__).parent / "mock"


def load_mock_data(file_name: str) -> dict:
    """加载指定的 mock JSON 文件"""
    with open(MOCK_DATA_DIR / file_name, "r") as f:
        return json.load(f)


@pytest.mark.parametrize(
    "response_model, json_file",
    [
        (UserInfoResponse, "query_user_self_info.json"),
        (DarenSearchResponse, "query_daren_sug_great_user_list.json"),
        (VideoSearchResponse, "search_info_by_keyword.json"),
        (AuthorDetailResponse, "get_author_detail.json"),
        (VideoIndexResponse, "get_video_index.json"),
    ],
)
def test_schema_alignment(response_model, json_file):
    """测试 Pydantic 模型是否与 mock JSON 数据对齐"""
    mock_data = load_mock_data(json_file)
    try:
        response_model.model_validate(mock_data)
    except Exception as e:
        pytest.fail(f"Failed to validate {json_file} with {response_model.__name__}: {e}")
