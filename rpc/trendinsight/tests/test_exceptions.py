"""
TrendInsight 异常类测试
"""

from rpc.trendinsight.exceptions import (
    DataFetchError,
    TrendInsightAuthenticationError,
    TrendInsightClientError,
    TrendInsightConfigError,
    TrendInsightError,
    TrendInsightRateLimitError,
    TrendInsightRequestError,
    TrendInsightResponseError,
    TrendInsightSignError,
    TrendInsightTimeoutError,
    TrendInsightValidationError,
)


class TestTrendInsightExceptions:
    """TrendInsight 异常类测试"""

    def test_base_exception(self):
        """测试基础异常类"""
        error = TrendInsightError("测试错误")

        assert str(error) == "测试错误"
        assert error.message == "测试错误"
        assert error.status_code is None
        assert error.response_data is None

    def test_base_exception_with_status_code(self):
        """测试带状态码的基础异常"""
        error = TrendInsightError("测试错误", status_code=400)

        assert str(error) == "[400] 测试错误"
        assert error.message == "测试错误"
        assert error.status_code == 400

    def test_base_exception_with_response_data(self):
        """测试带响应数据的基础异常"""
        response_data = {"error": "test", "code": 400}
        error = TrendInsightError("测试错误", response_data=response_data)

        assert error.message == "测试错误"
        assert error.response_data == response_data

    def test_base_exception_full(self):
        """测试完整的基础异常"""
        response_data = {"error": "test", "code": 400}
        error = TrendInsightError("测试错误", status_code=400, response_data=response_data)

        assert str(error) == "[400] 测试错误"
        assert error.message == "测试错误"
        assert error.status_code == 400
        assert error.response_data == response_data

    def test_client_error(self):
        """测试客户端错误"""
        error = TrendInsightClientError("客户端错误")

        assert isinstance(error, TrendInsightError)
        assert str(error) == "客户端错误"

    def test_request_error(self):
        """测试请求错误"""
        error = TrendInsightRequestError("请求错误", status_code=400)

        assert isinstance(error, TrendInsightError)
        assert str(error) == "[400] 请求错误"
        assert error.status_code == 400

    def test_response_error(self):
        """测试响应错误"""
        error = TrendInsightResponseError("响应错误")

        assert isinstance(error, TrendInsightError)
        assert str(error) == "响应错误"

    def test_authentication_error(self):
        """测试认证错误"""
        error = TrendInsightAuthenticationError("认证失败", status_code=401)

        assert isinstance(error, TrendInsightError)
        assert str(error) == "[401] 认证失败"
        assert error.status_code == 401

    def test_rate_limit_error(self):
        """测试频率限制错误"""
        error = TrendInsightRateLimitError("请求频率过高", status_code=429)

        assert isinstance(error, TrendInsightError)
        assert str(error) == "[429] 请求频率过高"
        assert error.status_code == 429

    def test_validation_error(self):
        """测试验证错误"""
        error = TrendInsightValidationError("数据验证失败")

        assert isinstance(error, TrendInsightError)
        assert str(error) == "数据验证失败"

    def test_timeout_error(self):
        """测试超时错误"""
        error = TrendInsightTimeoutError("请求超时")

        assert isinstance(error, TrendInsightError)
        assert str(error) == "请求超时"

    def test_data_fetch_error(self):
        """测试数据获取错误"""
        error = DataFetchError("数据获取失败")

        assert isinstance(error, TrendInsightError)
        assert str(error) == "数据获取失败"

    def test_sign_error(self):
        """测试签名错误"""
        error = TrendInsightSignError("签名生成失败")

        assert isinstance(error, TrendInsightError)
        assert str(error) == "签名生成失败"

    def test_config_error(self):
        """测试配置错误"""
        error = TrendInsightConfigError("配置错误")

        assert isinstance(error, TrendInsightError)
        assert str(error) == "配置错误"

    def test_exception_inheritance(self):
        """测试异常继承关系"""
        # 所有异常都应该继承自 TrendInsightError
        exceptions = [
            TrendInsightClientError,
            TrendInsightRequestError,
            TrendInsightResponseError,
            TrendInsightAuthenticationError,
            TrendInsightRateLimitError,
            TrendInsightValidationError,
            TrendInsightTimeoutError,
            DataFetchError,
            TrendInsightSignError,
            TrendInsightConfigError,
        ]

        for exc_class in exceptions:
            error = exc_class("测试")
            assert isinstance(error, TrendInsightError)
            assert isinstance(error, Exception)

    def test_exception_with_cause(self):
        """测试异常链"""
        original_error = ValueError("原始错误")

        try:
            raise original_error
        except ValueError as e:
            wrapped_error = TrendInsightClientError("包装错误")
            wrapped_error.__cause__ = e

            assert wrapped_error.__cause__ is original_error

    def test_exception_pickling(self):
        """测试异常序列化"""
        import pickle

        error = TrendInsightError("测试错误", status_code=400, response_data={"test": "data"})

        # 序列化和反序列化
        pickled = pickle.dumps(error)
        unpickled = pickle.loads(pickled)

        assert str(unpickled) == str(error)
        assert unpickled.message == error.message
        assert unpickled.status_code == error.status_code
        assert unpickled.response_data == error.response_data
