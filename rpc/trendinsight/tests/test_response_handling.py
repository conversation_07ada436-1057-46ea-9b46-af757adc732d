"""
测试响应处理的改进
"""

from unittest.mock import Async<PERSON><PERSON>, Mock, PropertyMock, patch

import httpx
import pytest

from rpc.trendinsight.api import AsyncTrendInsightAPI
from rpc.trendinsight.exceptions import TrendInsightResponseError


class TestAsyncResponseHandling:
    """异步响应处理测试"""

    def test_async_handle_response_success(self):
        """测试异步成功响应处理"""
        api = AsyncTrendInsightAPI()

        # 模拟成功响应
        response = Mock()
        response.status_code = 200
        response.text = '{"status": "success", "data": {"test": "value"}}'
        response.url = "https://example.com/api"

        data = api._handle_response(response)

        assert data == {"status": "success", "data": {"test": "value"}}

    def test_async_handle_response_html_content(self):
        """测试异步HTML响应内容"""
        api = AsyncTrendInsightAPI()

        response = Mock()
        response.status_code = 200
        response.text = "<html><body>Error Page</body></html>"
        response.url = "https://example.com/api"

        with pytest.raises(TrendInsightResponseError) as exc_info:
            api._handle_response(response)

        assert "HTML页面而非JSON数据" in str(exc_info.value)


@pytest.mark.integration
class TestResponseHandlingIntegration:
    """响应处理集成测试"""

    @pytest.mark.asyncio
    async def test_async_response_handling_with_real_client(self):
        """使用真实异步客户端测试响应处理"""
        from rpc.trendinsight import client_manager

        # 创建异步客户端但不发送真实请求
        client = client_manager.create_async_client()
        api = AsyncTrendInsightAPI(async_client=client)

        try:
            # 验证异步API实例有响应处理方法
            assert hasattr(api, "_handle_response")
            assert callable(api._handle_response)

            # 测试方法签名
            import inspect

            sig = inspect.signature(api._handle_response)
            assert "response" in sig.parameters

        finally:
            # 使用client_manager关闭客户端
            await client_manager.close_async_client(client)

    @pytest.mark.asyncio
    async def test_async_handle_response_html_content_integration(self):
        """测试集成客户端处理HTML响应"""
        from rpc.trendinsight import client_manager

        client = client_manager.create_async_client()
        api = AsyncTrendInsightAPI(async_client=client)

        try:
            mock_response = httpx.Response(
                200, text="<html><body>Error</body></html>", request=httpx.Request("GET", "https://example.com")
            )

            with patch.object(client, "get", new_callable=AsyncMock, return_value=mock_response):
                with pytest.raises(TrendInsightResponseError) as exc_info:
                    await api.query_user_self_info()

                assert "HTML页面而非JSON数据" in str(exc_info.value)
        finally:
            await client_manager.close_async_client(client)

    @pytest.mark.asyncio
    async def test_async_handle_response_unicode_decode_error_integration(self):
        """测试集成客户端处理Unicode解码错误"""
        from rpc.trendinsight import client_manager

        client = client_manager.create_async_client()
        api = AsyncTrendInsightAPI(async_client=client)

        try:
            success_json = {"status": "success"}
            success_text = '{"status": "success"}'

            mock_httpx_response = Mock(spec=httpx.Response)
            mock_httpx_response.status_code = 200
            mock_httpx_response.url = httpx.URL("https://example.com/api")
            mock_httpx_response.request = httpx.Request("GET", "https://example.com/api")

            # The .text property will raise the error
            type(mock_httpx_response).text = PropertyMock(
                side_effect=UnicodeDecodeError("utf-8", b"\xcc", 0, 1, "invalid byte")
            )
            mock_httpx_response.content = success_text.encode("utf-8")

            with patch.object(client, "get", new_callable=AsyncMock, return_value=mock_httpx_response):
                # We need to mock the response validation as well, since we are not returning a valid UserInfoResponse
                with patch("rpc.trendinsight.api.UserInfoResponse") as mock_user_info_response:
                    mock_user_info_response.return_value = success_json
                    data = await api.query_user_self_info()
                    # The _handle_response method is what we are testing, which returns the raw dict
                    # The wrapping into a pydantic model happens in the calling method, so we assert the dict here.
                    assert data == success_json

        finally:
            await client_manager.close_async_client(client)
