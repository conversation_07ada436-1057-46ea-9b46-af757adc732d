"""
测试 ItemIndexExistResponse 修复
"""

import json
import pytest
from pathlib import Path

from rpc.trendinsight.schemas.video import ItemIndexExistResponse


class TestItemIndexExistFix:
    """测试 ItemIndexExist 响应处理修复"""

    def test_successful_response(self):
        """测试成功响应"""
        # 加载成功响应的 mock 数据
        mock_file = Path(__file__).parent / "mock" / "get_item_index_exist.json"
        with open(mock_file, "r", encoding="utf-8") as f:
            mock_data = json.load(f)

        # 验证可以正确解析
        response = ItemIndexExistResponse(**mock_data)
        
        assert response.is_success is True
        assert response.item_status == "succeed"
        assert response.item_index == "237"

    def test_failed_response_without_item_index(self):
        """测试失败响应（没有 item_index 字段）"""
        # 加载失败响应的 mock 数据
        mock_file = Path(__file__).parent / "mock" / "get_item_index_exist_failed.json"
        with open(mock_file, "r", encoding="utf-8") as f:
            mock_data = json.load(f)

        # 验证可以正确解析（之前会抛出 ValidationError）
        response = ItemIndexExistResponse(**mock_data)
        
        assert response.is_success is True  # status 为 0 表示 API 调用成功
        assert response.item_status == "failed"  # 但业务状态为失败
        assert response.item_index is None  # item_index 字段不存在，应为 None

    def test_failed_response_with_explicit_none_item_index(self):
        """测试失败响应（item_index 字段显式为 null）"""
        mock_data = {
            "data": {
                "item_statue": "failed",
                "item_index": None,
                "BaseResp": {
                    "StatusMessage": "",
                    "StatusCode": 0
                }
            },
            "msg": "",
            "status": 0
        }

        # 验证可以正确解析
        response = ItemIndexExistResponse(**mock_data)
        
        assert response.is_success is True
        assert response.item_status == "failed"
        assert response.item_index is None

    def test_failed_response_with_empty_item_index(self):
        """测试失败响应（item_index 字段为空字符串）"""
        mock_data = {
            "data": {
                "item_statue": "failed",
                "item_index": "",
                "BaseResp": {
                    "StatusMessage": "",
                    "StatusCode": 0
                }
            },
            "msg": "",
            "status": 0
        }

        # 验证可以正确解析
        response = ItemIndexExistResponse(**mock_data)
        
        assert response.is_success is True
        assert response.item_status == "failed"
        assert response.item_index == ""
