"""
TrendInsight 请求增强器

基于 rpc.common.RequestEnhancer 实现的 TrendInsight 专用请求增强器
负责为 TrendInsight API 请求添加签名、验证参数等
"""

import time
from typing import Any, Dict, Optional

import httpx
from loguru import logger

from rpc.common.providers import RequestEnhancer

from .config import TrendInsightConfig
from .exceptions import TrendInsightSignError
from .utils import TrendInsightSignClient
from .verify_params import CommonVerifyParams, VerifyParamsGenerator


class TrendInsightRequestEnhancer(RequestEnhancer):
    """
    TrendInsight 请求增强器

    为 TrendInsight 平台的请求添加特定的参数，如 msToken、X-Bogus 等
    """

    def __init__(
        self,
        config: Optional[TrendInsightConfig] = None,
        user_agent: Optional[str] = None,
        auto_generate_params: bool = True,
    ):
        """
        初始化请求增强器

        Args:
            config: TrendInsight 配置
            user_agent: 用户代理字符串
            auto_generate_params: 是否自动生成验证参数
        """
        self.config = config or TrendInsightConfig()
        self.auto_generate_params = auto_generate_params

        # 用户代理
        self._user_agent = user_agent or self.config.default_headers.get("user-agent", "")

        # 初始化签名客户端
        self._sign_client = TrendInsightSignClient(
            use_javascript=self.config.use_javascript_sign,
            js_file_path=self.config.sign_js_file_path,
            enable_cache=self.config.sign_cache_enabled,
            cache_ttl=self.config.sign_cache_ttl,
            max_retries=self.config.sign_max_retries,
            retry_delay=self.config.sign_retry_delay,
        )

        # 验证参数生成器
        self._verify_params_generator = VerifyParamsGenerator(self._user_agent)
        self._verify_params: Optional[CommonVerifyParams] = None

        # 通用参数
        self._common_params = {
            "aid": 355922,
            "account_sdk_source": "web",
            "language": "zh",
        }

        logger.info("TrendInsightRequestEnhancer 初始化完成")

    def enhance_request(self, request: httpx.Request) -> None:
        """
        为 TrendInsight 请求添加特定参数

        Args:
            request: httpx.Request 对象
        """
        logger.debug(f"开始增强 TrendInsight 请求: {request.url}")

        try:
            # 1. 确保验证参数已生成（同步版本）
            self._ensure_verify_params_sync()

            # 2. 获取当前 URL 参数
            params = dict(request.url.params)

            # 3. 添加通用参数
            params.update(self._common_params)

            # 4. 添加验证参数
            if self._verify_params:
                params.update(
                    {
                        "msToken": self._verify_params.ms_token,
                        "webid": self._verify_params.webid,
                        "verifyFp": self._verify_params.verify_fp,
                        "s_v_web_id": self._verify_params.s_v_web_id,
                    }
                )

                if self._verify_params.uifid:
                    params["uifid"] = self._verify_params.uifid

            # 5. 生成签名（同步版本）
            self._add_signature_sync(request, params)

            # 6. 更新请求的 URL
            request.url = request.url.copy_with(params=params)

            # 7. 添加 TrendInsight 特有的请求头
            self._add_headers(request)

            logger.debug(f"TrendInsight 请求增强完成: {request.url}")

        except Exception as e:
            logger.error(f"TrendInsight 请求增强失败: {e}")
            raise TrendInsightSignError(f"请求增强失败: {e}")

    def _ensure_verify_params_sync(self):
        """确保验证参数已生成（同步版本）"""
        if self.auto_generate_params and self._verify_params is None:
            try:
                # 在同步方法中运行异步操作
                import asyncio

                try:
                    # 检查是否已经在事件循环中
                    asyncio.get_running_loop()
                    # 如果已经在事件循环中，在新线程中运行
                    import concurrent.futures

                    def run_async():
                        loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(loop)
                        try:
                            return loop.run_until_complete(
                                self._verify_params_generator.generate_common_verify_params()
                            )
                        finally:
                            loop.close()

                    with concurrent.futures.ThreadPoolExecutor() as executor:
                        future = executor.submit(run_async)
                        self._verify_params = future.result()

                except RuntimeError:
                    # 没有运行的事件循环，可以直接使用 asyncio.run
                    self._verify_params = asyncio.run(self._verify_params_generator.generate_common_verify_params())

                logger.debug("验证参数生成成功")
            except Exception as e:
                logger.warning(f"验证参数生成失败: {e}")
                # 如果生成失败，使用默认值
                self._verify_params = CommonVerifyParams(
                    ms_token=f"trendinsight_ms_token_{int(time.time())}",
                    webid=f"trendinsight_webid_{int(time.time())}",
                    verify_fp=f"verify_fp_{int(time.time())}",
                    s_v_web_id=f"s_v_web_id_{int(time.time())}",
                )

    def _add_signature_sync(self, request: httpx.Request, params: Dict[str, Any]):
        """添加签名参数（同步版本）"""
        try:
            # 获取请求路径
            uri = request.url.path
            if request.url.query:
                uri += "?" + request.url.query

            # 获取 cookies
            cookies = ""
            if "Cookie" in request.headers:
                cookies = request.headers["Cookie"]

            # 在同步方法中运行异步签名操作
            import asyncio

            try:
                # 检查是否已经在事件循环中
                asyncio.get_running_loop()
                # 如果已经在事件循环中，在新线程中运行
                import concurrent.futures

                def run_async():
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    try:
                        return loop.run_until_complete(self._sign_client.sign(uri=uri, params=params, cookies=cookies))
                    finally:
                        loop.close()

                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(run_async)
                    signature_response = future.result()

            except RuntimeError:
                # 没有运行的事件循环，可以直接使用 asyncio.run
                signature_response = asyncio.run(self._sign_client.sign(uri=uri, params=params, cookies=cookies))

            # 添加签名参数
            params["X-Bogus"] = signature_response.x_bogus
            params["_signature"] = signature_response.signature

            # 更新 msToken（如果签名响应中有新的）
            if hasattr(signature_response, "ms_token") and signature_response.ms_token:
                params["msToken"] = signature_response.ms_token

            logger.debug("签名参数添加成功")

        except Exception as e:
            logger.error(f"签名生成失败: {e}")
            # 如果签名失败，添加默认的签名参数
            params["X-Bogus"] = f"DFSzswVLQDCiJe8F{int(time.time())}"
            params["_signature"] = f"_02B4Z6wo00001{int(time.time())}"

    async def _ensure_verify_params(self):
        """确保验证参数已生成（异步版本）"""
        if self.auto_generate_params and self._verify_params is None:
            try:
                self._verify_params = await self._verify_params_generator.generate_common_verify_params()
                logger.debug("验证参数生成成功")
            except Exception as e:
                logger.warning(f"验证参数生成失败: {e}")
                # 如果生成失败，使用默认值
                self._verify_params = CommonVerifyParams(
                    ms_token=f"trendinsight_ms_token_{int(time.time())}",
                    webid=f"trendinsight_webid_{int(time.time())}",
                    verify_fp=f"verify_fp_{int(time.time())}",
                    s_v_web_id=f"s_v_web_id_{int(time.time())}",
                )

    async def _add_signature(self, request: httpx.Request, params: Dict[str, Any]):
        """添加签名参数"""
        try:
            # 获取请求路径
            uri = request.url.path
            if request.url.query:
                uri += "?" + request.url.query

            # 获取 cookies
            cookies = ""
            if "Cookie" in request.headers:
                cookies = request.headers["Cookie"]

            # 生成签名
            signature_response = await self._sign_client.sign(uri=uri, params=params, cookies=cookies)

            # 添加签名参数
            params["X-Bogus"] = signature_response.x_bogus
            params["_signature"] = signature_response.signature

            # 更新 msToken（如果签名响应中有新的）
            if hasattr(signature_response, "ms_token") and signature_response.ms_token:
                params["msToken"] = signature_response.ms_token

            logger.debug("签名参数添加成功")

        except Exception as e:
            logger.error(f"签名生成失败: {e}")
            # 如果签名失败，添加默认的签名参数
            params["X-Bogus"] = f"DFSzswVLQDCiJe8F{int(time.time())}"
            params["_signature"] = f"_02B4Z6wo00001{int(time.time())}"

    def _add_headers(self, request: httpx.Request):
        """添加 TrendInsight 特有的请求头"""
        # 添加平台标识
        request.headers["X-Platform"] = "trendinsight"

        # 添加 Referer
        if "Referer" not in request.headers:
            request.headers["Referer"] = "https://trendinsight.oceanengine.com/"

        # 添加 Origin
        if "Origin" not in request.headers:
            request.headers["Origin"] = "https://trendinsight.oceanengine.com"

        # 确保 User-Agent
        if "User-Agent" not in request.headers:
            request.headers["User-Agent"] = self._user_agent

        # 添加其他必要的请求头
        request.headers.update(
            {
                "Accept": "application/json, text/plain, */*",
                "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
                "Accept-Encoding": "gzip, deflate, br",
                "Cache-Control": "no-cache",
                "Pragma": "no-cache",
                "Sec-Fetch-Dest": "empty",
                "Sec-Fetch-Mode": "cors",
                "Sec-Fetch-Site": "same-origin",
            }
        )

    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        stats = {
            "verify_params_generated": self._verify_params is not None,
            "auto_generate_params": self.auto_generate_params,
        }

        # 添加签名客户端统计信息
        if self._sign_client:
            stats["sign_client_stats"] = self._sign_client.get_stats()

        return stats

    def clear_cache(self):
        """清空缓存"""
        if self._sign_client:
            self._sign_client.clear_cache()

        # 重置验证参数，下次请求时重新生成
        self._verify_params = None

        logger.info("TrendInsightRequestEnhancer 缓存已清空")
