"""
TrendInsight RPC 新架构模块

基于 rpc/common 的三步增强模式重构的 TrendInsight API 客户端
"""

# 导入 API 接口
from .api import AsyncTrendInsightAPI, async_api

# 导入客户端
from .client import TrendInsightClientManager, client_manager

# 导入配置
from .config import TrendInsightConfig

# 导入增强器
from .enhancer import TrendInsightRequestEnhancer

# 导入异常
from .exceptions import (
    DataFetchError,
    TrendInsightAuthenticationError,
    TrendInsightClientError,
    TrendInsightConfigError,
    TrendInsightError,
    TrendInsightRateLimitError,
    TrendInsightRequestError,
    TrendInsightResponseError,
    TrendInsightSignError,
    TrendInsightTimeoutError,
    TrendInsightValidationError,
)

# 导入模型
from .schemas import (  # 签名相关; 枚举类型; 用户信息相关; 达人搜索相关; 视频搜索相关; 作者详情相关; 视频指数相关
    AuthorDetailInfo,
    AuthorDetailRequest,
    AuthorDetailResponse,
    DarenSearchRequest,
    DarenSearchResponse,
    DarenUserInfo,
    DateType,
    DurationType,
    LabelType,
    PublishTimeType,
    SearchChannelType,
    SearchSortType,
    TrendInsightSignRequest,
    TrendInsightSignResponse,
    UserInfo,
    UserInfoRequest,
    UserInfoResponse,
    VideoIndexData,
    VideoIndexRequest,
    VideoIndexResponse,
    VideoIndexTrendItem,
    VideoInfo,
    VideoSearchRequest,
    VideoSearchResponse,
)

# 导入工具类
from .utils import TrendInsightSignClient

__all__ = [
    # 客户端
    "client_manager",
    "TrendInsightClientManager",
    # API 接口
    "AsyncTrendInsightAPI",
    "async_api",
    # 配置
    "TrendInsightConfig",
    # 模型
    "TrendInsightSignRequest",
    "TrendInsightSignResponse",
    "SearchChannelType",
    "SearchSortType",
    "PublishTimeType",
    "DateType",
    "LabelType",
    "DurationType",
    "UserInfoRequest",
    "UserInfo",
    "UserInfoResponse",
    "DarenSearchRequest",
    "DarenUserInfo",
    "DarenSearchResponse",
    "VideoSearchRequest",
    "VideoInfo",
    "VideoSearchResponse",
    "AuthorDetailRequest",
    "AuthorDetailInfo",
    "AuthorDetailResponse",
    "VideoIndexRequest",
    "VideoIndexTrendItem",
    "VideoIndexData",
    "VideoIndexResponse",
    # 异常
    "TrendInsightError",
    "TrendInsightClientError",
    "TrendInsightRequestError",
    "TrendInsightResponseError",
    "TrendInsightAuthenticationError",
    "TrendInsightRateLimitError",
    "TrendInsightValidationError",
    "TrendInsightTimeoutError",
    "DataFetchError",
    "TrendInsightSignError",
    "TrendInsightConfigError",
    # 增强器
    "TrendInsightRequestEnhancer",
    # 工具类
    "TrendInsightSignClient",
]

__version__ = "1.0.0"
