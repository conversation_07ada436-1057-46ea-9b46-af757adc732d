"""
TrendInsight 客户端集成测试

测试客户端的集成功能，包括请求增强、传输层等
"""

from unittest.mock import AsyncMock, patch

import httpx
import pytest
from loguru import logger

from models.enums import Platform
from rpc.common import DynamicProxyTransport
from rpc.trendinsight import (
    TrendInsightConfig,
    TrendInsightRequestEnhancer,
    client_manager,
)


@pytest.mark.integration
class TestClientIntegration:
    """客户端集成测试"""

    @pytest.mark.asyncio
    async def test_async_client_request_flow(self):
        """测试异步客户端请求流程"""
        logger.info("开始测试异步客户端请求流程")

        config = TrendInsightConfig()
        client = client_manager.create_async_client(config=config)

        try:
            # 验证客户端配置
            assert isinstance(client, httpx.AsyncClient)
            assert isinstance(client._transport, DynamicProxyTransport)
            assert client._transport.platform == Platform.TRENDINSIGHT

            # 验证传输层组件
            transport = client._transport
            assert transport.account_provider is not None
            assert transport.proxy_provider is not None
            assert transport.request_enhancer is not None
            assert isinstance(transport.request_enhancer, TrendInsightRequestEnhancer)

            logger.info("异步客户端请求流程验证成功")

        finally:
            await client_manager.close_async_client(client)

    @pytest.mark.asyncio
    async def test_async_request_enhancement_integration(self):
        """测试异步请求增强集成"""
        logger.info("开始测试异步请求增强集成")

        config = TrendInsightConfig()
        client = client_manager.create_async_client(config=config)

        try:
            # 模拟异步请求
            mock_response = httpx.Response(
                200, json={"test": "data"}, request=httpx.Request("GET", "https://httpbin.org/get")
            )

            with patch.object(
                client._transport, "handle_async_request", new_callable=AsyncMock, return_value=mock_response
            ) as mock_handle:
                # 发送异步请求
                response = await client.get("https://httpbin.org/get", params={"test": "param"})

                # 验证请求被处理
                mock_handle.assert_awaited_once()
                assert response.status_code == 200
                assert response.json() == {"test": "data"}

            logger.info("异步请求增强集成验证成功")

        finally:
            await client_manager.close_async_client(client)


@pytest.mark.integration
class TestTransportIntegration:
    """传输层集成测试"""
