"""
TrendInsight 真实API集成测试

测试与真实 TrendInsight API 的集成功能
需要有效的认证信息才能运行
"""

import pytest
from loguru import logger

from rpc.trendinsight import AsyncTrendInsightAPI, TrendInsightConfig, client_manager
from rpc.trendinsight.exceptions import TrendInsightAuthenticationError


@pytest.mark.real_api
@pytest.mark.ci_skip
class TestRealAPIIntegration:
    """真实API集成测试"""

    @pytest.fixture(scope="class")
    def async_api_client(self):
        """创建异步API客户端"""
        config = TrendInsightConfig(auto_generate_params=True)  # 启用自动参数生成，这样真实请求才能走通

        # client_manager 已经内置了所有必要的 providers 和 enhancer
        client = client_manager.create_async_client(config=config)

        api = AsyncTrendInsightAPI(async_client=client, config=config)

        yield api

        # 清理
        import asyncio

        asyncio.run(client_manager.close_async_client(client))

    @pytest.mark.asyncio
    async def test_query_user_self_info_async_real(self, async_api_client):
        """测试异步查询用户自己的信息（真实API）"""
        logger.info("开始测试异步查询用户自己的信息")

        try:
            response = await async_api_client.query_user_self_info()

            logger.info(f"异步用户信息查询成功: {response}")

            # 验证响应结构
            assert response is not None
            assert hasattr(response, "data")

        except TrendInsightAuthenticationError as e:
            logger.warning(f"异步认证失败: {e}")
            pytest.skip("需要有效的认证信息")
        except Exception as e:
            logger.error(f"异步用户信息查询失败: {e}")
            raise
