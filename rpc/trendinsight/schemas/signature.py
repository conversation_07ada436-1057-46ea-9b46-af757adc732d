"""
TrendInsight API 签名相关数据模型
"""

from typing import Any, Dict, Optional, Union

from pydantic import BaseModel, Field


class TrendInsightSignRequest(BaseModel):
    """TrendInsight 签名请求参数"""

    uri: str = Field(..., title="request_uri", description="请求的uri")
    params: Optional[Union[str, Dict[str, Any]]] = Field(
        None, title="params", description="请求参数，可以是字符串或字典"
    )
    query_params: Optional[str] = Field(
        None, title="query_params", description="请求的query_params(urlencode之后的参数)"
    )
    user_agent: Optional[str] = Field(None, title="user_agent", description="请求的user_agent")
    cookies: str = Field(..., title="cookies", description="请求的cookies")


class TrendInsightSignResponse(BaseModel):
    """TrendInsight 签名响应参数"""

    x_bogus: str = Field(..., title="x_bogus", description="X-Bogus签名参数")
    signature: str = Field(..., title="signature", description="_signature签名参数")
    ms_token: str = Field(..., title="ms_token", description="msToken参数")
