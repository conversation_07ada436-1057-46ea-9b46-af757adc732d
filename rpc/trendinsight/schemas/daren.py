"""
TrendInsight API 达人相关数据模型
"""

from typing import List, Optional

from pydantic import BaseModel, Field

from .base import DarenBaseResp


class DarenSearchRequest(BaseModel):
    """达人搜索请求参数"""

    # 业务参数
    keyword: str = Field(..., description="搜索关键词")
    total: int = Field(default=30, description="返回结果总数")


class DarenUserInfo(BaseModel):
    """达人用户信息"""

    user_id: Optional[str] = Field(None, description="用户ID")
    user_name: Optional[str] = Field(None, description="用户名")
    item_count: Optional[str] = Field(None, description="作品数（字符串）")
    follow_count: Optional[str] = Field(None, description="关注数（字符串）")
    like_count: Optional[str] = Field(None, description="点赞数（字符串）")
    user_head_logo: Optional[str] = Field(None, description="用户头像URL")
    first_tag_name: Optional[str] = Field(None, description="第一标签名")
    second_tag_name: Optional[str] = Field(None, description="第二标签名")
    aweme_id: Optional[str] = Field(None, description="抖音ID")
    aweme_url: Optional[str] = Field(None, description="抖音URL")

    # 向后兼容的属性别名
    @property
    def nickname(self) -> Optional[str]:
        """获取昵称（向后兼容）"""
        return self.user_name

    @property
    def avatar_url(self) -> Optional[str]:
        """获取头像URL（向后兼容）"""
        return self.user_head_logo

    @property
    def follower_count(self) -> Optional[int]:
        """获取粉丝数（向后兼容）"""
        try:
            return int(self.follow_count) if self.follow_count else None
        except (ValueError, TypeError):
            return None

    @property
    def aweme_count(self) -> Optional[int]:
        """获取作品数（向后兼容）"""
        try:
            return int(self.item_count) if self.item_count else None
        except (ValueError, TypeError):
            return None

    @property
    def sec_user_id(self) -> Optional[str]:
        """从aweme_url中提取sec_user_id（向后兼容）"""
        if self.aweme_url and "MS4wLjABAAAA" in self.aweme_url:
            # 从URL中提取sec_user_id
            parts = self.aweme_url.split("/")
            if len(parts) > 0:
                return parts[-1]
        return None

    class Config:
        extra = "allow"  # 允许额外字段


class DarenSearchData(BaseModel):
    """达人搜索数据"""

    userlist: Optional[List[DarenUserInfo]] = Field(None, description="达人列表")
    BaseResp: Optional[DarenBaseResp] = Field(None, description="基础响应信息")

    class Config:
        extra = "allow"


class DarenSearchResponse(BaseModel):
    """达人搜索响应"""

    data: Optional[DarenSearchData] = Field(None, description="响应数据")
    msg: Optional[str] = Field(None, description="消息")
    status: Optional[int] = Field(None, description="状态")

    # 为了向后兼容，提供属性别名
    @property
    def userlist(self) -> Optional[List[DarenUserInfo]]:
        """获取达人列表（向后兼容）"""
        return self.data.userlist if self.data else None

    @property
    def StatusCode(self) -> Optional[int]:
        """获取状态码（向后兼容）"""
        if self.data and self.data.BaseResp:
            return self.data.BaseResp.StatusCode
        return self.status

    @property
    def StatusMsg(self) -> Optional[str]:
        """获取状态消息（向后兼容）"""
        if self.data and self.data.BaseResp:
            return self.data.BaseResp.StatusMessage
        return self.msg

    class Config:
        extra = "allow"


class AuthorDetailRequest(BaseModel):
    """作者详情请求参数"""

    user_id: str = Field(..., description="用户ID")


class AuthorDetailInfo(BaseModel):
    """作者详情信息"""

    user_id: Optional[str] = Field(None, description="用户ID")
    user_name: Optional[str] = Field(None, description="用户名")
    avatar_url: Optional[str] = Field(None, alias="user_head_logo", description="头像URL")
    follower_count: Optional[str] = Field(None, alias="fans_count", description="粉丝数")
    following_count: Optional[str] = Field(None, description="关注数")
    aweme_count: Optional[str] = Field(None, alias="item_count", description="作品数")
    like_count: Optional[str] = Field(None, description="获赞数")
    description: Optional[str] = Field(None, alias="user_introduction", description="个人简介")
    user_gender: Optional[str] = Field(None, description="用户性别")
    user_location: Optional[str] = Field(None, description="用户所在地")
    first_tag_name: Optional[str] = Field(None, description="第一标签名")
    second_tag_name: Optional[str] = Field(None, description="第二标签名")
    aweme_id: Optional[str] = Field(None, description="抖音ID")
    user_aweme_url: Optional[str] = Field(None, description="抖音主页链接")
    aweme_pic: Optional[str] = Field(None, description="抖音图片")
    BaseResp: Optional[DarenBaseResp] = Field(None, description="基础响应信息")

    # 向后兼容的属性别名
    @property
    def user_head_logo(self) -> Optional[str]:
        """获取头像URL（向后兼容）"""
        return self.avatar_url

    @property
    def user_introduction(self) -> Optional[str]:
        """获取个人简介（向后兼容）"""
        return self.description

    @property
    def item_count(self) -> Optional[str]:
        """获取作品数（向后兼容）"""
        return self.aweme_count

    @property
    def fans_count(self) -> Optional[str]:
        """获取粉丝数（向后兼容）"""
        return self.follower_count

    class Config:
        extra = "allow"
        populate_by_name = True


class AuthorDetailResponse(BaseModel):
    """作者详情响应"""

    data: Optional[AuthorDetailInfo] = Field(None, description="作者详情")
    msg: Optional[str] = Field(None, description="响应消息")
    status: Optional[int] = Field(None, description="状态码")

    @property
    def is_success(self) -> bool:
        """判断请求是否成功"""
        return self.status == 0

    @property
    def author_detail(self) -> Optional[AuthorDetailInfo]:
        """获取作者详情数据"""
        return self.data

    class Config:
        extra = "allow"


class GreatUserTopVideoRequest(BaseModel):
    """获取优秀用户热门视频请求参数"""

    user_id: str = Field(..., description="用户ID")
    start_date: str = Field(..., description="开始日期，格式：YYYYMMDD")
    end_date: str = Field(..., description="结束日期，格式：YYYYMMDD")

    class Config:
        extra = "allow"


class TopVideoInfo(BaseModel):
    """热门视频信息"""

    picture: Optional[str] = Field(None, description="视频封面图片")
    video_text: Optional[str] = Field(None, description="视频文案")
    like_cnt: Optional[str] = Field(None, description="点赞数")
    coment_cnt: Optional[str] = Field(None, description="评论数")
    share_cnt: Optional[str] = Field(None, description="分享数")
    follow_cnt: Optional[str] = Field(None, description="关注数")
    video_url: Optional[str] = Field(None, description="视频链接")
    rank: Optional[str] = Field(None, description="排名")
    item_id: Optional[str] = Field(None, description="视频ID")
    create_time: Optional[str] = Field(None, description="创建时间")

    class Config:
        extra = "allow"


class BaseResp(BaseModel):
    """基础响应结构"""

    StatusMessage: Optional[str] = Field(None, description="状态消息")
    StatusCode: Optional[int] = Field(None, description="状态码")

    class Config:
        extra = "allow"


class GreatUserTopVideoData(BaseModel):
    """优秀用户热门视频数据"""

    comment_list: Optional[List[TopVideoInfo]] = Field(None, description="评论排序视频列表")
    create_time_list: Optional[List[TopVideoInfo]] = Field(None, description="创建时间排序视频列表")
    follow_list: Optional[List[TopVideoInfo]] = Field(None, description="关注排序视频列表")
    index_list: Optional[List[TopVideoInfo]] = Field(None, description="指数排序视频列表")
    like_list: Optional[List[TopVideoInfo]] = Field(None, description="点赞排序视频列表")
    share_list: Optional[List[TopVideoInfo]] = Field(None, description="分享排序视频列表")
    base_resp: Optional[BaseResp] = Field(None, description="基础响应")

    class Config:
        extra = "allow"


class GreatUserTopVideoResponse(BaseModel):
    """获取优秀用户热门视频响应"""

    data: Optional[GreatUserTopVideoData] = Field(None, description="视频数据")
    msg: Optional[str] = Field(None, description="响应消息")
    status: Optional[int] = Field(None, description="状态码")

    @property
    def is_success(self) -> bool:
        """判断请求是否成功"""
        return self.status == 0

    @property
    def video_data(self) -> Optional[GreatUserTopVideoData]:
        """获取视频数据"""
        return self.data

    class Config:
        extra = "allow"
