"""
TrendInsight API 用户信息相关数据模型
"""

from typing import List, Optional

from pydantic import BaseModel, Field


class UserInfoRequest(BaseModel):
    """用户信息查询请求参数"""

    # 业务参数（无需额外参数，使用默认配置）
    pass


class ConnectInfo(BaseModel):
    """连接信息"""

    platform: Optional[str] = Field(None, description="平台")
    profile_image_url: Optional[str] = Field(None, description="头像URL")
    expired_time: Optional[int] = Field(None, description="过期时间")
    expires_in: Optional[int] = Field(None, description="剩余时间")
    platform_screen_name: Optional[str] = Field(None, description="平台昵称")
    user_id: Optional[int] = Field(None, description="用户ID")
    platform_uid: Optional[str] = Field(None, description="平台用户ID")
    sec_platform_uid: Optional[str] = Field(None, description="安全平台用户ID")
    platform_app_id: Optional[int] = Field(None, description="平台应用ID")
    modify_time: Optional[int] = Field(None, description="修改时间")
    access_token: Optional[str] = Field(None, description="访问令牌")
    open_id: Optional[str] = Field(None, description="开放ID")

    class Config:
        extra = "allow"


class OperStaffRelationInfo(BaseModel):
    """运营人员关系信息"""

    has_password: Optional[int] = Field(None, description="是否有密码")
    mobile: Optional[str] = Field(None, description="手机号")
    sec_oper_staff_user_id: Optional[str] = Field(None, description="安全运营人员用户ID")
    relation_mobile_country_code: Optional[int] = Field(None, description="关联手机国家代码")

    class Config:
        extra = "allow"


class UserInfo(BaseModel):
    """用户信息"""

    app_id: Optional[int] = Field(None, description="应用ID")
    app_user_info: Optional[dict] = Field(None, description="应用用户信息")
    avatar_url: Optional[str] = Field(None, description="头像URL")
    bg_img_url: Optional[str] = Field(None, description="背景图片URL")
    can_be_found_by_phone: Optional[int] = Field(None, description="是否可通过手机号找到")
    connects: Optional[List[ConnectInfo]] = Field(None, description="连接信息列表")
    country_code: Optional[int] = Field(None, description="国家代码")
    description: Optional[str] = Field(None, description="描述")
    device_id: Optional[int] = Field(None, description="设备ID")
    email: Optional[str] = Field(None, description="邮箱")
    email_collected: Optional[bool] = Field(None, description="是否收集邮箱")
    gender: Optional[int] = Field(None, description="性别")
    has_password: Optional[int] = Field(None, description="是否有密码")
    hm_region: Optional[int] = Field(None, description="HM区域")
    is_blocked: Optional[int] = Field(None, description="是否被屏蔽")
    is_blocking: Optional[int] = Field(None, description="是否屏蔽")
    is_recommend_allowed: Optional[int] = Field(None, description="是否允许推荐")
    is_visitor_account: Optional[bool] = Field(None, description="是否访客账户")
    mobile: Optional[str] = Field(None, description="手机号")
    name: Optional[str] = Field(None, description="姓名")
    need_check_bind_status: Optional[bool] = Field(None, description="是否需要检查绑定状态")
    odin_user_type: Optional[int] = Field(None, description="Odin用户类型")
    oper_staff_relation_info: Optional[OperStaffRelationInfo] = Field(None, description="运营人员关系信息")
    phone_collected: Optional[bool] = Field(None, description="是否收集手机号")
    recommend_hint_message: Optional[str] = Field(None, description="推荐提示消息")
    screen_name: Optional[str] = Field(None, description="屏幕名称")
    sec_user_id: Optional[str] = Field(None, description="安全用户ID")
    session_key: Optional[str] = Field(None, description="会话密钥")
    use_hm_region: Optional[bool] = Field(None, description="是否使用HM区域")
    user_create_time: Optional[int] = Field(None, description="用户创建时间")
    user_id: Optional[int] = Field(None, description="用户ID")
    user_id_str: Optional[str] = Field(None, description="用户ID字符串")
    user_verified: Optional[bool] = Field(None, description="是否已验证")
    verified_content: Optional[str] = Field(None, description="验证内容")

    # 向后兼容的属性别名
    @property
    def nickname(self) -> Optional[str]:
        """获取昵称（向后兼容）"""
        return self.screen_name or self.name

    class Config:
        extra = "allow"  # 允许额外字段


class UserInfoResponse(BaseModel):
    """用户信息查询响应"""

    data: Optional[UserInfo] = Field(None, description="用户信息")
    message: Optional[str] = Field(None, description="响应消息")

    class Config:
        extra = "allow"  # 允许额外字段
