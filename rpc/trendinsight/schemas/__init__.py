"""
TrendInsight API 数据模型
"""

from .base import *
from .daren import *
from .enums import *
from .signature import *
from .user import *
from .video import *

__all__ = [
    # base.py
    "DarenBaseResp",
    # daren.py
    "DarenSearchRequest",
    "DarenUserInfo",
    "DarenSearchData",
    "DarenSearchResponse",
    "AuthorDetailRequest",
    "AuthorDetailInfo",
    "AuthorDetailResponse",
    "GreatUserTopVideoRequest",
    "TopVideoInfo",
    "BaseResp",
    "GreatUserTopVideoData",
    "GreatUserTopVideoResponse",
    # enums.py
    "SearchChannelType",
    "SearchSortType",
    "PublishTimeType",
    "DateType",
    "LabelType",
    "DurationType",
    # signature.py
    "TrendInsightSignRequest",
    "TrendInsightSignResponse",
    # user.py
    "UserInfoRequest",
    "ConnectInfo",
    "OperStaffRelationInfo",
    "UserInfo",
    "UserInfoResponse",
    # video.py
    "VideoSearchRequest",
    "VideoInfo",
    "VideoSearchData",
    "VideoSearchResponse",
    "VideoIndexRequest",
    "VideoIndexTrendItem",
    "VideoIndexData",
    "VideoIndexResponse",
    "ItemIndexExistData",
    "ItemIndexExistResponse",
]
