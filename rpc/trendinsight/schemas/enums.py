"""
TrendInsight API 枚举类型
"""

from enum import Enum


class SearchChannelType(Enum):
    """搜索频道类型"""

    GENERAL = "aweme_general"  # 综合
    VIDEO = "aweme_video_web"  # 视频
    USER = "aweme_user_web"  # 用户
    LIVE = "aweme_live"  # 直播


class SearchSortType(Enum):
    """搜索排序类型"""

    GENERAL = 0  # 综合排序
    MOST_LIKE = 1  # 最多点赞
    LATEST = 2  # 最新发布


class PublishTimeType(Enum):
    """发布时间类型"""

    UNLIMITED = 0  # 不限
    ONE_DAY = 1  # 一天内
    ONE_WEEK = 7  # 一周内
    SIX_MONTH = 180  # 半年内


class DateType(Enum):
    """日期类型筛选"""

    ALL = 0  # 全部时间
    RECENT_7_DAYS = 1  # 最近7天
    RECENT_30_DAYS = 2  # 最近30天
    RECENT_90_DAYS = 3  # 最近90天


class LabelType(Enum):
    """标签类型筛选"""

    ALL = 0  # 全部标签
    ORIGINAL = 1  # 原创
    REPOST = 2  # 转发


class DurationType(Enum):
    """时长类型筛选"""

    ALL = 0  # 全部时长
    SHORT = 1  # 短视频(<1分钟)
    MEDIUM = 2  # 中等时长(1-5分钟)
    LONG = 3  # 长视频(>5分钟)
