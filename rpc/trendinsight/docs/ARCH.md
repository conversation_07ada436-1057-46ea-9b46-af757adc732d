# rpc/trendinsight-new 重构计划

我们将 `rpc/trendinsight` 的功能使用 `rpc/common` 的新架构重构，并迁移到 `rpc/trendinsight-new` 目录中。

这个重构的核心思想是利用 `rpc/common` 中定义的 **“三步增强模式”**，实现一个职责清晰、可扩展、易于维护的 TrendInsight RPC 模块。

## 1. 核心理念与目标

我们将遵循 `rpc/common/example.py` 中展示的设计模式：

1. **职责分离**：
   * **账户管理 (`AccountProvider`)**: 专门负责提供和管理 TrendInsight 账号的 Cookies 和身份信息。
   * **代理管理 (`ProxyProvider`)**: 专门负责提供代理 IP。
   * **请求增强 (`RequestEnhancer`)**: 专门负责处理 TrendInsight 特有的请求参数，如 `msToken`, `X-Bogus` 等签名算法。
   * **API 调用**: 业务逻辑代码只负责调用 API，无需关心底层实现细节。

2. **业务方自治**：
   * 创建一个 TrendInsight 专用的、预配置好的 `httpx.Client`。
   * 所有与 TrendInsight 相关的业务（如巨量算数、TrendInsight API等）都将复用这个客户端实例。

3. **目标**：
   * 移除 `rpc/trendinsight` 中杂糅的请求逻辑。
   * 代码结构更清晰，符合单一职责原则。
   * 提高代码的可测试性和可维护性。
   * 为未来接入更多平台（如快手、小红书）提供一个标准的、可复制的模式。

## 2. 拟定目录结构

我们将在 `rpc` 目录下创建一个新的 `trendinsight-new` 文件夹：

```text
rpc/
├── common/          # (已存在) 存放通用 RPC 组件
│   ├── __init__.py
│   ├── base.py
│   └── example.py
├── trendinsight-new/      # (新建) 新的 TrendInsight RPC 模块
│   ├── __init__.py  # 导出 trendinsight_client，方便业务方使用
│   ├── client.py    # 负责组装和初始化 TrendInsight 专用客户端
│   ├── enhancer.py  # 存放 TrendInsight 请求增强器 (TrendInsightRequestEnhancer)
│   └── api.py       # 存放具体的业务 API 实现 (原 trendinsight 的功能)
└── trendinsight/    # (待废弃) 旧的实现
```

## 3. UML 设计

### 3.1. 类图 (Class Diagram)

这张图展示了新模块中各个组件之间的关系。

```mermaid
classDiagram
    direction LR

    class httpx.Client {
        +transport
        +get()
        +post()
    }

    class DynamicProxyTransport {
        +platform
        +account_provider
        +proxy_provider
        +request_enhancer
        +handle_request()
    }

    class RequestEnhancer {
        <<Interface>>
        +enhance_request(request)
    }

    class AccountProvider {
        <<Interface>>
        +get_account(platform)
    }

    class ProxyProvider {
        <<Interface>>
        +get_proxy()
    }

    class TrendInsightRequestEnhancer {
        +enhance_request(request)
    }

    class DatabaseAccountProvider {
        +get_account(platform)
    }

    class DefaultProxyProvider {
        +get_proxy()
    }

    class TrendInsightAPI {
        -client: httpx.Client
        +get_video_data()
        +get_author_data()
    }

    httpx.Client o-- DynamicProxyTransport
    DynamicProxyTransport o-- RequestEnhancer
    DynamicProxyTransport o-- AccountProvider
    DynamicProxyTransport o-- ProxyProvider

    RequestEnhancer <|-- TrendInsightRequestEnhancer
    AccountProvider <|-- DatabaseAccountProvider
    ProxyProvider <|-- DefaultProxyProvider

    TrendInsightAPI o-- httpx.Client

    namespace rpc.common {
        class DynamicProxyTransport
        class RequestEnhancer
        class AccountProvider
        class ProxyProvider
        class DatabaseAccountProvider
        class DefaultProxyProvider
    }

    namespace rpc.trendinsight-new {
        class TrendInsightRequestEnhancer
        class TrendInsightAPI
    }
```

* **核心**: `DynamicProxyTransport` 像一个调度中心，它组合了 `AccountProvider`、`ProxyProvider` 和 `RequestEnhancer`。
* **实现**: 我们将为 TrendInsight 创建一个具体的 `TrendInsightRequestEnhancer`，并复用通用的 `DatabaseAccountProvider` 和 `DefaultProxyProvider`。
* **使用**: `TrendInsightAPI` (业务代码) 将通过一个预配置的 `httpx.Client` 实例来发起请求，完全无需感知底层的复杂性。

### 3.2. 序列图 (Sequence Diagram)

这张图展示了一次完整的 API 调用流程。

```mermaid
sequenceDiagram
    participant BusinessLogic as 业务逻辑
    participant TrendInsightAPI as trendinsight_new.api
    participant trendinsight_client as trendinsight_new.client
    participant DynamicProxyTransport as common.transport
    participant TrendInsightRequestEnhancer as trendinsight_new.enhancer
    participant DatabaseAccountProvider as common.account
    participant DefaultProxyProvider as common.proxy

    BusinessLogic->>TrendInsightAPI: 调用 get_video_data(params)
    TrendInsightAPI->>trendinsight_client: client.get(url, params=params)
    trendinsight_client->>DynamicProxyTransport: handle_request(request)

    DynamicProxyTransport->>DatabaseAccountProvider: get_account("trendinsight")
    DatabaseAccountProvider-->>DynamicProxyTransport: 返回 AccountInfo (含 cookies)

    DynamicProxyTransport->>DefaultProxyProvider: get_proxy()
    DefaultProxyProvider-->>DynamicProxyTransport: 返回 Proxy Info (或 None)

    DynamicProxyTransport->>TrendInsightRequestEnhancer: enhance_request(request)
    Note right of TrendInsightRequestEnhancer: 1. 添加 msToken<br/>2. 计算 X-Bogus<br/>3. 添加其他必要 headers
    TrendInsightRequestEnhancer-->>DynamicProxyTransport: 完成请求增强

    DynamicProxyTransport->>Internet: 发送最终的 HTTP 请求
    Internet-->>DynamicProxyTransport: 返回 HTTP 响应
    DynamicProxyTransport-->>trendinsight_client: 返回 Response
    trendinsight_client-->>TrendInsightAPI: 返回 Response
    TrendInsightAPI-->>BusinessLogic: 返回处理后的数据
```

## 4. 流程图 (Flowchart)

这个流程图更直观地展示了请求处理的每一步。

```mermaid
graph TD
    A[开始: 业务代码调用 API] --> B{使用预配置的<br>trendinsight_client 发起请求};
    B --> C[请求进入 DynamicProxyTransport];
    C --> D{获取账户信息<br>(DatabaseAccountProvider)};
    D --> E{获取代理信息<br>(DefaultProxyProvider)};
    E --> F{增强请求<br>(TrendInsightRequestEnhancer)};
    F --> G[组合最终请求<br>(URL, Headers, Cookies, Proxy)];
    G --> H{发送 HTTP 请求到 TrendInsight 服务器};
    H --> I[接收响应];
    I --> J[返回响应给业务代码];
    J --> K[结束];

    subgraph "rpc.trendinsight-new"
        A
        J
        K
    end

    subgraph "rpc.common & httpx"
        B
        C
        D
        E
        F
        G
        H
        I
    end
```

## 5. 实施步骤

1. **创建目录和文件**:
   * 创建 `rpc/trendinsight-new` 目录。
   * 在 `rpc/trendinsight-new` 中创建 `__init__.py`, `client.py`, `enhancer.py`, `api.py` 四个文件。

2. **实现 `enhancer.py`**:
   * 创建 `TrendInsightRequestEnhancer` 类，继承自 `rpc.common.RequestEnhancer`。
   * 将 `rpc/trendinsight` 中所有关于请求参数修改、签名计算的逻辑迁移到 `enhance_request` 方法中。

3. **实现 `client.py`**:
   * 导入 `DynamicProxyTransport` 和 `httpx`。
   * 导入 `TrendInsightRequestEnhancer`、`DatabaseAccountProvider`、`DefaultProxyProvider`。
   * 实例化上述提供者和增强器。
   * 创建 `DynamicProxyTransport` 实例，并指定 `platform=Platform.TRENDINSIGHT`。
   * 创建并导出一个名为 `trendinsight_client` 的 `httpx.Client` 实例，将其 `transport` 参数设置为创建的 `DynamicProxyTransport` 实例。

4. **实现 `api.py`**:
   * 从 `rpc.trendinsight-new.client` 导入 `trendinsight_client`。
   * 将 `rpc/trendinsight` 中的业务函数（如 `get_aweme_list`, `get_challenge_sug` 等）迁移过来。
   * 修改这些函数，使其使用 `trendinsight_client` 来发送请求，移除所有手动的 `headers`, `cookies`, `proxies` 组装逻辑。

5. **更新 `__init__.py`**:
   * 在 `rpc/trendinsight-new/__init__.py` 中，`from .client import trendinsight_client`，方便外部模块直接从 `rpc.trendinsight-new` 导入客户端实例。

6. **重构业务代码**:
   * 找到项目中所有调用旧 `rpc/trendinsight` 函数的地方。
   * 将其修改为调用新的 `rpc.trendinsight-new.api` 中的函数。

7. **测试与清理**:
   * 编写单元测试或集成测试，确保新模块的功能与旧模块一致。
   * 确认所有功能正常后，可以安全地删除 `rpc/trendinsight` 目录。

这个计划为你提供了一个清晰的蓝图。如果你同意这个方案，我们可以开始着手实施第一步。
