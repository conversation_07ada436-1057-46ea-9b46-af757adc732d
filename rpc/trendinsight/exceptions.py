"""
TrendInsight API 异常类定义

定义 TrendInsight API 客户端可能抛出的各种异常
"""


class TrendInsightError(Exception):
    """TrendInsight API 基础异常类"""

    def __init__(self, message: str, status_code: int = None, response_data: dict = None):
        super().__init__(message)
        self.message = message
        self.status_code = status_code
        self.response_data = response_data

    def __str__(self):
        if self.status_code:
            return f"[{self.status_code}] {self.message}"
        return self.message


class TrendInsightClientError(TrendInsightError):
    """客户端错误"""

    pass


class TrendInsightRequestError(TrendInsightError):
    """请求错误"""

    pass


class TrendInsightResponseError(TrendInsightError):
    """响应错误"""

    pass


class TrendInsightAuthenticationError(TrendInsightError):
    """认证错误"""

    pass


class TrendInsightRateLimitError(TrendInsightError):
    """请求频率限制错误"""

    pass


class TrendInsightValidationError(TrendInsightError):
    """数据验证错误"""

    pass


class TrendInsightTimeoutError(TrendInsightError):
    """请求超时错误"""

    pass


class DataFetchError(TrendInsightError):
    """数据获取错误"""

    pass


class TrendInsightSignError(TrendInsightError):
    """签名生成错误"""

    pass


class TrendInsightConfigError(TrendInsightError):
    """配置错误"""

    pass
