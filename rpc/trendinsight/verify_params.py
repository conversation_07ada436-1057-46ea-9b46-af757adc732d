# -*- coding: utf-8 -*-
"""
TrendInsight 验证参数生成器

基于 MediaCrawlerPro-Python 项目的参数生成逻辑实现
用于自动生成 verify_fp、ms_token、webid、uifid 等参数
"""

import random
import time
from typing import Optional

import httpx
from loguru import logger
from pydantic import BaseModel, Field

from .exceptions import TrendInsightClientError

# ==================== 常量定义 ====================

# TrendInsight 固定 User-Agent
TRENDINSIGHT_FIXED_USER_AGENT = "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"

# MS Token 请求相关（复用抖音的接口）
DOUYIN_MS_TOKEN_REQ_URL = "https://mssdk.bytedance.com/web/common"
DOUYIN_MS_TOKEN_REQ_STR_DATA = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz"

# WebID 请求相关（复用抖音的接口）
DOUYIN_WEBID_REQ_URL = "https://mcs.zijieapi.com/webid?aid=6383&sdk_version=5.1.18_zip&device_platform=web"


# ==================== 工具函数 ====================


def get_current_timestamp() -> int:
    """获取当前时间戳（13位毫秒）"""
    return int(time.time() * 1000)


def get_random_str(random_len: int = 12) -> str:
    """生成随机字符串"""
    chars = "AaBbCcDdEeFfGgHhIiJjKkLlMmNnOoPpQqRrSsTtUuVvWwXxYyZz0123456789"
    length = len(chars) - 1
    random_str = ""
    for i in range(random_len):
        random_str += chars[random.randint(0, length)]
    return random_str


def get_web_id() -> str:
    """
    生成随机的 webid
    基于 MediaCrawlerPro-Python 项目的算法
    """

    def e(t):
        if t is not None:
            return str(t ^ (int(16 * random.random()) >> (t // 4)))
        else:
            return "".join(
                [
                    str(int(1e7)),
                    "-",
                    str(int(1e3)),
                    "-",
                    str(int(4e3)),
                    "-",
                    str(int(8e3)),
                    "-",
                    str(int(1e11)),
                ]
            )

    web_id = "".join(e(int(x)) if x in "018" else x for x in e(None))
    return web_id.replace("-", "")[:19]


# ==================== 数据模型 ====================


class CommonVerifyParams(BaseModel):
    """通用验证参数模型"""

    ms_token: str = Field(..., title="ms_token", description="ms_token")
    webid: str = Field(..., title="webid", description="webid")
    verify_fp: str = Field(..., title="verify_fp", description="verify_fp")
    s_v_web_id: str = Field(..., title="s_v_web_id", description="s_v_web_id")
    uifid: Optional[str] = Field(None, title="uifid", description="uifid")


# ==================== 核心类 ====================


class TokenManager:
    """
    Token 管理器
    基于 MediaCrawlerPro-Python 项目的 TokenManager 实现
    """

    def __init__(self, user_agent: str = TRENDINSIGHT_FIXED_USER_AGENT):
        self._user_agent = user_agent

    async def gen_real_msToken(self) -> str:
        """
        生成真实的 msToken
        """
        async with httpx.AsyncClient() as client:
            post_data = {
                "magic": 538969122,
                "version": 1,
                "dataType": 8,
                "strData": DOUYIN_MS_TOKEN_REQ_STR_DATA,
                "tspFromClient": get_current_timestamp(),
                "url": 0,
            }
            headers = {
                "Content-Type": "application/json; charset=utf-8",
                "User-Agent": self._user_agent,
            }
            response = await client.post(DOUYIN_MS_TOKEN_REQ_URL, json=post_data, headers=headers)
            ms_token = str(httpx.Cookies(response.cookies).get("msToken"))
            if len(ms_token) not in [120, 128]:
                raise TrendInsightClientError(f"获取msToken内容不符合要求: {ms_token}")
            return ms_token

    @classmethod
    def gen_fake_msToken(cls) -> str:
        """
        生成假的msToken
        """
        false_ms_token = get_random_str(126) + "=="
        return false_ms_token

    async def get_msToken(self) -> str:
        """
        获取 msToken
        """
        try:
            return await self.gen_real_msToken()
        except Exception as e:
            logger.warning(f"gen_real_msToken error: {e}, return a fake msToken")
            return self.gen_fake_msToken()

    async def gen_webid(self) -> str:
        """
        生成个性化追踪webid
        """
        async with httpx.AsyncClient() as client:
            post_data = {
                "app_id": 6383,
                "referer": "https://trendinsight.oceanengine.com/",
                "url": "https://trendinsight.oceanengine.com/",
                "user_agent": self._user_agent,
                "user_unique_id": "",
            }
            headers = {
                "User-Agent": self._user_agent,
                "Content-Type": "application/json; charset=UTF-8",
                "Referer": "https://trendinsight.oceanengine.com/",
            }
            try:
                response = await client.post(DOUYIN_WEBID_REQ_URL, json=post_data, headers=headers)
                webid = response.json().get("web_id")
                if not webid:
                    raise TrendInsightClientError("获取webid失败")
                return webid
            except Exception as e:
                logger.warning(f"gen_webid error: {e}, return a random webid")
                return get_web_id()


class VerifyFpManager:
    """
    VerifyFp 管理器
    基于 MediaCrawlerPro-Python 项目的 VerifyFpManager 实现
    """

    @classmethod
    def gen_verify_fp(cls) -> str:
        """
        生成verifyFp 与 s_v_web_id
        """
        base_str = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz"
        t = len(base_str)
        milliseconds = int(round(time.time() * 1000))
        base36 = ""
        while milliseconds > 0:
            remainder = milliseconds % 36
            if remainder < 10:
                base36 = str(remainder) + base36
            else:
                base36 = chr(ord("a") + remainder - 10) + base36
            milliseconds = int(milliseconds / 36)
        r = base36
        o = [""] * 36
        o[8] = o[13] = o[18] = o[23] = "_"
        o[14] = "4"

        for i in range(36):
            if not o[i]:
                n = 0 or int(random.random() * t)
                if i == 19:
                    n = 3 & n | 8
                o[i] = base_str[n]

        return "verify_" + r + "_" + "".join(o)

    @classmethod
    def gen_s_v_web_id(cls) -> str:
        return cls.gen_verify_fp()


class VerifyParamsGenerator:
    """
    验证参数生成器
    """

    def __init__(self, user_agent: str = TRENDINSIGHT_FIXED_USER_AGENT):
        self.user_agent = user_agent
        self.token_manager = TokenManager(user_agent)

    async def generate_common_verify_params(self) -> CommonVerifyParams:
        """
        生成通用验证参数
        """
        logger.info("开始生成 TrendInsight 通用验证参数")

        ms_token = await self.token_manager.get_msToken()
        webid = await self.token_manager.gen_webid()
        verify_fp = VerifyFpManager.gen_verify_fp()
        s_v_web_id = VerifyFpManager.gen_s_v_web_id()

        logger.info(
            f"生成参数完成 - ms_token: {ms_token[:20]}..., webid: {webid}, "
            f"verify_fp: {verify_fp[:20]}..., s_v_web_id: {s_v_web_id[:20]}..."
        )

        return CommonVerifyParams(ms_token=ms_token, webid=webid, verify_fp=verify_fp, s_v_web_id=s_v_web_id)


# 兼容性别名
CommonVerfiyParams = CommonVerifyParams
