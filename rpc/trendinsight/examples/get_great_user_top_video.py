"""
新增接口使用示例：获取优秀用户热门视频

这个接口基于您提供的 curl 命令和响应数据实现
API 地址: /api/v2/daren/get_great_user_top_video
"""

import asyncio

from log import logger

from rpc.trendinsight.api import AsyncTrendInsightAPI
from rpc.trendinsight.schemas import GreatUserTopVideoRequest


async def example_get_great_user_top_video():
    """
    使用示例：获取优秀用户热门视频
    """
    # 创建 API 客户端
    api = AsyncTrendInsightAPI()

    # 创建请求参数
    request = GreatUserTopVideoRequest(user_id="jjhbdigjfha", start_date="20250622", end_date="20250722")

    try:
        # 调用接口
        response = await api.get_great_user_top_video(request)

        # 检查响应
        if response.is_success:
            logger.info(f"✅ 请求成功，状态码: {response.status}")

            if response.data:
                # 输出各类排序的视频数量
                logger.info(f"评论排序视频数: {len(response.data.comment_list) if response.data.comment_list else 0}")
                logger.info(f"时间排序视频数: {len(response.data.create_time_list) if response.data.create_time_list else 0}")
                logger.info(f"关注排序视频数: {len(response.data.follow_list) if response.data.follow_list else 0}")
                logger.info(f"指数排序视频数: {len(response.data.index_list) if response.data.index_list else 0}")
                logger.info(f"点赞排序视频数: {len(response.data.like_list) if response.data.like_list else 0}")
                logger.info(f"分享排序视频数: {len(response.data.share_list) if response.data.share_list else 0}")

                # 输出第一个热门视频的信息
                if response.data.comment_list and len(response.data.comment_list) > 0:
                    video = response.data.comment_list[0]
                    logger.info(f"\n热门视频信息:")
                    logger.info(f"  视频ID: {video.item_id}")
                    logger.info(f"  排名: {video.rank}")
                    logger.info(f"  点赞数: {video.like_cnt}")
                    logger.info(f"  评论数: {video.coment_cnt}")
                    logger.info(f"  分享数: {video.share_cnt}")
                    logger.info(f"  关注数: {video.follow_cnt}")
                    logger.info(f"  创建时间: {video.create_time}")
                    logger.info(f"  视频文案: {video.video_text[:100]}...")
                    logger.info(f"  视频链接: {video.video_url}")
        else:
            logger.error(f"❌ 请求失败，状态码: {response.status}, 消息: {response.msg}")

    except Exception as e:
        logger.error(f"❌ 请求异常: {e}")


# 同步方法的使用（需要先实现同步版本）
def sync_example():
    """
    注意：当前只实现了异步版本，如需同步版本，需要在 DouyinAPI 类中也添加对应方法
    """
    pass


if __name__ == "__main__":
    logger.info("获取优秀用户热门视频接口使用示例")
    logger.info("=" * 50)
    asyncio.run(example_get_great_user_top_video())