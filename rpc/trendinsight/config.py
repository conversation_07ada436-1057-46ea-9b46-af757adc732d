"""
TrendInsight API 配置类

定义 TrendInsight API 客户端的配置参数
"""

from typing import Dict, Optional

from pydantic import BaseModel, Field, HttpUrl

from settings.config import settings


class TrendInsightConfig(BaseModel):
    """
    TrendInsight API 客户端配置类

    使用 Pydantic 进行强类型校验，并从 Dynaconf 读取配置
    """

    # 基础配置
    base_url: HttpUrl = Field(default="https://trendinsight.oceanengine.com", description="API 基础地址")
    timeout: float = Field(default=30.0, description="请求超时时间")
    max_retries: int = Field(default=3, ge=0, description="最大重试次数")
    retry_delay: float = Field(default=1.0, ge=0, description="重试延迟时间")

    # 代理配置
    proxy_enabled: bool = Field(default=True, description="是否启用代理")
    proxy_provider: Optional[str] = Field(default=None, description="代理提供商")

    # SSL 配置
    verify_ssl: bool = Field(default=True, description="是否校验 SSL 证书")

    # 重定向配置
    follow_redirects: bool = Field(default=True, description="是否跟随重定向")

    # 签名配置
    use_javascript_sign: bool = Field(default=True, description="是否使用 JavaScript 签名")
    sign_js_file_path: Optional[str] = Field(default=None, description="签名 JS 文件路径")
    sign_cache_enabled: bool = Field(default=True, description="是否启用签名缓存")
    sign_cache_ttl: int = Field(default=300, ge=0, description="签名缓存时间 (秒)")
    sign_max_retries: int = Field(default=3, ge=0, description="签名最大重试次数")
    sign_retry_delay: float = Field(default=1.0, ge=0, description="签名重试延迟时间")

    # 默认请求头
    default_headers: Dict[str, str] = Field(
        default_factory=lambda: {
            "accept": "application/json, text/plain, */*",
            "accept-encoding": "gzip, deflate, br",
            "accept-language": "zh-CN,zh;q=0.9,en;q=0.8",
            "cache-control": "no-cache",
            "content-type": "application/json;charset=UTF-8",
            "origin": "https://trendinsight.oceanengine.com",
            "pragma": "no-cache",
            "sec-ch-ua": '"Google Chrome";v="135", "Chromium";v="135", "Not?A_Brand";v="99"',
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": '"macOS"',
            "sec-fetch-dest": "empty",
            "sec-fetch-mode": "cors",
            "sec-fetch-site": "same-origin",
            "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        }
    )

    class Config:
        env_prefix = "TRENDINSIGHT_"
        case_sensitive = False

    @classmethod
    def from_dynaconf(cls) -> "TrendInsightConfig":
        """从 Dynaconf settings 加载配置"""
        # 假设所有 trendinsight 的配置都在 settings.trendinsight 下
        if "trendinsight" in settings:
            config_data = settings.get("trendinsight", {})
        else:
            # 保持旧的逻辑作为后备，以便于兼容环境变量
            config_data = {
                key.lower().replace(cls.Config.env_prefix.lower(), ""): value
                for key, value in settings.as_dict().items()
                if key.lower().startswith(cls.Config.env_prefix.lower())
            }
        return cls(**config_data)

    def to_dict(self) -> Dict[str, any]:
        """转换为字典"""
        return self.model_dump(exclude_none=True)


# 创建一个默认的配置实例，以便在其他地方直接导入使用
# 这个实例会从环境变量和配置文件中加载配置
trendinsight_config = TrendInsightConfig.from_dynaconf()
