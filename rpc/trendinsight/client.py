"""
TrendInsight 客户端模块

基于 rpc.common 的三步增强模式创建预配置的 TrendInsight 客户端
"""

import httpx

from log.log import logger
from models.enums import Platform
from rpc.common.transport import DynamicProxyTransport
from rpc.providers.account_provider import DefaultAccountProvider
from rpc.providers.proxy_provider import DefaultProxyProvider

from .config import TrendInsightConfig
from .enhancer import TrendInsightRequestEnhancer


def create_trendinsight_client(
    config: TrendInsightConfig = None,
    user_agent: str = None,
    auto_generate_params: bool = True,
    timeout: float = None,
) -> httpx.Client:
    """
    创建预配置的同步 TrendInsight 客户端

    Args:
        config: TrendInsight 配置
        user_agent: 用户代理字符串
        auto_generate_params: 是否自动生成验证参数
        timeout: 请求超时时间

    Returns:
        预配置的 httpx.Client 实例
    """
    # 1. 创建配置
    if config is None:
        config = TrendInsightConfig()

    # 2. 创建提供者实例
    account_provider = DefaultAccountProvider()
    proxy_provider = DefaultProxyProvider(proxy_source=config.proxy_provider) if config.proxy_enabled else None

    # 3. 创建 TrendInsight 专用请求增强器
    request_enhancer = TrendInsightRequestEnhancer(
        config=config,
        user_agent=user_agent,
        auto_generate_params=auto_generate_params,
    )

    # 4. 创建动态传输层
    transport = DynamicProxyTransport(
        platform=Platform.TRENDINSIGHT,  # TrendInsight 平台标识
        account_provider=account_provider,
        proxy_provider=proxy_provider,
        request_enhancer=request_enhancer,
    )

    # 5. 创建同步 httpx 客户端
    client_timeout = timeout or config.timeout
    client = httpx.Client(
        transport=transport,
        timeout=httpx.Timeout(client_timeout),
        verify=config.verify_ssl,
        follow_redirects=config.follow_redirects,
    )

    logger.info("同步 TrendInsight 客户端创建完成")
    return client


def create_async_trendinsight_client(
    config: TrendInsightConfig = None,
    user_agent: str = None,
    auto_generate_params: bool = True,
    timeout: float = None,
) -> httpx.AsyncClient:
    """
    创建预配置的异步 TrendInsight 客户端

    Args:
        config: TrendInsight 配置
        user_agent: 用户代理字符串
        auto_generate_params: 是否自动生成验证参数
        timeout: 请求超时时间

    Returns:
        预配置的 httpx.AsyncClient 实例
    """
    # 1. 创建配置
    if config is None:
        config = TrendInsightConfig()

    # 2. 创建提供者实例
    account_provider = DefaultAccountProvider()
    proxy_provider = DefaultProxyProvider(proxy_source=config.proxy_provider) if config.proxy_enabled else None

    # 3. 创建 TrendInsight 专用请求增强器
    request_enhancer = TrendInsightRequestEnhancer(
        config=config,
        user_agent=user_agent,
        auto_generate_params=auto_generate_params,
    )

    # 4. 创建动态传输层
    transport = DynamicProxyTransport(
        platform=Platform.TRENDINSIGHT,  # TrendInsight 平台标识
        account_provider=account_provider,
        proxy_provider=proxy_provider,
        request_enhancer=request_enhancer,
    )

    # 5. 创建异步 httpx 客户端
    client_timeout = timeout or config.timeout
    client = httpx.AsyncClient(
        transport=transport,
        timeout=httpx.Timeout(client_timeout),
        verify=config.verify_ssl,
        follow_redirects=config.follow_redirects,
    )

    logger.info("异步 TrendInsight 客户端创建完成")
    return client


class TrendInsightClientManager:
    """
    TrendInsight 客户端管理器

    提供客户端生命周期管理和便捷的创建方法
    """

    def __init__(self):
        self._clients = {}
        self._async_clients = {}

    def create_client(
        self, config: TrendInsightConfig = None, account_provider=None, proxy_provider=None, enhancer=None, **kwargs
    ) -> httpx.Client:
        """
        创建同步 TrendInsight 客户端

        Args:
            config: TrendInsight 配置
            account_provider: 账户提供者
            proxy_provider: 代理提供者
            enhancer: 请求增强器
            **kwargs: 传递给 httpx.Client 的其他参数

        Returns:
            配置好的 httpx.Client 实例
        """
        config = config or TrendInsightConfig()
        account_provider = account_provider or DefaultAccountProvider()
        proxy_provider = proxy_provider or (
            DefaultProxyProvider(proxy_source=config.proxy_provider) if config.proxy_enabled else None
        )
        enhancer = enhancer or TrendInsightRequestEnhancer(config=config)

        # 创建动态传输层
        transport = DynamicProxyTransport(
            platform=Platform.TRENDINSIGHT,
            account_provider=account_provider,
            proxy_provider=proxy_provider,
            request_enhancer=enhancer,
        )

        # 设置默认参数
        client_kwargs = {
            "transport": transport,
            "timeout": httpx.Timeout(config.timeout),
            "verify": config.verify_ssl,
            "follow_redirects": config.follow_redirects,
        }
        client_kwargs.update(kwargs)

        client = httpx.Client(**client_kwargs)

        # 缓存客户端
        client_id = id(client)
        self._clients[client_id] = {
            "client": client,
            "config": config,
            "enhancer": enhancer,
            "transport": transport,
        }

        logger.info(f"TrendInsight 同步客户端创建成功: {client_id}")
        return client

    def create_async_client(
        self, config: TrendInsightConfig = None, account_provider=None, proxy_provider=None, enhancer=None, **kwargs
    ) -> httpx.AsyncClient:
        """
        创建异步 TrendInsight 客户端

        Args:
            config: TrendInsight 配置
            account_provider: 账户提供者
            proxy_provider: 代理提供者
            enhancer: 请求增强器
            **kwargs: 传递给 httpx.AsyncClient 的其他参数

        Returns:
            配置好的 httpx.AsyncClient 实例
        """
        config = config or TrendInsightConfig()
        account_provider = account_provider or DefaultAccountProvider()
        proxy_provider = proxy_provider or (
            DefaultProxyProvider(proxy_source=config.proxy_provider) if config.proxy_enabled else None
        )
        enhancer = enhancer or TrendInsightRequestEnhancer(config=config)

        # 创建动态传输层
        transport = DynamicProxyTransport(
            platform=Platform.TRENDINSIGHT,
            account_provider=account_provider,
            proxy_provider=proxy_provider,
            request_enhancer=enhancer,
        )

        # 设置默认参数
        client_kwargs = {
            "transport": transport,
            "timeout": httpx.Timeout(config.timeout),
            "verify": config.verify_ssl,
            "follow_redirects": config.follow_redirects,
        }
        client_kwargs.update(kwargs)

        client = httpx.AsyncClient(**client_kwargs)

        # 缓存客户端
        client_id = id(client)
        self._async_clients[client_id] = {
            "client": client,
            "config": config,
            "enhancer": enhancer,
            "transport": transport,
        }

        logger.info(f"TrendInsight 异步客户端创建成功: {client_id}")
        return client

    def close_client(self, client: httpx.Client):
        """关闭同步客户端"""
        client_id = id(client)
        if client_id in self._clients:
            client.close()
            del self._clients[client_id]
            logger.info(f"TrendInsight 同步客户端已关闭: {client_id}")

    async def close_async_client(self, client: httpx.AsyncClient):
        """关闭异步客户端"""
        client_id = id(client)
        if client_id in self._async_clients:
            await client.aclose()
            del self._async_clients[client_id]
            logger.info(f"TrendInsight 异步客户端已关闭: {client_id}")

    def close_all_clients(self):
        """关闭所有同步客户端"""
        for client_info in list(self._clients.values()):
            client_info["client"].close()
        self._clients.clear()
        logger.info("TrendInsight 所有同步客户端已关闭")

    async def close_all_async_clients(self):
        """关闭所有异步客户端"""
        for client_info in list(self._async_clients.values()):
            await client_info["client"].aclose()
        self._async_clients.clear()
        logger.info("TrendInsight 所有异步客户端已关闭")

    async def close_all(self):
        """关闭所有客户端"""
        self.close_all_clients()
        await self.close_all_async_clients()
        logger.info("TrendInsight 所有客户端已关闭")


# 全局默认客户端管理器
client_manager = TrendInsightClientManager()

logger.info("TrendInsight 客户端管理器初始化完成")
