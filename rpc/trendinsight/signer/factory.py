# -*- coding: utf-8 -*-
"""
TrendInsight 签名工厂类

提供不同类型的签名实现创建功能
"""

from typing import Optional

from ..exceptions import TrendInsightClientError
from .logic import AbstractTrendInsightSign, TrendInsightJavascriptSign


class TrendInsightSignFactory:
    """TrendInsight 签名工厂类"""

    # 签名类型常量
    JAVASCRIPT_SIGN = "javascript"

    @staticmethod
    def get_sign(sign_type: str, **kwargs) -> AbstractTrendInsightSign:
        """
        获取签名实现

        Args:
            sign_type: 签名类型，支持的类型：
                - "javascript": JavaScript 签名实现
            **kwargs: 签名实现的初始化参数
                - js_file_path: JavaScript 文件路径（仅用于 javascript 类型）

        Returns:
            签名实现实例

        Raises:
            TrendInsightClientError: 不支持的签名类型
        """
        if sign_type == TrendInsightSignFactory.JAVASCRIPT_SIGN:
            return TrendInsightJavascriptSign(kwargs.get("js_file_path"))
        else:
            raise TrendInsightClientError(f"不支持的签名类型: {sign_type}")

    @staticmethod
    def get_available_sign_types() -> list:
        """
        获取支持的签名类型列表

        Returns:
            支持的签名类型列表
        """
        return [
            TrendInsightSignFactory.JAVASCRIPT_SIGN,
        ]

    @staticmethod
    def is_sign_type_supported(sign_type: str) -> bool:
        """
        检查签名类型是否支持

        Args:
            sign_type: 签名类型

        Returns:
            是否支持该签名类型
        """
        return sign_type in TrendInsightSignFactory.get_available_sign_types()

    @staticmethod
    def create_javascript_sign(js_file_path: Optional[str] = None) -> TrendInsightJavascriptSign:
        """
        创建 JavaScript 签名实现

        Args:
            js_file_path: JavaScript 文件路径，如果为 None 则使用默认路径

        Returns:
            JavaScript 签名实现实例
        """
        return TrendInsightJavascriptSign(js_file_path)

    @staticmethod
    def create_default_sign(**kwargs) -> AbstractTrendInsightSign:
        """
        创建默认签名实现（JavaScript）

        Args:
            **kwargs: 签名实现的初始化参数

        Returns:
            默认签名实现实例
        """
        return TrendInsightSignFactory.get_sign(TrendInsightSignFactory.JAVASCRIPT_SIGN, **kwargs)
