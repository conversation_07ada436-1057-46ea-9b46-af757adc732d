# -*- coding: utf-8 -*-
"""
TrendInsight 签名逻辑实现

基于 MediaCrawler-SignSrv 项目的 TrendInsight 签名算法
"""

import json
import os
from abc import ABC, abstractmethod
from typing import Optional

import execjs
from loguru import logger
from tenacity import RetryError, retry, stop_after_attempt, wait_fixed

from ..exceptions import TrendInsightClientError
from ..schemas import TrendInsightSignRequest, TrendInsightSignResponse


def extract_ms_token_from_cookies(cookies_str: str) -> Optional[str]:
    """
    从 cookies 字符串中提取 msToken

    Args:
        cookies_str: cookies 字符串

    Returns:
        msToken 值，如果未找到则返回 None
    """
    if not cookies_str:
        return None

    try:
        # 解析 cookies 字符串
        cookies = {}
        for item in cookies_str.split(";"):
            if "=" in item:
                key, value = item.strip().split("=", 1)
                cookies[key] = value

        return cookies.get("msToken")
    except Exception as e:
        logger.error(f"解析 cookies 失败: {e}")
        return None


class AbstractTrendInsightSign(ABC):
    """TrendInsight 签名抽象基类"""

    @abstractmethod
    async def sign(self, req_data: TrendInsightSignRequest, force_init: bool = False) -> TrendInsightSignResponse:
        """生成签名参数"""
        raise NotImplementedError


class TrendInsightJavascriptSign(AbstractTrendInsightSign):
    """TrendInsight JavaScript 签名实现"""

    def __init__(self, js_file_path: Optional[str] = None):
        """
        初始化 JavaScript 签名实现

        Args:
            js_file_path: JavaScript 文件路径，如果为 None 则使用默认路径
        """
        if js_file_path is None:
            # 使用默认的 JavaScript 文件路径
            current_dir = os.path.dirname(__file__)
            js_file_path = os.path.join(current_dir, "js", "trendinsight.js")

        self.js_file_path = js_file_path
        self._sign_obj = None
        self._init_js_context()

    def _init_js_context(self):
        """初始化 JavaScript 上下文"""
        try:
            if not os.path.exists(self.js_file_path):
                raise TrendInsightClientError(f"JavaScript 文件不存在: {self.js_file_path}")

            with open(self.js_file_path, "r", encoding="utf-8") as f:
                js_code = f.read()

            self._sign_obj = execjs.compile(js_code)
            logger.info("TrendInsight JavaScript 签名上下文初始化成功")

        except Exception as e:
            logger.error(f"初始化 JavaScript 上下文失败: {e}")
            raise TrendInsightClientError(f"初始化 JavaScript 上下文失败: {e}")

    @retry(stop=stop_after_attempt(3), wait=wait_fixed(0.5))
    async def sign(self, req: TrendInsightSignRequest, force_init: bool = False) -> TrendInsightSignResponse:
        """
        生成 TrendInsight 签名参数

        Args:
            req: 签名请求参数
            force_init: 是否强制重新初始化

        Returns:
            签名响应参数
        """
        try:
            # 如果强制初始化或者签名对象不存在，重新初始化
            if force_init or self._sign_obj is None:
                self._init_js_context()

            # 1. 获取 msToken
            ms_token = self._get_ms_token(req.cookies)
            if not ms_token:
                raise TrendInsightClientError("无法获取 msToken")

            # 2. 构建完整 URL
            base_url = f"https://trendinsight.oceanengine.com{req.uri}"

            # 3. 解析请求参数
            if hasattr(req, "params") and req.params:
                # 如果是字典格式的参数
                if isinstance(req.params, dict):
                    params_data = req.params
                else:
                    # 如果是字符串格式的参数，尝试解析为 JSON
                    try:
                        params_data = json.loads(req.params)
                    except (json.JSONDecodeError, TypeError):
                        # 如果解析失败，使用空字典
                        params_data = {}
            else:
                params_data = {}

            # 4. 生成签名参数
            sign_params = self._sign_obj.call("get_param", ms_token, base_url, params_data)
            if not sign_params:
                raise TrendInsightClientError("生成签名参数失败")

            return TrendInsightSignResponse(
                x_bogus=sign_params.get("X-Bogus", ""),
                signature=sign_params.get("_signature", ""),
                ms_token=sign_params.get("msToken", ms_token),
            )

        except Exception as e:
            logger.error(f"TrendInsight 签名生成失败: {e}")
            raise TrendInsightClientError(f"TrendInsight 签名生成失败: {e}")

    def _get_ms_token(self, cookies_str: str) -> str:
        """
        从 cookies 中提取 msToken

        Args:
            cookies_str: cookies 字符串

        Returns:
            msToken 值
        """
        # 从 cookies 中提取 msToken
        ms_token = extract_ms_token_from_cookies(cookies_str)
        if ms_token:
            logger.debug("从 cookies 中成功提取 msToken")
            return ms_token

        # 如果提取失败，直接报错
        logger.error("从 cookies 中提取 msToken 失败")
        raise TrendInsightClientError("从 cookies 中提取 msToken 失败，请检查 cookies 是否包含有效的 msToken")


class TrendInsightSignLogic:
    """TrendInsight 签名逻辑类"""

    def __init__(self, sign_type: str = "javascript", **kwargs):
        """
        初始化签名逻辑

        Args:
            sign_type: 签名类型，目前支持 "javascript"
            **kwargs: 其他参数，如 js_file_path
        """
        self.sign_type = sign_type
        self._sign_server = self._create_sign_server(**kwargs)

    def _create_sign_server(self, **kwargs) -> AbstractTrendInsightSign:
        """
        创建签名服务器实例

        Args:
            **kwargs: 创建参数

        Returns:
            签名服务器实例
        """
        if self.sign_type == "javascript":
            return TrendInsightJavascriptSign(kwargs.get("js_file_path"))
        else:
            raise NotImplementedError(f"不支持的签名类型: {self.sign_type}")

    async def sign(self, req_data: TrendInsightSignRequest) -> TrendInsightSignResponse:
        """
        生成签名

        Args:
            req_data: 签名请求参数

        Returns:
            签名响应参数
        """
        try:
            return await self._sign_server.sign(req_data)
        except RetryError:
            # 重试失败，尝试强制重新初始化
            return await self._sign_server.sign(req_data, force_init=True)
