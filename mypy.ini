[mypy]
# 启用 Pydantic 插件
plugins = pydantic.mypy

# Python 版本
python_version = 3.11

# 基本配置
warn_return_any = True
warn_unused_configs = True
warn_redundant_casts = True
warn_unused_ignores = True

# 严格模式配置（逐步启用）
disallow_untyped_defs = False
disallow_incomplete_defs = True
check_untyped_defs = True
disallow_untyped_decorators = False

# 错误处理
strict_optional = True
no_implicit_optional = True

# 导入处理
ignore_missing_imports = True
follow_imports = normal

# 输出配置
show_error_codes = True
show_column_numbers = True
pretty = True

# 项目特定配置
explicit_package_bases = True

# 针对第三方库的配置
[mypy-tortoise.*]
ignore_missing_imports = True

[mypy-asyncio.*]
ignore_missing_imports = True

[mypy-logging.*]
ignore_missing_imports = True

# Pydantic 插件配置
[pydantic-mypy]
init_forbid_extra = True
init_typed = True
warn_required_dynamic_aliases = True

# 针对项目特定模块的配置
[mypy-models.*]
disallow_untyped_defs = True

[mypy-services.*]
disallow_untyped_defs = True

[mypy-schemas.*]
disallow_untyped_defs = True

[mypy-rpc.*]
disallow_untyped_defs = True

[mypy-mappers.*]
disallow_untyped_defs = True
