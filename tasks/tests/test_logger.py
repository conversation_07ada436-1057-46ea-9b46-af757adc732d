"""测试任务日志记录器"""

import json
import logging
from datetime import datetime
from io import StringIO

pass  # 清除未使用的导入

from tasks.core.logger import TaskLogger
from tasks.core.models import TaskConfig, TaskResult


class TestTaskLogger:
    """测试任务日志记录器"""

    def setup_method(self):
        """每个测试方法前的设置"""
        self.logger = TaskLogger("test_task")

        # 创建一个字符串缓冲区来捕获日志输出
        self.log_capture = StringIO()
        handler = logging.StreamHandler(self.log_capture)
        formatter = logging.Formatter("%(message)s")  # 简化格式，只输出消息
        handler.setFormatter(formatter)

        # 清除现有handlers并添加我们的handler
        self.logger.logger.handlers.clear()
        self.logger.logger.addHandler(handler)
        self.logger.logger.setLevel(logging.DEBUG)

    def get_log_output(self):
        """获取日志输出"""
        return self.log_capture.getvalue()

    def get_last_log_json(self):
        """获取最后一条日志的JSON数据"""
        output = self.get_log_output().strip()
        if not output:
            return None

        lines = output.split("\n")
        last_line = lines[-1]
        return json.loads(last_line)

    def test_log_task_start(self):
        """测试记录任务开始"""
        config = TaskConfig(task_type="trend_refresh", batch_size=50, timeout=1800)

        self.logger.log_task_start(config)

        log_data = self.get_last_log_json()
        assert log_data["event"] == "task_start"
        assert log_data["task_type"] == "trend_refresh"
        assert log_data["config"]["batch_size"] == 50
        assert log_data["config"]["timeout"] == 1800
        assert "timestamp" in log_data

    def test_log_progress_without_total(self):
        """测试记录进度（不包含总数）"""
        self.logger.log_progress(50, message="处理中...")

        log_data = self.get_last_log_json()
        assert log_data["event"] == "progress"
        assert log_data["task_type"] == "test_task"
        assert log_data["processed"] == 50
        assert log_data["message"] == "处理中..."
        assert "total" not in log_data
        assert "percentage" not in log_data
        assert "timestamp" in log_data

    def test_log_progress_with_total(self):
        """测试记录进度（包含总数）"""
        self.logger.log_progress(30, total=100)

        log_data = self.get_last_log_json()
        assert log_data["event"] == "progress"
        assert log_data["processed"] == 30
        assert log_data["total"] == 100
        assert log_data["percentage"] == 30.0

    def test_log_progress_percentage_calculation(self):
        """测试百分比计算"""
        self.logger.log_progress(33, total=100)

        log_data = self.get_last_log_json()
        assert log_data["percentage"] == 33.0

        self.logger.log_progress(1, total=3)

        log_data = self.get_last_log_json()
        assert log_data["percentage"] == 33.33

    def test_log_task_complete_success(self):
        """测试记录任务成功完成"""
        start_time = datetime(2025, 1, 21, 10, 0, 0)
        end_time = datetime(2025, 1, 21, 10, 30, 0)

        result = TaskResult(
            task_type="trend_refresh",
            status="success",
            start_time=start_time,
            end_time=end_time,
            duration=1800.0,
            processed_count=100,
            success_count=95,
            failed_count=5,
            errors=[],
        )

        self.logger.log_task_complete(result)

        log_data = self.get_last_log_json()
        assert log_data["event"] == "task_complete"
        assert log_data["task_type"] == "trend_refresh"
        assert log_data["status"] == "success"
        assert log_data["duration"] == 1800.0
        assert log_data["processed_count"] == 100
        assert log_data["success_count"] == 95
        assert log_data["failed_count"] == 5
        assert log_data["success_rate"] == 95.0
        assert "error_count" not in log_data
        assert "sample_errors" not in log_data

    def test_log_task_complete_with_errors(self):
        """测试记录有错误的任务完成"""
        errors = ["错误1", "错误2", "错误3", "错误4", "错误5", "错误6", "错误7"]

        result = TaskResult(
            task_type="trend_refresh",
            status="partial",
            start_time=datetime.now(),
            end_time=datetime.now(),
            duration=1800.0,
            processed_count=100,
            success_count=93,
            failed_count=7,
            errors=errors,
        )

        self.logger.log_task_complete(result)

        log_data = self.get_last_log_json()
        assert log_data["status"] == "partial"
        assert log_data["error_count"] == 7
        assert log_data["sample_errors"] == errors[:5]  # 只显示前5个错误

    def test_log_error(self):
        """测试记录错误"""
        extra_data = {"video_id": "123456", "attempt": 1}

        self.logger.log_error("API调用失败", extra_data)

        log_data = self.get_last_log_json()
        assert log_data["event"] == "error"
        assert log_data["task_type"] == "test_task"
        assert log_data["error"] == "API调用失败"
        assert log_data["video_id"] == "123456"
        assert log_data["attempt"] == 1
        assert "timestamp" in log_data

    def test_log_warning(self):
        """测试记录警告"""
        extra_data = {"retry_count": 2}

        self.logger.log_warning("重试中...", extra_data)

        log_data = self.get_last_log_json()
        assert log_data["event"] == "warning"
        assert log_data["task_type"] == "test_task"
        assert log_data["message"] == "重试中..."
        assert log_data["retry_count"] == 2
        assert "timestamp" in log_data

    def test_log_info(self):
        """测试记录信息"""
        extra_data = {"operation": "sync_videos"}

        self.logger.log_info("处理完成", extra_data)

        log_data = self.get_last_log_json()
        assert log_data["event"] == "info"
        assert log_data["task_type"] == "test_task"
        assert log_data["message"] == "处理完成"
        assert log_data["operation"] == "sync_videos"
        assert "timestamp" in log_data

    def test_log_debug(self):
        """测试记录调试信息"""
        extra_data = {"query_time": 0.5}

        self.logger.log_debug("查询数据库", extra_data)

        log_data = self.get_last_log_json()
        assert log_data["event"] == "debug"
        assert log_data["task_type"] == "test_task"
        assert log_data["message"] == "查询数据库"
        assert log_data["query_time"] == 0.5
        assert "timestamp" in log_data

    def test_logger_name(self):
        """测试日志记录器名称"""
        custom_logger = TaskLogger("custom_task")
        assert custom_logger.task_type == "custom_task"
        assert custom_logger.logger.name == "tasks.custom_task"

    def test_multiple_log_calls(self):
        """测试多次日志调用"""
        self.logger.log_debug("调试信息1")
        self.logger.log_warning("警告信息")
        self.logger.log_error("错误信息")

        output = self.get_log_output()
        lines = output.strip().split("\n")
        assert len(lines) == 3

        # 验证每条日志都是有效的JSON
        for line in lines:
            data = json.loads(line)
            assert "timestamp" in data
            assert "task_type" in data
            assert data["task_type"] == "test_task"
