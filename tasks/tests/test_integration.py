"""集成测试 - 测试完整的任务执行流程"""

import json
import subprocess
import sys
from pathlib import Path

pass  # 清除未使用的导入

import pytest

# 项目根目录
PROJECT_ROOT = Path(__file__).parent.parent.parent


class TestTaskIntegration:
    """任务系统集成测试"""

    def test_command_line_help(self):
        """测试命令行帮助信息"""
        result = subprocess.run([sys.executable, "tasks/main.py"], cwd=PROJECT_ROOT, capture_output=True, text=True)

        assert result.returncode == 1
        assert "错误: 需要提供 JSON 配置参数" in result.stderr
        assert "用法:" in result.stderr

    def test_command_line_invalid_json(self):
        """测试无效 JSON 参数"""
        result = subprocess.run(
            [sys.executable, "tasks/main.py", '{"invalid_json'], cwd=PROJECT_ROOT, capture_output=True, text=True
        )

        assert result.returncode == 1
        assert "错误: JSON 格式无效" in result.stderr

    def test_command_line_invalid_task_type(self):
        """测试无效任务类型"""
        result = subprocess.run(
            [sys.executable, "tasks/main.py", '{"task_type": "invalid_task"}'],
            cwd=PROJECT_ROOT,
            capture_output=True,
            text=True,
        )

        assert result.returncode == 1
        assert "错误: 配置验证失败" in result.stderr

    def test_command_line_invalid_batch_size(self):
        """测试无效批处理大小"""
        result = subprocess.run(
            [sys.executable, "tasks/main.py", '{"task_type": "trend_refresh", "batch_size": 0}'],
            cwd=PROJECT_ROOT,
            capture_output=True,
            text=True,
        )

        assert result.returncode == 1
        assert "错误: 配置验证失败" in result.stderr

    def test_command_line_with_timeout(self):
        """测试任务超时"""
        # 设置很短的超时时间，确保任务会超时
        result = subprocess.run(
            [sys.executable, "tasks/main.py", '{"task_type": "trend_refresh", "batch_size": 1, "timeout": 1}'],
            cwd=PROJECT_ROOT,
            capture_output=True,
            text=True,
            timeout=30,  # 给进程本身30秒超时，防止测试卡死
        )

        # 任务超时应该返回错误码 3
        assert result.returncode == 3
        assert "任务执行超时" in result.stderr

    @pytest.mark.skipif(not Path(PROJECT_ROOT / "db.sqlite3").exists(), reason="数据库文件不存在，跳过实际数据库测试")
    def test_command_line_with_database(self):
        """测试与实际数据库的集成（如果数据库存在）"""
        # 使用较短的超时时间，避免测试运行太久
        result = subprocess.run(
            [sys.executable, "tasks/main.py", '{"task_type": "trend_refresh", "batch_size": 1, "timeout": 5}'],
            cwd=PROJECT_ROOT,
            capture_output=True,
            text=True,
            timeout=30,
        )

        # 应该能成功初始化并运行（可能超时，但不应该是配置错误）
        assert result.returncode in [0, 1, 2, 3]  # 成功、部分成功、失败或超时都是合理的

        if result.stdout:
            # 如果有输出，应该是有效的 JSON
            try:
                output_data = json.loads(result.stdout)
                assert "task_type" in output_data
                assert output_data["task_type"] == "trend_refresh"
            except json.JSONDecodeError:
                pytest.fail("输出不是有效的 JSON 格式")

    def test_keyboard_interrupt(self):
        """测试键盘中断处理"""
        # 这个测试比较难实现，因为需要模拟键盘中断
        # 这里只验证退出码定义是正确的
        assert 128 + 2 == 130  # SIGINT 的退出码

    def test_json_output_format(self):
        """测试 JSON 输出格式（使用 mock）"""
        from datetime import datetime

        from tasks.models import TaskResult

        # 创建一个示例结果
        result = TaskResult(
            task_type="trend_refresh",
            status="success",
            start_time=datetime(2025, 1, 21, 10, 0, 0),
            end_time=datetime(2025, 1, 21, 10, 5, 0),
            duration=300.0,
            processed_count=10,
            success_count=8,
            failed_count=2,
            errors=["示例错误1", "示例错误2"],
        )

        # 验证 JSON 输出格式
        json_output = result.to_json()
        data = json.loads(json_output)

        # 验证必需字段存在
        required_fields = [
            "task_type",
            "status",
            "start_time",
            "end_time",
            "duration",
            "processed_count",
            "success_count",
            "failed_count",
        ]

        for field in required_fields:
            assert field in data, f"缺少必需字段: {field}"

        # 验证数据类型和值
        assert data["task_type"] == "trend_refresh"
        assert data["status"] == "success"
        assert data["duration"] == 300.0
        assert data["processed_count"] == 10
        assert data["success_count"] == 8
        assert data["failed_count"] == 2
        assert len(data["errors"]) == 2

    def test_exit_codes_definition(self):
        """测试退出码定义正确"""
        # 验证各种情况下的退出码定义
        exit_codes = {
            "success": 0,
            "partial_success": 1,
            "complete_failure": 2,
            "timeout": 3,
            "execution_error": 4,
            "system_error": 5,
            "keyboard_interrupt": 130,
        }

        # 这些是在 tasks/main.py 中定义的退出码
        assert exit_codes["success"] == 0
        assert exit_codes["partial_success"] == 1
        assert exit_codes["complete_failure"] == 2
        assert exit_codes["timeout"] == 3
        assert exit_codes["execution_error"] == 4
        assert exit_codes["system_error"] == 5
        assert exit_codes["keyboard_interrupt"] == 130
