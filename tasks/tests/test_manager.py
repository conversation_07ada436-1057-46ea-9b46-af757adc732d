"""测试任务管理器"""

from datetime import datetime

pass  # 清除未使用的导入

import pytest

from tasks.base import BaseTask
from tasks.manager import TaskManager
from tasks.models import TaskConfig, TaskResult


class MockTask(BaseTask):
    """模拟任务类"""

    def __init__(self, config, logger):
        super().__init__(config, logger)
        self.validate_called = False
        self.execute_called = False
        self.should_validate = True
        self.should_succeed = True
        self.execution_time = 0.1  # 模拟执行时间

    async def validate_params(self):
        self.validate_called = True
        return self.should_validate

    async def execute(self):
        self.execute_called = True

        if not self.should_succeed:
            raise Exception("模拟任务执行失败")

        # 模拟执行时间
        import asyncio

        await asyncio.sleep(self.execution_time)

        return self._create_result(
            status="success", start_time=datetime.now(), processed_count=10, success_count=10, failed_count=0
        )


class TestTaskManager:
    """测试任务管理器"""

    def test_register_task(self):
        """测试任务注册"""
        manager = TaskManager()
        manager.register_task("mock_task", MockTask)

        assert "mock_task" in manager.tasks
        assert manager.tasks["mock_task"] == MockTask

    def test_validate_config_valid(self):
        """测试有效配置验证"""
        manager = TaskManager()
        manager.register_task("trend_refresh", MockTask)

        config_dict = {"task_type": "trend_refresh", "batch_size": 50}

        config = manager.validate_config(config_dict)
        assert isinstance(config, TaskConfig)
        assert config.task_type == "trend_refresh"
        assert config.batch_size == 50

    def test_validate_config_invalid_task_type(self):
        """测试无效任务类型"""
        manager = TaskManager()
        manager.register_task("trend_refresh", MockTask)

        config_dict = {"task_type": "unknown_task", "batch_size": 50}

        with pytest.raises(ValueError) as exc_info:
            manager.validate_config(config_dict)

        assert "配置验证失败" in str(exc_info.value)

    def test_validate_config_unregistered_task(self):
        """测试未注册的任务类型"""
        manager = TaskManager()
        # 不注册任何任务

        config_dict = {"task_type": "trend_refresh", "batch_size": 50}

        with pytest.raises(ValueError) as exc_info:
            manager.validate_config(config_dict)

        assert "未知的任务类型" in str(exc_info.value)

    def test_validate_config_invalid_format(self):
        """测试无效配置格式"""
        manager = TaskManager()
        manager.register_task("trend_refresh", MockTask)

        config_dict = {"task_type": "trend_refresh", "batch_size": "invalid"}  # 应该是数字

        with pytest.raises(ValueError) as exc_info:
            manager.validate_config(config_dict)

        assert "配置验证失败" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_execute_task_success(self):
        """测试成功执行任务"""
        manager = TaskManager()
        manager.register_task("trend_refresh", MockTask)

        config = TaskConfig(task_type="trend_refresh", batch_size=50)

        result = await manager.execute_task(config)

        assert isinstance(result, TaskResult)
        assert result.task_type == "trend_refresh"
        assert result.status == "success"
        assert result.processed_count == 10
        assert result.success_count == 10
        assert result.failed_count == 0

    @pytest.mark.asyncio
    async def test_execute_task_unregistered(self):
        """测试执行未注册的任务"""
        manager = TaskManager()
        # 不注册任何任务

        config = TaskConfig(task_type="trend_refresh", batch_size=50)

        with pytest.raises(ValueError) as exc_info:
            await manager.execute_task(config)

        assert "未注册的任务类型" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_execute_task_validation_failed(self):
        """测试任务参数验证失败"""

        class FailValidationTask(MockTask):
            def __init__(self, config, logger):
                super().__init__(config, logger)
                self.should_validate = False

        manager = TaskManager()
        manager.register_task("trend_refresh", FailValidationTask)

        config = TaskConfig(task_type="trend_refresh", batch_size=50)

        with pytest.raises(ValueError) as exc_info:
            await manager.execute_task(config)

        assert "任务参数验证失败" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_execute_task_execution_failed(self):
        """测试任务执行失败"""

        class FailExecutionTask(MockTask):
            def __init__(self, config, logger):
                super().__init__(config, logger)
                self.should_succeed = False

        manager = TaskManager()
        manager.register_task("trend_refresh", FailExecutionTask)

        config = TaskConfig(task_type="trend_refresh", batch_size=50)

        with pytest.raises(Exception) as exc_info:
            await manager.execute_task(config)

        assert "模拟任务执行失败" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_execute_task_timeout(self):
        """测试任务执行超时"""

        class SlowTask(MockTask):
            def __init__(self, config, logger):
                super().__init__(config, logger)
                self.execution_time = 2.0  # 2秒执行时间

        manager = TaskManager()
        manager.register_task("trend_refresh", SlowTask)

        config = TaskConfig(task_type="trend_refresh", batch_size=50, timeout=1)  # 1秒超时

        with pytest.raises(TimeoutError) as exc_info:
            await manager.execute_task(config)

        assert "任务执行超时" in str(exc_info.value)
