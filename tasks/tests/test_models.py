"""测试任务数据模型"""

from datetime import datetime

import pytest
from pydantic import ValidationError

from tasks.models import TaskConfig, TaskResult


class TestTaskConfig:
    """测试 TaskConfig 数据模型"""

    def test_valid_config(self):
        """测试有效配置"""
        config = TaskConfig(task_type="trend_refresh", batch_size=50, timeout=1800, max_age_hours=2)
        assert config.task_type == "trend_refresh"
        assert config.batch_size == 50
        assert config.timeout == 1800
        assert config.max_age_hours == 2
        assert config.filters is None

    def test_default_values(self):
        """测试默认值"""
        config = TaskConfig(task_type="trend_refresh")
        assert config.batch_size == 100
        assert config.timeout == 3600
        assert config.max_age_hours == 1
        assert config.filters is None

    def test_invalid_task_type(self):
        """测试无效任务类型"""
        with pytest.raises(ValidationError) as exc_info:
            TaskConfig(task_type="invalid_task")

        assert "task_type must be one of" in str(exc_info.value)

    def test_invalid_batch_size(self):
        """测试无效批处理大小"""
        # 测试负数
        with pytest.raises(ValidationError):
            TaskConfig(task_type="trend_refresh", batch_size=-1)

        # 测试零
        with pytest.raises(ValidationError):
            TaskConfig(task_type="trend_refresh", batch_size=0)

        # 测试过大值
        with pytest.raises(ValidationError):
            TaskConfig(task_type="trend_refresh", batch_size=1001)

    def test_invalid_timeout(self):
        """测试无效超时时间"""
        # 测试负数
        with pytest.raises(ValidationError):
            TaskConfig(task_type="trend_refresh", timeout=-1)

        # 测试过大值
        with pytest.raises(ValidationError):
            TaskConfig(task_type="trend_refresh", timeout=86401)

    def test_invalid_max_age_hours(self):
        """测试无效过期时间"""
        # 测试负数
        with pytest.raises(ValidationError):
            TaskConfig(task_type="trend_refresh", max_age_hours=-1)

        # 测试过大值
        with pytest.raises(ValidationError):
            TaskConfig(task_type="trend_refresh", max_age_hours=169)

    def test_with_filters(self):
        """测试带过滤条件的配置"""
        filters = {"video_ids": ["123", "456"], "date_range": {"start": "2025-01-01", "end": "2025-01-31"}}
        config = TaskConfig(task_type="trend_refresh", filters=filters)
        assert config.filters == filters


class TestTaskResult:
    """测试 TaskResult 数据模型"""

    def test_valid_result(self):
        """测试有效结果"""
        start_time = datetime(2025, 1, 21, 10, 0, 0)
        end_time = datetime(2025, 1, 21, 10, 30, 0)

        result = TaskResult(
            task_type="trend_refresh",
            status="success",
            start_time=start_time,
            end_time=end_time,
            duration=1800.0,
            processed_count=100,
            success_count=95,
            failed_count=5,
            errors=["error1", "error2"],
        )

        assert result.task_type == "trend_refresh"
        assert result.status == "success"
        assert result.duration == 1800.0
        assert result.processed_count == 100
        assert result.success_count == 95
        assert result.failed_count == 5
        assert len(result.errors) == 2

    def test_success_rate_calculation(self):
        """测试成功率计算"""
        result = TaskResult(
            task_type="trend_refresh",
            status="success",
            start_time=datetime.now(),
            end_time=datetime.now(),
            duration=100.0,
            processed_count=100,
            success_count=80,
            failed_count=20,
        )

        assert result.success_rate == 80.0

    def test_success_rate_zero_processed(self):
        """测试零处理量时的成功率"""
        result = TaskResult(
            task_type="trend_refresh",
            status="success",
            start_time=datetime.now(),
            end_time=datetime.now(),
            duration=100.0,
            processed_count=0,
            success_count=0,
            failed_count=0,
        )

        assert result.success_rate == 0.0

    def test_to_json(self):
        """测试 JSON 序列化"""
        start_time = datetime(2025, 1, 21, 10, 0, 0)
        end_time = datetime(2025, 1, 21, 10, 30, 0)

        result = TaskResult(
            task_type="trend_refresh",
            status="success",
            start_time=start_time,
            end_time=end_time,
            duration=1800.0,
            processed_count=100,
            success_count=95,
            failed_count=5,
        )

        json_str = result.to_json()
        assert isinstance(json_str, str)
        assert "trend_refresh" in json_str
        assert "success" in json_str
        assert "1800.0" in json_str

        # 验证 JSON 格式正确
        import json

        data = json.loads(json_str)
        assert data["task_type"] == "trend_refresh"
        assert data["status"] == "success"
        assert data["processed_count"] == 100
