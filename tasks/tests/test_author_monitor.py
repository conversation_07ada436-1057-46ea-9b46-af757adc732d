"""
作者监控任务单元测试
"""

from datetime import datetime
from unittest.mock import AsyncMock, MagicMock, patch

import pytest

from tasks.author_monitor import AuthorMonitorTask
from tasks.logger import TaskLogger
from tasks.models import TaskConfig, TaskResult


class TestAuthorMonitorTask:
    """作者监控任务测试类"""

    @pytest.fixture
    def task_config(self):
        """任务配置fixture"""
        return TaskConfig(
            task_type="author_monitor", batch_size=50, timeout=3600, max_age_hours=1, author_video_limit=50
        )

    @pytest.fixture
    def task_logger(self):
        """任务日志fixture"""
        return TaskLogger("test_author_monitor")

    @pytest.fixture
    def author_monitor_task(self, task_config, task_logger):
        """作者监控任务fixture"""
        return AuthorMonitorTask(task_config, task_logger)

    def test_init(self, author_monitor_task):
        """测试任务初始化"""
        assert author_monitor_task.config.task_type == "author_monitor"
        assert author_monitor_task.config.author_video_limit == 50
        assert author_monitor_task.douyin_controller is not None
        assert author_monitor_task.monitor is not None
        assert author_monitor_task.api_calls == 0

    async def test_validate_params_success(self, author_monitor_task):
        """测试参数验证成功"""
        result = await author_monitor_task.validate_params()
        assert result is True

    async def test_validate_params_with_filters(self, task_config, task_logger):
        """测试带过滤条件的参数验证"""
        task_config.filters = {"author_ids": ["123", "456"], "min_fans_count": 10000}
        task = AuthorMonitorTask(task_config, task_logger)
        result = await task.validate_params()
        assert result is True

    async def test_validate_params_invalid_author_ids(self, task_config, task_logger):
        """测试无效作者ID过滤条件"""
        task_config.filters = {"author_ids": [123, 456]}  # 应该是字符串列表
        task = AuthorMonitorTask(task_config, task_logger)
        result = await task.validate_params()
        assert result is False

    async def test_validate_params_invalid_min_fans_count(self, task_config, task_logger):
        """测试无效最小粉丝数过滤条件"""
        task_config.filters = {"min_fans_count": -100}  # 应该是非负整数
        task = AuthorMonitorTask(task_config, task_logger)
        result = await task.validate_params()
        assert result is False

    @patch("tasks.author_monitor.TrendInsightAuthor")
    async def test_query_stale_authors_batch(self, mock_model, author_monitor_task):
        """测试查询过期作者批次"""
        # 模拟数据库查询
        mock_authors = [
            MagicMock(id=1, user_id="user1", douyin_user_id="dy1"),
            MagicMock(id=2, user_id="user2", douyin_user_id="dy2"),
        ]

        mock_query = AsyncMock()
        mock_query.filter.return_value = mock_query
        mock_query.offset.return_value = mock_query
        mock_query.limit.return_value = mock_query
        mock_query.all.side_effect = [mock_authors, []]  # 第一批有数据，第二批空
        mock_model.filter.return_value = mock_query

        batches = []
        async for batch in author_monitor_task._query_stale_authors_batch():
            batches.append(batch)

        assert len(batches) == 1
        assert len(batches[0]) == 2
        assert batches[0][0].user_id == "user1"
        assert batches[0][1].user_id == "user2"

    @patch("tasks.author_monitor.TrendInsightVideoRelated")
    @patch("tasks.author_monitor.TrendInsightVideo")
    async def test_sync_videos_and_relations(self, mock_video_model, mock_relation_model, author_monitor_task):
        """测试同步视频和关联关系"""
        # 模拟视频数据
        video1 = MagicMock()
        video1.aweme_id = "video123"
        video1.id = "video123"

        video2 = MagicMock()
        video2.aweme_id = "video456"
        video2.id = "video456"

        videos = [video1, video2]

        # 模拟视频不存在
        mock_video_model.filter.return_value.first.return_value = None
        # 模拟关联关系不存在
        mock_relation_model.filter.return_value.first.return_value = None

        # 模拟保存操作
        mock_video_instance = AsyncMock()
        mock_relation_instance = AsyncMock()
        mock_video_model.return_value = mock_video_instance
        mock_relation_model.return_value = mock_relation_instance

        count = await author_monitor_task._sync_videos_and_relations(videos, "author123", "author")

        assert count == 2
        # 验证创建了视频记录
        assert mock_video_model.call_count == 2
        # 验证创建了关联关系
        assert mock_relation_model.call_count == 2

    async def test_monitor_author_videos_no_douyin_user_id(self, author_monitor_task):
        """测试监控没有douyin_user_id的作者"""
        author = MagicMock()
        author.user_id = "test_user"
        author.douyin_user_id = None

        result = await author_monitor_task._monitor_author_videos(author)
        assert result is False

    @patch("tasks.author_monitor.AuthorMonitorTask._sync_videos_and_relations")
    async def test_monitor_author_videos_success(self, mock_sync, author_monitor_task):
        """测试成功监控作者视频"""
        author = MagicMock()
        author.id = 123
        author.user_id = "test_user"
        author.douyin_user_id = "dy_test"
        author.updated_at = datetime.now()
        author.save = AsyncMock()

        # 模拟抖音API调用结果
        api_result = MagicMock()
        api_result.aweme_list = [MagicMock(), MagicMock()]

        # 注意：这个方法已被删除，因为收藏夹功能需要明确传入cookies
        # author_monitor_task.douyin_controller.get_self_aweme_collection_rpc_auto_cookies = AsyncMock(
        #     return_value=api_result
        # )
        mock_sync.return_value = 2

        result = await author_monitor_task._monitor_author_videos(author)

        assert result is True
        assert author_monitor_task.api_calls == 1
        author.save.assert_called_once()
        mock_sync.assert_called_once()

    @patch("tasks.author_monitor.AuthorMonitorTask._query_stale_authors_batch")
    @patch("tasks.author_monitor.AuthorMonitorTask._process_author_batch")
    async def test_execute_success_no_data(self, mock_process_batch, mock_query_batch, author_monitor_task):
        """测试执行任务但没有过期数据"""

        # 模拟没有过期作者
        async def empty_generator():
            return
            yield

        mock_query_batch.return_value = empty_generator()

        result = await author_monitor_task.execute()

        assert isinstance(result, TaskResult)
        assert result.task_type == "author_monitor"
        assert result.status == "success"
        assert result.processed_count == 0
        assert result.success_count == 0
        assert result.failed_count == 0

    @patch("tasks.author_monitor.AuthorMonitorTask._query_stale_authors_batch")
    @patch("tasks.author_monitor.AuthorMonitorTask._process_author_batch")
    async def test_execute_partial_success(self, mock_process_batch, mock_query_batch, author_monitor_task):
        """测试执行任务部分成功"""
        # 模拟有过期作者
        mock_authors = [MagicMock() for _ in range(3)]

        async def mock_generator():
            yield mock_authors

        mock_query_batch.return_value = mock_generator()
        mock_process_batch.return_value = {"success_count": 2, "failed_count": 1, "errors": ["作者3处理失败"]}

        result = await author_monitor_task.execute()

        assert isinstance(result, TaskResult)
        assert result.task_type == "author_monitor"
        assert result.status == "partial"
        assert result.processed_count == 3
        assert result.success_count == 2
        assert result.failed_count == 1
        assert len(result.errors) == 1

    @patch("tasks.author_monitor.AuthorMonitorTask._monitor_author_videos")
    async def test_process_author_batch_success(self, mock_monitor, author_monitor_task):
        """测试批量处理作者成功"""
        authors = [MagicMock(user_id=f"user{i}") for i in range(3)]
        mock_monitor.return_value = True

        result = await author_monitor_task._process_author_batch(authors)

        assert result["success_count"] == 3
        assert result["failed_count"] == 0
        assert len(result["errors"]) == 0

    @patch("tasks.author_monitor.AuthorMonitorTask._monitor_author_videos")
    async def test_process_author_batch_with_failures(self, mock_monitor, author_monitor_task):
        """测试批量处理作者有失败"""
        authors = [MagicMock(user_id=f"user{i}") for i in range(3)]
        mock_monitor.side_effect = [True, False, Exception("API错误")]

        result = await author_monitor_task._process_author_batch(authors)

        assert result["success_count"] == 1
        assert result["failed_count"] == 2
        assert len(result["errors"]) == 2
