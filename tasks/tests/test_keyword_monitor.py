"""
关键词监控任务单元测试
"""

from datetime import datetime
from unittest.mock import AsyncM<PERSON>, MagicMock, patch

import pytest

from tasks.keyword_monitor import KeywordMonitorTask
from tasks.logger import TaskLogger
from tasks.models import TaskConfig, TaskResult


class TestKeywordMonitorTask:
    """关键词监控任务测试类"""

    @pytest.fixture
    def task_config(self):
        """任务配置fixture"""
        return TaskConfig(
            task_type="keyword_monitor",
            batch_size=20,
            timeout=7200,
            max_age_hours=1,
            keyword_video_limit=100,
            keyword_search_days=7,
        )

    @pytest.fixture
    def task_logger(self):
        """任务日志fixture"""
        return TaskLogger("test_keyword_monitor")

    @pytest.fixture
    def keyword_monitor_task(self, task_config, task_logger):
        """关键词监控任务fixture"""
        return KeywordMonitorTask(task_config, task_logger)

    def test_init(self, keyword_monitor_task):
        """测试任务初始化"""
        assert keyword_monitor_task.config.task_type == "keyword_monitor"
        assert keyword_monitor_task.config.keyword_video_limit == 100
        assert keyword_monitor_task.config.keyword_search_days == 7
        assert keyword_monitor_task.douyin_controller is not None
        assert keyword_monitor_task.monitor is not None
        assert keyword_monitor_task.api_calls == 0

    async def test_validate_params_success(self, keyword_monitor_task):
        """测试参数验证成功"""
        result = await keyword_monitor_task.validate_params()
        assert result is True

    async def test_validate_params_with_filters(self, task_config, task_logger):
        """测试带过滤条件的参数验证"""
        task_config.filters = {"keywords": ["美食", "旅游"], "exclude_keywords": ["广告", "推广"]}
        task = KeywordMonitorTask(task_config, task_logger)
        result = await task.validate_params()
        assert result is True

    async def test_validate_params_invalid_keywords(self, task_config, task_logger):
        """测试无效关键词过滤条件"""
        task_config.filters = {"keywords": [123, 456]}  # 应该是字符串列表
        task = KeywordMonitorTask(task_config, task_logger)
        result = await task.validate_params()
        assert result is False

    async def test_validate_params_invalid_exclude_keywords(self, task_config, task_logger):
        """测试无效排除关键词过滤条件"""
        task_config.filters = {"exclude_keywords": {"key": "value"}}  # 应该是字符串列表
        task = KeywordMonitorTask(task_config, task_logger)
        result = await task.validate_params()
        assert result is False

    @patch("tasks.keyword_monitor.TrendInsightKeyword")
    async def test_query_stale_keywords_batch(self, mock_model, keyword_monitor_task):
        """测试查询过期关键词批次"""
        # 模拟数据库查询
        mock_keywords = [
            MagicMock(id=1, keyword="美食"),
            MagicMock(id=2, keyword="旅游"),
        ]

        mock_query = AsyncMock()
        mock_query.filter.return_value = mock_query
        mock_query.exclude.return_value = mock_query
        mock_query.offset.return_value = mock_query
        mock_query.limit.return_value = mock_query
        mock_query.all.side_effect = [mock_keywords, []]  # 第一批有数据，第二批空
        mock_model.filter.return_value = mock_query

        batches = []
        async for batch in keyword_monitor_task._query_stale_keywords_batch():
            batches.append(batch)

        assert len(batches) == 1
        assert len(batches[0]) == 2
        assert batches[0][0].keyword == "美食"
        assert batches[0][1].keyword == "旅游"

    @patch("tasks.keyword_monitor.TrendInsightVideoRelated")
    @patch("tasks.keyword_monitor.TrendInsightVideo")
    async def test_sync_videos_and_relations(self, mock_video_model, mock_relation_model, keyword_monitor_task):
        """测试同步视频和关联关系"""
        # 模拟视频数据
        video1 = MagicMock()
        video1.aweme_id = "video123"
        video1.id = "video123"

        video2 = MagicMock()
        video2.aweme_id = "video456"
        video2.id = "video456"

        videos = [video1, video2]

        # 模拟视频不存在
        mock_video_model.filter.return_value.first.return_value = None
        # 模拟关联关系不存在
        mock_relation_model.filter.return_value.first.return_value = None

        # 模拟保存操作
        mock_video_instance = AsyncMock()
        mock_relation_instance = AsyncMock()
        mock_video_model.return_value = mock_video_instance
        mock_relation_model.return_value = mock_relation_instance

        count = await keyword_monitor_task._sync_videos_and_relations(videos, "keyword123", "keyword")

        assert count == 2
        # 验证创建了视频记录
        assert mock_video_model.call_count == 2
        # 验证创建了关联关系
        assert mock_relation_model.call_count == 2

    async def test_search_keyword_videos_not_implemented(self, keyword_monitor_task):
        """测试关键词搜索功能（当前未实现）"""
        videos = await keyword_monitor_task._search_keyword_videos("测试关键词")
        assert videos == []

    @patch("tasks.keyword_monitor.KeywordMonitorTask._search_keyword_videos")
    @patch("tasks.keyword_monitor.KeywordMonitorTask._sync_videos_and_relations")
    async def test_monitor_keyword_videos_success(self, mock_sync, mock_search, keyword_monitor_task):
        """测试成功监控关键词视频"""
        keyword = MagicMock()
        keyword.id = 123
        keyword.keyword = "美食"
        keyword.updated_at = datetime.now()
        keyword.save = AsyncMock()

        # 模拟搜索结果
        mock_search.return_value = [MagicMock(), MagicMock()]
        mock_sync.return_value = 2

        result = await keyword_monitor_task._monitor_keyword_videos(keyword)

        assert result is True
        assert keyword_monitor_task.api_calls == 1
        keyword.save.assert_called_once()
        mock_sync.assert_called_once()

    @patch("tasks.keyword_monitor.KeywordMonitorTask._search_keyword_videos")
    async def test_monitor_keyword_videos_no_results(self, mock_search, keyword_monitor_task):
        """测试监控关键词没有搜索结果"""
        keyword = MagicMock()
        keyword.id = 123
        keyword.keyword = "测试"
        keyword.save = AsyncMock()

        # 模拟没有搜索结果
        mock_search.return_value = []

        result = await keyword_monitor_task._monitor_keyword_videos(keyword)

        assert result is True
        keyword.save.assert_called_once()

    @patch("tasks.keyword_monitor.KeywordMonitorTask._query_stale_keywords_batch")
    @patch("tasks.keyword_monitor.KeywordMonitorTask._process_keyword_batch")
    async def test_execute_success_no_data(self, mock_process_batch, mock_query_batch, keyword_monitor_task):
        """测试执行任务但没有过期数据"""

        # 模拟没有过期关键词
        async def empty_generator():
            return
            yield

        mock_query_batch.return_value = empty_generator()

        result = await keyword_monitor_task.execute()

        assert isinstance(result, TaskResult)
        assert result.task_type == "keyword_monitor"
        assert result.status == "success"
        assert result.processed_count == 0
        assert result.success_count == 0
        assert result.failed_count == 0

    @patch("tasks.keyword_monitor.KeywordMonitorTask._query_stale_keywords_batch")
    @patch("tasks.keyword_monitor.KeywordMonitorTask._process_keyword_batch")
    async def test_execute_partial_success(self, mock_process_batch, mock_query_batch, keyword_monitor_task):
        """测试执行任务部分成功"""
        # 模拟有过期关键词
        mock_keywords = [MagicMock() for _ in range(3)]

        async def mock_generator():
            yield mock_keywords

        mock_query_batch.return_value = mock_generator()
        mock_process_batch.return_value = {"success_count": 2, "failed_count": 1, "errors": ["关键词3处理失败"]}

        result = await keyword_monitor_task.execute()

        assert isinstance(result, TaskResult)
        assert result.task_type == "keyword_monitor"
        assert result.status == "partial"
        assert result.processed_count == 3
        assert result.success_count == 2
        assert result.failed_count == 1
        assert len(result.errors) == 1

    @patch("tasks.keyword_monitor.KeywordMonitorTask._monitor_keyword_videos")
    async def test_process_keyword_batch_success(self, mock_monitor, keyword_monitor_task):
        """测试批量处理关键词成功"""
        keywords = [MagicMock(keyword=f"关键词{i}") for i in range(3)]
        mock_monitor.return_value = True

        result = await keyword_monitor_task._process_keyword_batch(keywords)

        assert result["success_count"] == 3
        assert result["failed_count"] == 0
        assert len(result["errors"]) == 0

    @patch("tasks.keyword_monitor.KeywordMonitorTask._monitor_keyword_videos")
    async def test_process_keyword_batch_with_failures(self, mock_monitor, keyword_monitor_task):
        """测试批量处理关键词有失败"""
        keywords = [MagicMock(keyword=f"关键词{i}") for i in range(3)]
        mock_monitor.side_effect = [True, False, Exception("API错误")]

        result = await keyword_monitor_task._process_keyword_batch(keywords)

        assert result["success_count"] == 1
        assert result["failed_count"] == 2
        assert len(result["errors"]) == 2
