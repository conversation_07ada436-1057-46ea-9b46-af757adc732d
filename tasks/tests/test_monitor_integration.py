"""
监控任务集成测试
"""

from unittest.mock import AsyncMock, MagicMock, patch

import pytest

from tasks.author_monitor import AuthorMonitorTask
from tasks.keyword_monitor import KeywordMonitorTask
from tasks.manager import TaskManager
from tasks.models import TaskConfig


class TestMonitorTasksIntegration:
    """监控任务集成测试类"""

    @pytest.fixture
    async def task_manager(self):
        """任务管理器fixture"""
        manager = TaskManager()
        manager.register_task("author_monitor", AuthorMonitorTask)
        manager.register_task("keyword_monitor", KeywordMonitorTask)
        return manager

    @pytest.mark.asyncio
    async def test_author_monitor_task_registration(self, task_manager):
        """测试作者监控任务注册"""
        config = TaskConfig(task_type="author_monitor", batch_size=10)
        validated_config = task_manager.validate_config(config.dict())
        assert validated_config.task_type == "author_monitor"

    @pytest.mark.asyncio
    async def test_keyword_monitor_task_registration(self, task_manager):
        """测试关键词监控任务注册"""
        config = TaskConfig(task_type="keyword_monitor", batch_size=10)
        validated_config = task_manager.validate_config(config.dict())
        assert validated_config.task_type == "keyword_monitor"

    @pytest.mark.asyncio
    async def test_invalid_task_type_registration(self, task_manager):
        """测试无效任务类型"""
        config_dict = {"task_type": "invalid_task", "batch_size": 10}

        with pytest.raises(ValueError, match="未知的任务类型"):
            task_manager.validate_config(config_dict)

    @patch("tortoise.Tortoise.init")
    @patch("tortoise.Tortoise.close_connections")
    @patch("tasks.author_monitor.TrendInsightAuthor")
    @patch("tasks.author_monitor.AuthorMonitorTask._process_author_batch")
    @pytest.mark.asyncio
    async def test_author_monitor_end_to_end(self, mock_process_batch, mock_model, mock_close, mock_init, task_manager):
        """测试作者监控端到端流程"""
        # 模拟数据库初始化
        mock_init.return_value = None
        mock_close.return_value = None

        # 模拟查询结果
        mock_authors = [MagicMock(id=1, user_id="test_user")]
        mock_query = AsyncMock()
        mock_query.filter.return_value = mock_query
        mock_query.offset.return_value = mock_query
        mock_query.limit.return_value = mock_query
        mock_query.all.side_effect = [mock_authors, []]
        mock_model.filter.return_value = mock_query

        # 模拟批处理结果
        mock_process_batch.return_value = {"success_count": 1, "failed_count": 0, "errors": []}

        config = TaskConfig(task_type="author_monitor", batch_size=10, timeout=60, max_age_hours=1)

        # 执行任务
        result = await task_manager.execute_task(config)

        assert result.task_type == "author_monitor"
        assert result.status in ["success", "partial"]
        assert result.processed_count >= 0

    @patch("tortoise.Tortoise.init")
    @patch("tortoise.Tortoise.close_connections")
    @patch("tasks.keyword_monitor.TrendInsightKeyword")
    @patch("tasks.keyword_monitor.KeywordMonitorTask._process_keyword_batch")
    @pytest.mark.asyncio
    async def test_keyword_monitor_end_to_end(
        self, mock_process_batch, mock_model, mock_close, mock_init, task_manager
    ):
        """测试关键词监控端到端流程"""
        # 模拟数据库初始化
        mock_init.return_value = None
        mock_close.return_value = None

        # 模拟查询结果
        mock_keywords = [MagicMock(id=1, keyword="测试关键词")]
        mock_query = AsyncMock()
        mock_query.filter.return_value = mock_query
        mock_query.exclude.return_value = mock_query
        mock_query.offset.return_value = mock_query
        mock_query.limit.return_value = mock_query
        mock_query.all.side_effect = [mock_keywords, []]
        mock_model.filter.return_value = mock_query

        # 模拟批处理结果
        mock_process_batch.return_value = {"success_count": 1, "failed_count": 0, "errors": []}

        config = TaskConfig(
            task_type="keyword_monitor",
            batch_size=10,
            timeout=60,
            max_age_hours=1,
            keyword_video_limit=50,
            keyword_search_days=7,
        )

        # 执行任务
        result = await task_manager.execute_task(config)

        assert result.task_type == "keyword_monitor"
        assert result.status in ["success", "partial"]
        assert result.processed_count >= 0

    @pytest.mark.asyncio
    async def test_task_config_validation_author_monitor(self):
        """测试作者监控任务配置验证"""
        # 有效配置
        valid_config = TaskConfig(
            task_type="author_monitor", batch_size=50, timeout=3600, max_age_hours=1, author_video_limit=50
        )
        assert valid_config.task_type == "author_monitor"
        assert valid_config.author_video_limit == 50

        # 无效的 author_video_limit
        with pytest.raises(ValueError, match="author_video_limit must be between 1 and 200"):
            TaskConfig(task_type="author_monitor", author_video_limit=300)

    @pytest.mark.asyncio
    async def test_task_config_validation_keyword_monitor(self):
        """测试关键词监控任务配置验证"""
        # 有效配置
        valid_config = TaskConfig(
            task_type="keyword_monitor",
            batch_size=20,
            timeout=7200,
            max_age_hours=1,
            keyword_video_limit=100,
            keyword_search_days=7,
        )
        assert valid_config.task_type == "keyword_monitor"
        assert valid_config.keyword_video_limit == 100
        assert valid_config.keyword_search_days == 7

        # 无效的 keyword_video_limit
        with pytest.raises(ValueError, match="keyword_video_limit must be between 1 and 500"):
            TaskConfig(task_type="keyword_monitor", keyword_video_limit=600)

        # 无效的 keyword_search_days
        with pytest.raises(ValueError, match="keyword_search_days must be between 1 and 30"):
            TaskConfig(task_type="keyword_monitor", keyword_search_days=45)

    @pytest.mark.asyncio
    async def test_video_relation_management(self):
        """测试视频关联关系管理"""
        # 这里测试视频关联关系的创建和去重逻辑
        # 实际实现需要根据数据库模型来验证

        # 模拟视频数据
        video1 = MagicMock()
        video1.aweme_id = "test_video_123"
        video1.id = "test_video_123"

        videos = [video1]

        # 测试数据去重和关联关系创建
        # 这部分逻辑在实际的监控任务中实现
        assert len(videos) == 1
        assert videos[0].aweme_id == "test_video_123"

    @pytest.mark.asyncio
    async def test_batch_processing_optimization(self):
        """测试批处理优化"""
        # 测试批量插入优化和性能
        batch_size = 50
        total_items = 150

        # 计算预期批次数
        expected_batches = (total_items + batch_size - 1) // batch_size
        assert expected_batches == 3

        # 模拟批处理过程
        processed_items = 0
        for batch_num in range(expected_batches):
            batch_start = batch_num * batch_size
            batch_end = min(batch_start + batch_size, total_items)
            current_batch_size = batch_end - batch_start
            processed_items += current_batch_size

        assert processed_items == total_items

    @pytest.mark.asyncio
    async def test_data_integrity_check(self):
        """测试数据完整性检查"""
        # 测试关联关系的数据完整性

        # 模拟关联关系数据
        relation_data = {"source_id": "author_123", "source_type": "author", "video_id": "video_456"}

        # 验证必要字段存在
        assert "source_id" in relation_data
        assert "source_type" in relation_data
        assert "video_id" in relation_data

        # 验证字段值有效
        assert relation_data["source_id"]
        assert relation_data["source_type"] in ["author", "keyword"]
        assert relation_data["video_id"]

    @pytest.mark.asyncio
    async def test_error_handling_scenarios(self):
        """测试错误处理场景"""
        # 测试各种错误场景的处理

        # 1. 网络错误
        network_error = Exception("网络连接失败")
        assert "网络连接失败" in str(network_error)

        # 2. API限流错误
        api_limit_error = Exception("API调用频率限制")
        assert "API调用频率限制" in str(api_limit_error)

        # 3. 数据库错误
        db_error = Exception("数据库连接超时")
        assert "数据库连接超时" in str(db_error)

        # 4. 数据格式错误
        data_format_error = ValueError("视频数据格式无效")
        assert "视频数据格式无效" in str(data_format_error)
