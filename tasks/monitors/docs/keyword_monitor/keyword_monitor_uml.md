# 关键词监控任务 - UML 类图

本文档使用 Mermaid 语法描述 `KeywordMonitorTask` 及其相关组件的UML类图，展示了类之间的关系和结构。

## 1. 核心类图

这个图展示了 `KeywordMonitorTask` 的主要结构、依赖关系以及它与核心任务组件和外部服务的关系。

```mermaid
classDiagram
    direction LR

    class BaseTask {
        <<Abstract>>
        +TaskConfig config
        +TaskLogger logger
        +bool is_interrupted
        +execute()* TaskResult
        +validate_params()* bool
        +_create_result() TaskResult
    }

    class KeywordMonitorTask {
        -DouyinController douyin_controller
        -Monitor monitor
        -int api_calls
        -VideoSyncService video_sync_service
        -VideoRelationService video_relation_service
        -KeywordService keyword_service
        -UserInboxService user_inbox_service
        +__init__(config, logger)
        +validate_params() bool
        +execute() TaskResult
        -_query_stale_keywords_batch() AsyncIterator
        -_process_keyword_batch(keywords) Dict
        -_monitor_keyword_videos(keyword) bool
        -_search_keyword_videos(keyword) List
        -_process_keyword_videos_with_services(keyword, videos) bool
    }

    class TaskConfig {
        +int batch_size
        +int max_age_hours
        +int timeout
        +Dict filters
    }

    class TaskLogger {
        +log_progress()
        +log_error()
        +log_warning()
    }
    
    class Monitor {
        +log_performance_metrics()
        +check_memory_threshold()
        +generate_performance_report()
    }

    BaseTask <|-- KeywordMonitorTask

    KeywordMonitorTask --> TaskConfig : uses
    KeywordMonitorTask --> TaskLogger : uses
    KeywordMonitorTask --> Monitor : uses
    KeywordMonitorTask --> DouyinController : uses
    KeywordMonitorTask --> VideoSyncService : uses
    KeywordMonitorTask --> VideoRelationService : uses
    KeywordMonitorTask --> KeywordService : uses
    KeywordMonitorTask --> UserInboxService : uses

```

## 2. 服务层依赖关系

这个图聚焦于 `KeywordMonitorTask` 所使用的服务层，并展示了它们之间的关系。

```mermaid
classDiagram
    direction TB

    class KeywordMonitorTask {
        +execute()
    }

    package "服务层 (Services)" {
        class VideoSyncService {
            +sync_videos_from_search_results()
        }
        class VideoRelationService {
            +create_keyword_video_relations()
        }
        class KeywordService {
            +update_keyword_video_count()
            +update_keyword_timestamp()
        }
        class UserInboxService {
            +sync_user_keyword_subscriptions()
        }
    }
    
    class DouyinController {
        +search_videos()
    }
    
    class TrendInsightKeyword {
        <<Model>>
        +id
        +keyword
        +updated_at
    }

    KeywordMonitorTask ..> DouyinController : "uses (simulated)"
    KeywordMonitorTask ..> VideoSyncService
    KeywordMonitorTask ..> VideoRelationService
    KeywordMonitorTask ..> KeywordService
    KeywordMonitorTask ..> UserInboxService
    
    KeywordMonitorTask ..> TrendInsightKeyword : "queries & processes"

```

## 3. 关键数据模型

这个图展示了任务执行过程中涉及的关键数据模型。

```mermaid
classDiagram
    class TaskResult {
        +str status
        +str message
        +int processed_count
        +int success_count
        +int failed_count
        +List errors
        +Dict performance
    }

    class TrendInsightKeyword {
        <<ORM Model>>
        +id: UUID
        +keyword: str
        +video_count: int
        +created_at: datetime
        +updated_at: datetime
    }
    
    class DouyinAweme {
        <<ORM Model>>
        +aweme_id: str
        +title: str
        +...
    }
    
    class TrendInsightVideoRelated {
        <<ORM Model>>
        +source_id: UUID
        +video_id: str
        +source_type: str
    }
    
    class UserInbox {
        <<ORM Model>>
        +user_id: UUID
        +video_id: str
        +...
    }
```
