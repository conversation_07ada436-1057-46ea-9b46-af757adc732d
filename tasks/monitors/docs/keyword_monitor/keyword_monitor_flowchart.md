# 关键词监控任务 - 流程图

本文档使用 Mermaid 语法描述 `KeywordMonitorTask` 的核心工作流程。

## 1. 主执行流程 (`execute`)

```mermaid
graph TD
    A[开始 execute] --> B{验证任务参数};
    B -- 参数有效 --> C[记录任务开始时间];
    B -- 参数无效 --> Z[返回失败结果];
    C --> D{循环获取过期的关键词批次};
    D -- 有批次 --> E{任务是否被中断?};
    E -- 否 --> F[处理关键词批次];
    F --> G[更新处理计数和错误];
    G --> H{是否达到日志记录点?};
    H -- 是 --> I[记录进度和性能指标];
    I --> J[检查内存使用];
    J --> D;
    H -- 否 --> D;
    E -- 是 --> K[记录任务中断并跳出循环];
    D -- 无更多批次 --> L[判断最终任务状态];
    K --> L;
    L --> M[生成性能报告];
    M --> N[创建并返回最终 TaskResult];
    N --> O[结束];
```

## 2. 批处理流程 (`_process_keyword_batch`)

```mermaid
graph TD
    subgraph _process_keyword_batch
        A[开始] --> B{循环处理批次中的每个关键词};
        B -- 有关键词 --> C{任务是否被中断?};
        C -- 否 --> D[调用 _monitor_keyword_videos];
        D --> E{监控是否成功?};
        E -- 是 --> F[成功计数+1];
        E -- 否 --> G[失败计数+1, 记录错误];
        F --> H[短暂休眠, 避免API限流];
        G --> H;
        H --> B;
        C -- 是 --> I[跳出循环];
        B -- 处理完毕 --> J[返回批处理结果];
        I --> J;
    end
```

## 3. 单个关键词监控流程 (`_monitor_keyword_videos`)

```mermaid
graph TD
    subgraph _monitor_keyword_videos
        A[开始] --> B[调用 _search_keyword_videos 搜索视频];
        B --> C{是否找到视频?};
        C -- 否 --> D[更新关键词时间戳];
        D --> E[返回 True];
        C -- 是 --> F[调用 _process_keyword_videos_with_services];
        F --> G{处理是否成功?};
        G -- 是 --> H[记录成功日志, 返回 True];
        G -- 否 --> I[记录失败日志, 返回 False];
    end
```

## 4. 服务层处理流程 (`_process_keyword_videos_with_services`)

这是核心的数据处理部分，展示了服务层之间的协作。

```mermaid
graph TD
    subgraph _process_keyword_videos_with_services
        A[开始] --> B[VideoSyncService: 同步视频数据];
        B --> C{视频是否同步成功?};
        C -- 是 --> D[VideoRelationService: 创建关键词与视频的关联];
        D --> E[KeywordService: 更新关键词的视频总数];
        E --> F[UserInboxService: 同步用户订阅];
        F --> G[KeywordService: 更新关键词的最后监控时间];
        G --> H[记录处理完成日志];
        H --> I[返回 True];
        C -- 否 --> J[记录错误日志];
        J --> K[返回 False];
    end
```
