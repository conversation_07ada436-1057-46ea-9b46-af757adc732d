# 作者监控任务 - 流程图

本文档使用 Mermaid 语法描述 `AuthorMonitorTask` 的核心工作流程。

## 1. 主执行流程 (`execute`)

```mermaid
graph TD
    A[开始 execute] --> B{验证任务参数};
    B -- 参数有效 --> C[记录任务开始时间];
    B -- 参数无效 --> Z[返回失败结果];
    C --> D{循环获取过期的作者批次};
    D -- 有批次 --> E{任务是否被中断?};
    E -- 否 --> F[处理作者批次];
    F --> G[更新处理计数和错误];
    G --> H{是否达到日志记录点?};
    H -- 是 --> I[记录进度和性能指标];
    I --> J[检查内存使用];
    J --> D;
    H -- 否 --> D;
    E -- 是 --> K[记录任务中断并跳出循环];
    D -- 无更多批次 --> L[判断最终任务状态];
    K --> L;
    L --> M[生成性能报告];
    M --> N[创建并返回最终 TaskResult];
    N --> O[结束];
```

## 2. 批处理流程 (`_process_author_batch`)

```mermaid
graph TD
    subgraph _process_author_batch
        A[开始] --> B{循环处理批次中的每个作者};
        B -- 有作者 --> C{任务是否被中断?};
        C -- 否 --> D[调用 _monitor_author_videos];
        D --> E{监控是否成功?};
        E -- 是 --> F[成功计数+1];
        E -- 否 --> G[失败计数+1, 记录错误];
        F --> H[短暂休眠, 避免API限流];
        G --> H;
        H --> B;
        C -- 是 --> I[跳出循环];
        B -- 处理完毕 --> J[返回批处理结果];
        I --> J;
    end
```

## 3. 单个作者监控流程 (`_monitor_author_videos`)

```mermaid
graph TD
    subgraph _monitor_author_videos
        A[开始] --> B{作者是否有 user_id?};
        B -- 否 --> Z[记录警告并返回 False];
        B -- 是 --> C[获取 TrendInsight API 客户端];
        C --> D[调用 TrendInsight API 获取作者最近视频];
        D --> E{API调用是否成功且有数据?};
        E -- 否 --> F[记录调试日志, 更新作者时间戳];
        F --> G[返回 True];
        E -- 是 --> H[调用 _sync_videos_and_relations 同步视频和关联];
        H --> I[更新作者时间戳];
        I --> J[记录成功日志, 返回 True];
    end
```

## 4. 视频同步与关联流程 (`_sync_videos_and_relations`)

这是核心的数据持久化部分，直接与数据库模型交互。

```mermaid
graph TD
    subgraph _sync_videos_and_relations
        A[开始] --> B{循环处理视频列表中的每个视频};
        B -- 有视频 --> C{视频数据是否有效?};
        C -- 否 --> B;
        C -- 是 --> D[查询数据库中视频是否已存在];
        D -- 不存在 --> E[创建并保存新的 TrendInsightVideo 记录];
        E --> F;
        D -- 存在 --> F;
        F --> G[查询作者-视频关联是否已存在 (TrendInsightVideoRelated)];
        G -- 不存在 --> H[创建并保存新的 TrendInsightVideoRelated 记录];
        H --> J;
        G -- 存在 --> J;
        J[处理 user_inbox_source_related 关联];
        J --> K[查询 user_inbox_source_related 是否已存在];
        K -- 不存在 --> L[创建并保存新的 user_inbox_source_related 记录];
        L --> M;
        K -- 存在 --> M;
        M[处理 user_inbox_video_related 关联];
        M --> N[查询 user_inbox_video_related 是否已存在];
        N -- 不存在 --> O[创建并保存新的 user_inbox_video_related 记录];
        O --> B;
        N -- 存在 --> B;
        B -- 处理完毕 --> P[返回新创建的视频数量];
    end
```
