# 作者监控任务 - UML 类图

本文档使用 Mermaid 语法描述 `AuthorMonitorTask` 及其相关组件的UML类图，展示了类之间的关系和结构。

## 1. 核心类图

这个图展示了 `AuthorMonitorTask` 的主要结构、依赖关系以及它与核心任务组件和外部服务的关系。

```mermaid
classDiagram
    direction LR

    class BaseTask {
        <<Abstract>>
        +TaskConfig config
        +TaskLogger logger
        +bool is_interrupted
        +execute()* TaskResult
        +validate_params()* bool
        +_create_result() TaskResult
    }

    class AuthorMonitorTask {
        -DouyinController douyin_controller
        -Monitor monitor
        -int api_calls
        +__init__(config, logger)
        +validate_params() bool
        +execute() TaskResult
        -_get_trendinsight_client() AsyncTrendInsightAPI
        -_query_stale_authors_batch() AsyncIterator
        -_process_author_batch(authors) Dict
        -_monitor_author_videos(author) bool
        -_sync_videos_and_relations(videos, source_id, source_type) int
    }

    class TaskConfig {
        +int batch_size
        +int max_age_hours
        +Dict filters
    }

    class TaskLogger {
        +log_progress()
        +log_error()
    }
    
    class Monitor {
        +log_performance_metrics()
        +generate_performance_report()
    }
    
    class AsyncTrendInsightAPI {
        +get_great_user_top_video()
    }

    BaseTask <|-- AuthorMonitorTask

    AuthorMonitorTask --> TaskConfig : uses
    AuthorMonitorTask --> TaskLogger : uses
    AuthorMonitorTask --> Monitor : uses
    AuthorMonitorTask --> DouyinController : uses
    AuthorMonitorTask ..> AsyncTrendInsightAPI : creates & uses
```

## 2. 数据模型依赖关系

这个图聚焦于 `AuthorMonitorTask` 所操作的数据模型，展示了其数据持久化的目标。与 `KeywordMonitorTask` 不同，`AuthorMonitorTask` 目前直接操作数据模型，而不是通过服务层。

```mermaid
classDiagram
    direction TB

    class AuthorMonitorTask {
        +execute()
        -_monitor_author_videos()
        -_sync_videos_and_relations()
    }

    package "数据模型 (Models)" {
        class TrendInsightAuthor {
            <<ORM Model>>
            +id: str
            +user_id: str
            +updated_at: datetime
            +filter()
            +save()
        }
        class TrendInsightVideo {
            <<ORM Model>>
            +id: str
            +filter()
            +save()
        }
        class TrendInsightVideoRelated {
            <<ORM Model>>
            +source_id: str
            +video_id: str
            +filter()
            +save()
        }
    }
    
    AuthorMonitorTask ..> TrendInsightAuthor : "queries & updates"
    AuthorMonitorTask ..> TrendInsightVideo : "creates & queries"
    AuthorMonitorTask ..> TrendInsightVideoRelated : "creates & queries"

```
