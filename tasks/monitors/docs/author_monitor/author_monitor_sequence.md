# 作者监控任务 - 时序图

本文档使用 Mermaid 语法描述 `AuthorMonitorTask` 与其他组件的交互时序。

## 1. 整体执行时序

```mermaid
sequenceDiagram
    participant Task as AuthorMonitorTask
    participant DB as 数据库
    participant TrendInsightAPI as TrendInsight API

    Task->>DB: _query_stale_authors_batch()
    DB-->>Task: 返回一批过期的作者

    loop 对每个作者
        Task->>TrendInsightAPI: _monitor_author_videos(author)
        TrendInsightAPI-->>Task: 返回作者视频列表
        Task->>DB: _sync_videos_and_relations()
        DB-->>Task: 视频和关联同步完成
        Task->>DB: 更新作者时间戳
        DB-->>Task: 更新成功
    end

    Task->>Task: 汇总结果
    Task->>Task: 生成性能报告
    Task-->>Caller: 返回 TaskResult
```

## 2. 单个作者监控详细时序 (`_monitor_author_videos`)

这个时序图展示了任务如何与外部API和数据库进行交互来处理一个作者。

```mermaid
sequenceDiagram
    participant Task as AuthorMonitorTask
    participant TrendInsightAPI as TrendInsight API
    participant DB as 数据库

    Task->>TrendInsightAPI: get_great_user_top_video(user_id)
    TrendInsightAPI-->>Task: 返回视频列表 (video_response)

    alt API调用失败或无数据
        Task->>DB: 更新作者 updated_at
        DB-->>Task: 更新成功
    else API调用成功且有数据
        Task->>Task: _sync_videos_and_relations(videos, author_id)
        Note right of Task: 内部调用, 详见下一个图
        Task->>DB: 更新作者 updated_at
        DB-->>Task: 更新成功
    end
```

## 3. 视频同步与关联时序 (`_sync_videos_and_relations`)

这个时序图详细展示了 `_sync_videos_and_relations` 方法内部的数据库操作顺序。

```mermaid
sequenceDiagram
    participant Task as AuthorMonitorTask
    participant VideoModel as TrendInsightVideo
    participant RelationModel as TrendInsightVideoRelated

    Task->>Task: 循环处理视频列表

    loop 对每个视频
        Task->>VideoModel: filter(id=aweme_id).first()
        VideoModel-->>Task: 返回是否存在

        alt 视频不存在
            Task->>VideoModel: create and save new video
            VideoModel-->>Task: 创建成功
        end

        Task->>RelationModel: filter(source_id, video_id).first()
        RelationModel-->>Task: 返回是否存在

        alt 关联不存在
            Task->>RelationModel: create and save new relation
            RelationModel-->>Task: 创建成功
        end
    end
```
