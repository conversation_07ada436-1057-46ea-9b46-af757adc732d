"""
作者监控任务 - 监控TrendInsight作者数据并同步新视频
"""

import asyncio
import uuid
from datetime import datetime, timedelta
from typing import AsyncIterator, Dict, List, Union

from controllers.douyin import DouyinController
from models.qihaozhushou import UserInboxVideoRelated
from models.trendinsight import (
    TrendInsightAuthor,
    TrendInsightVideo,
    TrendInsightVideoRelated,
)
from rpc.trendinsight import AsyncTrendInsightAPI, client_manager
from rpc.trendinsight.schemas import GreatUserTopVideoRequest
from services.user.inbox_service import UserInboxService
from tasks.core.base import BaseTask
from tasks.core.logger import TaskLogger
from tasks.core.models import TaskConfig, TaskResult
from tasks.monitors.base import create_monitor, profile_operation


class AuthorMonitorTask(BaseTask):
    """作者监控任务 - 监控作者数据并同步新视频"""

    def __init__(self, config: TaskConfig, logger: TaskLogger):
        """
        初始化作者监控任务

        Args:
            config: 任务配置
            logger: 日志记录器
        """
        super().__init__(config, logger)
        self.douyin_controller = DouyinController()
        self.monitor = create_monitor("author_monitor")
        self.api_calls = 0

    async def _get_trendinsight_client(self) -> AsyncTrendInsightAPI:
        """
        获取 TrendInsight 异步客户端实例

        Returns:
            AsyncTrendInsightAPI: 配置好账号提供者的异步客户端实例
        """
        # 通过 client_manager 创建客户端，cookies 由账号提供者自动管理
        async_client = client_manager.create_async_client()
        return AsyncTrendInsightAPI(async_client=async_client)

    async def validate_params(self) -> bool:
        """
        验证任务参数

        Returns:
            bool: 参数是否有效
        """
        try:
            # 验证过滤条件
            filters = self.config.filters or {}
            if "author_ids" in filters:
                author_ids = filters["author_ids"]
                if not isinstance(author_ids, list) or not all(isinstance(aid, str) for aid in author_ids):
                    self.logger.log_error("author_ids 过滤条件必须是字符串列表")
                    return False

            if "min_fans_count" in filters:
                min_fans_count = filters["min_fans_count"]
                if not isinstance(min_fans_count, int) or min_fans_count < 0:
                    self.logger.log_error("min_fans_count 必须是非负整数")
                    return False

            return True

        except Exception as e:
            self.logger.log_error(f"参数验证失败: {str(e)}")
            return False

    async def execute(self) -> TaskResult:
        """
        执行作者监控任务

        Returns:
            TaskResult: 执行结果
        """
        start_time = datetime.now()
        processed_count = 0
        success_count = 0
        failed_count = 0
        errors = []

        try:
            self.logger.log_progress(0, message="开始查询过期作者数据")

            # 流式查询过期作者
            async for batch_authors in self._query_stale_authors_batch():
                if self.is_interrupted:
                    self.logger.log_warning("任务被中断，停止处理")
                    break

                # 批量处理作者
                batch_results = await self._process_author_batch(batch_authors)

                # 更新统计信息
                processed_count += len(batch_authors)
                success_count += batch_results["success_count"]
                failed_count += batch_results["failed_count"]
                errors.extend(batch_results["errors"])

                # 记录进度（每50条记录）
                if processed_count % 50 == 0 or processed_count < 50:
                    self.logger.log_progress(
                        processed_count,
                        message=f"已处理 {processed_count} 个作者，成功 {success_count}，失败 {failed_count}",
                    )

                    # 记录性能指标
                    self.monitor.log_performance_metrics(processed_count=processed_count, api_calls=self.api_calls)

                    # 检查内存使用
                    self.monitor.check_memory_threshold(threshold_mb=512.0)

            # 确定任务状态
            if processed_count == 0:
                status = "success"
                self.logger.log_progress(0, message="没有需要监控的过期作者")
            elif failed_count == 0:
                status = "success"
            elif success_count > 0:
                status = "partial"
            else:
                status = "failed"

            # 生成性能报告
            performance_report = self.monitor.generate_performance_report(
                final_processed_count=processed_count, final_api_calls=self.api_calls
            )

            result = self._create_result(
                status=status,
                start_time=start_time,
                processed_count=processed_count,
                success_count=success_count,
                failed_count=failed_count,
                errors=errors,
            )

            # 添加性能报告到结果
            if hasattr(result, "performance"):
                result.performance = performance_report

            return result

        except Exception as e:
            error_msg = f"任务执行失败: {str(e)}"
            self.logger.log_error(error_msg)
            errors.append(error_msg)

            return self._create_result(
                status="failed",
                start_time=start_time,
                processed_count=processed_count,
                success_count=success_count,
                failed_count=failed_count,
                errors=errors,
            )

    async def _query_stale_authors_batch(self) -> AsyncIterator[List[TrendInsightAuthor]]:
        """
        流式批量查询过期作者

        Yields:
            List[TrendInsightAuthor]: 作者批次
        """
        try:
            # 计算过期时间阈值
            cutoff_time = datetime.now() - timedelta(hours=self.config.max_age_hours)

            # 构建查询条件
            query = TrendInsightAuthor.filter(updated_at__lte=cutoff_time)

            # 应用过滤条件
            filters = self.config.filters or {}
            if "author_ids" in filters:
                author_ids = filters["author_ids"]
                query = query.filter(id__in=author_ids)

            if "min_fans_count" in filters:
                min_fans_count = filters["min_fans_count"]
                query = query.filter(fans_count_int__gte=min_fans_count)

            # 只处理有有效 user_id 的作者
            query = query.filter(user_id__isnull=False, user_id__not="")

            # 分批查询
            offset = 0
            batch_size = self.config.batch_size

            while True:
                if self.is_interrupted:
                    break

                batch = await query.offset(offset).limit(batch_size).all()
                if not batch:
                    break

                yield batch
                offset += batch_size

                # 批次间短暂休息，避免数据库压力
                await asyncio.sleep(0.1)

        except Exception as e:
            self.logger.log_error(f"查询过期作者失败: {str(e)}")
            raise

    async def _process_author_batch(self, authors: List[TrendInsightAuthor]) -> Dict[str, Union[int, List[str]]]:
        """
        批量处理作者监控

        Args:
            authors: 作者列表

        Returns:
            dict: 批次处理结果
        """
        success_count = 0
        failed_count = 0
        errors = []

        for author in authors:
            if self.is_interrupted:
                break

            try:
                success = await self._monitor_author_videos(author)
                if success:
                    success_count += 1
                else:
                    failed_count += 1
                    errors.append(f"监控作者 {author.user_id} 视频失败")

            except Exception as e:
                failed_count += 1
                error_msg = f"处理作者 {author.user_id} 异常: {str(e)}"
                errors.append(error_msg)
                self.logger.log_error(error_msg)

            # 避免API限流
            await asyncio.sleep(1.0)

        return {"success_count": success_count, "failed_count": failed_count, "errors": errors}

    async def _monitor_author_videos(self, author: TrendInsightAuthor) -> bool:
        """
        监控单个作者的新视频

        Args:
            author: 作者对象

        Returns:
            bool: 是否成功监控
        """
        try:
            if not author.user_id:
                self.logger.log_warning(f"作者记录没有有效的 user_id")
                return False

            # 调用 TrendInsight API 获取作者的视频列表
            try:
                with profile_operation(self.monitor, f"get_author_videos_{author.user_id}"):
                    # 使用 TrendInsight API 获取用户热门视频
                    trendinsight_client = await self._get_trendinsight_client()

                    # 构建日期范围（获取最近30天的视频）
                    end_date = datetime.now()
                    start_date = end_date - timedelta(days=30)

                    # 构建 TrendInsight 用户热门视频请求
                    video_request = GreatUserTopVideoRequest(
                        user_id=author.user_id,  # 使用 TrendInsight 用户ID
                        start_date=start_date.strftime("%Y%m%d"),
                        end_date=end_date.strftime("%Y%m%d"),
                    )

                    video_response = await trendinsight_client.get_great_user_top_video(video_request)
                    self.api_calls += 1

                if not video_response.is_success or not video_response.video_data:
                    self.logger.log_debug(f"作者 {author.user_id} 未获取到视频数据")
                    # 即使没有新视频，也更新作者的 updated_at
                    author.updated_at = datetime.now()
                    await author.save()
                    return True

                # 获取视频列表（使用指数排序的视频列表）
                video_list = video_response.video_data.index_list or []
                if not video_list:
                    self.logger.log_debug(f"作者 {author.user_id} 的视频列表为空")
                    # 即使没有新视频，也更新作者的 updated_at
                    author.updated_at = datetime.now()
                    await author.save()
                    return True

                # 处理获取到的视频数据
                new_videos_count = await self._sync_videos_and_relations(video_list, str(author.id), "author")

                # 更新作者的 updated_at 时间戳
                author.updated_at = datetime.now()
                await author.save()

                self.logger.log_debug(f"成功监控作者 {author.user_id}，同步了 {new_videos_count} 个新视频")
                return True

            except Exception as e:
                self.logger.log_error(f"调用TrendInsight API失败: {author.user_id}, 错误: {str(e)}")
                return False

        except Exception as e:
            self.logger.log_error(f"监控作者视频失败: {author.user_id}, 错误: {str(e)}")
            return False

    async def _sync_videos_and_relations(self, videos: List, source_id: str, source_type: str) -> int:
        """
        同步视频数据并创建关联关系

        Args:
            videos: 视频列表
            source_id: 来源ID（作者ID）
            source_type: 来源类型（"author"）

        Returns:
            int: 同步的新视频数量
        """
        new_videos_count = 0

        for video_data in videos:
            try:
                # 获取视频ID - 兼容不同的数据格式
                aweme_id = None
                if hasattr(video_data, "item_id") and video_data.item_id:
                    # TrendInsight TopVideoInfo 格式
                    aweme_id = video_data.item_id
                elif hasattr(video_data, "aweme_id") and video_data.aweme_id:
                    # 抖音 API 格式
                    aweme_id = video_data.aweme_id
                elif hasattr(video_data, "id") and video_data.id:
                    # 通用 ID 格式
                    aweme_id = video_data.id

                if not aweme_id:
                    continue

                # 检查视频是否已存在
                existing_video = await TrendInsightVideo.filter(id=aweme_id).first()

                if not existing_video:
                    # 创建新的视频记录
                    video_record = TrendInsightVideo(
                        id=aweme_id,
                        trend_score=0.0,
                        trend_radio=0.0,
                        created_at=datetime.now(),
                        updated_at=datetime.now(),
                        is_deleted=False,
                    )
                    await video_record.save()
                    new_videos_count += 1

                # 检查关联关系是否已存在
                existing_relation = await TrendInsightVideoRelated.filter(
                    source_id=source_id, video_id=aweme_id
                ).first()

                if not existing_relation:
                    # 创建视频关联关系
                    relation = TrendInsightVideoRelated(
                        source_id=source_id,
                        source_type=source_type,
                        video_id=aweme_id,
                        created_at=datetime.now(),
                        updated_at=datetime.now(),
                        is_deleted=False,
                    )
                    await relation.save()

                # 处理 user_inbox_source_related 关联
                await self._process_user_inbox_source_relations(source_id, source_type)

                # 处理 user_inbox_video_related 关联
                await self._process_user_inbox_video_relations(aweme_id, source_id, source_type)

            except Exception as e:
                self.logger.log_error(f"同步视频 {aweme_id} 失败: {str(e)}")
                continue

        return new_videos_count

    async def _process_user_inbox_source_relations(self, source_id: str, source_type: str) -> None:
        """
        处理用户收件箱来源关联关系

        Args:
            source_id: 来源ID（作者ID）
            source_type: 来源类型（"author"）
        """
        try:
            # 查找订阅了该作者的用户
            inbox_service = UserInboxService(self.logger.logger)
            subscribed_users = await inbox_service.find_subscribed_users(source_id, source_type)

            if not subscribed_users:
                self.logger.log_debug(f"没有用户订阅作者 {source_id}")
                return

            # 更新用户收件箱关联关系的时间戳
            updated_count = await inbox_service.update_user_inbox_relations(
                subscribed_users, source_id, source_type
            )

            self.logger.log_debug(f"更新了 {updated_count} 个用户收件箱来源关联")

        except Exception as e:
            self.logger.log_error(f"处理用户收件箱来源关联失败: {str(e)}")

    async def _process_user_inbox_video_relations(self, video_id: str, source_id: str, source_type: str) -> None:
        """
        处理用户收件箱视频关联关系

        Args:
            video_id: 视频ID
            source_id: 来源ID（作者ID）
            source_type: 来源类型（"author"）
        """
        try:
            # 查找订阅了该作者的用户
            inbox_service = UserInboxService(self.logger.logger)
            subscribed_users = await inbox_service.find_subscribed_users(source_id, source_type)

            if not subscribed_users:
                self.logger.log_debug(f"没有用户订阅作者 {source_id}，跳过视频关联创建")
                return

            # 为每个订阅用户创建视频关联
            new_relations_count = 0
            for user_uuid in subscribed_users:
                try:
                    # 检查关联是否已存在
                    existing_video_relation = await UserInboxVideoRelated.filter(
                        user_uuid=user_uuid,
                        video_id=video_id,
                        is_deleted=False
                    ).first()

                    if not existing_video_relation:
                        # 创建新的视频关联记录
                        relation_uuid = str(uuid.uuid4()).replace('-', '')
                        video_relation = UserInboxVideoRelated(
                            uuid=relation_uuid,
                            user_uuid=user_uuid,
                            video_id=video_id,
                            source_type=source_type,
                            create_time=datetime.now(),
                            update_time=datetime.now(),
                            is_deleted=False,
                            deleted_at=""
                        )
                        await video_relation.save()
                        new_relations_count += 1

                except Exception as e:
                    self.logger.log_error(f"为用户 {user_uuid} 创建视频关联失败: {str(e)}")
                    continue

            if new_relations_count > 0:
                self.logger.log_debug(f"为视频 {video_id} 创建了 {new_relations_count} 个用户关联")

        except Exception as e:
            self.logger.log_error(f"处理用户收件箱视频关联失败: {str(e)}")
