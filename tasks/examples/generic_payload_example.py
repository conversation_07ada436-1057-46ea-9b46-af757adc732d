#!/usr/bin/env python3
"""
EventPayload 泛型使用示例

展示如何使用新的泛型 EventPayload 模型
"""

import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from tasks.core.models import (
    EventPayload,
    FunctionComputeEvent,
    TrendRefreshParams,
    create_author_monitor_payload,
    create_keyword_monitor_payload,
    create_trend_refresh_payload,
)


def example_1_using_factory_functions():
    """示例1：使用工厂函数创建事件载荷"""
    print("=== 示例1：使用工厂函数 ===")

    # 创建趋势刷新载荷
    trend_payload = create_trend_refresh_payload(
        batch_size=200, max_age_hours=3, filters={"date_range": {"start": "2025-07-20", "end": "2025-07-27"}}
    )

    # 创建 Function Compute 事件
    event = FunctionComputeEvent(
        triggerTime="2025-07-28T10:00:00Z", triggerName="trend-refresh-trigger", payload=trend_payload
    )

    print(f"事件类型: {event.payload.event_name}")
    print(f"参数: {event.payload.event_params}")
    print()


def example_2_using_generic_eventpayload():
    """示例2：直接使用泛型 EventPayload"""
    print("=== 示例2：直接使用泛型 EventPayload ===")

    # 使用具体参数类型
    payload = EventPayload[TrendRefreshParams](
        event_name="trend_refresh_task",
        event_params=TrendRefreshParams(batch_size=150, max_age_hours=2, filters={"video_ids": ["123", "456"]}),
    )

    event = FunctionComputeEvent(triggerTime="2025-07-28T10:00:00Z", triggerName="custom-trigger", payload=payload)

    print(f"事件类型: {event.payload.event_name}")
    print(f"参数: {event.payload.event_params}")
    print()


def example_3_auto_conversion():
    """示例3：自动转换各种格式"""
    print("=== 示例3：自动转换 ===")

    # 从字典创建
    event1 = FunctionComputeEvent(
        payload={"event_name": "author_monitor_task", "event_params": {"batch_size": 80, "author_video_limit": 40}}
    )
    print(f"从字典: {event1.payload.event_name} - {event1.payload.event_params}")

    # 从 JSON 字符串创建
    event2 = FunctionComputeEvent(
        payload='{"event_name": "keyword_monitor_task", "event_params": {"keyword_search_days": 14}}'
    )
    print(f"从JSON: {event2.payload.event_name} - {event2.payload.event_params}")

    # 从配置字典创建（无 event_name）
    event3 = FunctionComputeEvent(payload={"task_type": "trend_refresh", "batch_size": 120})
    print(f"从配置: {event3.payload.event_name} - {event3.payload.event_params}")
    print()


def example_4_type_safety():
    """示例4：类型安全和验证"""
    print("=== 示例4：类型安全 ===")

    try:
        # 使用工厂函数，类型安全
        payload = create_author_monitor_payload(batch_size=25, author_video_limit=60, filters={"min_fans_count": 10000})
        print(f"成功创建: {payload.event_name}")

        # 参数验证
        # 如果传入无效参数，Pydantic 会抛出验证错误
        # invalid_payload = create_trend_refresh_payload(batch_size=-1)  # 这会报错

    except Exception as e:
        print(f"验证错误: {e}")


if __name__ == "__main__":
    example_1_using_factory_functions()
    example_2_using_generic_eventpayload()
    example_3_auto_conversion()
    example_4_type_safety()

    print("=== 总结 ===")
    print("✓ payload 现在一定是 EventPayload 类型")
    print("✓ 支持泛型，提供更好的类型安全性")
    print("✓ 提供工厂函数，简化创建过程")
    print("✓ 自动转换各种输入格式")
    print("✓ 保持向后兼容性")
