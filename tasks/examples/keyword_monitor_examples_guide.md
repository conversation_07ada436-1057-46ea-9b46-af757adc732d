# 关键词监控示例使用指南

本指南详细介绍如何使用关键词监控的各种示例代码。

## 📋 示例文件概览

### 1. 快速测试 - `keyword_monitor_quick_test.py`
**适用场景**: 验证功能是否正常工作
- ⚡ 执行时间短（约1分钟）
- 🔍 只处理少量数据
- ✅ 快速验证配置是否正确

### 2. 简单示例 - `keyword_monitor_simple_example.py`
**适用场景**: 学习和理解基本功能
- 📚 适合新手学习
- 🎯 交互式选择运行模式
- 📊 包含多种使用场景

## 🚀 快速开始

### 第一步：验证环境
```bash
# 运行快速测试，验证环境是否正常
python tasks/examples/keyword_monitor_quick_test.py
```

如果看到 "🎉 快速测试通过！" 说明环境配置正确。

### 第二步：学习基本用法
```bash
# 运行简单示例，学习基本功能
python tasks/examples/keyword_monitor_simple_example.py
```

选择 "1. 基本示例" 来了解完整的监控流程。

## 📖 详细使用说明

### 快速测试使用方法

```bash
python tasks/examples/keyword_monitor_quick_test.py
```

**输出示例**:
```
🚀 关键词监控快速测试
========================================
📡 连接数据库...
✅ 数据库连接成功
📊 数据库中关键词数量: 150
📝 示例关键词: 人工智能
🔍 开始执行关键词监控测试...

📊 测试结果:
   ✅ 状态: success
   📈 处理数量: 2
   ✅ 成功数量: 2
   ❌ 失败数量: 0
   ⏱️ 执行时间: 45.32 秒
🎉 快速测试通过！关键词监控功能正常
```

### 简单示例使用方法

```bash
python tasks/examples/keyword_monitor_simple_example.py
```

**交互选择**:
```
🎯 请选择要运行的示例:
1. 基本示例 (监控所有过期关键词)
2. 过滤示例 (只监控指定关键词)
3. 性能测试 (小批量快速测试)
4. 运行所有示例
5. 退出

请输入选择 (1-5): 1
```

**各选项说明**:
- **选项1**: 监控所有24小时内未更新的关键词
- **选项2**: 只监控指定的关键词（如"科技"、"AI"等）
- **选项3**: 小批量测试，查看性能指标
- **选项4**: 依次运行所有示例

## ⚙️ 配置参数说明

### 基本配置参数

```python
config = TaskConfig(
    task_type="keyword_monitor",    # 任务类型
    batch_size=10,                  # 每批处理的关键词数量
    max_age_hours=24,              # 监控多少小时内未更新的关键词
    timeout=300,                   # 任务超时时间（秒）
    filters=None                   # 过滤条件（可选）
)
```

### 过滤条件配置

```python
filters = {
    "keywords": ["科技", "AI", "人工智能"],      # 只监控这些关键词
    "exclude_keywords": ["广告", "推广"]        # 排除这些关键词
}
```

## 🔧 故障排除

### 常见问题

#### 1. 数据库连接失败
```
❌ 数据库连接失败: connection refused
```
**解决方案**:
- 检查数据库服务是否启动
- 验证 `settings/config.py` 中的数据库配置

#### 2. 没有关键词数据
```
⚠️ 数据库中没有关键词数据，请先添加关键词
```
**解决方案**:
- 确保 `trendinsight_keyword` 表中有数据
- 可以通过管理界面或API添加关键词

#### 3. API调用失败
```
❌ 搜索关键词视频异常: 网络连接超时
```
**解决方案**:
- 检查网络连接
- 确认API配置正确
- 注意API限流，适当增加延迟

### 调试技巧

1. **查看详细日志**
   - 所有示例都会输出详细的执行日志
   - 注意错误信息和警告

2. **从小批量开始**
   - 先运行快速测试验证环境
   - 再运行小批量配置测试功能
   - 最后使用正常批量配置

3. **监控性能指标**
   - 关注内存使用情况
   - 监控API调用频率
   - 记录处理速度

## 📊 性能参考

### 典型性能指标

- **小批量测试** (2-5个关键词): 30-60秒
- **标准配置** (10-20个关键词): 2-5分钟
- **大批量配置** (50+个关键词): 10-30分钟

### 影响性能的因素

1. **网络状况**: API调用速度
2. **关键词热度**: 热门关键词返回视频更多
3. **数据库性能**: 写入速度影响整体性能
4. **并发限制**: API限流影响处理速度

## 💡 最佳实践

1. **测试流程**
   ```bash
   # 1. 先运行快速测试
   python tasks/examples/keyword_monitor_quick_test.py
   
   # 2. 学习基本用法
   python tasks/examples/keyword_monitor_simple_example.py
   ```

2. **生产环境使用**
   - 使用 Makefile 中的预定义任务
   - 设置合适的批量大小和超时时间
   - 监控任务执行状态和性能

3. **开发调试**
   - 使用快速测试验证代码修改
   - 使用小批量配置测试新功能
   - 查看详细日志定位问题

## 🤝 获取帮助

如果遇到问题：
1. 查看示例输出的错误信息
2. 检查数据库和网络配置
3. 参考本指南的故障排除部分
4. 查看项目的其他文档
