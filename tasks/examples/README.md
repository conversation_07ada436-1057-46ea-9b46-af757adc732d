# 任务系统示例

这个目录包含了任务系统的各种示例代码，用于测试和学习不同类型的任务。

## 📁 文件列表

### 🔍 关键词监控
- `keyword_monitor_examples_guide.md` - **关键词监控示例详细使用指南** 📖
- `keyword_monitor_quick_test.py` - 关键词监控快速测试（验证功能是否正常）
- `keyword_monitor_simple_example.py` - 关键词监控任务简单示例（推荐新手使用）

### 🔧 通用示例
- `generic_payload_example.py` - 通用任务载荷示例

## 🚀 快速开始

### 关键词监控示例

#### 1. 快速验证功能
```bash
# 验证关键词监控功能是否正常
python tasks/examples/keyword_monitor_quick_test.py
```

#### 2. 学习基本用法
```bash
# 交互式学习关键词监控功能
python tasks/examples/keyword_monitor_simple_example.py
```

#### 3. 查看详细指南
查看 `keyword_monitor_examples_guide.md` 了解完整的使用说明。

## 📋 前置条件

1. **数据库初始化**
   ```bash
   # 确保数据库已初始化
   python -m tortoise init
   ```

2. **数据准备**
   - 关键词监控：确保 `trendinsight_keyword` 表中有关键词数据

## 🎯 使用建议

### 对于新手用户
1. 先运行快速测试验证环境配置
2. 再运行简单示例学习基本功能
3. 查看详细指南了解高级用法

### 对于开发者
- 使用快速测试验证代码修改
- 使用简单示例测试新功能
- 参考示例代码了解最佳实践

## 🔧 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查 `settings/config.py` 中的数据库配置
   - 确保数据库服务正在运行

2. **没有数据**
   - 关键词监控：检查 `trendinsight_keyword` 表是否有数据

3. **API 调用失败**
   - 检查网络连接
   - 确认 API 配置正确
   - 注意 API 限流

## 📝 注意事项

1. **API 限流**
   - 避免过于频繁的 API 调用
   - 测试间隔建议至少 10 秒

2. **数据一致性**
   - 确保数据库中的数据是最新的
   - 注意时区问题

3. **资源使用**
   - 大批量测试可能消耗较多内存
   - 建议在测试环境中运行

## 🤝 贡献

如果您发现问题或有改进建议，请：
1. 创建 Issue 描述问题
2. 提交 Pull Request 包含修复
3. 更新相关文档
