# Event Payload 使用指南

## 概述

我们为 Function Compute 时间触发器事件创建了强类型的泛型 Pydantic 模型，确保 payload 数据的类型安全和验证。现在 payload 一定是 `EventPayload` 类型，并且支持泛型参数。

## 数据模型

### EventPayload (泛型)

事件载荷模型，支持泛型参数：

```python
class EventPayload(BaseModel, Generic[T]):
    event_name: str          # 事件名称，用于标识要执行的任务类型
    event_params: T = {}     # 事件参数，支持泛型类型
```

### 具体参数类型

我们定义了三种具体的事件参数类型：

```python
class TrendRefreshParams(BaseModel):
    """趋势刷新任务参数"""
    batch_size: int = 100
    max_age_hours: int = 1
    timeout: int = 3600
    filters: Optional[Dict[str, Any]] = None

class AuthorMonitorParams(BaseModel):
    """作者监控任务参数"""
    batch_size: int = 50
    max_age_hours: int = 2
    timeout: int = 3600
    author_video_limit: int = 50
    filters: Optional[Dict[str, Any]] = None

class KeywordMonitorParams(BaseModel):
    """关键词监控任务参数"""
    batch_size: int = 20
    max_age_hours: int = 3
    timeout: int = 3600
    keyword_video_limit: int = 100
    keyword_search_days: int = 7
    filters: Optional[Dict[str, Any]] = None
```

### 类型别名

为了方便使用，我们定义了类型别名：

```python
TrendRefreshPayload = EventPayload[TrendRefreshParams]
AuthorMonitorPayload = EventPayload[AuthorMonitorParams]
KeywordMonitorPayload = EventPayload[KeywordMonitorParams]
GenericEventPayload = EventPayload[Dict[str, Any]]
```

### FunctionComputeEvent

Function Compute 事件模型，payload 字段现在一定是 `EventPayload` 类型：

```python
class FunctionComputeEvent(BaseModel):
    triggerTime: Optional[str] = None     # 触发时间
    triggerName: Optional[str] = None     # 触发器名称
    payload: GenericEventPayload          # 事件载荷，一定是 EventPayload 类型
    config: Optional[Dict[str, Any]] = None  # 额外配置
```

## 工厂函数

我们提供了便利的工厂函数来创建特定类型的事件载荷：

### create_trend_refresh_payload

```python
def create_trend_refresh_payload(
    batch_size: int = 100,
    max_age_hours: int = 1,
    timeout: int = 3600,
    filters: Optional[Dict[str, Any]] = None
) -> TrendRefreshPayload
```

### create_author_monitor_payload

```python
def create_author_monitor_payload(
    batch_size: int = 50,
    max_age_hours: int = 2,
    timeout: int = 3600,
    author_video_limit: int = 50,
    filters: Optional[Dict[str, Any]] = None
) -> AuthorMonitorPayload
```

### create_keyword_monitor_payload

```python
def create_keyword_monitor_payload(
    batch_size: int = 20,
    max_age_hours: int = 3,
    timeout: int = 3600,
    keyword_video_limit: int = 100,
    keyword_search_days: int = 7,
    filters: Optional[Dict[str, Any]] = None
) -> KeywordMonitorPayload
```

## 使用示例

### 1. 使用工厂函数创建载荷

```python
from tasks.core.models import create_trend_refresh_payload, FunctionComputeEvent

# 创建趋势刷新载荷
payload = create_trend_refresh_payload(
    batch_size=200,
    max_age_hours=3,
    filters={"date_range": {"start": "2025-07-20", "end": "2025-07-27"}}
)

# 创建事件
event = FunctionComputeEvent(
    triggerTime="2025-07-28T10:00:00Z",
    triggerName="trend-refresh-trigger",
    payload=payload
)
```

### 2. 直接使用泛型 EventPayload

```python
from tasks.core.models import EventPayload, TrendRefreshParams

# 使用具体参数类型
payload = EventPayload[TrendRefreshParams](
    event_name="trend_refresh_task",
    event_params=TrendRefreshParams(
        batch_size=150,
        max_age_hours=2,
        filters={"video_ids": ["123", "456"]}
    )
)

# 使用字典类型
payload = EventPayload[dict](
    event_name="custom_task",
    event_params={"custom_param": "value"}
)
```

### 3. JSON 配置示例

Function Compute 事件的 JSON 格式（系统会自动转换为 EventPayload）：

```json
{
  "triggerTime": "2025-07-28T10:00:00Z",
  "triggerName": "trend-refresh-trigger",
  "payload": {
    "event_name": "trend_refresh_task",
    "event_params": {
      "batch_size": 100,
      "max_age_hours": 1,
      "filters": {
        "date_range": {
          "start": "2025-07-20",
          "end": "2025-07-27"
        }
      }
    }
  }
}
```

## 自动转换机制

系统会自动将各种输入格式转换为 `EventPayload`：

1. **已有的 EventPayload 对象** - 如果 event_params 是 Pydantic 模型，会自动转换为字典
2. **JSON 字符串** - 自动解析为 EventPayload
3. **字典格式** - 自动转换为 EventPayload
4. **其他类型** - 创建默认的 EventPayload

## 任务类型推断

系统会根据 `event_name` 自动推断任务类型：

- 包含 "trend" 或 "refresh" → `trend_refresh`
- 包含 "author" 或 "monitor" → `author_monitor`  
- 包含 "keyword" → `keyword_monitor`

## 类型安全

通过泛型 EventPayload，我们实现了：

1. **编译时类型检查** - IDE 可以提供完整的类型提示
2. **运行时验证** - Pydantic 确保数据格式正确
3. **灵活性** - 支持多种参数类型
4. **向后兼容** - 旧的格式仍然可以正常工作

## 最佳实践

1. **使用工厂函数** - 推荐使用 `create_*_payload` 函数创建载荷
2. **类型注解** - 在函数参数中使用具体的载荷类型
3. **参数验证** - 利用 Pydantic 的验证器确保数据正确性
4. **错误处理** - 系统会自动处理各种输入格式，确保健壮性

这样的设计确保了类型安全，同时保持了灵活性和向后兼容性。
