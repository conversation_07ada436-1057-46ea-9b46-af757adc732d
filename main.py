"""
FastAPI 应用入口

初始化 FastAPI 应用和相关配置
"""

from contextlib import asynccontextmanager
from fastapi import FastAPI
from tortoise import Tortoise

from core.init_app import (
    init_data,
    make_middlewares,
    register_exceptions,
    register_routers,
)
from core.openapi_config import setup_openapi_docs
from settings.config import settings
from utils.cache_manager import start_cache_cleanup_task

# Validate settings before app starts
settings.validators.validate()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时初始化
    await Tortoise.init(config=settings.tortoise_orm)
    await init_data()
    start_cache_cleanup_task()

    yield

    # 关闭时清理
    await Tortoise.close_connections()


# 创建 FastAPI 应用
app = FastAPI(
    title=settings.APP_TITLE,
    description=settings.APP_DESCRIPTION,
    version=settings.VERSION,
    middleware=make_middlewares(),
    lifespan=lifespan,
)

# 注册异常处理器
register_exceptions(app)

# 注册路由
register_routers(app)

# 设置 OpenAPI 文档配置
setup_openapi_docs(app)

# Tortoise ORM 现在通过 lifespan 管理


# 根路由
@app.get("/")
async def root():
    """根路径"""
    return {"message": "Vue FastAPI Admin Backend", "version": settings.VERSION, "docs": "/docs", "redoc": "/redoc"}


# 健康检查
@app.get("/health")
async def health_check():
    """健康检查接口"""
    return {"status": "healthy"}
