# Suggested Commands

## Application Management
```bash
# Start the application
python run.py
make start
./start.sh

# Install dependencies
pip install -r requirements.txt
uv add pyproject.toml
```

## Development Commands
```bash
# Code formatting
make format           # Format code with black + isort
black ./             # Format with black
isort ./ --profile black  # Sort imports

# Code checking
make check           # Run format check + lint
make check-format    # Dry-run formatter
make lint           # Run ruff linter
ruff check ./       # Run ruff directly
```

## Database Management
```bash
# Migration commands
make migrate        # Generate migration files with aerich
make upgrade        # Apply migrations with aerich
make clean-db       # Delete migrations and database files

# Direct aerich commands
aerich migrate      # Generate migration files
aerich upgrade      # Apply migrations
```

## Testing
```bash
make test           # Run full test suite
pytest -vv -s --cache-clear ./  # Run tests with verbose output
```

## System Commands (Darwin/macOS)
```bash
# File operations
ls -la              # List files with details
find . -name "*.py" # Find Python files
grep -r "pattern"   # Search for pattern
cd /path/to/dir     # Change directory

# Git operations
git status          # Check git status
git add .           # Add all changes
git commit -m "message"  # Commit changes
git push origin branch   # Push to remote
```

## Development Server
- **Local server**: http://localhost:9999
- **API docs**: http://localhost:9999/docs
- **ReDoc**: http://localhost:9999/redoc