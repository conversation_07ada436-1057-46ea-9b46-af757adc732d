# Code Style and Conventions

## Python Code Style
- **Python Version**: >= 3.11 required
- **Line Length**: 120 characters (configured in pyproject.toml)
- **Formatter**: Black with isort for import sorting
- **Linter**: Ruff with custom ignore rules (F403, F405 ignored)
- **Type Hints**: Strongly encouraged, using typing module features

## Code Organization
- **Models**: Located in `models/` directory with platform-specific subdirectories
- **API Routes**: Organized in `api/` with versioned endpoints (`api/v1/`)
- **Controllers**: Business logic in `controllers/` directory
- **Schemas**: Pydantic models in `schemas/` for validation
- **RPC Services**: External service clients in `rpc/` directory

## Database Conventions
- **ORM**: Tortoise ORM with async support
- **Model Inheritance**: All models inherit from BaseModel and TimestampMixin
- **Table Names**: Snake_case with descriptive names
- **Field Types**: Use appropriate Tortoise field types
- **Indexes**: Define indexes for commonly queried fields

## Model Structure
```python
class ExampleModel(BaseModel, TimestampMixin):
    name = fields.CharField(max_length=64, description="Description")
    status = fields.IntEnumField(EnumType, default=EnumType.DEFAULT)
    
    class Meta:
        table = "example_table"
        indexes = [("field_name",)]
```

## Import Organization
- **Standard imports**: First
- **Third-party imports**: Second
- **Local imports**: Last
- **Relative imports**: Use sparingly, prefer absolute imports

## Testing Conventions
- **Framework**: pytest with asyncio support
- **Test Discovery**: Files named `test_*.py`
- **Markers**: Use `@pytest.mark.real_api` for external API tests
- **Mock Data**: Store in `tests/mock/` directories