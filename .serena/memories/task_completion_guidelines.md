# Task Completion Guidelines

## After Making Code Changes
1. **Run Format Check**: `make check-format` or `black ./ --check`
2. **Run Linter**: `make lint` or `ruff check ./`
3. **Run Tests**: `make test` or `pytest -vv -s --cache-clear ./`
4. **Database Migration**: If model changes, run `make migrate` then `make upgrade`

## Development Workflow
1. **Make Changes**: Edit code following style conventions
2. **Format Code**: `make format` (runs black + isort)
3. **Check Code**: `make check` (format check + lint)
4. **Test Changes**: `make test` to ensure no regressions
5. **Database Update**: Run migrations if models changed

## Model Changes Workflow
1. **Edit Model**: Make changes to model files
2. **Import Model**: Ensure model is imported in `models/__init__.py`
3. **Generate Migration**: `make migrate` or `aerich migrate`
4. **Apply Migration**: `make upgrade` or `aerich upgrade`
5. **Test**: Verify changes work correctly

## Key Quality Checks
- **Code Format**: Black + isort compliance
- **Linting**: Ruff checks pass
- **Tests**: All tests pass
- **Database**: Migrations applied successfully
- **Documentation**: API docs update automatically

## Common Issues
- **Model Import**: Ensure new models are imported in `models/__init__.py`
- **Migration**: Always generate and apply migrations for model changes
- **Testing**: Use appropriate test markers for different test types
- **Dependencies**: Keep requirements.txt updated