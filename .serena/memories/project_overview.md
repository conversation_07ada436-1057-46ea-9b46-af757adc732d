# Project Overview

## Project Purpose
This is a FastAPI-based social media crawler and management system that provides:
- Social media platform integration (抖音/Douyin, TrendInsight, etc.)
- User account management with JWT authentication
- Database management with Tortoise ORM
- API documentation with Swagger UI
- Crawler functionality for social media platforms

## Key Features
- **Platform Integration**: Supports multiple social media platforms including Douyin, TrendInsight, Xiaohongshu, Bilibili, etc.
- **Crawler Management**: Account pool management for crawling operations
- **Admin Panel**: Complete admin interface with user, role, menu, and API management
- **Database**: MySQL backend with SQLite support, using Tortoise ORM
- **Authentication**: JWT-based authentication with 7-day token expiration
- **API Documentation**: Auto-generated Swagger UI and ReDoc documentation

## Architecture
- **FastAPI Framework**: Modern, fast web framework for Python
- **Database**: Tortoise ORM with MySQL/SQLite support
- **Authentication**: JWT tokens with Argon2 password hashing
- **Migration**: Aerich for database schema management
- **Testing**: Pytest with asyncio support
- **API Versioning**: Structured API routes under /api/v1/

## Entry Points
- **Main Application**: `run.py` - starts uvicorn server on port 9999
- **API Documentation**: http://localhost:9999/docs
- **Admin Interface**: Built-in admin panel with user/role management