"""
测试 extract_douyin_user_id 函数

测试从抖音用户URL中提取用户ID的功能
"""

import pytest

from utils.douyin import extract_douyin_user_id


class TestExtractDouyinUserId:
    """测试 extract_douyin_user_id 函数"""

    def test_extract_standard_url(self):
        """测试标准格式的URL"""
        url = "https://www.douyin.com/user/MS4wLjABAAAA123"
        result = extract_douyin_user_id(url)
        assert result == "MS4wLjABAAAA123"

    def test_extract_url_with_trailing_slash(self):
        """测试带尾部斜杠的URL"""
        url = "https://www.douyin.com/user/123456789/"
        result = extract_douyin_user_id(url)
        assert result == "123456789"

    def test_extract_url_without_protocol(self):
        """测试不带协议的URL"""
        url = "douyin.com/user/test_user"
        result = extract_douyin_user_id(url)
        assert result == "test_user"

    def test_extract_url_without_www(self):
        """测试不带www的URL"""
        url = "https://douyin.com/user/no_www_user"
        result = extract_douyin_user_id(url)
        assert result == "no_www_user"

    def test_extract_url_with_query_params(self):
        """测试带查询参数的URL"""
        url = "https://www.douyin.com/user/user_with_params?tab=favorite"
        result = extract_douyin_user_id(url)
        assert result == "user_with_params"

    def test_extract_url_with_fragment(self):
        """测试带片段标识符的URL"""
        url = "https://www.douyin.com/user/user_with_fragment#section1"
        result = extract_douyin_user_id(url)
        assert result == "user_with_fragment"

    def test_extract_complex_user_id(self):
        """测试复杂的用户ID格式"""
        url = "https://www.douyin.com/user/MS4wLjABAAAADY7u-UqfGogzrFtNghKD7Rgnz1QnWBJUrBA4AYrCX20zcMMCg1d8xoslUJiVTQyP"
        result = extract_douyin_user_id(url)
        assert result == "MS4wLjABAAAADY7u-UqfGogzrFtNghKD7Rgnz1QnWBJUrBA4AYrCX20zcMMCg1d8xoslUJiVTQyP"

    def test_extract_invalid_url(self):
        """测试无效的URL"""
        url = "invalid_url"
        result = extract_douyin_user_id(url)
        assert result == ""

    def test_extract_empty_url(self):
        """测试空URL"""
        url = ""
        result = extract_douyin_user_id(url)
        assert result == ""

    def test_extract_none_url(self):
        """测试None URL"""
        url = None
        result = extract_douyin_user_id(url)
        assert result == ""

    def test_extract_non_string_url(self):
        """测试非字符串URL"""
        url = 123
        result = extract_douyin_user_id(url)
        assert result == ""

    def test_extract_wrong_domain(self):
        """测试错误的域名"""
        url = "https://www.tiktok.com/user/test_user"
        result = extract_douyin_user_id(url)
        assert result == ""

    def test_extract_wrong_path(self):
        """测试错误的路径"""
        url = "https://www.douyin.com/video/7123456789"
        result = extract_douyin_user_id(url)
        assert result == ""

    def test_extract_url_with_spaces(self):
        """测试带空格的URL"""
        url = "https://www.douyin.com/user/  user_with_spaces  "
        result = extract_douyin_user_id(url)
        assert result == "user_with_spaces"

    def test_extract_url_fallback_parsing(self):
        """测试URL解析备用方案"""
        # 这个URL格式可能不被正则表达式匹配，但应该被URL解析器处理
        url = "https://www.douyin.com/user/fallback_user?some=param"
        result = extract_douyin_user_id(url)
        assert result == "fallback_user"

    @pytest.mark.parametrize("url,expected", [
        ("https://www.douyin.com/user/test1", "test1"),
        ("https://douyin.com/user/test2/", "test2"),
        ("http://www.douyin.com/user/test3", "test3"),
        ("douyin.com/user/test4", "test4"),
        ("https://www.douyin.com/user/test5?tab=posts", "test5"),
        ("https://www.douyin.com/user/test6#top", "test6"),
        ("invalid", ""),
        ("", ""),
        ("https://www.tiktok.com/user/test", ""),
    ])
    def test_extract_parametrized(self, url, expected):
        """参数化测试多种URL格式"""
        result = extract_douyin_user_id(url)
        assert result == expected
