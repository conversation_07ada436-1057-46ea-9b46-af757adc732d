"""
抖音映射器集成测试

测试整个映射器系统的集成功能，包括：
- 与现有控制器的集成
- 端到端的数据流测试
- 实际数据源的模拟测试
"""

from datetime import datetime
from unittest.mock import patch

import pytest

from controllers.douyin.video import VideoFetcherController
from mappers.douyin import FetcherConfig


class TestVideoFetcherControllerIntegration:
    """测试 VideoFetcherController 与新映射器的集成"""

    @pytest.fixture
    def controller(self):
        return VideoFetcherController()

    @pytest.fixture
    def mock_config(self):
        from models.douyin import DouyinFetchMethod
        return FetcherConfig(
            fallback_methods=[DouyinFetchMethod.MOBILE.value, DouyinFetchMethod.JINGXUAN.value, DouyinFetchMethod.RPC.value],
            enable_fallback=True,
            retry_attempts=2,
            timeout=30,
        )











    async def test_backward_compatibility(self, controller):
        """测试向后兼容性 - 原有方法仍然可用"""
        # 测试VideoFetcherController的核心方法
        assert hasattr(controller, "fetch_video_data_auto")
        assert hasattr(controller, "fetch_with_fallback")

        # 测试可以创建其他控制器
        from controllers.douyin import DouyinHTMLController
        from controllers.douyin.video.rpc_video_controller import DouyinRPCVideoController
        html_controller = DouyinHTMLController()
        rpc_video_controller = DouyinRPCVideoController()

        assert html_controller is not None
        assert rpc_video_controller is not None

        # 测试HTML控制器的方法
        assert hasattr(html_controller, "fetch_jingxuan_video_data")
        assert hasattr(html_controller, "fetch_mobile_share_video_data")


class TestEndToEndDataFlow:
    """端到端数据流测试"""

    @pytest.fixture
    def controller(self):
        from controllers.douyin.video.rpc_video_controller import DouyinRPCVideoController
        return DouyinRPCVideoController()








class TestErrorHandlingIntegration:
    """错误处理集成测试"""

    @pytest.fixture
    def controller(self):
        return DouyinController()






