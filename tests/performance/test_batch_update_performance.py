#!/usr/bin/env python3
"""
批量更新性能测试

测试不同批量更新方法的性能差异
"""

import asyncio
import sys
import os
import time
from datetime import datetime
from typing import List

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "../.."))

from tortoise import Tortoise
from settings.config import settings
from models.douyin.models import (
    DouyinAweme, 
    update_douyin_aweme,
    batch_update_douyin_aweme, 
    batch_update_douyin_aweme_optimized,
    batch_update_douyin_aweme_with_optimizer
)
from log import logger


class PerformanceTest:
    """性能测试类"""
    
    def __init__(self):
        self.test_data_prefix = "perf_test"
        
    async def create_test_data(self, count: int) -> List[DouyinAweme]:
        """创建测试数据"""
        test_data = []
        
        for i in range(count):
            aweme = DouyinAweme(
                aweme_id=f"{self.test_data_prefix}_{i:06d}",
                user_id=f"user_{i % 100}",  # 模拟100个不同用户
                sec_uid=f"sec_uid_{i % 100}",
                nickname=f"测试用户{i % 100}",
                title=f"性能测试视频标题 {i}",
                desc=f"这是第 {i} 个性能测试视频的描述",
                aweme_type="video",
                create_time=datetime.now(),
                liked_count=str(i * 10),
                comment_count=str(i * 2),
                share_count=str(i),
                collected_count=str(i // 2),
                source_keyword=self.test_data_prefix
            )
            test_data.append(aweme)
        
        return test_data
    
    async def cleanup_test_data(self):
        """清理测试数据"""
        deleted_count = await DouyinAweme.filter(source_keyword=self.test_data_prefix).delete()
        logger.info(f"清理了 {deleted_count} 条测试数据")
    
    async def test_method_performance(self, method_name: str, method_func, test_data: List[DouyinAweme]) -> dict:
        """测试单个方法的性能"""
        logger.info(f"\n🔥 测试方法: {method_name}")
        logger.info(f"数据量: {len(test_data)} 条")
        
        # 清理之前的测试数据
        await self.cleanup_test_data()
        
        # 记录开始时间
        start_time = time.time()
        start_memory = self._get_memory_usage()
        
        try:
            # 执行测试方法
            success = await method_func(test_data)
            
            # 记录结束时间
            end_time = time.time()
            end_memory = self._get_memory_usage()
            
            duration = end_time - start_time
            memory_used = end_memory - start_memory
            
            # 验证数据是否正确保存
            saved_count = await DouyinAweme.filter(source_keyword=self.test_data_prefix).count()
            
            result = {
                "method": method_name,
                "success": success,
                "duration": duration,
                "memory_used": memory_used,
                "records_processed": len(test_data),
                "records_saved": saved_count,
                "records_per_second": len(test_data) / duration if duration > 0 else 0,
                "data_integrity": saved_count == len(test_data)
            }
            
            logger.info(f"✅ {method_name} 完成:")
            logger.info(f"   耗时: {duration:.2f} 秒")
            logger.info(f"   处理速度: {result['records_per_second']:.0f} 条/秒")
            logger.info(f"   内存使用: {memory_used:.2f} MB")
            logger.info(f"   数据完整性: {'✅' if result['data_integrity'] else '❌'}")
            
            return result
            
        except Exception as e:
            logger.error(f"❌ {method_name} 测试失败: {e}")
            return {
                "method": method_name,
                "success": False,
                "error": str(e),
                "duration": 0,
                "memory_used": 0,
                "records_processed": len(test_data),
                "records_saved": 0,
                "records_per_second": 0,
                "data_integrity": False
            }
    
    def _get_memory_usage(self) -> float:
        """获取当前内存使用量（MB）"""
        try:
            import psutil
            process = psutil.Process()
            return process.memory_info().rss / 1024 / 1024
        except ImportError:
            return 0.0
    
    async def run_performance_comparison(self, data_sizes: List[int]):
        """运行性能对比测试"""
        logger.info("🚀 开始批量更新性能对比测试")
        
        # 定义测试方法
        test_methods = [
            ("优化后批量更新", batch_update_douyin_aweme),
            ("分批优化更新", lambda data: batch_update_douyin_aweme_optimized(data, batch_size=500)),
            ("数据库优化器", batch_update_douyin_aweme_with_optimizer),
        ]
        
        all_results = []
        
        for data_size in data_sizes:
            logger.info(f"\n{'='*60}")
            logger.info(f"测试数据量: {data_size} 条记录")
            logger.info(f"{'='*60}")
            
            # 创建测试数据
            test_data = await self.create_test_data(data_size)
            
            size_results = []
            
            for method_name, method_func in test_methods:
                result = await self.test_method_performance(method_name, method_func, test_data.copy())
                result["data_size"] = data_size
                size_results.append(result)
                all_results.append(result)
                
                # 测试间短暂休息
                await asyncio.sleep(1)
            
            # 显示本轮对比结果
            self._print_size_comparison(size_results)
        
        # 显示总体对比结果
        self._print_overall_comparison(all_results)
        
        # 清理测试数据
        await self.cleanup_test_data()
    
    def _print_size_comparison(self, results: List[dict]):
        """打印单个数据量的对比结果"""
        logger.info(f"\n📊 本轮测试结果对比:")
        logger.info(f"{'方法':<20} {'耗时(秒)':<10} {'速度(条/秒)':<12} {'内存(MB)':<10} {'完整性':<8}")
        logger.info("-" * 70)
        
        for result in results:
            integrity = "✅" if result['data_integrity'] else "❌"
            logger.info(f"{result['method']:<20} {result['duration']:<10.2f} {result['records_per_second']:<12.0f} {result['memory_used']:<10.2f} {integrity:<8}")
    
    def _print_overall_comparison(self, all_results: List[dict]):
        """打印总体对比结果"""
        logger.info(f"\n📈 总体性能对比分析:")
        
        # 按方法分组
        method_stats = {}
        for result in all_results:
            method = result['method']
            if method not in method_stats:
                method_stats[method] = []
            method_stats[method].append(result)
        
        logger.info(f"{'方法':<20} {'平均速度':<12} {'最佳速度':<12} {'平均内存':<10}")
        logger.info("-" * 60)
        
        for method, results in method_stats.items():
            avg_speed = sum(r['records_per_second'] for r in results) / len(results)
            max_speed = max(r['records_per_second'] for r in results)
            avg_memory = sum(r['memory_used'] for r in results) / len(results)
            
            logger.info(f"{method:<20} {avg_speed:<12.0f} {max_speed:<12.0f} {avg_memory:<10.2f}")


async def main():
    """主函数"""
    # 初始化数据库连接
    await Tortoise.init(config=settings.TORTOISE_ORM)
    
    try:
        # 创建性能测试实例
        perf_test = PerformanceTest()
        
        # 定义测试数据量
        data_sizes = [100, 500, 1000, 2000]  # 可以根据需要调整
        
        # 运行性能对比测试
        await perf_test.run_performance_comparison(data_sizes)
        
        logger.info("\n🎉 性能测试完成！")
        
    except Exception as e:
        logger.error(f"❌ 性能测试过程中发生错误: {e}")
        
    finally:
        # 关闭数据库连接
        await Tortoise.close_connections()


if __name__ == "__main__":
    asyncio.run(main())
