"""
抖音映射器测试套件

测试所有映射器的功能，包括：
- BaseDataMapper 抽象基类
- 各种数据映射器（Mobile, Jingxuan, RPC）
- VideoFetcherController 统一控制器
- 配置和结果模型
"""

from datetime import datetime
from typing import Any, Dict
from unittest.mock import patch

import pytest

from controllers.douyin.video.video_fetcher import VideoFetcherController
from mappers.douyin import (
    BaseDataMapper,
    BatchFetchResult,
    FetcherConfig,
    FetchResult,
    JingxuanDataMapper,
    MobileDataMapper,
    NetworkException,
    ParseException,
    RPCDataMapper,
)


class TestFetcherConfig:
    """测试 FetcherConfig 配置模型"""

    def test_default_config(self):
        """测试默认配置"""
        config = FetcherConfig()

        assert config.fallback_methods == ["jingxuan", "mobile", "rpc"]
        assert config.enable_fallback is True
        assert config.retry_attempts == 3
        assert config.timeout == 30
        assert config.max_concurrent == 5
        assert config.use_proxy is True
        assert config.save_to_db is True

    def test_custom_config(self):
        """测试自定义配置"""
        config = FetcherConfig(
            fallback_methods=["rpc", "mobile"],
            enable_fallback=False,
            retry_attempts=5,
            timeout=60,
            use_proxy=False,
        )

        assert config.fallback_methods == ["rpc", "mobile"]
        assert config.enable_fallback is False
        assert config.retry_attempts == 5
        assert config.timeout == 60
        assert config.use_proxy is False

    def test_config_validation(self):
        """测试配置验证"""
        # 测试有效配置
        config = FetcherConfig()
        assert config.validate() is True

        # 测试无效的重试次数
        config = FetcherConfig(retry_attempts=-1)
        assert config.validate() is False

        # 测试无效的超时时间
        config = FetcherConfig(timeout=0)
        assert config.validate() is False

        # 测试无效的方法
        config = FetcherConfig(fallback_methods=["invalid_method"])
        assert config.validate() is False


class TestFetchResult:
    """测试 FetchResult 结果模型"""

    def test_success_result(self):
        """测试成功结果"""
        data = {"aweme_id": "123", "title": "测试视频"}
        result = FetchResult(
            success=True,
            aweme_id="123",
            data=data,
            source="mobile",
            method="mobile",
        )

        assert result.success is True
        assert result.aweme_id == "123"
        assert result.data == data
        assert result.source == "mobile"
        assert result.method == "mobile"
        assert isinstance(result.start_time, float)

    def test_failure_result(self):
        """测试失败结果"""
        result = FetchResult(
            success=False,
            aweme_id="123",
            error="网络连接失败",
        )

        assert result.success is False
        assert result.aweme_id == "123"
        assert result.data is None
        assert result.error == "网络连接失败"


class TestBatchFetchResult:
    """测试 BatchFetchResult 批量结果模型"""

    def test_batch_result(self):
        """测试批量结果"""
        results = [
            FetchResult(success=True, aweme_id="1", data={"id": "1"}),
            FetchResult(success=False, aweme_id="2", error="解析失败"),
            FetchResult(success=True, aweme_id="3", data={"id": "3"}),
        ]

        batch_result = BatchFetchResult(total_count=3, results=results)

        # Add results to update counts
        for result in results:
            batch_result.add_result(result)

        assert batch_result.total_count == 3
        assert batch_result.success_count == 2
        assert batch_result.failed_count == 1
        assert batch_result.success_rate == 2 / 3


class TestBaseDataMapper:
    """测试 BaseDataMapper 抽象基类"""

    def test_abstract_methods(self):
        """测试抽象方法"""
        # 不能直接实例化抽象类
        with pytest.raises(TypeError):
            BaseDataMapper()

    def test_utility_methods(self):
        """测试工具方法"""

        # 创建一个测试用的具体实现
        class TestMapper(BaseDataMapper):
            def map_to_standard_format(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
                return {"aweme_id": raw_data.get("aweme_id", "")}

            def validate_raw_data(self, raw_data: Dict[str, Any]) -> bool:
                return True

        mapper = TestMapper()

        # 测试 _safe_extract 方法
        data = {"a": {"b": {"c": "value"}}}
        assert mapper._safe_extract(data, "a") == {"b": {"c": "value"}}
        assert mapper._safe_extract(data, ["a", "b", "c"]) == "value"
        assert mapper._safe_extract(data, "d") is None
        assert mapper._safe_extract(data, "d", "default") == "default"

        # 测试 _convert_timestamp 方法
        timestamp = mapper._convert_timestamp(1640995200)  # 2022-01-01 00:00:00 UTC
        assert isinstance(timestamp, int)
        assert timestamp == 1640995200


class TestMobileDataMapper:
    """测试 MobileDataMapper 移动端数据映射器"""

    @pytest.fixture
    def mapper(self):
        return MobileDataMapper()

    @pytest.fixture
    def mock_mobile_data(self):
        """模拟移动端数据"""
        return {
            "aweme_detail": {
                "aweme_id": "7123456789",
                "desc": "测试视频描述",
                "create_time": 1640995200,
                "author": {"nickname": "测试用户", "unique_id": "test_user"},
                "video": {
                    "play_addr": {"url_list": ["https://example.com/video1.mp4", "https://example.com/video2.mp4"]},
                    "cover": {"url_list": ["https://example.com/cover1.jpg"]},
                    "duration": 15000,
                },
                "statistics": {"digg_count": 100, "comment_count": 50, "share_count": 25},
            }
        }

    def test_validate_raw_data_success(self, mapper, mock_mobile_data):
        """测试数据验证成功"""
        assert mapper.validate_raw_data(mock_mobile_data) is True

    def test_validate_raw_data_failure(self, mapper):
        """测试数据验证失败"""
        # 缺少必要字段
        invalid_data = {"invalid": "data"}
        assert mapper.validate_raw_data(invalid_data) is False

        # aweme_detail 为空
        invalid_data = {"aweme_detail": None}
        assert mapper.validate_raw_data(invalid_data) is False

    def test_map_to_standard_format(self, mapper, mock_mobile_data):
        """测试数据映射"""
        result = mapper.map_to_standard_format(mock_mobile_data)

        assert result["aweme_id"] == "7123456789"
        assert result["title"] == "测试视频描述"
        assert result["author_name"] == "测试用户"
        assert result["author_id"] == "test_user"
        assert result["duration"] == 15000
        assert result["like_count"] == 100
        assert result["comment_count"] == 50
        assert result["share_count"] == 25
        assert len(result["video_urls"]) == 2
        assert len(result["cover_urls"]) == 1
        assert isinstance(result["create_time"], datetime)

    async def test_process_mobile_video_urls(self, mapper):
        """测试移动端视频URL处理"""
        video_data = {"play_addr": {"url_list": ["https://example.com/video1.mp4", "https://example.com/video2.mp4"]}}

        urls = mapper._process_mobile_video_urls(video_data)
        assert len(urls) == 2
        assert "https://example.com/video1.mp4" in urls
        assert "https://example.com/video2.mp4" in urls

    async def test_process_mobile_video_urls_empty(self, mapper):
        """测试空的视频URL数据"""
        video_data = {}
        urls = mapper._process_mobile_video_urls(video_data)
        assert urls == []


class TestJingxuanDataMapper:
    """测试 JingxuanDataMapper 精选页面数据映射器"""

    @pytest.fixture
    def mapper(self):
        return JingxuanDataMapper()

    @pytest.fixture
    def mock_jingxuan_data(self):
        """模拟精选页面数据"""
        return {
            "aweme_id": "7123456789",
            "desc": "测试视频",
            "create_time": 1640995200,
            "author": {"nickname": "测试用户", "sec_uid": "test_sec_uid"},
            "video": {
                "bit_rate": [
                    {"play_addr": {"url_list": ["https://example.com/h264.mp4"]}, "gear_name": "adapt_540_1"},
                    {"play_addr": {"url_list": ["https://example.com/hd.mp4"]}, "gear_name": "normal_1080_0"},
                ],
                "cover": {"url_list": ["https://example.com/cover.jpg"]},
                "duration": 20000,
            },
            "statistics": {"digg_count": 200, "comment_count": 100},
        }

    async def test_validate_raw_data_success(self, mapper, mock_jingxuan_data):
        """测试数据验证成功"""
        assert await mapper.validate_raw_data(mock_jingxuan_data) is True

    async def test_validate_raw_data_failure(self, mapper):
        """测试数据验证失败"""
        invalid_data = {"invalid": "data"}
        assert await mapper.validate_raw_data(invalid_data) is False

    async def test_map_to_standard_format(self, mapper, mock_jingxuan_data):
        """测试数据映射"""
        result = await mapper.map_to_standard_format(mock_jingxuan_data, "7123456789")

        assert result["aweme_id"] == "7123456789"
        assert result["title"] == "测试视频"
        assert result["author_name"] == "测试用户"
        assert result["duration"] == 20000
        assert result["like_count"] == 200
        assert result["comment_count"] == 100
        assert len(result["video_urls"]) == 2  # H.264 优先
        assert "h264.mp4" in str(result["video_urls"])

    async def test_extract_jingxuan_metadata(self, mapper, mock_jingxuan_data):
        """测试精选页面元数据提取"""
        metadata = mapper._extract_jingxuan_metadata(mock_jingxuan_data)

        assert "bit_rate_info" in metadata
        assert len(metadata["bit_rate_info"]) == 2


class TestRPCDataMapper:
    """测试 RPCDataMapper RPC API数据映射器"""

    @pytest.fixture
    def mapper(self):
        return RPCDataMapper()

    @pytest.fixture
    def mock_rpc_data(self):
        """模拟RPC API数据"""
        return {
            "aweme_detail": {
                "aweme_id": "7123456789",
                "desc": "RPC测试视频",
                "create_time": 1640995200,
                "author": {"nickname": "RPC用户", "unique_id": "rpc_user"},
                "video": {
                    "play_addr": {"url_list": ["https://api.example.com/video.mp4"]},
                    "cover": {"url_list": ["https://api.example.com/cover.jpg"]},
                    "duration": 30000,
                },
                "statistics": {"digg_count": 300, "comment_count": 150, "share_count": 75},
                "risk_infos": {"content": "风险信息"},
                "group_id_str": "group123",
            }
        }

    async def test_validate_raw_data_success(self, mapper, mock_rpc_data):
        """测试数据验证成功"""
        assert await mapper.validate_raw_data(mock_rpc_data) is True

    async def test_validate_raw_data_failure(self, mapper):
        """测试数据验证失败"""
        invalid_data = {"invalid": "data"}
        assert await mapper.validate_raw_data(invalid_data) is False

    async def test_map_to_standard_format(self, mapper, mock_rpc_data):
        """测试数据映射"""
        result = await mapper.map_to_standard_format(mock_rpc_data, "7123456789")

        assert result["aweme_id"] == "7123456789"
        assert result["title"] == "RPC测试视频"
        assert result["author_name"] == "RPC用户"
        assert result["author_id"] == "rpc_user"
        assert result["duration"] == 30000
        assert result["like_count"] == 300
        assert result["comment_count"] == 150
        assert result["share_count"] == 75

    async def test_extract_rpc_metadata(self, mapper, mock_rpc_data):
        """测试RPC元数据提取"""
        metadata = mapper._extract_rpc_metadata(mock_rpc_data["aweme_detail"])

        assert "risk_infos" in metadata
        assert "group_id_str" in metadata
        assert metadata["risk_infos"]["content"] == "风险信息"
        assert metadata["group_id_str"] == "group123"


class TestVideoFetcherController:
    """测试 VideoFetcherController 统一控制器"""

    @pytest.fixture
    def controller(self):
        return VideoFetcherController()

    @pytest.fixture
    def mock_config(self):
        from models.douyin import DouyinFetchMethod
        return FetcherConfig(
            fallback_methods=[DouyinFetchMethod.MOBILE.value, DouyinFetchMethod.JINGXUAN.value],
            enable_fallback=True,
            retry_attempts=2,
            timeout=30,
        )

    @pytest.fixture
    def mock_successful_data(self):
        return {"aweme_id": "123", "title": "测试视频"}

    @patch("mapper.douyin.video_fetcher_controller.VideoFetcherController._fetch_mobile_data")
    async def test_fetch_video_data_success(self, mock_fetch_mobile, controller, mock_config, mock_successful_data):
        """测试单个视频数据获取成功"""
        # 模拟移动端获取成功
        mock_fetch_mobile.return_value = mock_successful_data

        result = await controller.fetch_video_data("123", mock_config)

        assert result.success is True
        assert result.aweme_id == "123"
        assert result.data == mock_successful_data
        assert result.method_used == "mobile"
        assert result.source == "mobile_share"

    @patch("mapper.douyin.video_fetcher_controller.VideoFetcherController._fetch_mobile_data")
    @patch("mapper.douyin.video_fetcher_controller.VideoFetcherController._fetch_jingxuan_data")
    async def test_fetch_video_data_fallback(
        self, mock_fetch_jingxuan, mock_fetch_mobile, controller, mock_config, mock_successful_data
    ):
        """测试回退机制"""
        # 模拟移动端失败，精选页面成功
        mock_fetch_mobile.side_effect = NetworkException("移动端网络错误")
        mock_fetch_jingxuan.return_value = mock_successful_data

        result = await controller.fetch_video_data("123", mock_config)

        assert result.success is True
        assert result.aweme_id == "123"
        assert result.data == mock_successful_data
        assert result.method_used == "jingxuan"
        assert result.source == "jingxuan_page"

    @patch("mapper.douyin.video_fetcher_controller.VideoFetcherController._fetch_mobile_data")
    @patch("mapper.douyin.video_fetcher_controller.VideoFetcherController._fetch_jingxuan_data")
    async def test_fetch_video_data_all_fail(self, mock_fetch_jingxuan, mock_fetch_mobile, controller, mock_config):
        """测试所有方法都失败"""
        # 模拟所有方法都失败
        mock_fetch_mobile.side_effect = NetworkException("移动端网络错误")
        mock_fetch_jingxuan.side_effect = ParseException("精选页面解析错误")

        result = await controller.fetch_video_data("123", mock_config)

        assert result.success is False
        assert result.aweme_id == "123"
        assert len(result.errors) > 0

    async def test_batch_fetch(self, controller):
        """测试批量获取"""
        aweme_ids = ["123", "456", "789"]
        config = FetcherConfig(preferred_methods=["mobile"])

        with patch.object(controller, "fetch_video_data") as mock_fetch:
            # 模拟批量获取结果
            mock_results = [
                FetchResult(success=True, aweme_id="123", data={"id": "123"}),
                FetchResult(success=False, aweme_id="456", errors=[NetworkException("网络错误")]),
                FetchResult(success=True, aweme_id="789", data={"id": "789"}),
            ]
            mock_fetch.side_effect = mock_results

            batch_result = await controller.batch_fetch(aweme_ids, config)

            assert batch_result.total_count == 3
            assert batch_result.success_count == 2
            assert batch_result.failure_count == 1
            assert len(batch_result.results) == 3

    async def test_fetch_with_fallback(self, controller, mock_config):
        """测试带回退的获取"""
        with (
            patch.object(controller, "_fetch_mobile_data") as mock_mobile,
            patch.object(controller, "_fetch_jingxuan_data") as mock_jingxuan,
        ):

            # 模拟移动端失败，精选页面成功
            mock_mobile.side_effect = NetworkException("网络错误")
            mock_jingxuan.return_value = {"aweme_id": "123", "title": "测试"}

            result = await controller.fetch_with_fallback("123", mock_config)

            assert result.success is True
            assert result.method_used == "jingxuan"
