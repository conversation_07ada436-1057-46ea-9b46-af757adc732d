"""
基础映射器测试 - 测试核心功能是否正常工作
"""

from controllers.douyin import VideoFetcherController
from mappers.douyin import (
    BatchFetchResult,
    FetcherConfig,
    FetchResult,
    JingxuanDataMapper,
    MobileDataMapper,
    RPCDataMapper,
)


class TestBasicFunctionality:
    """测试基础功能"""

    def test_fetcher_config_creation(self):
        """测试配置创建"""
        config = FetcherConfig()
        assert config is not None
        assert config.retry_attempts == 3
        assert config.timeout == 30
        assert config.use_proxy is True

    def test_fetcher_config_validation(self):
        """测试配置验证"""
        config = FetcherConfig()
        assert config.validate() is True

        # 测试无效配置
        invalid_config = FetcherConfig(retry_attempts=-1)
        assert invalid_config.validate() is False

    def test_fetch_result_creation(self):
        """测试结果创建"""
        result = FetchResult(success=True, aweme_id="123")
        assert result.success is True
        assert result.aweme_id == "123"
        assert result.data is None

    def test_batch_result_creation(self):
        """测试批量结果创建"""
        batch_result = BatchFetchResult(total_count=3)
        assert batch_result.total_count == 3
        assert batch_result.success_count == 0
        assert batch_result.failed_count == 0

    def test_video_fetcher_controller_creation(self):
        """测试视频获取控制器创建"""
        controller = VideoFetcherController()
        assert controller is not None

    def test_mappers_creation(self):
        """测试映射器创建"""
        mobile_mapper = MobileDataMapper()
        jingxuan_mapper = JingxuanDataMapper()
        rpc_mapper = RPCDataMapper()

        assert mobile_mapper is not None
        assert jingxuan_mapper is not None
        assert rpc_mapper is not None

    def test_mobile_mapper_validation(self):
        """测试移动端映射器数据验证"""
        mapper = MobileDataMapper()

        # 测试有效数据 - 移动端数据需要aweme_id字段在根级别
        valid_data = {"aweme_id": "123", "desc": "测试"}
        assert mapper.validate_raw_data(valid_data) is True

        # 测试无效数据
        invalid_data = {"invalid": "data"}
        assert mapper.validate_raw_data(invalid_data) is False

    def test_jingxuan_mapper_validation(self):
        """测试精选页面映射器数据验证"""
        mapper = JingxuanDataMapper()

        # 测试有效数据
        valid_data = {"aweme_id": "123", "desc": "测试"}
        assert mapper.validate_raw_data(valid_data) is True

        # 测试无效数据
        invalid_data = {"invalid": "data"}
        assert mapper.validate_raw_data(invalid_data) is False

    def test_rpc_mapper_validation(self):
        """测试RPC映射器数据验证"""
        mapper = RPCDataMapper()

        # 测试有效数据
        valid_data = {"aweme_detail": {"aweme_id": "123", "desc": "测试"}}
        assert mapper.validate_raw_data(valid_data) is True

        # 测试无效数据
        invalid_data = {"invalid": "data"}
        assert mapper.validate_raw_data(invalid_data) is False


class TestIntegrationBasic:
    """测试基础集成功能"""

    def test_import_douyin_controller(self):
        """测试导入控制器"""
        from controllers.douyin import DouyinController  # 这是向后兼容的别名
        from controllers.douyin.video.rpc_video_controller import DouyinRPCVideoController

        controller = DouyinController()  # 实际上是DouyinRPCVideoController
        rpc_controller = DouyinRPCVideoController()
        assert controller is not None
        assert rpc_controller is not None



    def test_controller_has_unified_methods(self):
        """测试控制器具有统一方法"""
        from controllers.douyin.video.video_fetcher import VideoFetcherController

        controller = VideoFetcherController()

        # 检查视频获取控制器的方法
        assert hasattr(controller, "fetch_video_data_auto")
        assert hasattr(controller, "fetch_with_fallback")

    def test_config_method_priority(self):
        """测试配置方法优先级"""
        config = FetcherConfig()

        # 测试默认方法优先级
        assert config.get_method_priority("jingxuan") == 0
        assert config.get_method_priority("mobile") == 1
        assert config.get_method_priority("rpc") == 2
        assert config.get_method_priority("invalid") == 999
