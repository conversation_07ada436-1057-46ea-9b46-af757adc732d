"""
DouyinAwemeService 单元测试

测试服务层的数据库操作逻辑
"""

from datetime import datetime
from typing import List
from unittest.mock import AsyncMock, patch

import pytest

from services.trendinsight import DouyinAwemeService
from mappers.douyin.pydantic_models import DouyinVideoData, DouyinVideoDataItem
from models.douyin.models import DouyinAweme


@pytest.fixture
def sample_video_data() -> DouyinVideoData:
    """创建测试用的视频数据"""
    return DouyinVideoData(
        root=[
            DouyinVideoDataItem(
                aweme_id="test_001",
                aweme_type="video",
                title="测试标题1",
                desc="测试描述1",
                create_time=datetime.fromtimestamp(1642771200),
                user_id="user_001",
                sec_uid="sec_user_001",
                short_user_id="short_user_001",
                user_unique_id="unique_user_001",
                nickname="user_nickname_001",
                avatar="avatar_url_001",
                user_signature="signature_001",
                ip_location="ip_location_001",
                liked_count="100",
                comment_count="10",
                share_count="5",
                collected_count="1",
                aweme_url="https://www.douyin.com/video/1",
                cover_url="https://example.com/cover1.jpg",
                video_download_url="https://example.com/video1.mp4",
                source_keyword="测试关键词",
            ),
            DouyinVideoDataItem(
                aweme_id="test_002",
                aweme_type="video",
                title="测试标题2",
                desc="测试描述2",
                create_time=datetime.fromtimestamp(1642771300),
                user_id="user_002",
                sec_uid="sec_user_002",
                short_user_id="short_user_002",
                user_unique_id="unique_user_002",
                nickname="user_nickname_002",
                avatar="avatar_url_002",
                user_signature="signature_002",
                ip_location="ip_location_002",
                liked_count="200",
                comment_count="20",
                share_count="15",
                collected_count="2",
                aweme_url="https://www.douyin.com/video/2",
                cover_url="https://example.com/cover2.jpg",
                video_download_url="https://example.com/video2.mp4",
                source_keyword="测试关键词",
            ),
        ]
    )


@pytest.fixture
def sample_video_ids(sample_video_data: DouyinVideoData) -> List[str]:
    """创建测试用的视频ID列表"""
    return [video.aweme_id for video in sample_video_data.root]


@pytest.mark.asyncio
class TestDouyinAwemeService:
    """DouyinAwemeService 测试类"""

    @patch("services.trendinsight.douyin_aweme_service.DouyinAweme.bulk_create", new_callable=AsyncMock)
    @patch("services.trendinsight.douyin_aweme_service.DouyinAweme.filter", new_callable=AsyncMock)
    async def test_ensure_douyin_aweme_records_all_new(
        self, mock_filter, mock_bulk_create, sample_video_data, sample_video_ids
    ):
        """测试所有记录都是新记录的情况"""
        mock_filter.return_value = []

        created, existing = await DouyinAwemeService.ensure_douyin_aweme_records(
            video_data_list=sample_video_data, video_ids=sample_video_ids
        )

        assert created == 2
        assert existing == 0
        mock_filter.assert_called_once_with(aweme_id__in=sample_video_ids)
        mock_bulk_create.assert_called_once()
        args, kwargs = mock_bulk_create.call_args
        assert len(args[0]) == 2

    @patch("services.trendinsight.douyin_aweme_service.DouyinAweme.bulk_update", new_callable=AsyncMock)
    @patch("services.trendinsight.douyin_aweme_service.DouyinAweme.filter", new_callable=AsyncMock)
    async def test_ensure_douyin_aweme_records_all_existing(
        self, mock_filter, mock_bulk_update, sample_video_data, sample_video_ids
    ):
        """测试所有记录都已存在的情况"""
        mock_existing_record1 = DouyinAweme(
            aweme_id="test_001",
            liked_count=50,  # 不同的值，需要更新
            comment_count=50,
            share_count=25,
            collected_count=10,
            title="旧标题",
            desc="旧描述",
            source_keyword="旧关键词",
        )
        mock_existing_record2 = DouyinAweme(
            aweme_id="test_002",
            liked_count=200,
            comment_count=100,
            share_count=50,
            collected_count=20,
            title="测试描述2",
            desc="测试描述2",
            source_keyword="测试关键词",
        )
        mock_filter.return_value = [mock_existing_record1, mock_existing_record2]

        created, existing = await DouyinAwemeService.ensure_douyin_aweme_records(
            video_data_list=sample_video_data, video_ids=sample_video_ids
        )

        assert created == 0
        assert existing == 2
        mock_filter.assert_called_once_with(aweme_id__in=sample_video_ids)
        mock_bulk_update.assert_called_once()
        args, kwargs = mock_bulk_update.call_args
        assert len(args[0]) == 2  # 两条记录都需要更新
        assert kwargs["fields"] == [
            "liked_count",
            "comment_count",
            "share_count",
            "collected_count",
            "title",
            "desc",
            "source_keyword",
        ]

    @patch("services.trendinsight.douyin_aweme_service.DouyinAweme.bulk_update", new_callable=AsyncMock)
    @patch("services.trendinsight.douyin_aweme_service.DouyinAweme.bulk_create", new_callable=AsyncMock)
    @patch("services.trendinsight.douyin_aweme_service.DouyinAweme.filter", new_callable=AsyncMock)
    async def test_ensure_douyin_aweme_records_mixed(
        self, mock_filter, mock_bulk_create, mock_bulk_update, sample_video_data, sample_video_ids
    ):
        """测试部分新记录部分已存在记录的情况"""
        mock_existing_record = DouyinAweme(
            aweme_id="test_001",
            liked_count=50,
            comment_count=50,
            share_count=25,
            collected_count=10,
            title="旧标题",
            desc="旧描述",
            source_keyword="旧关键词",
        )
        mock_filter.return_value = [mock_existing_record]

        created, existing = await DouyinAwemeService.ensure_douyin_aweme_records(
            video_data_list=sample_video_data, video_ids=sample_video_ids
        )

        assert created == 1
        assert existing == 1
        mock_filter.assert_called_once_with(aweme_id__in=sample_video_ids)
        mock_bulk_create.assert_called_once()
        mock_bulk_update.assert_called_once()
        create_args, _ = mock_bulk_create.call_args
        assert len(create_args[0]) == 1
        update_args, _ = mock_bulk_update.call_args
        assert len(update_args[0]) == 1

    async def test_ensure_douyin_aweme_records_empty_input(self):
        """测试空输入的情况"""
        created, existing = await DouyinAwemeService.ensure_douyin_aweme_records(
            video_data_list=DouyinVideoData(root=[]), video_ids=[]
        )
        assert created == 0
        assert existing == 0

    @patch("services.trendinsight.douyin_aweme_service.DouyinAweme.filter", new_callable=AsyncMock)
    async def test_ensure_douyin_aweme_records_database_error(self, mock_filter, sample_video_data, sample_video_ids):
        """测试数据库错误的情况"""
        mock_filter.side_effect = Exception("数据库连接失败")

        # 移除 try-except 块后，我们期望服务直接抛出异常
        with pytest.raises(Exception, match="数据库连接失败"):
            await DouyinAwemeService.ensure_douyin_aweme_records(
                video_data_list=sample_video_data, video_ids=sample_video_ids
            )


@pytest.mark.asyncio
class TestServiceIntegration:
    """服务层集成测试"""

    @patch("services.trendinsight.douyin_aweme_service.DouyinAweme.bulk_create", new_callable=AsyncMock)
    @patch("services.trendinsight.douyin_aweme_service.DouyinAweme.filter", new_callable=AsyncMock)
    async def test_service_data_transformation_integration(self, mock_filter, mock_bulk_create):
        """测试服务层与数据转换的集成"""
        mock_videos = [
            DouyinVideoDataItem(
                aweme_id="integration_001",
                aweme_type="video",
                title="title1",
                desc="desc1",
                create_time=datetime.fromtimestamp(1234567890),
                user_id="user1",
                sec_uid="sec_user1",
                short_user_id="short_user1",
                user_unique_id="unique_user1",
                nickname="user_nickname1",
                avatar="avatar_url1",
                user_signature="signature1",
                ip_location="ip_location1",
                liked_count="1000",
                comment_count="11",
                share_count="50",
                collected_count="10",
                aweme_url="https://www.douyin.com/video/101",
                cover_url="https://example.com/cover101.jpg",
                video_download_url="https://example.com/video101.mp4",
                source_keyword="集成测试",
            ),
            DouyinVideoDataItem(
                aweme_id="integration_002",
                aweme_type="video",
                title="title2",
                desc="desc2",
                create_time=datetime.fromtimestamp(1234567891),
                user_id="user2",
                sec_uid="sec_user2",
                short_user_id="short_user2",
                user_unique_id="unique_user2",
                nickname="user_nickname2",
                avatar="avatar_url2",
                user_signature="signature2",
                ip_location="ip_location2",
                liked_count="2000",
                comment_count="21",
                share_count="60",
                collected_count="20",
                aweme_url="https://www.douyin.com/video/102",
                cover_url="https://example.com/cover102.jpg",
                video_download_url="https://example.com/video102.mp4",
                source_keyword="集成测试",
            ),
        ]
        video_data_list = DouyinVideoData(root=mock_videos)
        video_ids = [v.aweme_id for v in video_data_list.root]

        mock_filter.return_value = []

        created, existing = await DouyinAwemeService.ensure_douyin_aweme_records(
            video_data_list=video_data_list, video_ids=video_ids
        )

        assert created == 2
        assert existing == 0
        mock_filter.assert_called_once_with(aweme_id__in=video_ids)
        mock_bulk_create.assert_called_once()
