"""
VideoUrlParseController 测试模块

测试抖音视频URL解析控制器的各种功能
"""

from unittest.mock import Mock, patch

import pytest

from controllers.douyin.video.video_url_parse import VideoUrlParseController


class TestVideoUrlParseController:
    """VideoUrlParseController 测试类"""

    @pytest.fixture
    def controller(self):
        """创建控制器实例"""
        return VideoUrlParseController(timeout=10)

    @pytest.mark.asyncio
    async def test_parse_jingxuan_url(self, controller):
        """测试解析精选页面URL"""
        url = "https://www.douyin.com/jingxuan?modal_id=7425961429783579941"

        result = await controller.parse_video_url(url, resolve_redirects=False)

        assert result["success"] is True
        assert result["aweme_id"] == "7425961429783579941"
        assert result["url_type"] == "jingxuan"
        assert result["original_url"] == url
        assert result["resolved_url"] is None
        assert result["error"] is None

    @pytest.mark.asyncio
    async def test_parse_pc_video_url(self, controller):
        """测试解析PC视频URL"""
        url = "https://www.douyin.com/video/7485337020965948711"

        result = await controller.parse_video_url(url, resolve_redirects=False)

        assert result["success"] is True
        assert result["aweme_id"] == "7485337020965948711"
        assert result["url_type"] == "pc_video"
        assert result["original_url"] == url
        assert result["resolved_url"] is None
        assert result["error"] is None

    @pytest.mark.asyncio
    async def test_parse_mobile_share_url(self, controller):
        """测试解析移动端分享URL"""
        url = "https://m.douyin.com/share/video/7485337020965948711"

        result = await controller.parse_video_url(url, resolve_redirects=False)

        assert result["success"] is True
        assert result["aweme_id"] == "7485337020965948711"
        assert result["url_type"] == "mobile_share"
        assert result["original_url"] == url
        assert result["resolved_url"] is None
        assert result["error"] is None

    @pytest.mark.asyncio
    async def test_parse_short_url_with_redirect(self, controller):
        """测试解析短链接URL（模拟重定向）"""
        short_url = "https://v.douyin.com/pSKg1x5CLYc/"
        full_url = "https://www.douyin.com/video/7485337020965948711"

        # 模拟短链接重定向
        with patch.object(controller.url_processor, "convert_short_url_to_full", return_value=full_url):
            result = await controller.parse_video_url(short_url, resolve_redirects=True)

            assert result["success"] is True
            assert result["aweme_id"] == "7485337020965948711"
            assert result["url_type"] == "pc_video"  # 重定向后的类型
            assert result["original_url"] == short_url
            assert result["resolved_url"] == full_url
            assert result["error"] is None

    @pytest.mark.asyncio
    async def test_parse_invalid_url(self, controller):
        """测试解析无效URL"""
        invalid_url = "https://invalid-url.com/video/123"

        with pytest.raises(Exception):  # 应该抛出HTTPException
            await controller.parse_video_url(invalid_url)

    @pytest.mark.asyncio
    async def test_parse_empty_url(self, controller):
        """测试解析空URL"""
        with pytest.raises(Exception):  # 应该抛出HTTPException
            await controller.parse_video_url("")

    @pytest.mark.asyncio
    async def test_parse_none_url(self, controller):
        """测试解析None URL"""
        with pytest.raises(Exception):  # 应该抛出HTTPException
            await controller.parse_video_url(None)

    @pytest.mark.asyncio
    async def test_batch_parse_urls_success(self, controller):
        """测试批量解析URL - 全部成功"""
        urls = [
            "https://www.douyin.com/jingxuan?modal_id=7425961429783579941",
            "https://www.douyin.com/video/7485337020965948711",
            "https://m.douyin.com/share/video/7123456789012345678",
        ]

        result = await controller.batch_parse_urls(urls, resolve_redirects=False)

        assert result["success"] is True
        assert result["total_count"] == 3
        assert result["success_count"] == 3
        assert result["error_count"] == 0
        assert len(result["results"]) == 3
        assert len(result["errors"]) == 0

        # 验证每个结果
        assert result["results"][0]["aweme_id"] == "7425961429783579941"
        assert result["results"][1]["aweme_id"] == "7485337020965948711"
        assert result["results"][2]["aweme_id"] == "7123456789012345678"

    @pytest.mark.asyncio
    async def test_batch_parse_urls_mixed_results(self, controller):
        """测试批量解析URL - 混合结果"""
        urls = [
            "https://www.douyin.com/jingxuan?modal_id=7425961429783579941",  # 有效
            "https://invalid-url.com/video/123",  # 无效
            "https://www.douyin.com/video/7485337020965948711",  # 有效
        ]

        result = await controller.batch_parse_urls(urls, continue_on_error=True)

        assert result["success"] is True  # 有成功的
        assert result["total_count"] == 3
        assert result["success_count"] == 2
        assert result["error_count"] == 1
        assert len(result["errors"]) == 1
        assert result["errors"][0]["index"] == 1
        assert result["errors"][0]["url"] == "https://invalid-url.com/video/123"

    @pytest.mark.asyncio
    async def test_batch_parse_empty_list(self, controller):
        """测试批量解析空列表"""
        with pytest.raises(Exception):  # 应该抛出HTTPException
            await controller.batch_parse_urls([])

    @pytest.mark.asyncio
    async def test_batch_parse_none_list(self, controller):
        """测试批量解析None列表"""
        with pytest.raises(Exception):  # 应该抛出HTTPException
            await controller.batch_parse_urls(None)

    @pytest.mark.asyncio
    async def test_validate_url_valid_jingxuan(self, controller):
        """测试验证有效的精选URL"""
        url = "https://www.douyin.com/jingxuan?modal_id=7425961429783579941"

        result = await controller.validate_url(url)

        assert result["is_valid"] is True
        assert result["url_type"] == "jingxuan"
        assert result["is_douyin_url"] is True
        assert result["can_extract_aweme_id"] is True
        assert len(result["errors"]) == 0

    @pytest.mark.asyncio
    async def test_validate_url_valid_pc_video(self, controller):
        """测试验证有效的PC视频URL"""
        url = "https://www.douyin.com/video/7485337020965948711"

        result = await controller.validate_url(url)

        assert result["is_valid"] is True
        assert result["url_type"] == "pc_video"
        assert result["is_douyin_url"] is True
        assert result["can_extract_aweme_id"] is True
        assert len(result["errors"]) == 0

    @pytest.mark.asyncio
    async def test_validate_url_invalid_domain(self, controller):
        """测试验证无效域名的URL"""
        url = "https://invalid-domain.com/video/123"

        result = await controller.validate_url(url)

        assert result["is_valid"] is False
        assert result["is_douyin_url"] is False
        assert "不是抖音URL" in result["errors"]

    @pytest.mark.asyncio
    async def test_validate_url_malformed(self, controller):
        """测试验证格式错误的URL"""
        url = "not-a-url"

        result = await controller.validate_url(url)

        assert result["is_valid"] is False
        assert "URL格式无效" in str(result["errors"])

    @pytest.mark.asyncio
    async def test_validate_url_empty(self, controller):
        """测试验证空URL"""
        result = await controller.validate_url("")

        assert result["is_valid"] is False
        assert "URL不能为空" in result["errors"]

    @pytest.mark.asyncio
    async def test_validate_url_none(self, controller):
        """测试验证None URL"""
        result = await controller.validate_url(None)

        assert result["is_valid"] is False
        assert "URL不能为空且必须为字符串" in result["errors"]

    def test_detect_url_type_jingxuan(self, controller):
        """测试检测精选URL类型"""
        url = "https://www.douyin.com/jingxuan?modal_id=7425961429783579941"
        url_type = controller._detect_url_type(url)

        assert url_type == "jingxuan"

    def test_detect_url_type_pc_video(self, controller):
        """测试检测PC视频URL类型"""
        url = "https://www.douyin.com/video/7485337020965948711"
        url_type = controller._detect_url_type(url)

        assert url_type == "pc_video"

    def test_detect_url_type_short(self, controller):
        """测试检测短链接URL类型"""
        url = "https://v.douyin.com/pSKg1x5CLYc/"
        url_type = controller._detect_url_type(url)

        assert url_type == "short"

    def test_extract_aweme_id_jingxuan(self, controller):
        """测试从精选URL提取aweme_id"""
        url = "https://www.douyin.com/jingxuan?modal_id=7425961429783579941"
        aweme_id = controller._extract_aweme_id(url)

        assert aweme_id == "7425961429783579941"

    def test_extract_aweme_id_pc_video(self, controller):
        """测试从PC视频URL提取aweme_id"""
        url = "https://www.douyin.com/video/7485337020965948711"
        aweme_id = controller._extract_aweme_id(url)

        assert aweme_id == "7485337020965948711"

    def test_extract_aweme_id_mobile_share(self, controller):
        """测试从移动端分享URL提取aweme_id"""
        url = "https://m.douyin.com/share/video/7485337020965948711"
        aweme_id = controller._extract_aweme_id(url)

        assert aweme_id == "7485337020965948711"

    def test_validate_aweme_id_valid(self, controller):
        """测试验证有效的aweme_id"""
        valid_id = "7425961429783579941"
        is_valid = controller._validate_aweme_id(valid_id)

        assert is_valid is True

    def test_validate_aweme_id_invalid(self, controller):
        """测试验证无效的aweme_id"""
        invalid_id = "invalid_id"
        is_valid = controller._validate_aweme_id(invalid_id)

        assert is_valid is False


if __name__ == "__main__":
    # 简单的手动测试
    import asyncio

    async def manual_test():
        controller = VideoUrlParseController()

        # 测试URL
        test_urls = [
            "https://www.douyin.com/jingxuan?modal_id=7425961429783579941",
            "https://www.douyin.com/video/7485337020965948711",
            "https://m.douyin.com/share/video/7123456789012345678",
        ]

        print("=== 单个URL解析测试 ===")
        for url in test_urls:
            try:
                result = await controller.parse_video_url(url, resolve_redirects=False)
                print(f"✅ {url}")
                print(f"   aweme_id: {result['aweme_id']}")
                print(f"   url_type: {result['url_type']}")
                print()
            except Exception as e:
                print(f"❌ {url}: {str(e)}")

        print("=== 批量URL解析测试 ===")
        try:
            batch_result = await controller.batch_parse_urls(test_urls)
            print(f"✅ 批量解析: 成功={batch_result['success_count']}, 失败={batch_result['error_count']}")
        except Exception as e:
            print(f"❌ 批量解析失败: {str(e)}")

        print("=== URL验证测试 ===")
        for url in test_urls:
            try:
                validation = await controller.validate_url(url)
                print(f"{'✅' if validation['is_valid'] else '❌'} {url}: {validation['url_type']}")
            except Exception as e:
                print(f"❌ {url}: {str(e)}")

    # 运行手动测试
    asyncio.run(manual_test())
