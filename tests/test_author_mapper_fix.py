"""
测试 TrendInsightAuthorMapper 修复
验证 author_update_data_to_model_data 方法是否正确包含 user_id 字段
"""

import pytest
from mappers.trendinsight.author_mapper import TrendInsightAuthorMapper
from schemas.trendinsight import AuthorUpdateData


class TestAuthorMapperFix:
    """测试作者映射器修复"""

    def test_author_update_data_to_model_data_includes_user_id(self):
        """测试 author_update_data_to_model_data 方法是否包含 user_id 字段"""
        # 创建测试数据
        test_user_id = "ebgefebdijeiacad"
        author_data = AuthorUpdateData(
            user_id=test_user_id,
            user_name="测试用户",
            user_head_logo="https://example.com/avatar.jpg",
            user_gender="男",
            user_location="北京",
            user_introduction="这是一个测试用户",
            item_count="100",
            fans_count="1000",
            like_count="10000",
            first_tag_name="科技",
            second_tag_name="数码",
            aweme_id="test_aweme_id",
            user_aweme_url="https://www.douyin.com/user/test_user",
            aweme_pic="https://example.com/aweme_pic.jpg",
            platform="trendinsight",
            source_keyword="test_keyword",
            raw_data='{"test": "data"}'
        )

        # 调用映射方法
        result = TrendInsightAuthorMapper.author_update_data_to_model_data(author_data)

        # 验证结果包含 user_id 字段
        assert "user_id" in result, "映射结果应该包含 user_id 字段"
        assert result["user_id"] == test_user_id, f"user_id 应该是 {test_user_id}"

        # 验证其他必要字段也存在
        assert result["user_name"] == "测试用户"
        assert result["user_head_logo"] == "https://example.com/avatar.jpg"
        assert result["item_count"] == "100"
        assert result["fans_count"] == "1000"
        assert result["like_count"] == "10000"
        assert result["item_count_int"] == 100
        assert result["fans_count_int"] == 1000
        assert result["like_count_int"] == 10000
        assert "crawl_time" in result
        assert isinstance(result["crawl_time"], int)

    def test_author_update_data_to_model_data_with_empty_user_id(self):
        """测试当 user_id 为空时的情况"""
        author_data = AuthorUpdateData(
            user_id="",  # 空的 user_id
            user_name="测试用户",
            item_count="0",
            fans_count="0",
            like_count="0"
        )

        result = TrendInsightAuthorMapper.author_update_data_to_model_data(author_data)

        # 验证即使 user_id 为空，字段也应该存在
        assert "user_id" in result, "映射结果应该包含 user_id 字段，即使它为空"
        assert result["user_id"] == "", "空的 user_id 应该被保留"

    def test_author_update_data_to_model_data_with_special_characters(self):
        """测试包含特殊字符的 user_id"""
        special_user_id = "MS4wLjABAAAA-test_123"
        author_data = AuthorUpdateData(
            user_id=special_user_id,
            user_name="特殊字符用户",
            item_count="50",
            fans_count="500",
            like_count="5000"
        )

        result = TrendInsightAuthorMapper.author_update_data_to_model_data(author_data)

        assert result["user_id"] == special_user_id, "特殊字符的 user_id 应该被正确保留"


if __name__ == "__main__":
    # 运行测试
    test_instance = TestAuthorMapperFix()
    test_instance.test_author_update_data_to_model_data_includes_user_id()
    test_instance.test_author_update_data_to_model_data_with_empty_user_id()
    test_instance.test_author_update_data_to_model_data_with_special_characters()
    print("所有测试通过！")
