"""
抖音平台 Tortoise ORM 模型
"""

import asyncio
import logging
from typing import List, Dict, Any

from tortoise import fields

from models.base import BaseModel

logger = logging.getLogger(__name__)


class DouyinAweme(BaseModel):
    """抖音视频模型"""

    # 用户信息
    user_id = fields.CharField(max_length=64, null=True, description="用户ID")
    sec_uid = fields.CharField(max_length=128, null=True, description="用户sec_uid")
    short_user_id = fields.CharField(max_length=64, null=True, description="用户短ID")
    user_unique_id = fields.CharField(max_length=64, null=True, description="用户唯一ID")
    nickname = fields.Char<PERSON>ield(max_length=64, null=True, description="用户昵称")
    avatar = fields.CharField(max_length=512, null=True, description="用户头像地址")
    user_signature = fields.CharField(max_length=500, null=True, description="用户签名")
    ip_location = fields.Char<PERSON>ield(max_length=255, null=True, description="评论时的IP地址")

    # 视频信息
    aweme_id = fields.CharField(max_length=64, unique=True, description="视频ID")
    aweme_type = fields.CharField(max_length=16, description="视频类型")
    title = fields.CharField(max_length=1024, null=True, description="视频标题")
    desc = fields.TextField(null=True, description="视频描述")
    create_time = fields.DatetimeField(description="视频发布时间")

    # 统计信息
    liked_count = fields.CharField(max_length=16, null=True, description="视频点赞数")
    comment_count = fields.CharField(max_length=16, null=True, description="视频评论数")
    share_count = fields.CharField(max_length=16, null=True, description="视频分享数")
    collected_count = fields.CharField(max_length=16, null=True, description="视频收藏数")

    # 媒体信息
    aweme_url = fields.CharField(max_length=500, null=True, description="视频详情页URL")
    cover_url = fields.CharField(max_length=500, null=True, description="视频封面图URL")
    video_download_url = fields.CharField(max_length=1024, null=True, description="视频下载地址")

    # 搜索来源
    source_keyword = fields.CharField(max_length=255, default="", description="搜索来源关键字")

    class Meta:
        table = "douyin_aweme"
        indexes = [
            ("aweme_id",),
            ("create_time",),
            ("user_id",),
            ("sec_uid",),
        ]

    class PydanticMeta:
        computed = ["id"]
        backward_relations = False

    def __str__(self) -> str:
        return f"DouyinAweme({self.aweme_id}, {self.title})"


class DouyinAwemeComment(BaseModel):
    """抖音视频评论模型"""

    # 用户信息
    user_id = fields.CharField(max_length=64, null=True, description="用户ID")
    sec_uid = fields.CharField(max_length=128, null=True, description="用户sec_uid")
    short_user_id = fields.CharField(max_length=64, null=True, description="用户短ID")
    user_unique_id = fields.CharField(max_length=64, null=True, description="用户唯一ID")
    nickname = fields.CharField(max_length=64, null=True, description="用户昵称")
    avatar = fields.CharField(max_length=255, null=True, description="用户头像地址")
    user_signature = fields.CharField(max_length=500, null=True, description="用户签名")
    ip_location = fields.CharField(max_length=255, null=True, description="评论时的IP地址")

    # 评论信息
    comment_id = fields.CharField(max_length=64, unique=True, description="评论ID")
    aweme_id = fields.CharField(max_length=64, description="视频ID")
    content = fields.TextField(null=True, description="评论内容")
    create_time = fields.DatetimeField(description="评论时间")
    sub_comment_count = fields.CharField(max_length=16, description="评论回复数")

    # 扩展字段
    parent_comment_id = fields.CharField(max_length=64, null=True, description="父评论ID")
    like_count = fields.CharField(max_length=255, default="0", description="点赞数")
    pictures = fields.CharField(max_length=500, default="", description="评论图片列表")
    reply_to_reply_id = fields.CharField(max_length=64, null=True, description="目标评论ID")

    class Meta:
        table = "douyin_aweme_comment"
        indexes = [
            ("comment_id",),
            ("aweme_id",),
            ("user_id",),
            ("create_time",),
        ]

    def __str__(self) -> str:
        return f"DouyinAwemeComment({self.comment_id}, {self.aweme_id})"

    async def to_dict(self, m2m: bool = False, exclude_fields: list[str] | None = None) -> dict:
        """转换为字典"""
        # 调用父类方法
        return await super().to_dict(m2m=m2m, exclude_fields=exclude_fields)  # type: ignore


class DouyinCreator(BaseModel):
    """抖音创作者模型"""

    # 用户基本信息
    user_id = fields.CharField(max_length=128, unique=True, description="用户ID")
    nickname = fields.CharField(max_length=64, null=True, description="用户昵称")
    avatar = fields.CharField(max_length=255, null=True, description="用户头像地址")
    ip_location = fields.CharField(max_length=255, null=True, description="评论时的IP地址")

    # 用户详细信息
    desc = fields.TextField(null=True, description="用户描述")
    gender = fields.CharField(max_length=2, null=True, description="性别")

    # 统计信息
    follows = fields.CharField(max_length=16, null=True, description="关注数")
    fans = fields.CharField(max_length=16, null=True, description="粉丝数")
    interaction = fields.CharField(max_length=16, null=True, description="获赞数")

    class Meta:
        table = "douyin_creator"
        indexes = [
            ("user_id",),
            ("nickname",),
        ]

    def __str__(self) -> str:
        return f"DouyinCreator({self.user_id}, {self.nickname})"


class DouyinCollect(BaseModel):
    """抖音收藏夹模型"""

    # 基本信息
    app_id = fields.IntField(description="应用ID")
    user_id = fields.BigIntField(description="用户ID")
    user_id_str = fields.CharField(max_length=255, description="用户ID字符串")

    # 收藏夹信息
    collects_id = fields.BigIntField(unique=True, description="收藏夹ID")
    collects_id_str = fields.CharField(max_length=255, description="收藏夹ID字符串")
    collects_name = fields.CharField(max_length=255, description="收藏夹名称")
    collects_cover = fields.CharField(max_length=1024, null=True, description="收藏夹封面")
    sicily_collects_cover = fields.CharField(max_length=1024, null=True, description="Sicily收藏夹封面列表")

    # 状态信息
    follow_status = fields.IntField(default=0, description="关注状态")
    is_normal_status = fields.BooleanField(default=True, description="是否正常状态")
    status = fields.IntField(default=0, description="状态")
    states = fields.IntField(default=0, description="状态值")

    # 统计信息
    followed_count = fields.IntField(default=0, description="关注数")
    play_count = fields.IntField(default=0, description="播放数")
    total_number = fields.IntField(default=0, description="总数量")

    # 类型信息
    item_type = fields.IntField(default=0, description="项目类型")
    system_type = fields.IntField(default=0, description="系统类型")

    # 时间信息
    create_time = fields.DatetimeField(description="创建时间")
    last_collect_time = fields.DatetimeField(description="最后收藏时间")

    class Meta:
        table = "douyin_collect"
        indexes = [
            ("collects_id",),
            ("user_id",),
            ("create_time",),
            ("last_collect_time",),
        ]

    def __str__(self) -> str:
        return f"DouyinCollect({self.collects_id}, {self.collects_name})"


class DouyinCollectVideo(BaseModel):
    """抖音收藏夹视频关联模型"""

    # 关联信息
    collects_id = fields.BigIntField(description="收藏夹ID")
    aweme_id = fields.CharField(max_length=64, description="视频ID")

    class Meta:
        table = "douyin_collect_video"
        indexes = [
            ("collects_id",),
            ("aweme_id",),
        ]
        unique_together = [("collects_id", "aweme_id")]  # 联合唯一索引

    def __str__(self) -> str:
        return f"DouyinCollectVideo({self.collects_id}, {self.aweme_id})"


# 数据操作函数


async def update_douyin_aweme(aweme_data: DouyinAweme) -> bool:
    """
    更新或创建抖音视频数据

    注意：此函数只负责数据库的创建/更新操作，不再处理数据转换。
    数据转换应该在控制器层完成，并创建 DouyinAweme 实例。

    Args:
        aweme_data: DouyinAweme 模型实例

    Returns:
        bool: 操作是否成功
    """
    try:
        aweme_id: str = aweme_data.aweme_id
        if not aweme_id:
            logger.error("update_douyin_aweme: aweme_id 不能为空")
            return False

        # 打印调试信息
        logger.debug("save_content_item: ", aweme_data)
        logger.info(f"[models.douyin.update_douyin_aweme] douyin aweme id:{aweme_id}, title:{aweme_data.title}")

        # 将模型实例转换为字典用于 update_or_create
        aweme_dict: Dict[str, Any] = {}
        for field_name in DouyinAweme._meta.fields:
            if hasattr(aweme_data, field_name):
                aweme_dict[field_name] = getattr(aweme_data, field_name)

        # 使用 update_or_create 方法直接保存已转换的数据
        _, created = await DouyinAweme.update_or_create(aweme_id=aweme_id, defaults=aweme_dict)

        action: str = "创建" if created else "更新"
        logger.info(f"update_douyin_aweme: {action}视频数据成功 - aweme_id: {aweme_id}")
        return True

    except Exception as e:
        logger.error(f"update_douyin_aweme: 操作失败 - {e}")
        return False


async def batch_update_douyin_aweme(aweme_list: List[DouyinAweme]) -> bool:
    """
    批量更新或创建抖音视频数据 - 使用高效的批量SQL操作

    注意：此函数只负责数据库的批量创建/更新操作，不再处理数据转换。
    数据转换应该在控制器层完成，并创建 DouyinAweme 实例列表。

    Args:
        aweme_list: DouyinAweme 模型实例列表

    Returns:
        bool: 操作是否成功
    """
    try:
        if not aweme_list:
            logger.warning("batch_update_douyin_aweme: aweme_list 为空")
            return True

        # 提取所有 aweme_id 用于批量查询
        aweme_ids: List[str] = [aweme.aweme_id for aweme in aweme_list if aweme.aweme_id]
        if not aweme_ids:
            logger.error("batch_update_douyin_aweme: 没有有效的 aweme_id")
            return False

        # 批量查询已存在的记录
        existing_records: List[DouyinAweme] = await DouyinAweme.filter(aweme_id__in=aweme_ids).all()
        existing_aweme_ids: set[str] = {record.aweme_id for record in existing_records}

        # 分离新增和更新的记录
        records_to_create: List[DouyinAweme] = []
        records_to_update: List[DouyinAweme] = []

        # 创建映射字典，便于快速查找已存在的记录
        existing_records_map: dict[str, DouyinAweme] = {record.aweme_id: record for record in existing_records}

        for aweme in aweme_list:
            if aweme.aweme_id in existing_aweme_ids:
                # 需要更新的记录
                existing_record: DouyinAweme = existing_records_map[aweme.aweme_id]
                needs_update: bool = False

                # 检查并更新各个字段
                update_fields: List[str] = [
                    'user_id', 'sec_uid', 'short_user_id', 'user_unique_id', 'nickname',
                    'avatar', 'user_signature', 'ip_location', 'aweme_type', 'title',
                    'desc', 'create_time', 'liked_count', 'comment_count', 'share_count',
                    'collected_count', 'aweme_url', 'cover_url', 'video_download_url',
                    'source_keyword'
                ]

                for field in update_fields:
                    new_value: Any = getattr(aweme, field, None)
                    current_value: Any = getattr(existing_record, field, None)
                    if new_value != current_value:
                        setattr(existing_record, field, new_value)
                        needs_update = True

                if needs_update:
                    records_to_update.append(existing_record)
            else:
                # 需要创建的新记录
                records_to_create.append(aweme)

        # 执行批量操作
        created_count: int = 0
        updated_count: int = 0

        # 批量创建新记录
        if records_to_create:
            await DouyinAweme.bulk_create(records_to_create)
            created_count = len(records_to_create)
            logger.info(f"batch_update_douyin_aweme: 成功创建 {created_count} 个新记录")

        # 批量更新已存在的记录
        if records_to_update:
            await DouyinAweme.bulk_update(
                records_to_update,
                fields=[
                    'user_id', 'sec_uid', 'short_user_id', 'user_unique_id', 'nickname',
                    'avatar', 'user_signature', 'ip_location', 'aweme_type', 'title',
                    'desc', 'create_time', 'liked_count', 'comment_count', 'share_count',
                    'collected_count', 'aweme_url', 'cover_url', 'video_download_url',
                    'source_keyword'
                ]
            )
            updated_count = len(records_to_update)
            logger.info(f"batch_update_douyin_aweme: 成功更新 {updated_count} 个已存在记录")

        total_processed: int = created_count + updated_count
        logger.info(f"batch_update_douyin_aweme: 批量处理完成 - 创建: {created_count}, 更新: {updated_count}, 总计: {total_processed}/{len(aweme_list)}")

        return total_processed > 0

    except Exception as e:
        logger.error(f"batch_update_douyin_aweme: 批量操作失败 - {e}")
        return False


async def batch_update_douyin_aweme_optimized(aweme_list: List[DouyinAweme], batch_size: int = 1000) -> bool:
    """
    优化的批量更新或创建抖音视频数据 - 支持分批处理大量数据

    Args:
        aweme_list: DouyinAweme 模型实例列表
        batch_size: 每批处理的记录数量，默认1000

    Returns:
        bool: 操作是否成功
    """
    try:
        if not aweme_list:
            logger.warning("batch_update_douyin_aweme_optimized: aweme_list 为空")
            return True

        total_batches: int = (len(aweme_list) + batch_size - 1) // batch_size

        logger.info(f"batch_update_douyin_aweme_optimized: 开始处理 {len(aweme_list)} 条记录，分 {total_batches} 批处理")

        # 分批处理
        for i in range(0, len(aweme_list), batch_size):
            batch: List[DouyinAweme] = aweme_list[i:i + batch_size]
            batch_num: int = i // batch_size + 1

            logger.info(f"batch_update_douyin_aweme_optimized: 处理第 {batch_num}/{total_batches} 批，共 {len(batch)} 条记录")

            # 调用原有的批量处理函数
            success: bool = await batch_update_douyin_aweme(batch)

            if not success:
                logger.error(f"batch_update_douyin_aweme_optimized: 第 {batch_num} 批处理失败")
                continue

            # 短暂休息以减少数据库压力
            if i + batch_size < len(aweme_list):
                await asyncio.sleep(0.01)

        logger.info(f"batch_update_douyin_aweme_optimized: 所有批次处理完成")
        return True

    except Exception as e:
        logger.error(f"batch_update_douyin_aweme_optimized: 优化批量操作失败 - {e}")
        return False


async def batch_update_douyin_aweme_with_optimizer(aweme_list: List[DouyinAweme]) -> bool:
    """
    使用数据库优化器的批量更新或创建抖音视频数据

    Args:
        aweme_list: DouyinAweme 模型实例列表

    Returns:
        bool: 操作是否成功
    """
    try:
        from services.base.database_optimizer import DatabaseOptimizer
        from services.base.logging_config import ServiceLogger

        # 创建数据库优化器实例
        logger: ServiceLogger = ServiceLogger("batch_update_douyin_aweme")

        if not aweme_list:
            logger.logger.warning("batch_update_douyin_aweme_with_optimizer: aweme_list 为空")
            return True

        optimizer: DatabaseOptimizer = DatabaseOptimizer(logger)

        # 定义需要更新的字段
        update_fields: List[str] = [
            'user_id', 'sec_uid', 'short_user_id', 'user_unique_id', 'nickname',
            'avatar', 'user_signature', 'ip_location', 'aweme_type', 'title',
            'desc', 'create_time', 'liked_count', 'comment_count', 'share_count',
            'collected_count', 'aweme_url', 'cover_url', 'video_download_url',
            'source_keyword'
        ]

        # 使用优化器进行批量更新或创建
        created_count: int
        updated_count: int
        created_count, updated_count = await optimizer.bulk_update_or_create_optimized(
            model=DouyinAweme,
            records=aweme_list,
            unique_field='aweme_id',
            update_fields=update_fields,
            batch_size=1000
        )

        total_processed: int = created_count + updated_count
        logger.log_operation_success(
            "batch_update_douyin_aweme_with_optimizer",
            0.0,  # duration will be calculated elsewhere if needed
            created=created_count,
            updated=updated_count,
            total=total_processed,
            input_count=len(aweme_list)
        )

        return total_processed > 0

    except Exception as e:
        logger.log_operation_error("batch_update_douyin_aweme_with_optimizer", e)
        return False


# 注意：所有数据转换逻辑应放在 mappers 目录中，models 文件夹下的内容应该只关注和数据库层的交互
