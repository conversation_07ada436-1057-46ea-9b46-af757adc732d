"""
抖音平台枚举定义
"""

from models.enums import EnumBase


class DouyinVideoStatus(EnumBase):
    """抖音视频状态枚举"""

    NORMAL = "normal"  # 正常
    DELETED = "deleted"  # 已删除
    PRIVATE = "private"  # 私密
    REVIEWING = "reviewing"  # 审核中
    PROHIBITED = "prohibited"  # 被禁止


class DouyinUserVerifyType(EnumBase):
    """抖音用户认证类型"""

    NONE = "none"  # 无认证
    PERSONAL = "personal"  # 个人认证
    ENTERPRISE = "enterprise"  # 企业认证
    GOVERNMENT = "government"  # 政府认证
    MEDIA = "media"  # 媒体认证


class DouyinFetchMethod(EnumBase):
    """抖音视频获取方法枚举"""

    AUTO = "auto"  # 自动选择最佳方法
    JINGXUAN = "jingxuan"  # 精选页面方式
    MOBILE = "mobile"  # 移动端方式
    RPC = "rpc"  # RPC接口方式


class DouyinHTMLMethod(EnumBase):
    """抖音HTML获取方法枚举"""

    JINGXUAN = "jingxuan"  # 精选页面方式
    MOBILE_SHARE = "mobile_share"  # 移动端分享页面方式
    PC_VIDEO = "pc_video"  # PC端视频页面方式
