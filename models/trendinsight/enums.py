"""
TrendInsight 平台枚举定义
"""

from models.enums import EnumBase


class TrendInsightUserType(EnumBase):
    """TrendInsight 用户类型枚举"""

    NORMAL = "normal"  # 普通用户
    DAREN = "daren"  # 达人
    BRAND = "brand"  # 品牌
    MCN = "mcn"  # MCN机构


class TrendInsightVideoCategory(EnumBase):
    """TrendInsight 视频分类枚举"""

    LIFESTYLE = "lifestyle"  # 生活
    ENTERTAINMENT = "entertainment"  # 娱乐
    FOOD = "food"  # 美食
    BEAUTY = "beauty"  # 美妆
    FASHION = "fashion"  # 时尚
    SPORTS = "sports"  # 体育
    TRAVEL = "travel"  # 旅游
    EDUCATION = "education"  # 教育
    TECHNOLOGY = "technology"  # 科技
    BUSINESS = "business"  # 商业


class TrendInsightAnalysisStatus(EnumBase):
    """TrendInsight 分析状态枚举"""

    PENDING = "pending"  # 待分析
    ANALYZING = "analyzing"  # 分析中
    COMPLETED = "completed"  # 已完成
    FAILED = "failed"  # 分析失败
