# 平台模型使用指南

本文档说明如何使用抖音和 TrendInsight 平台的 Tortoise ORM 模型。

## 📁 模型文件结构

```
models/
├── __init__.py           # 主模型导入文件
├── base.py              # 基础模型类
├── enums.py             # 通用枚举定义
├── admin.py             # 管理员相关模型
├── douyin/              # 抖音平台模型包
│   ├── __init__.py      # 导出抖音模型
│   ├── enums.py         # 抖音平台枚举
│   └── models.py        # 抖音平台模型
└── trendinsight/        # TrendInsight平台模型包
    ├── __init__.py      # 导出TrendInsight模型
    ├── enums.py         # TrendInsight平台枚举
    └── models.py        # TrendInsight平台模型
```

## 🗂️ 模型概览

### 抖音平台模型

| 模型 | 描述 | 主要字段 |
|------|------|----------|
| `DouyinUser` | 用户信息 | uid, nickname, follower_count, verify_type |
| `DouyinVideo` | 视频信息 | aweme_id, desc, author, digg_count, duration |
| `DouyinComment` | 评论信息 | cid, text, video, user, digg_count |
| `DouyinCollection` | 收藏夹 | collection_id, name, user, videos |
| `DouyinLive` | 直播信息 | room_id, title, host, viewer_count |
| `DouyinSearchRecord` | 搜索记录 | keyword, search_type, result_count |
| `DouyinCrawlTask` | 爬取任务 | task_id, task_type, status, progress |

### TrendInsight 平台模型

| 模型 | 描述 | 主要字段 |
|------|------|----------|
| `TrendInsightUser` | 用户信息 | user_id, nickname, user_type, engagement_rate |
| `TrendInsightVideo` | 视频信息 | item_id, title, author, trend_score, heat_index |
| `TrendInsightVideoAnalysis` | 视频分析 | video, analysis_date, total_views, insights |
| `TrendInsightDaren` | 达人信息 | user, level, commercial_value, fans_profile |
| `TrendInsightSearchRecord` | 搜索记录 | keyword, search_type, category_id |
| `TrendInsightTrendTopic` | 趋势话题 | topic_name, heat_value, participant_count |
| `TrendInsightCrawlTask` | 爬取任务 | task_id, task_type, status, date_range |

## 🚀 使用方法

### 1. 导入模型

```python
# 方式1: 从主模块导入(推荐)
from models import DouyinUser, DouyinVideo
from models import TrendInsightUser, TrendInsightVideo

# 方式2: 从平台子模块导入
from models.douyin import DouyinUser, DouyinVideo
from models.trendinsight import TrendInsightUser, TrendInsightVideo
```

### 2. 基本 CRUD 操作

#### 创建用户

```python
# 创建抖音用户
douyin_user = await DouyinUser.create(
    uid="123456789",
    sec_uid="MS4wLjABAAAAXXX",
    nickname="测试用户",
    signature="这是个人简介",
    follower_count=10000,
    following_count=500
)

# 创建 TrendInsight 用户
trend_user = await TrendInsightUser.create(
    user_id="user_001",
    sec_user_id="sec_001", 
    nickname="达人用户",
    user_type="daren",
    follower_count=50000
)
```

#### 创建视频

```python
from datetime import datetime

# 创建抖音视频
douyin_video = await DouyinVideo.create(
    aweme_id="video_123",
    desc="这是视频描述",
    author=douyin_user,  # 关联用户
    duration=30000,
    digg_count=1000,
    comment_count=50,
    create_time=datetime.now()
)

# 创建 TrendInsight 视频
trend_video = await TrendInsightVideo.create(
    item_id="item_123",
    title="视频标题",
    description="视频描述", 
    author=trend_user,  # 关联用户
    duration=45,
    view_count=100000,
    trend_score=8.5,
    publish_time=datetime.now()
)
```

#### 查询数据

```python
# 查询抖音用户及其视频
user_with_videos = await DouyinUser.filter(
    uid="123456789"
).prefetch_related("videos").first()

# 查询热门视频
popular_videos = await DouyinVideo.filter(
    digg_count__gte=1000
).order_by("-digg_count").limit(10)

# 查询 TrendInsight 达人
darens = await TrendInsightUser.filter(
    user_type="daren",
    follower_count__gte=10000
).all()
```

## 🔧 数据库迁移

### 1. 生成迁移文件
```bash
aerich migrate
```

### 2. 应用迁移
```bash
aerich upgrade
```

### 3. 清理数据库（开发环境）
```bash
make clean-db
```

## 🎯 注意事项

1. **外键关系**: 模型使用了正确的外键关系，确保关联对象存在
2. **唯一约束**: uid、aweme_id 等关键字段有唯一约束
3. **枚举使用**: 状态、类型等字段使用了枚举类型
4. **时间戳**: 所有模型都包含创建和更新时间戳
5. **原始数据**: raw_data 字段保存完整的 API 响应数据

这套模型设计提供了完整的抖音和 TrendInsight 平台数据存储解决方案。