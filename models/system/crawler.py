"""
爬虫相关的Tortoise ORM模型
"""

from tortoise import fields

from models.base import BaseModel, TimestampMixin


class CrawlerCookiesAccount(BaseModel, TimestampMixin):
    """爬虫账号池模型"""

    # 账号基本信息
    account_name = fields.CharField(max_length=64, default="", description="账号名称")
    platform_name = fields.CharField(
        max_length=64,
        default="",
        description="平台名称 (xiaohongshu | douyin | kuaishou | weibo | bilibili | tieba | zhihu | trendinsight)",
    )
    cookies = fields.TextField(null=True, default=None, description="对应自媒体平台登录成功后的cookies")

    # 状态信息
    invalid_timestamp = fields.BigIntField(default=0, description="账号失效时间戳")
    status = fields.IntField(default=0, description="账号状态枚举值(0：有效，-1：无效)")

    class Meta:
        table = "crawler_cookies_account"
        indexes = [
            ("platform_name",),
            ("status",),
            ("updated_at",),
            ("platform_name", "status"),
        ]

    def __str__(self):
        return f"CrawlerCookiesAccount({self.account_name}, {self.platform_name}, {self.status})"
