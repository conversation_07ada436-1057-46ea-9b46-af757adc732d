import asyncio
from datetime import datetime

from tortoise import fields, models
from tortoise.queryset import QuerySet

from settings import settings


class SoftDeleteQuerySet(QuerySet):
    """支持软删除的查询集"""

    def active(self):
        """只查询未删除的记录"""
        return self.filter(is_deleted=False)

    def deleted(self):
        """只查询已删除的记录"""
        return self.filter(is_deleted=True)

    def with_deleted(self):
        """查询所有记录（包括已删除的）"""
        return self


class SoftDeleteManager:
    """软删除管理器"""

    def __init__(self, model_class):
        self.model_class = model_class

    def get_queryset(self):
        """获取查询集"""
        return SoftDeleteQuerySet(self.model_class)

    def all(self):
        """获取所有未删除的记录"""
        return self.get_queryset().active()

    def filter(self, *args, **kwargs):
        """过滤未删除的记录"""
        return self.get_queryset().active().filter(*args, **kwargs)

    def get(self, *args, **kwargs):
        """获取单个未删除的记录"""
        return self.get_queryset().active().get(*args, **kwargs)

    def with_deleted(self):
        """获取包含已删除记录的查询集"""
        return self.get_queryset().with_deleted()

    def deleted_only(self):
        """只获取已删除的记录"""
        return self.get_queryset().deleted()


class BaseModel(models.Model):
    id = fields.BigIntField(pk=True, index=True)
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")
    is_deleted = fields.BooleanField(default=False, description="是否被软删除")

    # 软删除管理器
    objects = None  # 将在 __init_subclass__ 中设置

    def __init_subclass__(cls, **kwargs):
        """子类初始化时设置软删除管理器"""
        super().__init_subclass__(**kwargs)
        if not hasattr(cls, "_meta") or not getattr(cls._meta, "abstract", False):
            cls.objects = SoftDeleteManager(cls)

    async def soft_delete(self):
        """软删除记录"""
        self.is_deleted = True
        await self.save(update_fields=["is_deleted", "updated_at"])

    async def restore(self):
        """恢复软删除的记录"""
        self.is_deleted = False
        await self.save(update_fields=["is_deleted", "updated_at"])

    @classmethod
    async def soft_delete_bulk(cls, **filters):
        """批量软删除"""
        return await cls.filter(**filters).update(is_deleted=True)

    @classmethod
    async def restore_bulk(cls, **filters):
        """批量恢复"""
        return await cls.filter(is_deleted=True, **filters).update(is_deleted=False)

    async def to_dict(self, m2m: bool = False, exclude_fields: list[str] | None = None):
        if exclude_fields is None:
            exclude_fields = []

        d = {}
        for field in self._meta.db_fields:
            if field not in exclude_fields:
                value = getattr(self, field)
                if isinstance(value, datetime):
                    value = value.strftime(settings.DATETIME_FORMAT)
                d[field] = value

        if m2m:
            tasks = [
                self.__fetch_m2m_field(field, exclude_fields)
                for field in self._meta.m2m_fields
                if field not in exclude_fields
            ]
            results = await asyncio.gather(*tasks)
            for field, values in results:
                d[field] = values

        return d

    async def __fetch_m2m_field(self, field, exclude_fields):
        values = await getattr(self, field).all().values()
        formatted_values = []

        for value in values:
            formatted_value = {}
            for k, v in value.items():
                if k not in exclude_fields:
                    if isinstance(v, datetime):
                        formatted_value[k] = v.strftime(settings.DATETIME_FORMAT)
                    else:
                        formatted_value[k] = v
            formatted_values.append(formatted_value)

        return field, formatted_values

    class Meta:
        abstract = True


class UUIDModel:
    uuid = fields.UUIDField(unique=True, pk=False, index=True)


class TimestampMixin:
    """时间戳混入类（已集成到 BaseModel 中，保留用于向后兼容）"""

    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")


class SoftDeleteMixin:
    """软删除混入类（已集成到 BaseModel 中，保留用于向后兼容）"""

    is_deleted = fields.BooleanField(default=False, description="是否被软删除")
