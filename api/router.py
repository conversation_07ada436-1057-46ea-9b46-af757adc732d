"""
API 路由配置

包含所有 API 路由的主入口和配置
"""

from fastapi import APIRouter

from .v1 import v1_router

# 创建主路由
api_router = APIRouter()


# 基础路由
@api_router.get("/")
async def api_root():
    """API 根路径"""
    return {"message": "API is working"}


@api_router.get("/status")
async def api_status():
    """API 状态检查"""
    return {"status": "ok", "message": "API is healthy"}


# 包含 v1 路由（包含所有子路由：douyin, trendinsight, users, etc.）
api_router.include_router(v1_router, prefix="/v1")
