edition: 3.0.0
name: qihaozhushou-media-crawler-python
access: "tdid"

# 公共变量定义
vars:
  region: "cn-shanghai"
  functionName: "qihaozhushou-media-crawler-python__test"
  environment: "test"
  
  # 公共配置变量
  commonLayers:
    - "acs:fc:cn-shanghai:official:layers/Python312/versions/1"
    - "acs:fc:cn-shanghai:official:layers/Nodejs22/versions/1"
    - "acs:fc:cn-shanghai:1877883754323429:layers/mediacrawler-python312/versions/4"
    
  # 公共网络配置
  commonVpcConfig:
    securityGroupId: sg-uf63h58x0cojg6eutqnf
    vpcId: vpc-uf6v6g274feupv6noxj32
    vSwitchIds:
      - vsw-uf6qsib9g2qrznlq1rfph
      - vsw-uf6mx7rnyrwf6oudrr3d9
      - vsw-uf6n7jmd6rex8jcnpbg1b
      - vsw-uf6k0u4uxlgcqka6b4b6n
      - vsw-uf6t92ej9dn4k03sgxlgu
      - vsw-uf60ltqf5qwt01ygkxkfg
      - vsw-uf65nzjri9ijy0l682f3u
  
  # 公共运行时配置
  commonRuntime: custom.debian12
  commonCode: .
  commonLogConfig: auto
  
  # 公共标签
  commonTags:
    - Value: 起号助手
      Key: project
    - Value: ${vars.environment}
      Key: deploy_env
  
  # 公共路径配置
  commonPath: "/opt/python3.12/bin:/opt/nodejs22/bin:/usr/local/bin/apache-maven/bin:/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/local/ruby/bin:/opt/bin:/code:/code/bin:/root/.local/bin"

# YAML 锚点定义，用于共享配置
common_env_vars: &common_env_vars
  # Dynaconf 环境配置
  ENV_FOR_DYNACONF: "staging"
  
  # 应用配置
  ACCOUNT_POOL_SAVE_TYPE: "mysql"
  
  # Redis配置
  REDIS_DB_HOST: "r-uf6ptu753vuhd6lbz4.redis.rds.aliyuncs.com"
  REDIS_DB_NUM: "13"
  REDIS_DB_PORT: "6379"
  REDIS_DB_PWD: "uf6ptu753vuhd6lbz4:Qihaozhushou2025"

  # 签名服务配置
  SIGN_SRV_URL: "https://qihaozhsrv-test-kobothkzvh.cn-shanghai.fcapp.run"

  # KDL代理配置
  APP_KUAIDAILI_DPS_SECRET_ID: "ozq6lt7ganftum6gbyah"
  APP_KUAIDAILI_DPS_SIGNATURE: "0dp5ja9rjtdlgcv19s9u0yq28yg2kf1l"
  APP_KUAIDAILI_USERNAME: "d4704932204"
  APP_KUAIDAILI_PASSWORD: "cbgwdyz3"

  # 数据库配置
  APP_DATABASE_URL: "mysql://qihaozhushou:qihaozhushou2025%40@*************:3306/media_crawler"
  
  # Tortoise ORM 配置 - 使用 MySQL
  APP_TORTOISE_ORM__CONNECTIONS__DEFAULT: "mysql://qihaozhushou:qihaozhushou2025%40@**************:3306/media_crawler"
  APP_TORTOISE_ORM__CONNECTIONS__QIHAOZHUSHOU: "mysql://qihaozhushou:qihaozhushou2025%40@**************:3306/qihaozhushou"

  # 系统路径配置
  PYTHONPATH: /opt/python:/code
  TZ: Asia/Shanghai
resources:
  server:
    component: fc3
    props:
      region: ${vars.region}
      description: "【测试环境】起号助手爬虫服务"
      layers: ${vars.commonLayers}
      environmentVariables:
        # 公共环境变量
        <<: *common_env_vars
        
        # Server特定配置
        APP_HOST: "0.0.0.0"
        APP_PORT: "80"
        APP_LOG_LEVEL: "ERROR"
        PATH: ${vars.commonPath}
      vpcConfig: ${vars.commonVpcConfig}
      runtime: ${vars.commonRuntime}
      timeout: 60
      cpu: 0.35
      memorySize: 512
      diskSize: 512
      tags: ${vars.commonTags}
      customRuntimeConfig:
        port: 8000
        command:
          - python3
        args:
          - run.py
      functionName: qihaozhushou-media-crawler-python__test
      code: ${vars.commonCode}
      logConfig: ${vars.commonLogConfig}
      triggers:
        - triggerName: httpTrigger
          triggerType: http
          triggerConfig:
            authType: anonymous
            methods:
              - GET
              - POST
              - PUT
              - DELETE
      customDomain:
        domainName: auto
        protocol: HTTP
        route:
          path: /*
          qualifier: LATEST
      provisionConfig:
        scheduledActions: []
        defaultTarget: 1
        currentError: ''
        current: 1
        alwaysAllocateCPU: false
        alwaysAllocateGPU: false
        targetTrackingPolicies: []
        functionArn: >-
          acs:fc:cn-shanghai:1877883754323429:functions/${vars.functionName}
        target: 1
  # 定时任务调度器
  task-scheduler:
    component: fc3
    props:
      region: ${vars.region}
      description: "【测试环境】起号助手爬虫定时任务调度器"
      layers: ${vars.commonLayers}
      environmentVariables:
        # 公共环境变量
        <<: *common_env_vars
        
        # 任务特定配置
        APP_LOG_LEVEL: "INFO"
        PATH: ${vars.commonPath}
      vpcConfig: ${vars.commonVpcConfig}
      runtime: ${vars.commonRuntime}
      timeout: 900  # 15分钟超时，适合长时间任务
      cpu: 0.5
      memorySize: 1024  # 增加内存以处理大量数据
      diskSize: 512
      tags:
        - Value: 起号助手
          Key: project
        - Value: ${vars.environment}
          Key: deploy_env
        - Value: 定时任务
          Key: service_type
      customRuntimeConfig:
        port: 8000
        command:
          - python3
        args:
          - tasks/main.py
      functionName: qihaozhushou-media-crawler-python--task__test
      code: ${vars.commonCode}
      logConfig: ${vars.commonLogConfig}
      triggers:
        # 趋势刷新任务 - 每小时执行一次
        - triggerName: trend-refresh-hourly
          triggerType: timer
          triggerConfig:
            # 每小时执行一次 (UTC时间)
            cronExpression: "0 0 */1 * * *"
            enable: true
            payload: |
              {
                "event_name": "trend_refresh_task",
                "event_params": {
                  "task_type": "trend_refresh",
                  "batch_size": 100,
                  "max_age_hours": 1,
                  "timeout": 3600
                }
              }

        # 大批量趋势刷新任务 - 每6小时执行一次
        - triggerName: trend-refresh-large
          triggerType: timer
          triggerConfig:
            # 每6小时执行一次 (UTC时间: 0, 6, 12, 18点)
            cronExpression: "0 0 0,6,12,18 * * *"
            enable: true
            payload: |
              {
                "event_name": "trend_refresh_task",
                "event_params": {
                  "task_type": "trend_refresh",
                  "batch_size": 200,
                  "max_age_hours": 6,
                  "timeout": 7200
                }
              }

        # 作者监控任务 - 每小时第15分钟执行
        - triggerName: author-monitor-hourly
          triggerType: timer
          triggerConfig:
            # 每小时第15分钟执行 (UTC时间)
            cronExpression: "0 15 */1 * * *"
            enable: true
            payload: |
              {
                "event_name": "author_monitor_task",
                "event_params": {
                  "task_type": "author_monitor",
                  "batch_size": 50,
                  "max_age_hours": 2,
                  "timeout": 3600,
                  "author_video_limit": 50
                }
              }

        # 关键词监控任务 - 每小时第30分钟执行
        - triggerName: keyword-monitor-hourly
          triggerType: timer
          triggerConfig:
            # 每小时第30分钟执行 (UTC时间)
            cronExpression: "0 30 */1 * * *"
            enable: true
            payload: |
              {
                "event_name": "keyword_monitor_task",
                "event_params": {
                  "task_type": "keyword_monitor",
                  "batch_size": 20,
                  "max_age_hours": 3,
                  "timeout": 3600,
                  "keyword_video_limit": 100,
                  "keyword_search_days": 7
                }
              }

        # 深度趋势刷新任务 - 每天凌晨2点执行（带过滤条件）
        - triggerName: trend-refresh-deep
          triggerType: timer
          triggerConfig:
            # 每天凌晨2点执行 (UTC时间，北京时间凌晨2点 -> UTC时间18点)
            cronExpression: "0 0 18 * * *"
            enable: false  # 默认禁用，可根据需要启用
            payload: |
              {
                "event_name": "trend_refresh_task",
                "event_params": {
                  "task_type": "trend_refresh",
                  "batch_size": 500,
                  "max_age_hours": 24,
                  "timeout": 10800,
                  "filters": {
                    "date_range": {
                      "start": "2025-08-01",
                      "end": "2025-08-04"
                    }
                  }
                }
              }

        # 大批量作者监控任务 - 每天凌晨1点执行
        - triggerName: author-monitor-large
          triggerType: timer
          triggerConfig:
            # 每天凌晨1点执行 (UTC时间，北京时间凌晨1点 -> UTC时间17点)
            cronExpression: "0 0 17 * * *"
            enable: false  # 默认禁用，可根据需要启用
            payload: |
              {
                "event_name": "author_monitor_task",
                "event_params": {
                  "task_type": "author_monitor",
                  "batch_size": 100,
                  "max_age_hours": 6,
                  "timeout": 7200,
                  "author_video_limit": 100
                }
              }

        # 周度关键词监控任务 - 每周日凌晨3点执行
        - triggerName: keyword-monitor-weekly
          triggerType: timer
          triggerConfig:
            # 每周日凌晨3点执行 (UTC时间，北京时间凌晨3点 -> UTC时间19点)
            cronExpression: "0 0 19 * * 0"
            enable: false  # 默认禁用，可根据需要启用
            payload: |
              {
                "event_name": "keyword_monitor_task",
                "event_params": {
                  "task_type": "keyword_monitor",
                  "batch_size": 50,
                  "max_age_hours": 24,
                  "timeout": 7200,
                  "keyword_video_limit": 200,
                  "keyword_search_days": 14
                }
              }

      # 预留配置，定时任务不需要预留实例
      provisionConfig:
        scheduledActions: []
        defaultTarget: 0
        current: 0
        alwaysAllocateCPU: false
        alwaysAllocateGPU: false
        targetTrackingPolicies: []
        target: 0

