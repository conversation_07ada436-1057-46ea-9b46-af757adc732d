# 测试环境配置文件示例
# 复制此文件为 .env.test 并填入真实的测试数据

# =============================================================================
# 集成测试配置
# =============================================================================

# 是否启用真实API测试（true/false）
# 设置为 true 时会真实调用抖音API服务
ENABLE_REAL_API_TESTS=false

# 测试用的抖音cookies
# 从浏览器开发者工具中获取，用于真实API调用
# 注意：cookies会过期，需要定期更新
DOUYIN_TEST_COOKIES="your_cookies_here"

# 测试用的视频ID
# 使用一个公开的抖音视频ID进行测试
TEST_VIDEO_ID=7509429111140420874

# 测试用的用户ID
# 使用一个公开的抖音用户sec_user_id进行测试
TEST_USER_ID=MS4wLjABAAAADY7u-UqfGogzrFtNghKD7Rgnz1QnWBJUrBA4AYrCX20zcMMCg1d8xoslUJiVTQyP

# 测试用的收藏夹ID
# 用于测试分页收藏视频列表功能，需要是一个真实存在且有内容的收藏夹ID
TEST_COLLECTS_ID=7234567890123456789

# 测试用的视频ID
# 用于测试视频详情、评论等功能，需要是一个真实存在的视频ID
TEST_VIDEO_ID=7234567890123456789

# =============================================================================
# API测试配置
# =============================================================================

# API测试超时时间（秒）
API_TEST_TIMEOUT=30

# API测试重试次数
API_TEST_RETRIES=2

# 是否启用详细日志
ENABLE_TEST_LOGGING=true

# 测试日志级别（DEBUG, INFO, WARNING, ERROR）
TEST_LOG_LEVEL=INFO

# =============================================================================
# 性能测试配置
# =============================================================================

# 并发测试的并发数
CONCURRENT_TEST_COUNT=3

# 性能测试的最大响应时间（秒）
MAX_RESPONSE_TIME=30

# =============================================================================
# CI/CD配置
# =============================================================================

# CI环境标识（由CI系统自动设置）
# CI=true

# Python版本（由CI系统设置）
# PYTHON_VERSION=3.11

# 测试模式（unit, integration, all）
TEST_MODE=unit

# =============================================================================
# 开发环境配置
# =============================================================================

# 是否在测试失败时启动调试器
ENABLE_TEST_DEBUGGER=false

# 测试报告输出目录
TEST_REPORT_DIR=rpc/douyin/tests/reports

# 覆盖率报告输出目录
COVERAGE_REPORT_DIR=rpc/douyin/tests/coverage

# =============================================================================
# 使用说明
# =============================================================================

# 1. 复制此文件为 .env.test
#    cp .env.test.example .env.test

# 2. 填入真实的测试数据（特别是cookies）

# 3. 运行不同类型的测试：
#    
#    # 只运行单元测试
#    ./scripts/test_commands.sh unit
#    
#    # 运行集成测试（模拟API）
#    ./scripts/test_commands.sh integration
#    
#    # 运行真实API测试（需要设置ENABLE_REAL_API_TESTS=true）
#    ENABLE_REAL_API_TESTS=true ./scripts/test_commands.sh real-api
#    
#    # 运行所有测试
#    ./scripts/test_commands.sh all
#    
#    # CI环境测试（跳过集成测试）
#    ./scripts/test_commands.sh ci

# 4. 获取cookies的方法：
#    a. 打开浏览器，访问 https://www.douyin.com
#    b. 登录你的抖音账号
#    c. 打开开发者工具（F12）
#    d. 切换到 Network 标签
#    e. 刷新页面，找到任意一个请求
#    f. 在请求头中找到 Cookie 字段，复制其值
#    g. 将复制的值填入 DOUYIN_TEST_COOKIES

# 5. 获取收藏夹ID的方法：
#    a. 登录抖音网页版，进入个人主页
#    b. 点击"收藏"标签，选择一个有内容的收藏夹
#    c. 打开开发者工具（F12），切换到 Network 标签
#    d. 刷新页面或滚动加载更多内容
#    e. 找到类似 "/aweme/v1/web/aweme/listcollection/" 的请求
#    f. 在请求参数中找到 "collects_id" 字段，复制其值
#    g. 将复制的值填入 TEST_COLLECTS_ID

# 5. 注意事项：
#    - cookies会过期，通常几小时到几天不等
#    - 真实API测试会消耗你的抖音账号请求配额
#    - 不要在公共仓库中提交包含真实cookies的配置文件
#    - 建议定期更新测试用的视频ID和用户ID
