"""
抖音相关的 Pydantic 模式
"""

from datetime import datetime
from typing import List, Optional

from pydantic import BaseModel, Field


class DouyinAwemeBase(BaseModel):
    """抖音视频基础模式"""

    user_id: Optional[str] = Field(None, description="用户ID")
    sec_uid: Optional[str] = Field(None, description="用户sec_uid")
    short_user_id: Optional[str] = Field(None, description="用户短ID")
    user_unique_id: Optional[str] = Field(None, description="用户唯一ID")
    nickname: Optional[str] = Field(None, description="用户昵称")
    avatar: Optional[str] = Field(None, description="用户头像地址")
    user_signature: Optional[str] = Field(None, description="用户签名")
    ip_location: Optional[str] = Field(None, description="评论时的IP地址")
    aweme_id: str = Field(..., description="视频ID")
    aweme_type: Optional[str] = Field(None, description="视频类型")
    title: Optional[str] = Field(None, description="视频标题")
    desc: Optional[str] = Field(None, description="视频描述")
    create_time: Optional[datetime] = Field(None, description="视频发布时间")
    liked_count: Optional[str] = Field(None, description="视频点赞数")
    comment_count: Optional[str] = Field(None, description="视频评论数")
    share_count: Optional[str] = Field(None, description="视频分享数")
    collected_count: Optional[str] = Field(None, description="视频收藏数")
    aweme_url: Optional[str] = Field(None, description="视频详情页URL")
    cover_url: Optional[str] = Field(None, description="视频封面图URL")
    video_download_url: Optional[str] = Field(None, description="视频下载地址")
    source_keyword: Optional[str] = Field(None, description="搜索来源关键字")


class DouyinAwemeCreate(DouyinAwemeBase):
    """创建抖音视频时的模式"""

    pass


class DouyinAwemeUpdate(BaseModel):
    """更新抖音视频时的模式"""

    user_id: Optional[str] = Field(None, description="用户ID")
    sec_uid: Optional[str] = Field(None, description="用户sec_uid")
    short_user_id: Optional[str] = Field(None, description="用户短ID")
    user_unique_id: Optional[str] = Field(None, description="用户唯一ID")
    nickname: Optional[str] = Field(None, description="用户昵称")
    avatar: Optional[str] = Field(None, description="用户头像地址")
    user_signature: Optional[str] = Field(None, description="用户签名")
    ip_location: Optional[str] = Field(None, description="评论时的IP地址")
    aweme_type: Optional[str] = Field(None, description="视频类型")
    title: Optional[str] = Field(None, description="视频标题")
    desc: Optional[str] = Field(None, description="视频描述")
    create_time: Optional[int] = Field(None, description="视频发布时间戳")
    liked_count: Optional[str] = Field(None, description="视频点赞数")
    comment_count: Optional[str] = Field(None, description="视频评论数")
    share_count: Optional[str] = Field(None, description="视频分享数")
    collected_count: Optional[str] = Field(None, description="视频收藏数")
    aweme_url: Optional[str] = Field(None, description="视频详情页URL")
    cover_url: Optional[str] = Field(None, description="视频封面图URL")
    video_download_url: Optional[str] = Field(None, description="视频下载地址")
    source_keyword: Optional[str] = Field(None, description="搜索来源关键字")


class VideoQueryRequest(BaseModel):
    """视频查询请求模式"""

    video_id: str = Field(..., description="视频ID", example="7123456789012345678")
    cookies: Optional[str] = Field(None, description="可选的cookies，如果不提供将从数据库获取")
    response_type: str = Field("db", description="响应类型", pattern="^(db)$", example="db")


class SingleCollectionSyncRequest(BaseModel):
    """单个收藏夹同步请求模式"""

    collection_id: str = Field(..., description="抖音收藏夹ID", example="7495633625980131112")


class VideoItemResponse(BaseModel):
    """视频项响应模型（用于同步响应中的 video_items 字段）"""

    aweme_id: str = Field(..., description="视频ID", example="7123456789012345678")
    create_time: int = Field(..., description="创建时间戳", example=1705314645)


class CollectionSyncResponse(BaseModel):
    """收藏夹同步响应模式"""

    collections_synced: int = Field(..., description="同步的收藏夹数量")
    videos_synced: int = Field(..., description="同步的视频数量")
    collections_filtered: int = Field(..., description="过滤后的收藏夹数量")
    relations_created: int = Field(..., description="创建的关联关系数量")
    relations_existing: int = Field(..., description="已存在的关联关系数量")
    trendinsight_relations_created: int = Field(..., description="创建的TrendInsight视频关联数量")
    trendinsight_relations_existing: int = Field(..., description="已存在的TrendInsight视频关联数量")
    aweme_ids: List[str] = Field(
        ..., description="获取到的所有视频aweme_id列表", example=["7123456789012345678", "7123456789012345679"]
    )
    video_items: List[VideoItemResponse] = Field(
        default_factory=list,
        description="视频详细信息列表",
        example=[{"aweme_id": "7123456789012345678", "create_time": 1705314645}],
    )
    errors: list = Field(default_factory=list, description="错误信息列表")

    def __getitem__(self, key: str):
        """支持字典式访问以保持向后兼容"""
        return getattr(self, key)

    def __setitem__(self, key: str, value):
        """支持字典式设置以保持向后兼容"""
        setattr(self, key, value)

    def __contains__(self, key: str) -> bool:
        """支持 in 操作符"""
        return hasattr(self, key)

    def get(self, key: str, default=None):
        """支持 get 方法以保持向后兼容"""
        return getattr(self, key, default)

    def update(self, other):
        """支持 update 方法以保持向后兼容"""
        if isinstance(other, dict):
            for key, value in other.items():
                if hasattr(self, key):
                    setattr(self, key, value)


class DouyinAwemeResponse(BaseModel):
    """抖音视频数据库模型响应（用于 db-auto-cookies 端点）"""

    # 数据库主键
    id: Optional[int] = Field(None, description="数据库主键ID")

    # 用户信息
    user_id: Optional[str] = Field(None, description="用户ID", example="1750021778709080")
    sec_uid: Optional[str] = Field(None, description="用户sec_uid", example="MS4wLjABAAAAXXXXXXXXX")
    short_user_id: Optional[str] = Field(None, description="用户短ID", example="123456")
    user_unique_id: Optional[str] = Field(None, description="用户唯一ID", example="user123")
    nickname: Optional[str] = Field(None, description="用户昵称", example="科技博主")
    avatar: Optional[str] = Field(None, description="用户头像地址", example="https://example.com/avatar.jpg")
    user_signature: Optional[str] = Field(None, description="用户签名", example="分享科技知识")
    ip_location: Optional[str] = Field(None, description="IP归属地", example="北京")

    # 视频信息
    aweme_id: str = Field(..., description="视频ID", example="7123456789012345678")
    aweme_type: Optional[str] = Field(None, description="视频类型", example="0")
    title: Optional[str] = Field(None, description="视频标题", example="精彩视频标题")
    desc: Optional[str] = Field(None, description="视频描述", example="这是一个精彩的视频描述")
    create_time: Optional[str] = Field(None, description="视频发布时间", example="2024-01-01T12:00:00")

    # 统计信息
    liked_count: Optional[str] = Field(None, description="视频点赞数", example="10000")
    comment_count: Optional[str] = Field(None, description="视频评论数", example="500")
    share_count: Optional[str] = Field(None, description="视频分享数", example="200")
    collected_count: Optional[str] = Field(None, description="视频收藏数", example="300")

    # 媒体信息
    aweme_url: Optional[str] = Field(
        None, description="视频详情页URL", example="https://www.douyin.com/video/7123456789012345678"
    )
    cover_url: Optional[str] = Field(None, description="视频封面图URL", example="https://example.com/cover.jpg")
    video_download_url: Optional[str] = Field(None, description="视频下载地址", example="https://example.com/video.mp4")

    # 搜索来源
    source_keyword: Optional[str] = Field(None, description="搜索来源关键字", example="科技")

    class Config:
        json_schema_extra = {
            "example": {
                "id": 1,
                "aweme_id": "7123456789012345678",
                "aweme_type": "0",
                "title": "精彩视频标题",
                "desc": "这是一个精彩的视频描述",
                "create_time": "2024-01-01T12:00:00",
                "user_id": "1750021778709080",
                "sec_uid": "MS4wLjABAAAAXXXXXXXXX",
                "nickname": "科技博主",
                "avatar": "https://example.com/avatar.jpg",
                "user_signature": "分享科技知识",
                "liked_count": "10000",
                "comment_count": "500",
                "share_count": "200",
                "collected_count": "300",
                "aweme_url": "https://www.douyin.com/video/7123456789012345678",
                "cover_url": "https://example.com/cover.jpg",
                "video_download_url": "https://example.com/video.mp4",
                "source_keyword": "科技",
            }
        }


class VideoProcessResponse(BaseModel):
    """视频处理响应模型（用于 process/{video_id} 端点）"""

    video_id: str = Field(..., description="视频ID", example="7123456789012345678")
    input_type: str = Field(..., description="输入类型", example="aweme_id")
    original_input: str = Field(..., description="原始输入", example="7123456789012345678")
    processed: bool = Field(..., description="是否处理成功", example=True)
    data: DouyinAwemeResponse = Field(..., description="视频数据")
    source: str = Field(..., description="数据来源", example="database")

    class Config:
        json_schema_extra = {
            "example": {
                "video_id": "7123456789012345678",
                "input_type": "aweme_id",
                "original_input": "7123456789012345678",
                "processed": True,
                "data": {
                    "id": 1,
                    "aweme_id": "7123456789012345678",
                    "aweme_type": "0",
                    "title": "精彩视频标题",
                    "desc": "这是一个精彩的视频描述",
                    "create_time": "2024-01-01T12:00:00",
                    "user_id": "1750021778709080",
                    "nickname": "科技博主",
                    "liked_count": "10000",
                    "comment_count": "500",
                    "share_count": "200",
                    "collected_count": "300",
                },
                "source": "database",
            }
        }


class AwemeDataItem(BaseModel):
    """抖音视频数据项模型（用于 _convert_video_info_to_aweme_data 函数返回值）"""

    # 视频信息
    aweme_id: str = Field(..., description="视频ID")
    desc: Optional[str] = Field(None, description="视频描述")
    create_time: Optional[datetime] = Field(None, description="视频发布时间")
    aweme_type: Optional[str] = Field(None, description="视频类型")
    title: Optional[str] = Field(None, description="视频标题")

    # 用户信息
    user_id: Optional[str] = Field(None, description="用户ID")
    sec_uid: Optional[str] = Field(None, description="用户sec_uid")
    short_user_id: Optional[str] = Field(None, description="用户短ID")
    user_unique_id: Optional[str] = Field(None, description="用户唯一ID")
    nickname: Optional[str] = Field(None, description="用户昵称")
    avatar: Optional[str] = Field(None, description="用户头像地址")
    user_signature: Optional[str] = Field(None, description="用户签名")
    ip_location: Optional[str] = Field(None, description="IP归属地")

    # 统计信息
    liked_count: Optional[str] = Field(None, description="视频点赞数")
    comment_count: Optional[str] = Field(None, description="视频评论数")
    share_count: Optional[str] = Field(None, description="视频分享数")
    collected_count: Optional[str] = Field(None, description="视频收藏数")

    # 媒体信息
    aweme_url: Optional[str] = Field(None, description="视频详情页URL")
    cover_url: Optional[str] = Field(None, description="视频封面图URL")
    video_download_url: Optional[str] = Field(None, description="视频下载地址")

    # 搜索来源
    source_keyword: Optional[str] = Field(None, description="搜索来源关键字")


class CookiesValidateResponse(BaseModel):
    """Cookies验证响应模型（用于 cookies/validate 端点）"""

    valid: bool = Field(..., description="Cookies是否有效", example=True)
    message: str = Field(..., description="验证结果消息", example="Cookies验证成功")

    class Config:
        json_schema_extra = {
            "examples": [
                {"summary": "验证成功", "value": {"valid": True, "message": "Cookies验证成功"}},
                {"summary": "验证失败", "value": {"valid": False, "message": "Cookies验证失败"}},
                {"summary": "验证异常", "value": {"valid": False, "message": "Cookies验证失败: 网络连接超时"}},
            ]
        }
