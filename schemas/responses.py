"""
OpenAPI 响应模型定义
为 API 文档提供标准化的响应格式定义
"""

from typing import Generic, List, Optional, TypeVar

from pydantic import BaseModel, Field

# 定义泛型类型变量
T = TypeVar("T")


class BaseResponse(BaseModel):
    """基础响应模型"""

    code: int = Field(..., description="响应状态码", example=200)
    message: str = Field(..., description="响应消息", example="Success")

    class Config:
        json_schema_extra = {"example": {"code": 200, "message": "Success"}}


class SuccessResponse(BaseResponse, Generic[T]):
    """成功响应模型"""

    data: Optional[T] = Field(None, description="响应数据")

    class Config:
        json_schema_extra = {"example": {"code": 200, "message": "Success", "data": {}}}


class ErrorResponse(BaseResponse):
    """错误响应模型"""

    detail: Optional[str] = Field(None, description="详细错误信息")

    class Config:
        json_schema_extra = {"example": {"code": 400, "message": "Bad Request", "detail": "请求参数错误"}}


class PaginatedResponse(BaseResponse, Generic[T]):
    """分页响应模型"""

    data: List[T] = Field(..., description="数据列表")
    total: int = Field(..., description="总记录数", example=100)
    page: int = Field(..., description="当前页码", example=1)
    page_size: int = Field(..., description="每页记录数", example=10)

    class Config:
        json_schema_extra = {
            "example": {"code": 200, "message": "Success", "data": [], "total": 100, "page": 1, "page_size": 10}
        }


# 用户相关响应模型
class UserInfo(BaseModel):
    """用户信息模型"""

    id: int = Field(..., description="用户ID", example=1)
    username: str = Field(..., description="用户名", example="admin")
    email: str = Field(..., description="邮箱地址", example="<EMAIL>")
    is_active: bool = Field(..., description="是否激活", example=True)
    is_superuser: bool = Field(..., description="是否超级用户", example=False)
    created_at: Optional[str] = Field(None, description="创建时间", example="2024-01-01T00:00:00")
    last_login: Optional[str] = Field(None, description="最后登录时间", example="2024-01-01T12:00:00")


class LoginResponse(BaseModel):
    """登录响应数据模型"""

    access_token: str = Field(..., description="访问令牌", example="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...")
    username: str = Field(..., description="用户名", example="admin")


class DeptInfo(BaseModel):
    """部门信息模型"""

    id: int = Field(..., description="部门ID", example=1)
    name: str = Field(..., description="部门名称", example="技术部")
    parent_id: int = Field(..., description="父部门ID", example=0)
    order: int = Field(..., description="排序", example=1)


class RoleInfo(BaseModel):
    """角色信息模型"""

    id: int = Field(..., description="角色ID", example=1)
    name: str = Field(..., description="角色名称", example="管理员")
    desc: str = Field(..., description="角色描述", example="系统管理员角色")


class MenuInfo(BaseModel):
    """菜单信息模型"""

    id: int = Field(..., description="菜单ID", example=1)
    name: str = Field(..., description="菜单名称", example="用户管理")
    path: str = Field(..., description="菜单路径", example="/system/user")
    icon: str = Field(..., description="菜单图标", example="user")
    parent_id: int = Field(..., description="父菜单ID", example=0)
    order: int = Field(..., description="排序", example=1)
    is_hidden: bool = Field(..., description="是否隐藏", example=False)


class ApiInfo(BaseModel):
    """API信息模型"""

    id: int = Field(..., description="API ID", example=1)
    method: str = Field(..., description="请求方法", example="GET")
    path: str = Field(..., description="API路径", example="/api/v1/user/list")
    summary: str = Field(..., description="API摘要", example="获取用户列表")
    tags: str = Field(..., description="API标签", example="用户模块")


# 常用的响应类型别名
UserListResponse = PaginatedResponse[UserInfo]
UserDetailResponse = SuccessResponse[UserInfo]
LoginSuccessResponse = SuccessResponse[LoginResponse]

DeptListResponse = PaginatedResponse[DeptInfo]
DeptDetailResponse = SuccessResponse[DeptInfo]

RoleListResponse = PaginatedResponse[RoleInfo]
RoleDetailResponse = SuccessResponse[RoleInfo]

MenuListResponse = PaginatedResponse[MenuInfo]
MenuDetailResponse = SuccessResponse[MenuInfo]

ApiListResponse = PaginatedResponse[ApiInfo]
ApiDetailResponse = SuccessResponse[ApiInfo]

# 通用响应类型
MessageResponse = SuccessResponse[str]
EmptyResponse = SuccessResponse[None]


# 常用的错误响应
class ValidationErrorResponse(ErrorResponse):
    """验证错误响应"""

    class Config:
        json_schema_extra = {"example": {"code": 422, "message": "Validation Error", "detail": "请求数据格式错误"}}


class AuthErrorResponse(ErrorResponse):
    """认证错误响应"""

    class Config:
        json_schema_extra = {"example": {"code": 401, "message": "Unauthorized", "detail": "认证失败，请检查 token"}}


class PermissionErrorResponse(ErrorResponse):
    """权限错误响应"""

    class Config:
        json_schema_extra = {"example": {"code": 403, "message": "Forbidden", "detail": "权限不足"}}


class NotFoundErrorResponse(ErrorResponse):
    """资源不存在错误响应"""

    class Config:
        json_schema_extra = {"example": {"code": 404, "message": "Not Found", "detail": "请求的资源不存在"}}


# 标准 HTTP 状态码响应映射
STANDARD_RESPONSES = {
    200: {"model": SuccessResponse, "description": "请求成功"},
    400: {"model": ErrorResponse, "description": "请求参数错误"},
    401: {"model": AuthErrorResponse, "description": "未授权访问"},
    403: {"model": PermissionErrorResponse, "description": "权限不足"},
    404: {"model": NotFoundErrorResponse, "description": "资源不存在"},
    422: {"model": ValidationErrorResponse, "description": "数据验证错误"},
    500: {"model": ErrorResponse, "description": "服务器内部错误"},
}
