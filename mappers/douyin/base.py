"""
Base data mapper for Douyin video data transformation.
"""

import time
from abc import ABC, abstractmethod
from datetime import datetime
from typing import Any, Dict, List, Union


class BaseDataMapper(ABC):
    """
    抽象基类，定义数据映射器的通用接口和工具方法
    """

    @abstractmethod
    def map_to_standard_format(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        将原始数据映射为标准格式

        Args:
            raw_data: 原始数据字典

        Returns:
            Dict[str, Any]: 标准格式的数据字典
        """
        pass

    @abstractmethod
    def validate_raw_data(self, raw_data: Dict[str, Any]) -> bool:
        """
        验证原始数据的有效性

        Args:
            raw_data: 原始数据字典

        Returns:
            bool: 数据是否有效
        """
        pass

    def extract_basic_info(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        提取基础视频信息

        Args:
            raw_data: 原始数据字典

        Returns:
            Dict[str, Any]: 基础信息字典
        """
        return {
            "aweme_id": self._safe_extract(raw_data, "aweme_id", ""),
            "title": self._safe_extract(raw_data, "desc", ""),
            "desc": self._safe_extract(raw_data, "desc", ""),
            "aweme_type": self._safe_extract(raw_data, "aweme_type", "0"),
            "create_time": self._convert_timestamp(self._safe_extract(raw_data, "create_time")),
        }

    def extract_user_info(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        提取用户信息

        Args:
            raw_data: 原始数据字典

        Returns:
            Dict[str, Any]: 用户信息字典
        """
        author = self._safe_extract(raw_data, "author", {})
        if not isinstance(author, dict):
            author = {}

        return {
            "user_id": self._safe_extract(author, "uid", ""),
            "sec_uid": self._safe_extract(author, "sec_uid", ""),
            "short_user_id": self._safe_extract(author, "short_id", ""),
            "user_unique_id": self._safe_extract(author, "unique_id", ""),
            "nickname": self._safe_extract(author, "nickname", ""),
            "avatar": self._extract_avatar_url(author),
            "user_signature": self._safe_extract(author, "signature", ""),
            "ip_location": self._safe_extract(raw_data, "ip_location", ""),
        }

    def extract_statistics(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        提取统计信息

        Args:
            raw_data: 原始数据字典

        Returns:
            Dict[str, Any]: 统计信息字典
        """
        statistics = self._safe_extract(raw_data, "statistics", {})
        if not isinstance(statistics, dict):
            statistics = {}

        return {
            "liked_count": str(self._safe_extract(statistics, "digg_count", 0)),
            "comment_count": str(self._safe_extract(statistics, "comment_count", 0)),
            "share_count": str(self._safe_extract(statistics, "share_count", 0)),
            "collected_count": str(self._safe_extract(statistics, "collect_count", 0)),
        }

    def extract_media_urls(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        提取媒体URL信息

        Args:
            raw_data: 原始数据字典

        Returns:
            Dict[str, Any]: 媒体URL信息字典
        """
        aweme_id = self._safe_extract(raw_data, "aweme_id", "")

        return {
            "cover_url": self._extract_cover_url(raw_data),
            "video_download_url": self._extract_video_download_url(raw_data),
            "aweme_url": f"https://www.douyin.com/video/{aweme_id}" if aweme_id else "",
        }

    def _safe_extract(self, data: Dict[str, Any], path: Union[str, List[str]], default: Any = None) -> Any:
        """
        安全地从嵌套字典中提取数据

        Args:
            data: 数据字典
            path: 键路径，可以是字符串或字符串列表
            default: 默认值

        Returns:
            Any: 提取的值或默认值
        """
        if not isinstance(data, dict):
            return default

        if isinstance(path, str):
            return data.get(path, default)

        # 处理嵌套路径
        current = data
        for key in path:
            if isinstance(current, dict) and key in current:
                current = current[key]
            else:
                return default
        return current

    def _convert_timestamp(self, timestamp: Union[str, int, float, None]) -> int:
        """
        将各种时间格式转换为时间戳

        Args:
            timestamp: 时间值

        Returns:
            int: 时间戳（秒）
        """
        if timestamp is None:
            return int(time.time())

        # 如果已经是数字，直接返回
        if isinstance(timestamp, (int, float)):
            # 如果是毫秒时间戳，转换为秒
            if timestamp > 1e12:
                return int(timestamp / 1000)
            return int(timestamp)

        # 如果是字符串，尝试解析
        if isinstance(timestamp, str):
            timestamp = timestamp.strip()

            # 如果是纯数字字符串
            if timestamp.isdigit():
                ts = int(timestamp)
                if ts > 1e12:
                    return int(ts / 1000)
                return ts

            # 尝试解析ISO格式时间字符串
            try:
                if timestamp.endswith("Z"):
                    dt = datetime.fromisoformat(timestamp.replace("Z", "+00:00"))
                elif "+" in timestamp or timestamp.count("-") > 2:
                    dt = datetime.fromisoformat(timestamp)
                else:
                    timestamp = timestamp.replace(" ", "T")
                    dt = datetime.fromisoformat(timestamp)
                return int(dt.timestamp())
            except (ValueError, TypeError):
                pass

        # 其他情况返回当前时间
        return int(time.time())

    def _convert_to_datetime(self, timestamp: Union[str, int, float, None]) -> datetime:
        """
        将各种时间格式转换为 datetime 对象

        Args:
            timestamp: 时间值

        Returns:
            datetime: datetime 对象
        """
        if timestamp is None:
            return datetime.now()

        # 如果已经是 datetime 对象，直接返回
        if isinstance(timestamp, datetime):
            return timestamp

        # 如果已经是数字，当作时间戳处理
        if isinstance(timestamp, (int, float)):
            # 如果是毫秒时间戳，转换为秒
            if timestamp > 1e12:
                timestamp = timestamp / 1000
            try:
                return datetime.fromtimestamp(timestamp)
            except (ValueError, OSError):
                return datetime.now()

        # 如果是字符串，尝试解析
        if isinstance(timestamp, str):
            timestamp = timestamp.strip()

            # 如果是纯数字字符串，当作时间戳处理
            if timestamp.isdigit():
                ts = int(timestamp)
                if ts > 1e12:
                    ts = ts / 1000
                try:
                    return datetime.fromtimestamp(ts)
                except (ValueError, OSError):
                    return datetime.now()

            # 尝试解析ISO格式时间字符串
            try:
                if timestamp.endswith("Z"):
                    return datetime.fromisoformat(timestamp.replace("Z", "+00:00"))
                elif "+" in timestamp or timestamp.count("-") > 2:
                    return datetime.fromisoformat(timestamp)
                else:
                    timestamp = timestamp.replace(" ", "T")
                    return datetime.fromisoformat(timestamp)
            except (ValueError, TypeError):
                pass

        # 其他情况返回当前时间
        return datetime.now()

    def _validate_required_fields(self, data: Dict[str, Any], fields: List[str]) -> bool:
        """
        验证必需字段是否存在

        Args:
            data: 数据字典
            fields: 必需字段列表

        Returns:
            bool: 是否包含所有必需字段
        """
        for field in fields:
            if not self._safe_extract(data, field):
                return False
        return True

    def _extract_avatar_url(self, user_info: Dict[str, Any]) -> str:
        """
        提取用户头像URL

        Args:
            user_info: 用户信息字典

        Returns:
            str: 头像URL
        """
        if not user_info:
            return ""

        # 尝试从avatar_thumb获取
        avatar_thumb = user_info.get("avatar_thumb", {})
        if isinstance(avatar_thumb, dict):
            url_list = avatar_thumb.get("url_list", [])
            if url_list and len(url_list) > 0:
                return str(url_list[0])

        # 尝试从avatar_medium获取
        avatar_medium = user_info.get("avatar_medium", {})
        if isinstance(avatar_medium, dict):
            url_list = avatar_medium.get("url_list", [])
            if url_list and len(url_list) > 0:
                return str(url_list[0])

        return ""

    def _extract_cover_url(self, aweme_item: Dict[str, Any]) -> str:
        """
        提取视频封面URL

        Args:
            aweme_item: 视频数据字典

        Returns:
            str: 视频封面URL
        """
        if not aweme_item:
            return ""

        # 从video字段提取封面
        video_info = aweme_item.get("video", {})
        if isinstance(video_info, dict):
            cover_info = video_info.get("cover", {})
            if isinstance(cover_info, dict):
                url_list = cover_info.get("url_list", [])
                if url_list and len(url_list) > 0:
                    return str(url_list[0])

        return ""

    def _extract_video_download_url(self, aweme_detail: Dict[str, Any], by_mobile: bool = False) -> str:
        """
        提取视频下载地址

        Args:
            aweme_detail: 抖音视频详情数据
            by_mobile: 是否为移动端数据

        Returns:
            str: 视频下载地址
        """
        if not aweme_detail:
            return ""

        video_item = aweme_detail.get("video", {})
        url_h264_list = video_item.get("play_addr_h264", {}).get("url_list", [])
        url_256_list = video_item.get("play_addr_256", {}).get("url_list", [])
        url_list = video_item.get("play_addr", {}).get("url_list", [])
        actual_url_list = url_h264_list or url_256_list or url_list

        if not actual_url_list or (not by_mobile and len(actual_url_list) < 2):
            return ""

        return actual_url_list[-1]

    def convert_aweme_item_to_db_model(self, aweme_item: Dict[str, Any], by_mobile: bool = False) -> Dict[str, Any]:
        """
        将 aweme_item 数据转换为数据库模型数据格式

        Args:
            aweme_item: aweme_item 数据字典
            by_mobile: 是否为移动端数据，默认为False

        Returns:
            Dict: 符合DouyinAweme模型字段要求的数据字典
        """
        # 导入必要的函数
        from utils.time_utils import safe_convert_to_datetime

        # 提取嵌套的用户信息和统计信息
        user_info = aweme_item.get("author", {})
        interact_info = aweme_item.get("statistics", {})

        # 使用与原update_douyin_aweme相同的字段映射逻辑
        aweme_data = {
            # 基本信息
            "aweme_id": str(aweme_item.get("aweme_id", "")),
            "aweme_type": str(aweme_item.get("aweme_type", "0")),
            "title": aweme_item.get("desc", "")[:1024],  # 限制标题长度
            "desc": aweme_item.get("desc", ""),
            "create_time": safe_convert_to_datetime(aweme_item.get("create_time")),
            # 用户信息（从author字段提取）
            "user_id": user_info.get("uid", user_info.get("short_id", "")),  # 兼容不同字段名
            "sec_uid": user_info.get("sec_uid", ""),
            "short_user_id": user_info.get("short_id", ""),
            "user_unique_id": user_info.get("unique_id", ""),
            "nickname": user_info.get("nickname", ""),
            "user_signature": user_info.get("signature", ""),
            "avatar": self._extract_avatar_url(user_info),
            # 统计信息（从statistics字段提取）
            "liked_count": str(interact_info.get("digg_count", 0)),
            "comment_count": str(interact_info.get("comment_count", 0)),
            "share_count": str(interact_info.get("share_count", 0)),
            "collected_count": str(interact_info.get("collect_count", 0)),
            # 位置信息
            "ip_location": aweme_item.get("ip_label", ""),
            # URL信息
            "aweme_url": self._extract_aweme_detail_url(aweme_item),
            "cover_url": self._extract_cover_url(aweme_item),
            "video_download_url": self._extract_video_download_url(aweme_item, by_mobile),
            # 其他信息
            "source_keyword": aweme_item.get("source_keyword", ""),
        }

        return aweme_data

    def _extract_aweme_detail_url(self, aweme_item: Dict[str, Any]) -> str:
        """
        提取视频详情页URL

        Args:
            aweme_item: 视频数据字典

        Returns:
            str: 视频详情URL
        """
        share_url = aweme_item.get("share_url", "")
        if share_url:
            return share_url

        # 如果没有share_url，构造一个基础的抖音链接
        aweme_id = aweme_item.get("aweme_id", "")
        if aweme_id:
            return f"https://www.douyin.com/video/{aweme_id}"

        return ""
