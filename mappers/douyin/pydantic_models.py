"""
Standardized Pydantic data models for Douyin data.
"""

from datetime import datetime
from typing import List, Optional

from pydantic import BaseModel, Field, RootModel


class DouyinVideoAuthor(BaseModel):
    """抖音视频作者模型"""

    author_id: str = Field(..., description="作者ID")
    author_name: str = Field(..., description="作者名称")
    author_avatar: str = Field(..., description="作者头像")


class DouyinVideoStatistics(BaseModel):
    """抖音视频统计数据模型"""

    like_count: int = Field(0, description="点赞数")
    comment_count: int = Field(0, description="评论数")
    share_count: int = Field(0, description="分享数")
    collect_count: int = Field(0, description="收藏数")


class DouyinVideoMedia(BaseModel):
    """抖音视频媒体数据模型"""

    video_download_url: Optional[str] = Field(None, description="视频下载地址")
    play_url: Optional[str] = Field(None, description="视频播放地址")
    cover_url: Optional[str] = Field(None, description="视频封面地址")


class DouyinVideoDataItem(BaseModel):
    """抖音视频标准化数据模型"""

    # 基础必需字段
    aweme_id: str = Field(..., description="视频ID")
    aweme_type: Optional[str] = Field(default="0", description="视频类型")
    title: Optional[str] = Field(None, description="视频标题")
    desc: Optional[str] = Field(None, description="视频描述")
    create_time: datetime = Field(..., description="视频发布时间")

    # 用户信息字段
    user_id: Optional[str] = Field(None, description="用户ID")
    sec_uid: Optional[str] = Field(None, description="加密用户ID")
    short_user_id: Optional[str] = Field(None, description="短用户ID")
    user_unique_id: Optional[str] = Field(None, description="用户唯一ID")
    nickname: Optional[str] = Field(None, description="用户昵称")
    avatar: Optional[str] = Field(None, description="用户头像")
    user_signature: Optional[str] = Field(None, description="用户签名")
    ip_location: Optional[str] = Field(None, description="IP归属地")

    # 统计信息字段（字符串类型以匹配数据库模型）
    liked_count: Optional[str] = Field(None, description="点赞数")
    comment_count: Optional[str] = Field(None, description="评论数")
    share_count: Optional[str] = Field(None, description="分享数")
    collected_count: Optional[str] = Field(None, description="收藏数")

    # 媒体信息字段
    aweme_url: Optional[str] = Field(None, description="视频链接")
    cover_url: Optional[str] = Field(None, description="视频封面")
    video_download_url: Optional[str] = Field(None, description="视频下载链接")

    # 其他字段
    source_keyword: str = Field("", description="数据来源关键字")
    group_id_str: Optional[str] = Field(None, description="RPC特有字段")


class DouyinVideoData(RootModel):
    """抖音视频列表模型"""

    root: List[DouyinVideoDataItem]

    def __iter__(self):
        return iter(self.root)

    def __getitem__(self, item):
        return self.root[item]
