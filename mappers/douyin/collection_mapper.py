"""
Collection data mapper for converting VideoInfo to DouyinAweme model.
"""

from typing import List

from mappers.douyin.pydantic_models import DouyinVideoData, DouyinVideoDataItem
from rpc.douyin.schemas import VideoInfo
from utils.time_utils import safe_convert_to_datetime


class CollectionDataMapper:
    """
    收藏夹数据映射器，处理从收藏夹API获取的VideoInfo数据转换为DouyinAweme模型
    """

    @staticmethod
    def map_video_info_to_douyin_aweme(video: VideoInfo) -> DouyinVideoDataItem:
        """
        将VideoInfo对象映射为DouyinAweme模型实例

        Args:
            video: VideoInfo对象

        Returns:
            DouyinVideoDataItem: DouyinVideoDataItem模型实例
        """
        # 获取用户信息
        user_info = video.author

        # 获取统计信息
        interact_info = video.statistics

        video_data = {
            # 用户信息
            "user_id": str(video.author_user_id) if video.author_user_id else None,
            "sec_uid": user_info.sec_uid if user_info else None,
            "short_user_id": getattr(user_info, "short_id", None) if user_info else None,
            "user_unique_id": user_info.unique_id if user_info else None,
            "nickname": user_info.nickname if user_info else None,
            "avatar": CollectionDataMapper._extract_avatar_url(user_info),
            "user_signature": getattr(user_info, "signature", None) if user_info else None,
            "ip_location": getattr(video, "ip_label", None),
            # 视频信息
            "aweme_id": video.aweme_id,
            "aweme_type": str(video.aweme_type),
            "title": (video.desc or "")[:1024] if video.desc else None,  # 限制标题长度
            "desc": video.desc or None,
            "create_time": CollectionDataMapper._convert_create_time(video.create_time),
            # 统计信息
            "liked_count": str(interact_info.digg_count if interact_info else 0),
            "comment_count": str(interact_info.comment_count if interact_info else 0),
            "share_count": str(interact_info.share_count if interact_info else 0),
            "collected_count": str(getattr(interact_info, "collect_count", 0) if interact_info else 0),
            # 媒体信息
            "aweme_url": CollectionDataMapper._extract_aweme_detail_url(video),
            "cover_url": CollectionDataMapper._extract_content_cover_url(video),
            "video_download_url": CollectionDataMapper._extract_video_download_url(video),
            # 搜索来源
            "source_keyword": "",  # 默认为空字符串
        }

        return DouyinVideoDataItem(**video_data)

    @staticmethod
    def _convert_create_time(create_time):
        """
        转换创建时间为 datetime 对象

        Args:
            create_time: 原始时间数据

        Returns:
            datetime: 转换后的 datetime 对象
        """
        from utils.time_utils import safe_convert_to_datetime

        return safe_convert_to_datetime(create_time)

    @staticmethod
    def map_video_infos_to_douyin_awemes(videos: List[VideoInfo]) -> List[DouyinVideoDataItem]:
        """
        将VideoInfo对象列表映射为DouyinAweme模型实例列表

        Args:
            videos: VideoInfo对象列表

        Returns:
            List[DouyinVideoDataItem]: DouyinVideoDataItem模型实例列表
        """
        return [CollectionDataMapper.map_video_info_to_douyin_aweme(video) for video in videos]

    @staticmethod
    def _extract_avatar_url(user_info) -> str:
        """
        提取用户头像URL

        Args:
            user_info: 用户信息对象

        Returns:
            str: 头像URL，如果没有则返回None
        """
        if not user_info:
            return None

        # 尝试获取 avatar_thumb 属性，由于 VideoAuthor 使用 extra="allow"，
        # 某些属性可能不存在，所以仍需要使用 getattr
        avatar_thumb = getattr(user_info, "avatar_thumb", {})
        if isinstance(avatar_thumb, dict):
            url_list = avatar_thumb.get("url_list", [])
            if url_list and len(url_list) > 0:
                return str(url_list[0])

        return None

    @staticmethod
    def _extract_aweme_detail_url(video: VideoInfo) -> str:
        """
        提取视频详情页URL

        Args:
            video: VideoInfo对象

        Returns:
            str: 视频详情页URL，如果没有则返回None
        """
        if video.aweme_id:
            return f"https://www.douyin.com/video/{video.aweme_id}"
        return None

    @staticmethod
    def _extract_content_cover_url(video: VideoInfo) -> str:
        """
        提取视频封面URL

        Args:
            video: VideoInfo对象

        Returns:
            str: 视频封面URL，如果没有则返回None
        """
        # 直接访问 video 属性，它是 Optional[VideoDetail] 类型
        if video.video:
            # VideoDetail 使用 extra="allow"，cover 属性可能不存在
            cover_info = getattr(video.video, "cover", {})
            if isinstance(cover_info, dict):
                url_list = cover_info.get("url_list", [])
                if url_list and len(url_list) > 0:
                    return str(url_list[0])

        return None

    @staticmethod
    def _extract_video_download_url(video: VideoInfo) -> str:
        """
        提取视频下载URL

        Args:
            video: VideoInfo对象

        Returns:
            str: 视频下载URL，如果没有则返回None
        """
        # 直接访问 video 属性，它是 Optional[VideoDetail] 类型
        if video.video:
            # VideoDetail 使用 extra="allow"，播放地址属性可能不存在
            video_detail = video.video
            url_h264_list = (
                getattr(video_detail, "play_addr_h264", {}).get("url_list", [])
                if hasattr(video_detail, "play_addr_h264")
                else []
            )
            url_256_list = (
                getattr(video_detail, "play_addr_256", {}).get("url_list", [])
                if hasattr(video_detail, "play_addr_256")
                else []
            )
            url_list = (
                getattr(video_detail, "play_addr", {}).get("url_list", []) if hasattr(video_detail, "play_addr") else []
            )
            actual_url_list = url_h264_list or url_256_list or url_list

            if actual_url_list and len(actual_url_list) > 0:
                return str(actual_url_list[-1])

        return None
