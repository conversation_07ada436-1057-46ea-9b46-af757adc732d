"""
RPC data mapper for converting RPC API response data to standard format.
"""

from typing import Any, Dict

from .base import BaseDataMapper
from .exceptions import MappingException, ValidationException
from .pydantic_models import (
    DouyinVideoAuthor,
    DouyinVideoData,
    DouyinVideoDataItem,
    DouyinVideoMedia,
    DouyinVideoStatistics,
)


class RPCDataMapper(BaseDataMapper):
    """
    RPC数据映射器，处理从RPC API获取的数据
    """

    def map_to_standard_format(self, raw_data: Dict[str, Any]) -> DouyinVideoDataItem:
        """
        将RPC原始数据映射为标准格式

        Args:
            raw_data: RPC原始数据字典

        Returns:
            DouyinVideoDataItem: 标准格式的数据模型

        Raises:
            MappingException: 映射过程中发生错误
        """
        try:
            if not self.validate_raw_data(raw_data):
                raise ValidationException(
                    "RPC data validation failed",
                    validation_type="rpc_data",
                    failed_fields=["aweme_id"],
                    context={"mapper_type": "rpc"},
                )

            # 提取各部分数据
            basic_info = self.extract_basic_info(raw_data)
            user_info = self.extract_user_info(raw_data)
            statistics = self.extract_statistics(raw_data)
            media_urls = self.extract_media_urls(raw_data)
            rpc_specific = self._extract_rpc_specific_data(raw_data)

            # 组装 Pydantic 模型
            # DouyinVideoDataItem 使用平铺结构，不包含嵌套的 author、statistics、media 字段
            standard_data = DouyinVideoDataItem(
                **basic_info,
                **user_info,  # 平铺用户信息字段
                **{f"liked_count": str(statistics.get("like_count", 0))},  # 转换统计信息为字符串格式
                **{f"comment_count": str(statistics.get("comment_count", 0))},
                **{f"share_count": str(statistics.get("share_count", 0))},
                **{f"collected_count": str(statistics.get("collect_count", 0))},
                **{
                    k: v for k, v in media_urls.items() if k in ["aweme_url", "cover_url", "video_download_url"]
                },  # 只包含媒体URL字段
                source_keyword="rpc",
                aweme_type=rpc_specific.get("aweme_type"),
                group_id_str=rpc_specific.get("group_id_str"),
            )

            return standard_data

        except Exception as e:
            if isinstance(e, (ValidationException, MappingException)):
                raise

            raise MappingException(
                f"Failed to map RPC data: {str(e)}",
                mapper_type="rpc",
                failed_field="unknown",
                raw_data_sample=str(raw_data)[:200],
                context={"original_error": str(e)},
            )

    def validate_raw_data(self, raw_data: Dict[str, Any]) -> bool:
        """
        验证RPC原始数据的有效性

        Args:
            raw_data: 原始数据字典

        Returns:
            bool: 数据是否有效
        """
        if not isinstance(raw_data, dict):
            return False

        # 检查必需字段 - RPC数据可能在不同的层级
        # 可能是直接的aweme_detail，也可能包装在aweme_detail字段中
        aweme_id = (
            self._safe_extract(raw_data, "aweme_id")
            or self._safe_extract(raw_data, ["aweme_detail", "aweme_id"])
            or self._safe_extract(raw_data, ["data", "aweme_id"])
        )

        return bool(aweme_id)

    def _extract_rpc_specific_data(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        提取RPC特有的数据

        Args:
            raw_data: 原始数据字典

        Returns:
            Dict[str, Any]: RPC特有数据字典
        """
        # 处理RPC特有的视频URL提取
        video_urls = self._process_rpc_video_urls(raw_data)

        # 提取RPC特有字段
        rpc_data = {
            "video_download_url": video_urls.get("download_url", ""),
        }

        # 如果video_download_url为空，使用play_addr作为备选
        if not rpc_data["video_download_url"]:
            rpc_data["video_download_url"] = video_urls.get("play_url", "")

        # 提取RPC响应中的额外信息
        rpc_data.update(self._extract_rpc_metadata(raw_data))

        return rpc_data

    def _process_rpc_video_urls(self, raw_data: Dict[str, Any]) -> Dict[str, str]:
        """
        处理RPC视频URL

        Args:
            raw_data: 原始数据字典

        Returns:
            Dict[str, str]: 包含各种URL的字典
        """
        # RPC数据可能在aweme_detail字段中
        aweme_detail = self._safe_extract(raw_data, "aweme_detail", raw_data)
        video_info = self._safe_extract(aweme_detail, "video", {})

        if not isinstance(video_info, dict):
            return {}

        urls = {}

        # RPC响应通常包含完整的视频URL信息
        # 按优先级顺序尝试提取

        # 1. 优先使用H.264格式（最高质量）
        play_addr_h264 = video_info.get("play_addr_h264", {})
        if isinstance(play_addr_h264, dict):
            h264_url_list = play_addr_h264.get("url_list", [])
            if h264_url_list and len(h264_url_list) >= 2:  # RPC通常要求至少2个URL
                urls["download_url"] = h264_url_list[-1]

        # 2. 尝试使用download_addr
        if not urls.get("download_url"):
            download_addr = video_info.get("download_addr", {})
            if isinstance(download_addr, dict):
                download_url_list = download_addr.get("url_list", [])
                if download_url_list:
                    urls["download_url"] = download_url_list[-1]

        # 3. 使用通用play_addr作为备选
        play_addr = video_info.get("play_addr", {})
        if isinstance(play_addr, dict):
            play_url_list = play_addr.get("url_list", [])
            if play_url_list and len(play_url_list) >= 2:  # RPC通常要求至少2个URL
                if not urls.get("download_url"):
                    urls["download_url"] = play_url_list[-1]
                urls["play_url"] = play_url_list[-1]

        # 4. 尝试256p格式作为最后备选
        if not urls.get("download_url") and not urls.get("play_url"):
            play_addr_256 = video_info.get("play_addr_256", {})
            if isinstance(play_addr_256, dict):
                url_256_list = play_addr_256.get("url_list", [])
                if url_256_list and len(url_256_list) >= 2:
                    urls["play_url"] = url_256_list[-1]

        return urls

    def _extract_rpc_metadata(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        提取RPC特有的元数据

        Args:
            raw_data: 原始数据字典

        Returns:
            Dict[str, Any]: RPC元数据字典
        """
        metadata = {}

        # RPC数据可能在aweme_detail字段中
        aweme_detail = self._safe_extract(raw_data, "aweme_detail", raw_data)

        # 提取aweme_type
        aweme_type = self._safe_extract(aweme_detail, "aweme_type", 0)
        if aweme_type:
            metadata["aweme_type"] = str(aweme_type)

        # 提取group_id_str（RPC特有字段）
        group_id_str = self._safe_extract(aweme_detail, "group_id_str", "")
        if group_id_str:
            metadata["group_id_str"] = group_id_str

        # 提取视频时长信息
        video_info = self._safe_extract(aweme_detail, "video", {})
        if isinstance(video_info, dict):
            duration = video_info.get("duration", 0)
            if duration:
                metadata["duration"] = duration

            # 提取视频尺寸信息
            width = video_info.get("width", 0)
            height = video_info.get("height", 0)
            if width and height:
                metadata["video_width"] = width
                metadata["video_height"] = height

        # 提取音乐信息
        music_info = self._safe_extract(aweme_detail, "music", {})
        if isinstance(music_info, dict):
            music_title = music_info.get("title", "")
            if music_title:
                metadata["music_title"] = music_title

            music_id = music_info.get("id", "")
            if music_id:
                metadata["music_id"] = str(music_id)

        # 提取risk_infos（RPC特有的风险信息）
        risk_infos = self._safe_extract(aweme_detail, "risk_infos", {})
        if isinstance(risk_infos, dict) and risk_infos:
            metadata["risk_infos"] = str(risk_infos)

        return metadata

    def _extract_video_download_url(self, aweme_detail: Dict[str, Any], by_mobile: bool = False) -> str:
        """
        重写父类方法，专门处理RPC视频下载URL提取

        Args:
            aweme_detail: 抖音视频详情数据
            by_mobile: 是否为移动端数据（RPC默认False）

        Returns:
            str: 视频下载地址
        """
        if not aweme_detail:
            return ""

        video_urls = self._process_rpc_video_urls(
            {"aweme_detail": aweme_detail} if "video" in aweme_detail else aweme_detail
        )

        # 优先使用下载地址
        download_url = video_urls.get("download_url", "")
        if download_url:
            return download_url

        # 使用播放地址作为备选
        play_url = video_urls.get("play_url", "")
        if play_url:
            return play_url

        # 如果RPC特有逻辑都没有找到，回退到父类方法
        return super()._extract_video_download_url(aweme_detail, by_mobile=False)

    def extract_basic_info(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        重写父类方法，处理RPC特有的基础信息格式

        Args:
            raw_data: 原始数据字典

        Returns:
            Dict[str, Any]: 基础信息字典
        """
        # RPC数据可能在aweme_detail字段中
        aweme_detail = self._safe_extract(raw_data, "aweme_detail", raw_data)

        return {
            "aweme_id": self._safe_extract(aweme_detail, "aweme_id", ""),
            "title": self._safe_extract(aweme_detail, "desc", ""),
            "desc": self._safe_extract(aweme_detail, "desc", ""),
            "aweme_type": str(self._safe_extract(aweme_detail, "aweme_type", "0")),
            "create_time": self._convert_to_datetime(self._safe_extract(aweme_detail, "create_time")),
        }

    def extract_user_info(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        重写父类方法，处理RPC特有的用户信息格式

        Args:
            raw_data: 原始数据字典

        Returns:
            Dict[str, Any]: 用户信息字典
        """
        # RPC数据可能在aweme_detail字段中
        aweme_detail = self._safe_extract(raw_data, "aweme_detail", raw_data)
        author = self._safe_extract(aweme_detail, "author", {})

        if not isinstance(author, dict):
            author = {}

        return {
            "user_id": self._safe_extract(author, ["uid", "user_id"], ""),
            "sec_uid": self._safe_extract(author, "sec_uid", ""),
            "short_user_id": self._safe_extract(author, ["short_id", "short_user_id"], ""),
            "user_unique_id": self._safe_extract(author, "unique_id", ""),
            "nickname": self._safe_extract(author, "nickname", ""),
            "avatar": self._extract_avatar_url(author),
            "user_signature": self._safe_extract(author, ["signature", "desc"], ""),
            "ip_location": self._safe_extract(aweme_detail, "ip_location", ""),
        }

    def extract_statistics(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        重写父类方法，处理RPC特有的统计数据格式

        Args:
            raw_data: 原始数据字典

        Returns:
            Dict[str, Any]: 统计信息字典
        """
        # RPC数据可能在aweme_detail字段中
        aweme_detail = self._safe_extract(raw_data, "aweme_detail", raw_data)
        statistics = self._safe_extract(aweme_detail, "statistics", {})

        if not isinstance(statistics, dict):
            statistics = {}

        # RPC的统计数据格式通常很标准
        return {
            "liked_count": str(self._safe_extract(statistics, "digg_count", 0)),
            "comment_count": str(self._safe_extract(statistics, "comment_count", 0)),
            "share_count": str(self._safe_extract(statistics, "share_count", 0)),
            "collected_count": str(self._safe_extract(statistics, ["collect_count", "collected_count"], 0)),
        }

    def convert_video_info_to_aweme_data(self, video_info: Any) -> Any:
        """
        将RPC返回的VideoInfo对象转换为AwemeDataItem

        Args:
            video_info: RPC返回的VideoInfo对象

        Returns:
            AwemeDataItem: 转换后的数据模型
        """
        try:
            # 基础信息
            aweme_data = {
                "aweme_id": getattr(video_info, "aweme_id", ""),
                "title": getattr(video_info, "desc", ""),
                "desc": getattr(video_info, "desc", ""),
                "create_time": self._convert_to_datetime(getattr(video_info, "create_time", 0)),
                "aweme_type": str(getattr(video_info, "aweme_type", "0")),
            }

            # 作者信息
            author = getattr(video_info, "author", None)
            if author:
                # 提取头像URL
                avatar_url = ""
                avatar_thumb = getattr(author, "avatar_thumb", None)
                if avatar_thumb:
                    if isinstance(avatar_thumb, dict):
                        # avatar_thumb是字典
                        url_list = avatar_thumb.get("url_list", [])
                        if url_list and len(url_list) > 0:
                            avatar_url = str(url_list[0])
                    elif hasattr(avatar_thumb, "url_list") and avatar_thumb.url_list:
                        # avatar_thumb是对象
                        if len(avatar_thumb.url_list) > 0:
                            avatar_url = str(avatar_thumb.url_list[0])

                aweme_data.update(
                    {
                        "user_id": str(getattr(author, "uid", "")),
                        "sec_uid": getattr(author, "sec_uid", ""),
                        "short_user_id": getattr(author, "short_id", ""),
                        "user_unique_id": getattr(author, "unique_id", ""),
                        "nickname": getattr(author, "nickname", ""),
                        "avatar": avatar_url,
                        "user_signature": getattr(author, "signature", ""),
                    }
                )

            # 统计信息
            statistics = getattr(video_info, "statistics", None)
            if statistics:
                aweme_data.update(
                    {
                        "liked_count": str(getattr(statistics, "digg_count", 0)),
                        "comment_count": str(getattr(statistics, "comment_count", 0)),
                        "share_count": str(getattr(statistics, "share_count", 0)),
                        "collected_count": str(getattr(statistics, "collect_count", 0)),
                    }
                )

            # 视频链接信息
            aweme_data.update(
                {
                    "aweme_url": getattr(video_info, "share_url", ""),
                }
            )

            # 音乐信息
            music = getattr(video_info, "music", None)
            if music:
                aweme_data.update(
                    {
                        "music_id": str(getattr(music, "id", "")),
                        "music_title": getattr(music, "title", ""),
                        "music_author": getattr(music, "author", ""),
                    }
                )

            # 导入AwemeDataItem模型
            from schemas.douyin import AwemeDataItem

            return AwemeDataItem(**aweme_data)

        except Exception as e:
            # 返回基础数据，确保至少有aweme_id
            aweme_data = {
                "aweme_id": getattr(video_info, "aweme_id", ""),
                "title": getattr(video_info, "desc", ""),
                "desc": getattr(video_info, "desc", ""),
                "create_time": getattr(video_info, "create_time", 0),
            }
            from schemas.douyin import AwemeDataItem
            return AwemeDataItem(**aweme_data)

    def convert_video_detail_response_to_db_model(self, rpc_response, by_mobile: bool = False) -> Dict:
        """
        将 VideoDetailResponse 转换为数据库模型数据格式

        Args:
            rpc_response: VideoDetailResponse 对象
            by_mobile: 是否为移动端数据，默认为False

        Returns:
            Dict: 符合DouyinAweme模型字段要求的数据字典
        """
        if not rpc_response.aweme_detail:
            raise ValueError("视频详情为空")

        detail = rpc_response.aweme_detail

        # 导入必要的函数
        from utils.time_utils import safe_convert_to_datetime

        # 提取作者信息和统计信息
        author = detail.author
        statistics = detail.statistics

        # 构建用户信息字典（用于兼容原有的字典格式处理函数）
        user_info = {}
        if author:
            user_info = {
                "uid": getattr(author, "uid", ""),
                "sec_uid": getattr(author, "sec_uid", ""),
                "short_id": getattr(author, "short_id", ""),
                "unique_id": getattr(author, "unique_id", ""),
                "nickname": getattr(author, "nickname", ""),
                "signature": getattr(author, "signature", ""),
                "avatar_thumb": getattr(author, "avatar_thumb", {}),
                "avatar_medium": getattr(author, "avatar_medium", {}),
                "avatar_larger": getattr(author, "avatar_larger", {}),
            }

        # 构建统计信息字典
        interact_info = {}
        if statistics:
            interact_info = {
                "digg_count": getattr(statistics, "digg_count", 0),
                "comment_count": getattr(statistics, "comment_count", 0),
                "share_count": getattr(statistics, "share_count", 0),
                "collect_count": getattr(statistics, "collect_count", 0),
            }

        # 构建视频信息字典（处理video对象或字典）
        video_info = {}
        if hasattr(detail, "video") and detail.video:
            video_obj = detail.video

            # 如果video已经是字典，直接使用
            if isinstance(video_obj, dict):
                video_info = video_obj
            else:
                # 如果video是对象，转换为字典
                # 处理封面信息
                cover_info = {}
                if hasattr(video_obj, "cover") and video_obj.cover:
                    cover_obj = video_obj.cover
                    if isinstance(cover_obj, dict):
                        cover_info = cover_obj
                    else:
                        cover_info = {"url_list": getattr(cover_obj, "url_list", [])}

                # 处理播放地址信息
                def extract_play_addr(play_addr_obj):
                    if not play_addr_obj:
                        return {}
                    if isinstance(play_addr_obj, dict):
                        return play_addr_obj
                    return {"url_list": getattr(play_addr_obj, "url_list", [])}

                video_info = {
                    "cover": cover_info,
                    "play_addr_h264": extract_play_addr(getattr(video_obj, "play_addr_h264", None)),
                    "play_addr_256": extract_play_addr(getattr(video_obj, "play_addr_256", None)),
                    "play_addr": extract_play_addr(getattr(video_obj, "play_addr", None)),
                }

        # 构建视频数据字典（用于兼容原有的字典格式处理函数）
        aweme_item = {
            "aweme_id": str(detail.aweme_id),
            "aweme_type": getattr(detail, "aweme_type", 0),
            "desc": getattr(detail, "desc", ""),
            "create_time": getattr(detail, "create_time", 0),
            "share_url": getattr(detail, "share_url", ""),
            "author": user_info,
            "statistics": interact_info,
            "video": video_info,
            "ip_label": getattr(detail, "ip_label", ""),
            "source_keyword": "",  # 这个字段通常由调用方设置
        }

        # 使用与update_douyin_aweme相同的字段映射逻辑
        aweme_data = {
            # 基本信息
            "aweme_id": str(aweme_item.get("aweme_id", "")),
            "aweme_type": str(aweme_item.get("aweme_type", "0")),
            "title": aweme_item.get("desc", "")[:1024],  # 限制标题长度
            "desc": aweme_item.get("desc", ""),
            "create_time": safe_convert_to_datetime(aweme_item.get("create_time")),
            # 用户信息（从author字段提取）
            "user_id": user_info.get("uid", user_info.get("short_id", "")),  # 兼容不同字段名
            "sec_uid": user_info.get("sec_uid", ""),
            "short_user_id": user_info.get("short_id", ""),
            "user_unique_id": user_info.get("unique_id", ""),
            "nickname": user_info.get("nickname", ""),
            "user_signature": user_info.get("signature", ""),
            "avatar": self._extract_avatar_url_from_dict(user_info),
            # 统计信息（从statistics字段提取）
            "liked_count": str(interact_info.get("digg_count", 0)),
            "comment_count": str(interact_info.get("comment_count", 0)),
            "share_count": str(interact_info.get("share_count", 0)),
            "collected_count": str(interact_info.get("collect_count", 0)),
            # 位置信息
            "ip_location": aweme_item.get("ip_label", ""),
            # URL信息
            "aweme_url": self._extract_aweme_detail_url(aweme_item),
            "cover_url": self._extract_content_cover_url(aweme_item),
            "video_download_url": self._extract_video_download_url_from_dict(aweme_item, by_mobile),
            # 其他信息
            "source_keyword": aweme_item.get("source_keyword", ""),
        }

        return aweme_data

    def _extract_avatar_url_from_dict(self, user_info: Dict[str, Any]) -> str:
        """
        从用户信息字典中提取头像URL

        Args:
            user_info: 用户信息字典

        Returns:
            str: 头像URL
        """
        # 优先使用avatar_larger
        avatar_larger = user_info.get("avatar_larger", {})
        if isinstance(avatar_larger, dict):
            url_list = avatar_larger.get("url_list", [])
            if url_list:
                return str(url_list[0])

        # 其次使用avatar_medium
        avatar_medium = user_info.get("avatar_medium", {})
        if isinstance(avatar_medium, dict):
            url_list = avatar_medium.get("url_list", [])
            if url_list:
                return str(url_list[0])

        # 最后使用avatar_thumb
        avatar_thumb = user_info.get("avatar_thumb", {})
        if isinstance(avatar_thumb, dict):
            url_list = avatar_thumb.get("url_list", [])
            if url_list:
                return str(url_list[0])

        return ""

    def _extract_aweme_detail_url(self, aweme_item: Dict[str, Any]) -> str:
        """
        提取视频详情URL

        Args:
            aweme_item: 视频数据字典

        Returns:
            str: 视频详情URL
        """
        share_url = aweme_item.get("share_url", "")
        if share_url:
            return share_url

        # 如果没有share_url，构造一个基础的抖音链接
        aweme_id = aweme_item.get("aweme_id", "")
        if aweme_id:
            return f"https://www.douyin.com/video/{aweme_id}"

        return ""

    def _extract_content_cover_url(self, aweme_item: Dict[str, Any]) -> str:
        """
        提取视频封面URL

        Args:
            aweme_item: 视频数据字典

        Returns:
            str: 封面URL
        """
        video_info = aweme_item.get("video", {})
        if not isinstance(video_info, dict):
            return ""

        cover_info = video_info.get("cover", {})
        if isinstance(cover_info, dict):
            url_list = cover_info.get("url_list", [])
            if url_list:
                return str(url_list[0])

        return ""

    def _extract_video_download_url_from_dict(self, aweme_item: Dict[str, Any], by_mobile: bool = False) -> str:
        """
        从视频数据字典中提取下载URL

        Args:
            aweme_item: 视频数据字典
            by_mobile: 是否为移动端数据

        Returns:
            str: 视频下载URL
        """
        video_info = aweme_item.get("video", {})
        if not isinstance(video_info, dict):
            return ""

        # 按优先级顺序尝试提取
        # 1. 优先使用H.264格式（最高质量）
        play_addr_h264 = video_info.get("play_addr_h264", {})
        if isinstance(play_addr_h264, dict):
            h264_url_list = play_addr_h264.get("url_list", [])
            if h264_url_list and len(h264_url_list) >= 2:
                return str(h264_url_list[-1])

        # 2. 使用通用play_addr作为备选
        play_addr = video_info.get("play_addr", {})
        if isinstance(play_addr, dict):
            play_url_list = play_addr.get("url_list", [])
            if play_url_list and len(play_url_list) >= 2:
                return str(play_url_list[-1])

        # 3. 尝试256p格式作为最后备选
        play_addr_256 = video_info.get("play_addr_256", {})
        if isinstance(play_addr_256, dict):
            url_256_list = play_addr_256.get("url_list", [])
            if url_256_list and len(url_256_list) >= 2:
                return str(url_256_list[-1])

        return ""
