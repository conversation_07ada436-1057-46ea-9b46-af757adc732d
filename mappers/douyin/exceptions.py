"""
Exception classes for Douyin data mappers.
"""

from typing import Any, Dict, Optional


class VideoFetcherException(Exception):
    """
    视频获取器基础异常类
    """

    def __init__(self, message: str, error_code: str = "", context: Optional[Dict[str, Any]] = None):
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.context = context or {}

    def __str__(self):
        if self.error_code:
            return f"[{self.error_code}] {self.message}"
        return self.message


class NetworkException(VideoFetcherException):
    """
    网络相关异常
    """

    def __init__(
        self,
        message: str,
        status_code: int = 0,
        url: str = "",
        timeout: bool = False,
        error_code: str = "NETWORK_ERROR",
        context: Optional[Dict[str, Any]] = None,
    ):
        super().__init__(message, error_code, context)
        self.status_code = status_code
        self.url = url
        self.timeout = timeout


class ParseException(VideoFetcherException):
    """
    数据解析异常
    """

    def __init__(
        self,
        message: str,
        parsing_stage: str = "",
        content_length: int = 0,
        content_type: str = "",
        error_code: str = "PARSE_ERROR",
        context: Optional[Dict[str, Any]] = None,
    ):
        super().__init__(message, error_code, context)
        self.parsing_stage = parsing_stage
        self.content_length = content_length
        self.content_type = content_type


class MappingException(VideoFetcherException):
    """
    数据映射异常
    """

    def __init__(
        self,
        message: str,
        mapper_type: str = "",
        failed_field: str = "",
        raw_data_sample: str = "",
        error_code: str = "MAPPING_ERROR",
        context: Optional[Dict[str, Any]] = None,
    ):
        super().__init__(message, error_code, context)
        self.mapper_type = mapper_type
        self.failed_field = failed_field
        self.raw_data_sample = raw_data_sample


class ValidationException(VideoFetcherException):
    """
    数据验证异常
    """

    def __init__(
        self,
        message: str,
        validation_type: str = "",
        failed_fields: Optional[list] = None,
        expected_format: str = "",
        error_code: str = "VALIDATION_ERROR",
        context: Optional[Dict[str, Any]] = None,
    ):
        super().__init__(message, error_code, context)
        self.validation_type = validation_type
        self.failed_fields = failed_fields or []
        self.expected_format = expected_format


class RetryExhaustedException(VideoFetcherException):
    """
    重试次数耗尽异常
    """

    def __init__(
        self,
        message: str,
        retry_count: int = 0,
        last_error: Optional[Exception] = None,
        error_code: str = "RETRY_EXHAUSTED",
        context: Optional[Dict[str, Any]] = None,
    ):
        super().__init__(message, error_code, context)
        self.retry_count = retry_count
        self.last_error = last_error


class FallbackExhaustedException(VideoFetcherException):
    """
    回退策略耗尽异常
    """

    def __init__(
        self,
        message: str,
        attempted_methods: Optional[list] = None,
        method_errors: Optional[Dict[str, Exception]] = None,
        error_code: str = "FALLBACK_EXHAUSTED",
        context: Optional[Dict[str, Any]] = None,
    ):
        super().__init__(message, error_code, context)
        self.attempted_methods = attempted_methods or []
        self.method_errors = method_errors or {}


class ConfigurationException(VideoFetcherException):
    """
    配置相关异常
    """

    def __init__(
        self,
        message: str,
        config_key: str = "",
        invalid_value: Any = None,
        error_code: str = "CONFIG_ERROR",
        context: Optional[Dict[str, Any]] = None,
    ):
        super().__init__(message, error_code, context)
        self.config_key = config_key
        self.invalid_value = invalid_value


class DataSourceException(VideoFetcherException):
    """
    数据源异常
    """

    def __init__(
        self,
        message: str,
        source_type: str = "",
        source_url: str = "",
        response_code: int = 0,
        error_code: str = "DATA_SOURCE_ERROR",
        context: Optional[Dict[str, Any]] = None,
    ):
        super().__init__(message, error_code, context)
        self.source_type = source_type
        self.source_url = source_url
        self.response_code = response_code
