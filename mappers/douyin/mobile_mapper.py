"""
Mobile data mapper for converting mobile share page data to standard format.
"""

from typing import Any, Dict

from .base import BaseDataMapper
from .exceptions import MappingException, ValidationException


class MobileDataMapper(BaseDataMapper):
    """
    移动端数据映射器，处理从移动端分享页面获取的数据
    """

    def map_to_standard_format(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        将移动端原始数据映射为标准格式

        Args:
            raw_data: 移动端原始数据字典

        Returns:
            Dict[str, Any]: 标准格式的数据字典

        Raises:
            MappingException: 映射过程中发生错误
        """
        try:
            if not self.validate_raw_data(raw_data):
                raise ValidationException(
                    "Mobile data validation failed",
                    validation_type="mobile_data",
                    failed_fields=["aweme_id"],
                    context={"mapper_type": "mobile"},
                )

            # 提取各部分数据
            basic_info = self.extract_basic_info(raw_data)
            user_info = self.extract_user_info(raw_data)
            statistics = self.extract_statistics(raw_data)
            media_urls = self.extract_media_urls(raw_data)
            mobile_specific = self._extract_mobile_specific_data(raw_data)

            # 合并所有数据
            standard_data = {
                **basic_info,
                **user_info,
                **statistics,
                **media_urls,
                **mobile_specific,
                "source_keyword": "mobile_share",
            }

            return standard_data

        except Exception as e:
            if isinstance(e, (ValidationException, MappingException)):
                raise

            raise MappingException(
                f"Failed to map mobile data: {str(e)}",
                mapper_type="mobile",
                failed_field="unknown",
                raw_data_sample=str(raw_data)[:200],
                context={"original_error": str(e)},
            )

    def validate_raw_data(self, raw_data: Dict[str, Any]) -> bool:
        """
        验证移动端原始数据的有效性

        Args:
            raw_data: 原始数据字典

        Returns:
            bool: 数据是否有效
        """
        if not isinstance(raw_data, dict):
            return False

        # 检查必需字段
        required_fields = ["aweme_id"]
        return self._validate_required_fields(raw_data, required_fields)

    def _extract_mobile_specific_data(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        提取移动端特有的数据

        Args:
            raw_data: 原始数据字典

        Returns:
            Dict[str, Any]: 移动端特有数据字典
        """
        # 处理移动端特有的视频URL提取
        video_urls = self._process_mobile_video_urls(raw_data)

        # 提取移动端特有字段
        mobile_data = {
            "video_download_url": video_urls.get("download_url", ""),
        }

        # 如果video_download_url为空，使用play_addr作为备选
        if not mobile_data["video_download_url"]:
            mobile_data["video_download_url"] = video_urls.get("play_url", "")

        return mobile_data

    def _process_mobile_video_urls(self, raw_data: Dict[str, Any]) -> Dict[str, str]:
        """
        处理移动端视频URL

        Args:
            raw_data: 原始数据字典

        Returns:
            Dict[str, str]: 包含各种URL的字典
        """
        video_info = self._safe_extract(raw_data, "video", {})
        if not isinstance(video_info, dict):
            return {}

        urls = {}

        # 提取下载地址
        download_addr = video_info.get("download_addr", {})
        if isinstance(download_addr, dict):
            download_url_list = download_addr.get("url_list", [])
            if download_url_list:
                urls["download_url"] = download_url_list[-1]

        # 提取播放地址作为备选
        play_addr = video_info.get("play_addr", {})
        if isinstance(play_addr, dict):
            play_url_list = play_addr.get("url_list", [])
            if play_url_list:
                urls["play_url"] = play_url_list[-1]

        # 如果都没有，尝试从其他字段获取
        if not urls.get("download_url") and not urls.get("play_url"):
            # 尝试从play_addr_h264获取
            play_addr_h264 = video_info.get("play_addr_h264", {})
            if isinstance(play_addr_h264, dict):
                h264_url_list = play_addr_h264.get("url_list", [])
                if h264_url_list:
                    urls["play_url"] = h264_url_list[-1]

            # 尝试从play_addr_256获取
            if not urls.get("play_url"):
                play_addr_256 = video_info.get("play_addr_256", {})
                if isinstance(play_addr_256, dict):
                    url_256_list = play_addr_256.get("url_list", [])
                    if url_256_list:
                        urls["play_url"] = url_256_list[-1]

        return urls

    def _extract_video_download_url(self, aweme_detail: Dict[str, Any], by_mobile: bool = True) -> str:
        """
        重写父类方法，专门处理移动端视频下载URL提取

        Args:
            aweme_detail: 抖音视频详情数据
            by_mobile: 是否为移动端数据（默认True）

        Returns:
            str: 视频下载地址
        """
        if not aweme_detail:
            return ""

        video_urls = self._process_mobile_video_urls(aweme_detail)

        # 优先使用下载地址
        download_url = video_urls.get("download_url", "")
        if download_url:
            return download_url

        # 使用播放地址作为备选
        play_url = video_urls.get("play_url", "")
        if play_url:
            return play_url

        # 如果移动端特有逻辑都没有找到，回退到父类方法
        return super()._extract_video_download_url(aweme_detail, by_mobile=True)

    def extract_statistics(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        重写父类方法，处理移动端特有的统计数据格式

        Args:
            raw_data: 原始数据字典

        Returns:
            Dict[str, Any]: 统计信息字典
        """
        statistics = self._safe_extract(raw_data, "statistics", {})
        if not isinstance(statistics, dict):
            statistics = {}

        # 移动端可能使用不同的字段名
        return {
            "liked_count": str(self._safe_extract(statistics, ["digg_count", "like_count"], 0)),
            "comment_count": str(self._safe_extract(statistics, "comment_count", 0)),
            "share_count": str(self._safe_extract(statistics, "share_count", 0)),
            "collected_count": str(self._safe_extract(statistics, ["collect_count", "collected_count"], 0)),
        }

    def extract_user_info(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        重写父类方法，处理移动端特有的用户信息格式

        Args:
            raw_data: 原始数据字典

        Returns:
            Dict[str, Any]: 用户信息字典
        """
        author = self._safe_extract(raw_data, "author", {})
        if not isinstance(author, dict):
            author = {}

        return {
            "user_id": self._safe_extract(author, ["uid", "user_id"], ""),
            "sec_uid": self._safe_extract(author, "sec_uid", ""),
            "short_user_id": self._safe_extract(author, ["short_id", "short_user_id"], ""),
            "user_unique_id": self._safe_extract(author, "unique_id", ""),
            "nickname": self._safe_extract(author, "nickname", ""),
            "avatar": self._extract_avatar_url(author),
            "user_signature": self._safe_extract(author, ["signature", "desc"], ""),
            "ip_location": self._safe_extract(raw_data, "ip_location", ""),
        }
