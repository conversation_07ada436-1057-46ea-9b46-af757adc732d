"""
Jingxuan data extractor mapper for converting jingxuan JSON data to AwemeItem format.
"""

import json
import time
from typing import Dict, Any, Optional
from dataclasses import dataclass

from .base import BaseDataMapper
from .exceptions import MappingException, ValidationException
from utils.douyin.extract.douyin_data_extractor import AwemeItem, AuthorInfo as Author, MusicInfo as Music, StatisticsInfo as Statistics, VideoInfo as Video, TextExtraInfo as TextExtra


@dataclass
class JingxuanProcessResult:
    """精选数据处理结果"""
    success: bool
    aweme_item: Optional[AwemeItem] = None
    error_message: Optional[str] = None
    processing_time: float = 0.0


class JingxuanDataExtractorMapper(BaseDataMapper):
    """
    精选数据提取映射器，专门处理从精选页面获取的JSON数据并转换为AwemeItem格式
    """

    def map_to_standard_format(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        将精选JSON数据映射为标准格式

        Args:
            raw_data: 精选JSON数据字典

        Returns:
            Dict[str, Any]: 标准格式的数据字典

        Raises:
            MappingException: 映射过程中发生错误
        """
        try:
            # 首先转换为AwemeItem
            aweme_item = self.convert_to_aweme_item(raw_data)
            
            # 然后转换为标准字典格式
            return self._aweme_item_to_dict(aweme_item)
            
        except Exception as e:
            if isinstance(e, (ValidationException, MappingException)):
                raise

            raise MappingException(
                f"Failed to map jingxuan extractor data: {str(e)}",
                mapper_type="jingxuan_extractor",
                failed_field="unknown",
                raw_data_sample=str(raw_data)[:200],
                context={"original_error": str(e)},
            )

    def convert_to_aweme_item(self, json_data: Dict[str, Any]) -> AwemeItem:
        """
        将精选JSON数据转换为AwemeItem格式

        Args:
            json_data: 精选JSON数据

        Returns:
            AwemeItem: 转换后的AwemeItem对象

        Raises:
            ValidationException: 数据验证失败
            MappingException: 数据转换失败
        """
        start_time = time.time()
        
        try:
            # 1. 输入验证
            if not json_data:
                raise ValidationException(
                    "JSON数据为空",
                    validation_type="input_validation",
                    failed_fields=["json_data"],
                    context={"mapper_type": "jingxuan_extractor"}
                )
            
            if not isinstance(json_data, dict):
                raise ValidationException(
                    "JSON数据必须是字典类型",
                    validation_type="input_validation",
                    failed_fields=["json_data"],
                    context={"mapper_type": "jingxuan_extractor", "data_type": type(json_data)}
                )
            
            # 2. 提取aweme数据
            aweme_data = self._extract_aweme_data_from_json(json_data)
            
            # 3. 验证必需字段
            self._validate_required_fields_detailed(aweme_data)
            
            # 4. 构建AwemeItem对象
            aweme_item = self._build_aweme_item_with_error_handling(aweme_data)
            
            # 5. 验证结果
            self._validate_aweme_item(aweme_item)
            
            return aweme_item
            
        except Exception as e:
            if isinstance(e, (ValidationException, MappingException)):
                raise
            
            raise MappingException(
                f"转换精选数据失败: {str(e)}",
                mapper_type="jingxuan_extractor",
                failed_field="unknown",
                raw_data_sample=str(json_data)[:200] if json_data else "",
                context={"original_error": str(e)}
            )
        finally:
            processing_time = time.time() - start_time

    def _extract_aweme_data_from_json(self, json_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        从JSON数据中提取aweme数据

        Args:
            json_data: 原始JSON数据

        Returns:
            Dict[str, Any]: 提取的aweme数据
        """
        # 识别JSON结构类型
        structure_type = self._identify_json_structure_type(json_data)
        
        if structure_type == "aweme_info_wrapped":
            # 直接包含aweme_info的结构
            return json_data.get("aweme_info", {})
        elif structure_type == "aweme_list_wrapped":
            # 包含aweme_list的结构
            aweme_list = json_data.get("aweme_list", [])
            if aweme_list and isinstance(aweme_list, list):
                return aweme_list[0]  # 返回第一个元素
        elif structure_type == "direct_aweme":
            # 直接是aweme数据
            return json_data
        else:
            # 默认尝试直接使用
            return json_data

    def _identify_json_structure_type(self, json_data: Dict[str, Any]) -> str:
        """
        识别JSON结构类型

        Args:
            json_data: JSON数据

        Returns:
            str: 结构类型标识
        """
        if "aweme_info" in json_data:
            return "aweme_info_wrapped"
        elif "aweme_list" in json_data:
            return "aweme_list_wrapped"
        elif "aweme_id" in json_data:
            return "direct_aweme"
        else:
            return "unknown"

    def _validate_required_fields_detailed(self, aweme_data: Dict[str, Any]) -> None:
        """
        详细验证必需字段

        Args:
            aweme_data: aweme数据

        Raises:
            ValidationException: 字段验证失败
        """
        required_fields = ["aweme_id"]
        missing_fields = []
        
        for field in required_fields:
            if field not in aweme_data or not aweme_data[field]:
                missing_fields.append(field)
        
        if missing_fields:
            raise ValidationException(
                f"缺少必需字段: {missing_fields}",
                validation_type="required_fields",
                failed_fields=missing_fields,
                context={"mapper_type": "jingxuan_extractor"}
            )

    def _build_aweme_item_with_error_handling(self, aweme_data: Dict[str, Any]) -> AwemeItem:
        """
        带错误处理地构建AwemeItem

        Args:
            aweme_data: aweme数据

        Returns:
            AwemeItem: 构建的AwemeItem对象
        """
        try:
            # 构建基本信息
            basic_info = self._build_basic_info(aweme_data)
            
            # 构建作者信息
            author_info = self._build_author_info(aweme_data)
            
            # 构建音乐信息
            music_info = self._build_music_info(aweme_data)
            
            # 构建视频信息
            video_info = self._build_video_info(aweme_data)
            
            # 构建统计信息
            statistics_info = self._build_statistics_info(aweme_data)
            
            # 构建文本额外信息
            text_extra_info = self._build_text_extra_info(aweme_data)
            
            # 创建AwemeItem对象
            aweme_item = AwemeItem(
                aweme_id=basic_info.get("aweme_id"),
                desc=basic_info.get("desc", ""),
                create_time=basic_info.get("create_time", 0),
                author=author_info,
                music=music_info,
                cha_list=aweme_data.get("cha_list"),
                video=video_info,
                statistics=statistics_info,
                text_extra=text_extra_info,
                video_labels=aweme_data.get("video_labels"),
                aweme_type=basic_info.get("aweme_type", 0),
                image_infos=aweme_data.get("image_infos"),
                risk_infos=aweme_data.get("risk_infos", {}),
                comment_list=aweme_data.get("comment_list"),
                geofencing=aweme_data.get("geofencing"),
                video_text=aweme_data.get("video_text"),
                label_top_text=aweme_data.get("label_top_text"),
                promotions=aweme_data.get("promotions"),
                long_video=aweme_data.get("long_video"),
                images=aweme_data.get("images"),
                group_id_str=aweme_data.get("group_id_str", ""),
                chapter_list=aweme_data.get("chapter_list"),
                interaction_stickers=aweme_data.get("interaction_stickers"),
                img_bitrate=aweme_data.get("img_bitrate"),
                chapter_bar_color=aweme_data.get("chapter_bar_color"),
            )
            
            return aweme_item
            
        except Exception as e:
            raise MappingException(
                f"构建AwemeItem失败: {str(e)}",
                mapper_type="jingxuan_extractor",
                failed_field="aweme_item_construction",
                context={"original_error": str(e)}
            )

    def _build_basic_info(self, aweme_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        构建基本信息

        Args:
            aweme_data: aweme数据

        Returns:
            Dict[str, Any]: 基本信息字典
        """
        return {
            "aweme_id": str(aweme_data.get("aweme_id", "")),
            "desc": aweme_data.get("desc", ""),
            "create_time": int(aweme_data.get("create_time", 0)),
            "duration": int(aweme_data.get("duration", 0)),
            "aweme_type": int(aweme_data.get("aweme_type", 0)),
        }

    def _build_author_info(self, aweme_data: Dict[str, Any]) -> Author:
        """
        构建作者信息

        Args:
            aweme_data: aweme数据

        Returns:
            Author: 作者信息对象
        """
        author_data = aweme_data.get("author", {})
        if not isinstance(author_data, dict):
            author_data = {}
            
        # Extract avatar URLs
        avatar_thumb_data = author_data.get("avatar_thumb", {})
        avatar_thumb_urls = avatar_thumb_data.get("url_list", []) if isinstance(avatar_thumb_data, dict) else []
        avatar_medium_data = author_data.get("avatar_medium", {})
        avatar_medium_urls = avatar_medium_data.get("url_list", []) if isinstance(avatar_medium_data, dict) else []
            
        return Author(
            short_id=str(author_data.get("short_id", "")),
            nickname=author_data.get("nickname", ""),
            signature=author_data.get("signature", ""),
            avatar_thumb={
                "uri": avatar_thumb_data.get("uri", ""),
                "url_list": avatar_thumb_urls
            },
            avatar_medium={
                "uri": avatar_medium_data.get("uri", ""),
                "url_list": avatar_medium_urls
            },
            follow_status=int(author_data.get("follow_status", 0)),
            following_count=int(author_data.get("following_count", 0)),
            favoriting_count=int(author_data.get("favoriting_count", 0)),
            unique_id=author_data.get("unique_id", ""),
            mplatform_followers_count=int(author_data.get("mplatform_followers_count", 0)),
            sec_uid=author_data.get("sec_uid", ""),
        )

    def _build_music_info(self, aweme_data: Dict[str, Any]) -> Music:
        """
        构建音乐信息

        Args:
            aweme_data: aweme数据

        Returns:
            Music: 音乐信息对象
        """
        music_data = aweme_data.get("music", {})
        if not isinstance(music_data, dict):
            music_data = {}
            
        # Extract cover URLs
        cover_hd_data = music_data.get("cover_hd", {})
        cover_hd_urls = cover_hd_data.get("url_list", []) if isinstance(cover_hd_data, dict) else []
        cover_large_data = music_data.get("cover_large", {})
        cover_large_urls = cover_large_data.get("url_list", []) if isinstance(cover_large_data, dict) else []
        cover_medium_data = music_data.get("cover_medium", {})
        cover_medium_urls = cover_medium_data.get("url_list", []) if isinstance(cover_medium_data, dict) else []
        cover_thumb_data = music_data.get("cover_thumb", {})
        cover_thumb_urls = cover_thumb_data.get("url_list", []) if isinstance(cover_thumb_data, dict) else []
            
        return Music(
            mid=str(music_data.get("mid", "")),
            title=music_data.get("title", ""),
            author=music_data.get("author", ""),
            cover_hd={
                "uri": cover_hd_data.get("uri", ""),
                "url_list": cover_hd_urls
            },
            cover_large={
                "uri": cover_large_data.get("uri", ""),
                "url_list": cover_large_urls
            },
            cover_medium={
                "uri": cover_medium_data.get("uri", ""),
                "url_list": cover_medium_urls
            },
            cover_thumb={
                "uri": cover_thumb_data.get("uri", ""),
                "url_list": cover_thumb_urls
            },
            duration=int(music_data.get("duration", 0)),
            status=int(music_data.get("status", 0)),
        )

    def _build_video_info(self, aweme_data: Dict[str, Any]) -> Video:
        """
        构建视频信息

        Args:
            aweme_data: aweme数据

        Returns:
            Video: 视频信息对象
        """
        video_data = aweme_data.get("video", {})
        if not isinstance(video_data, dict):
            video_data = {}
            
        # 提取播放地址
        play_addr = video_data.get("play_addr", {})
        play_urls = []
        if isinstance(play_addr, dict) and "url_list" in play_addr:
            play_urls = play_addr.get("url_list", [])
            
        # 提取封面
        cover_data = video_data.get("cover", {})
        cover_urls = []
        if isinstance(cover_data, dict) and "url_list" in cover_data:
            cover_urls = cover_data.get("url_list", [])
            
        return Video(
            play_addr={
                "uri": play_addr.get("uri", ""),
                "url_list": play_urls
            },
            cover={
                "uri": cover_data.get("uri", ""),
                "url_list": cover_urls
            },
            height=int(video_data.get("height", 0)),
            width=int(video_data.get("width", 0)),
            bit_rate=video_data.get("bit_rate"),
            big_thumbs=video_data.get("big_thumbs"),
        )

    def _build_statistics_info(self, aweme_data: Dict[str, Any]) -> Statistics:
        """
        构建统计信息

        Args:
            aweme_data: aweme数据

        Returns:
            Statistics: 统计信息对象
        """
        statistics_data = aweme_data.get("statistics", {})
        if not isinstance(statistics_data, dict):
            statistics_data = {}
            
        return Statistics(
            aweme_id=str(statistics_data.get("aweme_id", "")),
            comment_count=int(statistics_data.get("comment_count", 0)),
            digg_count=int(statistics_data.get("digg_count", 0)),
            play_count=int(statistics_data.get("play_count", 0)),
            share_count=int(statistics_data.get("share_count", 0)),
            collect_count=int(statistics_data.get("collect_count", 0)),
        )

    def _build_text_extra_info(self, aweme_data: Dict[str, Any]) -> list:
        """
        构建文本额外信息

        Args:
            aweme_data: aweme数据

        Returns:
            list: 文本额外信息列表
        """
        text_extra_data = aweme_data.get("text_extra", [])
        if not isinstance(text_extra_data, list):
            text_extra_data = []
            
        text_extra_list = []
        for item in text_extra_data:
            if isinstance(item, dict):
                text_extra_list.append(
                    TextExtra(
                        start=int(item.get("start", 0)),
                        end=int(item.get("end", 0)),
                        type=int(item.get("type", 0)),
                        hashtag_name=item.get("hashtag_name", ""),
                        hashtag_id=int(item.get("hashtag_id", 0)),
                    )
                )
                
        return text_extra_list

    def _validate_aweme_item(self, aweme_item: AwemeItem) -> None:
        """
        验证AwemeItem对象

        Args:
            aweme_item: AwemeItem对象

        Raises:
            ValidationException: 验证失败
        """
        if not aweme_item.aweme_id:
            raise ValidationException(
                "AwemeItem aweme_id不能为空",
                validation_type="aweme_item_validation",
                failed_fields=["aweme_id"],
                context={"mapper_type": "jingxuan_extractor"}
            )

    def _aweme_item_to_dict(self, aweme_item: AwemeItem) -> Dict[str, Any]:
        """
        将AwemeItem对象转换为字典

        Args:
            aweme_item: AwemeItem对象

        Returns:
            Dict[str, Any]: 字典格式的数据
        """
        return {
            "aweme_id": aweme_item["aweme_id"],
            "desc": aweme_item["desc"],
            "create_time": aweme_item["create_time"],
            "author": aweme_item["author"],
            "music": aweme_item["music"],
            "cha_list": aweme_item["cha_list"],
            "video": aweme_item["video"],
            "statistics": aweme_item["statistics"],
            "text_extra": aweme_item["text_extra"],
            "video_labels": aweme_item["video_labels"],
            "aweme_type": aweme_item["aweme_type"],
            "image_infos": aweme_item["image_infos"],
            "risk_infos": aweme_item["risk_infos"],
            "comment_list": aweme_item["comment_list"],
            "geofencing": aweme_item["geofencing"],
            "video_text": aweme_item["video_text"],
            "label_top_text": aweme_item["label_top_text"],
            "promotions": aweme_item["promotions"],
            "long_video": aweme_item["long_video"],
            "images": aweme_item["images"],
            "group_id_str": aweme_item["group_id_str"],
            "chapter_list": aweme_item["chapter_list"],
            "interaction_stickers": aweme_item["interaction_stickers"],
            "img_bitrate": aweme_item["img_bitrate"],
            "chapter_bar_color": aweme_item["chapter_bar_color"],
        }


# Add missing newline at end of file