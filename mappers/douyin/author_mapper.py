"""
抖音作者数据转换器
"""

import logging
from typing import Dict, Any

from models.douyin.models import DouyinCreator

logger = logging.getLogger(__name__)


class DouyinAuthorMapper:
    """抖音作者数据转换器"""
    
    @staticmethod
    def creator_data_to_model_data(creator_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        将抖音创作者数据转换为数据库模型数据
        
        Args:
            creator_data: 抖音创作者原始数据
            
        Returns:
            Dict[str, Any]: 转换后的数据库模型数据
        """
        try:
            # 提取用户基本信息
            user_info = creator_data.get("user_info", {})
            user_id = user_info.get("uid", "")
            
            # 如果没有从user_info获取到user_id，则从creator_data直接获取
            if not user_id:
                user_id = creator_data.get("uid", "")
            
            # 提取头像URL
            avatar_url = ""
            if user_info:
                avatar_thumb = user_info.get("avatar_thumb", {})
                if isinstance(avatar_thumb, dict):
                    url_list = avatar_thumb.get("url_list", [])
                    if url_list and len(url_list) > 0:
                        avatar_url = str(url_list[0])
            
            # 准备转换后的数据
            model_data = {
                "user_id": user_id,
                "nickname": user_info.get("nickname", "") if user_info else "",
                "avatar": avatar_url,
                "desc": user_info.get("signature", "") if user_info else "",
                "gender": user_info.get("gender", "") if user_info else "",
                "follows": str(user_info.get("following_count", "0")) if user_info else "0",
                "fans": str(user_info.get("follower_count", "0")) if user_info else "0",
                "interaction": str(user_info.get("total_favorited", "0")) if user_info else "0",
            }
            
            return model_data
        
        except Exception as e:
            logger.error(f"转换抖音创作者数据失败: {e}")
            # 返回默认数据结构
            return {
                "user_id": "",
                "nickname": "",
                "avatar": "",
                "desc": "",
                "gender": "",
                "follows": "0",
                "fans": "0",
                "interaction": "0",
            }
    
    @staticmethod
    def model_to_creator_data(model: DouyinCreator) -> Dict[str, Any]:
        """
        将数据库模型转换为抖音创作者数据
        
        Args:
            model: DouyinCreator模型实例
            
        Returns:
            Dict[str, Any]: 抖音创作者数据
        """
        try:
            creator_data = {
                "user_id": model.user_id,
                "nickname": model.nickname or "",
                "avatar": model.avatar or "",
                "signature": model.desc or "",
                "gender": model.gender or "",
                "following_count": model.follows or "0",
                "follower_count": model.fans or "0",
                "total_favorited": model.interaction or "0",
            }
            
            return creator_data
            
        except Exception as e:
            logger.error(f"转换模型到抖音创作者数据失败: {e}")
            return {}