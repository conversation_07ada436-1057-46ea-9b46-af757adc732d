"""
Jingxuan data mapper for converting jingxuan page data to standard format.
"""

from typing import Any, Dict

from .base import BaseDataMapper
from .exceptions import MappingException, ValidationException


class JingxuanDataMapper(BaseDataMapper):
    """
    精选页面数据映射器，处理从精选页面获取的数据
    """

    def map_to_standard_format(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        将精选页面原始数据映射为标准格式

        Args:
            raw_data: 精选页面原始数据字典

        Returns:
            Dict[str, Any]: 标准格式的数据字典

        Raises:
            MappingException: 映射过程中发生错误
        """
        try:
            if not self.validate_raw_data(raw_data):
                raise ValidationException(
                    "Jingxuan data validation failed",
                    validation_type="jingxuan_data",
                    failed_fields=["aweme_id"],
                    context={"mapper_type": "jingxuan"},
                )

            # 提取各部分数据
            basic_info = self.extract_basic_info(raw_data)
            user_info = self.extract_user_info(raw_data)
            statistics = self.extract_statistics(raw_data)
            media_urls = self.extract_media_urls(raw_data)
            jingxuan_specific = self._extract_jingxuan_specific_data(raw_data)

            # 合并所有数据
            standard_data = {
                **basic_info,
                **user_info,
                **statistics,
                **media_urls,
                **jingxuan_specific,
                "source_keyword": "jingxuan",
            }

            return standard_data

        except Exception as e:
            if isinstance(e, (ValidationException, MappingException)):
                raise

            raise MappingException(
                f"Failed to map jingxuan data: {str(e)}",
                mapper_type="jingxuan",
                failed_field="unknown",
                raw_data_sample=str(raw_data)[:200],
                context={"original_error": str(e)},
            )

    def validate_raw_data(self, raw_data: Dict[str, Any]) -> bool:
        """
        验证精选页面原始数据的有效性

        Args:
            raw_data: 原始数据字典

        Returns:
            bool: 数据是否有效
        """
        if not isinstance(raw_data, dict):
            return False

        # 检查必需字段
        required_fields = ["aweme_id"]
        return self._validate_required_fields(raw_data, required_fields)

    def _extract_jingxuan_specific_data(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        提取精选页面特有的数据

        Args:
            raw_data: 原始数据字典

        Returns:
            Dict[str, Any]: 精选页面特有数据字典
        """
        # 处理精选页面特有的视频URL提取
        video_urls = self._process_jingxuan_video_urls(raw_data)

        # 提取精选页面特有字段
        jingxuan_data = {
            "video_download_url": video_urls.get("download_url", ""),
        }

        # 如果video_download_url为空，使用play_addr作为备选
        if not jingxuan_data["video_download_url"]:
            jingxuan_data["video_download_url"] = video_urls.get("play_url", "")

        # 提取精选页面可能包含的额外信息
        jingxuan_data.update(self._extract_jingxuan_metadata(raw_data))

        return jingxuan_data

    def _process_jingxuan_video_urls(self, raw_data: Dict[str, Any]) -> Dict[str, str]:
        """
        处理精选页面视频URL

        Args:
            raw_data: 原始数据字典

        Returns:
            Dict[str, str]: 包含各种URL的字典
        """
        video_info = self._safe_extract(raw_data, "video", {})
        if not isinstance(video_info, dict):
            return {}

        urls = {}

        # 精选页面通常包含多种格式的视频URL
        # 按优先级顺序尝试提取

        # 1. 优先使用H.264格式
        play_addr_h264 = video_info.get("play_addr_h264", {})
        if isinstance(play_addr_h264, dict):
            h264_url_list = play_addr_h264.get("url_list", [])
            if h264_url_list:
                urls["download_url"] = h264_url_list[-1]

        # 2. 尝试使用download_addr
        if not urls.get("download_url"):
            download_addr = video_info.get("download_addr", {})
            if isinstance(download_addr, dict):
                download_url_list = download_addr.get("url_list", [])
                if download_url_list:
                    urls["download_url"] = download_url_list[-1]

        # 3. 使用通用play_addr作为备选
        play_addr = video_info.get("play_addr", {})
        if isinstance(play_addr, dict):
            play_url_list = play_addr.get("url_list", [])
            if play_url_list:
                if not urls.get("download_url"):
                    urls["download_url"] = play_url_list[-1]
                urls["play_url"] = play_url_list[-1]

        # 4. 尝试256p格式作为最后备选
        if not urls.get("download_url") and not urls.get("play_url"):
            play_addr_256 = video_info.get("play_addr_256", {})
            if isinstance(play_addr_256, dict):
                url_256_list = play_addr_256.get("url_list", [])
                if url_256_list:
                    urls["play_url"] = url_256_list[-1]

        return urls

    def _extract_jingxuan_metadata(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        提取精选页面特有的元数据

        Args:
            raw_data: 原始数据字典

        Returns:
            Dict[str, Any]: 精选页面元数据字典
        """
        metadata = {}

        # 提取aweme_type（精选页面可能有特殊类型）
        aweme_type = self._safe_extract(raw_data, "aweme_type", 0)
        if aweme_type:
            metadata["aweme_type"] = str(aweme_type)

        # 提取视频时长信息
        video_info = self._safe_extract(raw_data, "video", {})
        if isinstance(video_info, dict):
            duration = video_info.get("duration", 0)
            if duration:
                metadata["duration"] = duration

            # 提取视频尺寸信息
            width = video_info.get("width", 0)
            height = video_info.get("height", 0)
            if width and height:
                metadata["video_width"] = width
                metadata["video_height"] = height

        # 提取音乐信息
        music_info = self._safe_extract(raw_data, "music", {})
        if isinstance(music_info, dict):
            music_title = music_info.get("title", "")
            if music_title:
                metadata["music_title"] = music_title

        return metadata

    def _extract_video_download_url(self, aweme_detail: Dict[str, Any], by_mobile: bool = False) -> str:
        """
        重写父类方法，专门处理精选页面视频下载URL提取

        Args:
            aweme_detail: 抖音视频详情数据
            by_mobile: 是否为移动端数据（精选页面默认False）

        Returns:
            str: 视频下载地址
        """
        if not aweme_detail:
            return ""

        video_urls = self._process_jingxuan_video_urls(aweme_detail)

        # 优先使用下载地址
        download_url = video_urls.get("download_url", "")
        if download_url:
            return download_url

        # 使用播放地址作为备选
        play_url = video_urls.get("play_url", "")
        if play_url:
            return play_url

        # 如果精选页面特有逻辑都没有找到，回退到父类方法
        return super()._extract_video_download_url(aweme_detail, by_mobile=False)

    def extract_statistics(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        重写父类方法，处理精选页面特有的统计数据格式

        Args:
            raw_data: 原始数据字典

        Returns:
            Dict[str, Any]: 统计信息字典
        """
        statistics = self._safe_extract(raw_data, "statistics", {})
        if not isinstance(statistics, dict):
            statistics = {}

        # 精选页面的统计数据格式可能与其他页面略有不同
        return {
            "liked_count": str(self._safe_extract(statistics, "digg_count", 0)),
            "comment_count": str(self._safe_extract(statistics, "comment_count", 0)),
            "share_count": str(self._safe_extract(statistics, "share_count", 0)),
            "collected_count": str(self._safe_extract(statistics, "collect_count", 0)),
        }

    def extract_basic_info(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        重写父类方法，处理精选页面特有的基础信息格式

        Args:
            raw_data: 原始数据字典

        Returns:
            Dict[str, Any]: 基础信息字典
        """
        basic_info = super().extract_basic_info(raw_data)

        # 精选页面可能有特殊的标题处理
        desc = self._safe_extract(raw_data, "desc", "")
        if desc:
            # 如果没有单独的title字段，使用desc作为title
            if not basic_info.get("title") or basic_info["title"] == basic_info["desc"]:
                basic_info["title"] = desc[:100] + "..." if len(desc) > 100 else desc

        return basic_info
