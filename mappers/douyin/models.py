"""
Configuration and result models for the video fetcher system.
"""

import time
import uuid
from dataclasses import dataclass, field
from typing import Any, Dict, Generic, List, Optional, TypeVar

T = TypeVar("T")


@dataclass
class FetcherConfig:
    """
    视频获取器配置类
    """

    # 重试配置
    retry_attempts: int = 3
    timeout: int = 30

    # 回退策略配置
    fallback_methods: List[str] = field(default_factory=lambda: ["jingxuan", "mobile", "rpc"])  # 保持字符串格式以兼容现有代码
    enable_fallback: bool = True

    # 网络配置
    use_proxy: bool = True

    # 缓存配置
    enable_caching: bool = True
    cache_ttl: int = 3600  # 秒

    # 并发控制
    max_concurrent: int = 5
    rate_limit_per_minute: int = 60

    # 数据库配置
    save_to_db: bool = True

    # 日志配置
    enable_detailed_logging: bool = True
    log_performance_metrics: bool = True

    # 错误处理配置
    fail_fast: bool = False  # 如果为True，第一个方法失败就立即返回
    raise_on_all_failed: bool = True  # 如果所有方法都失败是否抛出异常

    def validate(self) -> bool:
        """
        验证配置的有效性

        Returns:
            bool: 配置是否有效
        """
        if self.retry_attempts < 0 or self.retry_attempts > 10:
            return False

        if self.timeout <= 0 or self.timeout > 300:
            return False

        if self.max_concurrent <= 0 or self.max_concurrent > 50:
            return False

        if self.rate_limit_per_minute <= 0:
            return False

        if self.cache_ttl < 0:
            return False

        # 验证回退方法是否有效
        valid_methods = {"mobile", "jingxuan", "rpc"}
        for method in self.fallback_methods:
            if method not in valid_methods:
                return False

        return True

    def get_method_priority(self, method: str) -> int:
        """
        获取方法的优先级（数字越小优先级越高）

        Args:
            method: 方法名称

        Returns:
            int: 优先级值，如果方法不在列表中返回999
        """
        try:
            return self.fallback_methods.index(method)
        except ValueError:
            return 999


@dataclass
class FetchResult(Generic[T]):
    """
    获取结果类
    """

    # 基本结果信息
    success: bool
    data: Optional[T] = None

    # 请求信息
    aweme_id: str = ""
    method: str = ""
    source: str = ""

    # 性能信息
    response_time: float = 0.0
    start_time: float = field(default_factory=time.time)
    end_time: Optional[float] = None

    # 错误信息
    error: Optional[str] = None
    error_code: Optional[str] = None
    exception_type: Optional[str] = None

    # 重试信息
    retry_count: int = 0
    attempted_methods: List[str] = field(default_factory=list)

    # 请求标识
    request_id: str = field(default_factory=lambda: str(uuid.uuid4()))

    # 额外信息
    metadata: Dict[str, Any] = field(default_factory=dict)

    def mark_completed(self, success: bool = True, error: str = None) -> None:
        """
        标记结果完成

        Args:
            success: 是否成功
            error: 错误信息
        """
        self.success = success
        self.end_time = time.time()
        self.response_time = self.end_time - self.start_time

        if error:
            self.error = error

    def add_attempted_method(self, method: str) -> None:
        """
        添加尝试的方法

        Args:
            method: 方法名称
        """
        if method not in self.attempted_methods:
            self.attempted_methods.append(method)

    def increment_retry(self) -> None:
        """
        增加重试次数
        """
        self.retry_count += 1

    def set_metadata(self, key: str, value: Any) -> None:
        """
        设置元数据

        Args:
            key: 元数据键
            value: 元数据值
        """
        self.metadata[key] = value

    def get_metadata(self, key: str, default: Any = None) -> Any:
        """
        获取元数据

        Args:
            key: 元数据键
            default: 默认值

        Returns:
            Any: 元数据值
        """
        return self.metadata.get(key, default)

    def to_dict(self) -> Dict[str, Any]:
        """
        转换为字典格式

        Returns:
            Dict[str, Any]: 字典格式的结果
        """
        data_dict = self.data.model_dump() if hasattr(self.data, "model_dump") and self.data else self.data
        return {
            "success": self.success,
            "data": data_dict,
            "aweme_id": self.aweme_id,
            "method": self.method,
            "source": self.source,
            "response_time": self.response_time,
            "start_time": self.start_time,
            "end_time": self.end_time,
            "error": self.error,
            "error_code": self.error_code,
            "exception_type": self.exception_type,
            "retry_count": self.retry_count,
            "attempted_methods": self.attempted_methods,
            "request_id": self.request_id,
            "metadata": self.metadata,
        }

    def get_summary(self) -> str:
        """
        获取结果摘要

        Returns:
            str: 结果摘要字符串
        """
        status = "SUCCESS" if self.success else "FAILED"
        methods = " -> ".join(self.attempted_methods) if self.attempted_methods else "None"

        summary = f"[{status}] aweme_id={self.aweme_id}, method={self.method}, "
        summary += f"response_time={self.response_time:.2f}s, "
        summary += f"retry_count={self.retry_count}, attempted_methods=[{methods}]"

        if self.error:
            summary += f", error={self.error}"

        return summary


@dataclass
class BatchFetchResult(Generic[T]):
    """
    批量获取结果类
    """

    # 总体信息
    total_count: int
    success_count: int = 0
    failed_count: int = 0

    # 结果列表
    results: List[FetchResult[T]] = field(default_factory=list)

    # 性能信息
    total_time: float = 0.0
    average_time: float = 0.0

    # 批次信息
    batch_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    start_time: float = field(default_factory=time.time)
    end_time: Optional[float] = None

    def add_result(self, result: FetchResult[T]) -> None:
        """
        添加单个结果

        Args:
            result: 获取结果
        """
        self.results.append(result)

        if result.success:
            self.success_count += 1
        else:
            self.failed_count += 1

    def mark_completed(self) -> None:
        """
        标记批量操作完成
        """
        self.end_time = time.time()
        self.total_time = self.end_time - self.start_time

        if self.total_count > 0:
            self.average_time = self.total_time / self.total_count

    @property
    def success_rate(self) -> float:
        """
        获取成功率

        Returns:
            float: 成功率 (0.0 - 1.0)
        """
        if self.total_count == 0:
            return 0.0
        return self.success_count / self.total_count

    @property
    def is_completed(self) -> bool:
        """
        检查是否已完成

        Returns:
            bool: 是否已完成
        """
        return len(self.results) == self.total_count

    def get_successful_results(self) -> List[FetchResult[T]]:
        """
        获取成功的结果

        Returns:
            List[FetchResult]: 成功的结果列表
        """
        return [result for result in self.results if result.success]

    def get_failed_results(self) -> List[FetchResult[T]]:
        """
        获取失败的结果

        Returns:
            List[FetchResult]: 失败的结果列表
        """
        return [result for result in self.results if not result.success]

    def get_summary(self) -> str:
        """
        获取批量结果摘要

        Returns:
            str: 批量结果摘要字符串
        """
        summary = f"BatchResult: total={self.total_count}, "
        summary += f"success={self.success_count}, failed={self.failed_count}, "
        summary += f"success_rate={self.success_rate:.2%}, "
        summary += f"total_time={self.total_time:.2f}s, "
        summary += f"average_time={self.average_time:.2f}s"

        return summary

    def to_dict(self) -> Dict[str, Any]:
        """
        转换为字典格式

        Returns:
            Dict[str, Any]: 字典格式的批量结果
        """
        return {
            "total_count": self.total_count,
            "success_count": self.success_count,
            "failed_count": self.failed_count,
            "success_rate": self.success_rate,
            "total_time": self.total_time,
            "average_time": self.average_time,
            "batch_id": self.batch_id,
            "start_time": self.start_time,
            "end_time": self.end_time,
            "is_completed": self.is_completed,
            "results": [result.to_dict() for result in self.results],
        }
