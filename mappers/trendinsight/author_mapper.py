"""
TrendInsight 作者数据映射器

负责将 TrendInsight API 返回的作者数据转换为内部数据模型，
以及将内部数据模型转换为数据库模型。
"""

import time
from typing import Dict, Any

from models.trendinsight.models import TrendInsightAuthor
from schemas.trendinsight import AuthorUpdateData
from utils.douyin import extract_douyin_user_id


class TrendInsightAuthorMapper:
    """TrendInsight 作者数据映射器"""

    @staticmethod
    def author_update_data_to_model_data(author_data: AuthorUpdateData) -> Dict[str, Any]:
        """
        将 AuthorUpdateData 转换为 TrendInsightAuthor 模型需要的数据

        Args:
            author_data: AuthorUpdateData 数据对象

        Returns:
            Dict[str, Any]: 转换后的数据字典，可直接用于 TrendInsightAuthor 模型
        """
        # 设置当前时间戳
        current_time = int(time.time())

        # 转换字符串数值为整数
        item_count_str = author_data.item_count
        fans_count_str = author_data.fans_count
        like_count_str = author_data.like_count

        item_count_int = int(item_count_str) if str(item_count_str).isdigit() else 0
        fans_count_int = int(fans_count_str) if str(fans_count_str).isdigit() else 0
        like_count_int = int(like_count_str) if str(like_count_str).isdigit() else 0

        # 处理粉丝里程碑
        fans_milestone = author_data.fans_milestone or {}
        fans_milestone_create_time = fans_milestone.get("create_time", "") if isinstance(fans_milestone, dict) else ""

        # 提取抖音用户ID
        douyin_user_id = extract_douyin_user_id(author_data.user_aweme_url) if author_data.user_aweme_url else ""

        # 准备数据
        return {
            "user_id": author_data.user_id,  # 添加缺失的 user_id 字段
            "user_name": author_data.user_name,
            "user_head_logo": author_data.user_head_logo,
            "user_gender": author_data.user_gender,
            "user_location": author_data.user_location,
            "user_introduction": author_data.user_introduction,
            # 原始字符串数据
            "item_count": item_count_str,
            "fans_count": fans_count_str,
            "like_count": like_count_str,
            # 转换后的数值数据
            "item_count_int": item_count_int,
            "fans_count_int": fans_count_int,
            "like_count_int": like_count_int,
            # 标签字段
            "first_tag_name": author_data.first_tag_name,
            "second_tag_name": author_data.second_tag_name,
            # 粉丝里程碑
            "fans_milestone_create_time": fans_milestone_create_time,
            # 抖音平台字段
            "aweme_id": author_data.aweme_id,
            "user_aweme_url": author_data.user_aweme_url,
            "douyin_user_id": douyin_user_id,
            "aweme_pic": author_data.aweme_pic,
            # 系统字段
            "platform": author_data.platform,
            "crawl_time": current_time,
            "source_keyword": author_data.source_keyword,
            "raw_data": author_data.raw_data,
        }

    @staticmethod
    def author_model_to_data(author_model: TrendInsightAuthor) -> Dict[str, Any]:
        """
        将 TrendInsightAuthor 模型转换为字典数据

        Args:
            author_model: TrendInsightAuthor 数据库模型实例

        Returns:
            Dict[str, Any]: 转换后的数据字典
        """
        return {
            "id": author_model.id,
            "user_id": author_model.user_id,
            "user_name": author_model.user_name,
            "user_head_logo": author_model.user_head_logo,
            "user_gender": author_model.user_gender,
            "user_location": author_model.user_location,
            "user_introduction": author_model.user_introduction,
            "item_count": author_model.item_count,
            "fans_count": author_model.fans_count,
            "like_count": author_model.like_count,
            "item_count_int": author_model.item_count_int,
            "fans_count_int": author_model.fans_count_int,
            "like_count_int": author_model.like_count_int,
            "first_tag_name": author_model.first_tag_name,
            "second_tag_name": author_model.second_tag_name,
            "fans_milestone_create_time": author_model.fans_milestone_create_time,
            "aweme_id": author_model.aweme_id,
            "user_aweme_url": author_model.user_aweme_url,
            "douyin_user_id": author_model.douyin_user_id,
            "aweme_pic": author_model.aweme_pic,
            "platform": author_model.platform,
            "crawl_time": author_model.crawl_time,
            "source_keyword": author_model.source_keyword,
            "raw_data": author_model.raw_data,
            "created_at": author_model.created_at,
            "updated_at": author_model.updated_at,
        }