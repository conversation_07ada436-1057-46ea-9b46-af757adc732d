"""
TrendInsight 关键词数据映射器

负责处理关键词相关的数据转换逻辑
"""

from models.trendinsight.models import TrendInsightKeyword
from schemas.trendinsight import KeywordData


class TrendInsightKeywordMapper:
    """TrendInsight 关键词数据映射器"""

    @staticmethod
    def keyword_model_to_data(keyword_model: TrendInsightKeyword) -> KeywordData:
        """
        将 TrendInsightKeyword 模型转换为 KeywordData 响应数据

        Args:
            keyword_model: TrendInsightKeyword 数据库模型实例

        Returns:
            KeywordData: 转换后的响应数据对象
        """
        return KeywordData(
            id=keyword_model.id,
            keyword=keyword_model.keyword,
            keyword_hash=keyword_model.keyword_hash,
            video_count=keyword_model.video_count,
            created_at=keyword_model.created_at,
            updated_at=keyword_model.updated_at,
        )
