{"permissions": {"allow": ["Bash(python test_author_search_fix.py)", "Bash(rm:*)", "Bash(find:*)", "<PERSON><PERSON>(true)", "<PERSON><PERSON>(python test:*)", "Bash(rg:*)", "Bash(make lint)", "<PERSON><PERSON>(aerich migrate:*)", "Bash(ls:*)", "<PERSON><PERSON>(aerich init:*)", "<PERSON><PERSON>(make:*)", "<PERSON><PERSON>(python:*)", "Bash(grep:*)", "mcp__serena__read_file", "mcp__serena__initial_instructions", "Bash(sqlite3:*)", "mcp__serena__check_onboarding_performed", "mcp__serena__search_for_pattern", "mcp__serena__find_symbol", "mcp__serena__find_referencing_symbols", "<PERSON><PERSON>(mkdir:*)"], "deny": []}}