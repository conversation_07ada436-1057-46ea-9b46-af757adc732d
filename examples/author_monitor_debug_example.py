#!/usr/bin/env python3
"""
作者监控任务调试示例

这个示例用于调试作者监控任务，包括：
1. 直接调用作者监控任务
2. 模拟不同的配置参数
3. 详细的日志输出和错误处理
4. 性能监控和统计信息

使用方法:
1. 确保数据库已初始化并有作者数据
2. 运行此脚本: python examples/author_monitor_debug_example.py
"""

import asyncio
import json
import sys
from datetime import datetime
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from tortoise import Tortoise
from settings.config import settings
from tasks.core.manager import TaskManager
from tasks.core.models import TaskConfig
from tasks.monitors.author import AuthorMonitorTask

from log import logger


class AuthorMonitorDebugger:
    """作者监控任务调试器"""

    def __init__(self):
        self.manager = TaskManager()
        self.manager.register_task("author_monitor", AuthorMonitorTask)

    async def init_database(self):
        """初始化数据库连接"""
        try:
            await Tortoise.init(config=settings.tortoise_orm)
            logger.info("✅ 数据库连接初始化成功")
        except Exception as e:
            logger.error(f"❌ 数据库连接失败: {str(e)}")
            raise

    async def close_database(self):
        """关闭数据库连接"""
        try:
            await Tortoise.close_connections()
            logger.info("✅ 数据库连接已关闭")
        except Exception as e:
            logger.warning(f"⚠️ 关闭数据库连接时出错: {str(e)}")

    async def check_authors_status(self):
        """检查作者状态"""
        try:
            from models.trendinsight import TrendInsightAuthor
            
            total_count = await TrendInsightAuthor.all().count()
            logger.info(f"📊 数据库中总作者数量: {total_count}")
            
            if total_count > 0:
                # 获取前5个作者作为示例
                sample_authors = await TrendInsightAuthor.all().limit(5)
                logger.info(f"📝 示例作者:")
                for i, author in enumerate(sample_authors, 1):
                    logger.info(f"   {i}. {author.user_name} (ID: {author.user_id}, 粉丝: {author.fans_count}, 更新时间: {author.updated_at})")
                
                # 检查有 douyin_user_id 的作者数量
                douyin_authors_count = await TrendInsightAuthor.filter(
                    douyin_user_id__isnull=False, 
                    douyin_user_id__not=""
                ).count()
                logger.info(f"📱 有抖音用户ID的作者数量: {douyin_authors_count}")
                
                # 检查高粉丝数作者
                high_fans_count = await TrendInsightAuthor.filter(fans_count_int__gte=10000).count()
                logger.info(f"🌟 粉丝数超过1万的作者数量: {high_fans_count}")
                
            else:
                logger.warning("⚠️ 数据库中没有作者数据，请先添加作者")
                
        except Exception as e:
            logger.error(f"❌ 检查作者状态失败: {str(e)}")

    async def run_debug_task(self, config_dict: dict):
        """运行调试任务"""
        logger.info(f"\n🚀 开始执行作者监控调试任务")
        logger.info(f"📋 任务配置: {json.dumps(config_dict, ensure_ascii=False, indent=2)}")
        logger.info("=" * 60)

        try:
            # 验证配置
            task_config = self.manager.validate_config(config_dict)
            logger.info(f"✅ 配置验证通过")

            # 执行任务
            start_time = datetime.now()
            result = await self.manager.execute_task(task_config)
            end_time = datetime.now()
            
            # 输出结果
            logger.info(f"\n🎉 任务执行完成!")
            logger.info(f"⏱️ 执行时间: {(end_time - start_time).total_seconds():.2f} 秒")
            logger.info(f"📊 执行结果:")
            logger.info(f"   - 状态: {result.status}")
            logger.info(f"   - 处理数量: {result.processed_count}")
            logger.info(f"   - 成功数量: {result.success_count}")
            logger.info(f"   - 失败数量: {result.failed_count}")
            
            if hasattr(result, 'errors') and result.errors:
                logger.error(f"❌ 错误信息:")
                for i, error in enumerate(result.errors[:5], 1):  # 只显示前5个错误
                    logger.error(f"   {i}. {error}")
                if len(result.errors) > 5:
                    logger.error(f"   ... 还有 {len(result.errors) - 5} 个错误")

            if hasattr(result, 'performance'):
                logger.info(f"📈 性能指标:")
                perf = result.performance
                if isinstance(perf, dict):
                    for key, value in perf.items():
                        logger.info(f"   - {key}: {value}")

            return result

        except Exception as e:
            logger.error(f"❌ 任务执行失败: {str(e)}")
            import traceback
            logger.error(f"详细错误信息:\n{traceback.format_exc()}")
            return None

    async def run_multiple_configs(self):
        """运行多种配置进行对比测试"""
        configs = [
            {
                "name": "小批量测试",
                "config": {
                    "task_type": "author_monitor",
                    "batch_size": 5,
                    "author_video_limit": 20,
                    "max_age_hours": 24,
                    "timeout": 300
                }
            },
            {
                "name": "标准配置",
                "config": {
                    "task_type": "author_monitor",
                    "batch_size": 50,
                    "author_video_limit": 50,
                    "max_age_hours": 1,
                    "timeout": 600
                }
            },
            {
                "name": "大批量测试",
                "config": {
                    "task_type": "author_monitor",
                    "batch_size": 100,
                    "author_video_limit": 100,
                    "max_age_hours": 6,
                    "timeout": 1200
                }
            }
        ]

        results = []
        for test_case in configs:
            logger.info(f"\n{'='*80}")
            logger.info(f"🧪 测试配置: {test_case['name']}")
            logger.info(f"{'='*80}")
            
            result = await self.run_debug_task(test_case['config'])
            results.append({
                "name": test_case['name'],
                "config": test_case['config'],
                "result": result
            })
            
            # 测试间隔，避免API限流
            logger.info(f"\n⏳ 等待 10 秒后进行下一个测试...")
            await asyncio.sleep(10)

        # 输出对比结果
        logger.info(f"\n{'='*80}")
        logger.info(f"📊 测试结果对比")
        logger.info(f"{'='*80}")
        
        for test_result in results:
            result = test_result['result']
            if result:
                logger.info(f"\n🔍 {test_result['name']}:")
                logger.info(f"   - 批次大小: {test_result['config']['batch_size']}")
                logger.info(f"   - 视频限制: {test_result['config']['author_video_limit']}")
                logger.info(f"   - 处理数量: {result.processed_count}")
                logger.info(f"   - 成功率: {result.success_count}/{result.processed_count if result.processed_count > 0 else 1}")
                logger.info(f"   - 状态: {result.status}")

    async def run_single_debug(self):
        """运行单个调试配置"""
        # 使用与 Makefile 中相同的参数
        debug_config = {
            "task_type": "author_monitor",
            "batch_size": 50,
            "author_video_limit": 50,
            "max_age_hours": 1,
            "timeout": 600
        }
        
        await self.run_debug_task(debug_config)

    async def run_filtered_debug(self):
        """运行带过滤条件的调试配置"""
        from models.trendinsight import TrendInsightAuthor

        # 获取一些示例作者ID
        sample_authors = await TrendInsightAuthor.filter(
            douyin_user_id__isnull=False,
            douyin_user_id__not=""
        ).limit(3)

        if not sample_authors:
            logger.error("❌ 没有找到有效的作者数据进行过滤测试")
            return

        author_ids = [author.user_id for author in sample_authors]
        logger.info(f"🎯 使用作者ID过滤: {author_ids}")

        debug_config = {
            "task_type": "author_monitor",
            "batch_size": 10,
            "author_video_limit": 30,
            "max_age_hours": 24,
            "timeout": 300,
            "filters": {
                "author_ids": author_ids,
                "min_fans_count": 1000
            }
        }

        await self.run_debug_task(debug_config)

    async def check_stale_authors(self):
        """检查过期的作者数据"""
        try:
            from models.trendinsight import TrendInsightAuthor
            from datetime import timedelta

            # 检查不同时间范围的过期作者
            time_ranges = [1, 6, 24, 72, 168]  # 1小时, 6小时, 1天, 3天, 7天

            logger.info(f"\n📅 过期作者统计:")
            for hours in time_ranges:
                cutoff_time = datetime.now() - timedelta(hours=hours)
                stale_count = await TrendInsightAuthor.filter(
                    updated_at__lte=cutoff_time,
                    douyin_user_id__isnull=False,
                    douyin_user_id__not=""
                ).count()

                logger.info(f"   - {hours}小时前: {stale_count} 个作者")

        except Exception as e:
            logger.error(f"❌ 检查过期作者失败: {str(e)}")

    async def show_author_details(self):
        """显示作者详细信息"""
        try:
            from models.trendinsight import TrendInsightAuthor

            # 获取一些有代表性的作者
            authors = await TrendInsightAuthor.filter(
                douyin_user_id__isnull=False,
                douyin_user_id__not=""
            ).order_by('-fans_count_int').limit(10)

            if not authors:
                logger.error("❌ 没有找到有效的作者数据")
                return

            logger.info(f"\n👥 作者详细信息 (按粉丝数排序):")
            logger.info("-" * 100)
            logger.info(f"{'序号':<4} {'用户名':<20} {'粉丝数':<12} {'作品数':<8} {'抖音ID':<15} {'更新时间':<20}")
            logger.info("-" * 100)

            for i, author in enumerate(authors, 1):
                logger.info(f"{i:<4} {author.user_name[:18]:<20} {author.fans_count:<12} {author.item_count:<8} {author.douyin_user_id or 'N/A':<15} {author.updated_at.strftime('%Y-%m-%d %H:%M'):<20}")

        except Exception as e:
            logger.error(f"❌ 显示作者详细信息失败: {str(e)}")

    async def run_performance_test(self):
        """运行性能测试"""
        logger.info(f"\n🚀 开始性能测试...")

        # 小批量快速测试
        small_config = {
            "task_type": "author_monitor",
            "batch_size": 5,
            "author_video_limit": 10,
            "max_age_hours": 168,  # 7天
            "timeout": 180
        }

        logger.info(f"\n📊 小批量性能测试:")
        start_time = datetime.now()
        result = await self.run_debug_task(small_config)
        end_time = datetime.now()

        if result:
            duration = (end_time - start_time).total_seconds()
            if result.processed_count > 0:
                avg_time = duration / result.processed_count
                logger.info(f"⚡ 性能指标:")
                logger.info(f"   - 总耗时: {duration:.2f} 秒")
                logger.info(f"   - 平均每个作者: {avg_time:.2f} 秒")
                logger.info(f"   - 处理速度: {result.processed_count / duration * 60:.1f} 作者/分钟")


async def main():
    """主函数"""
    logger.info("🔧 作者监控任务调试器")
    logger.info("=" * 60)
    
    debugger = AuthorMonitorDebugger()
    
    try:
        # 初始化数据库
        await debugger.init_database()
        
        # 检查作者状态
        await debugger.check_authors_status()
        
        # 询问用户选择调试模式
        logger.info(f"\n🎯 请选择调试模式:")
        logger.info(f"1. 单个配置调试 (与 Makefile 相同参数)")
        logger.info(f"2. 多配置对比测试")
        logger.info(f"3. 过滤条件测试")
        logger.info(f"4. 检查过期作者统计")
        logger.info(f"5. 显示作者详细信息")
        logger.info(f"6. 性能测试")
        logger.info(f"7. 退出")

        choice = input("\n请输入选择 (1-7): ").strip()

        if choice == "1":
            await debugger.run_single_debug()
        elif choice == "2":
            await debugger.run_multiple_configs()
        elif choice == "3":
            await debugger.run_filtered_debug()
        elif choice == "4":
            await debugger.check_stale_authors()
        elif choice == "5":
            await debugger.show_author_details()
        elif choice == "6":
            await debugger.run_performance_test()
        elif choice == "7":
            logger.info("👋 退出调试器")
        else:
            logger.error("❌ 无效选择，退出")
            
    except KeyboardInterrupt:
        logger.warning(f"\n⚠️ 用户中断执行")
    except Exception as e:
        logger.error(f"❌ 调试器运行失败: {str(e)}")
        import traceback
        logger.error(f"详细错误信息:\n{traceback.format_exc()}")
    finally:
        # 关闭数据库连接
        await debugger.close_database()


if __name__ == "__main__":
    logger.info("作者监控任务调试器")
    logger.info("=" * 60)
    logger.info("📝 注意: 请确保数据库已初始化并包含作者数据")
    logger.info("🔧 此工具用于调试和测试作者监控任务的执行")
    logger.info("=" * 60)
    
    asyncio.run(main())
