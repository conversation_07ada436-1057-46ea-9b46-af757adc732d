#!/usr/bin/env python3
"""
关键词监控任务调试示例

这个示例用于调试关键词监控任务，包括：
1. 直接调用关键词监控任务
2. 模拟不同的配置参数
3. 详细的日志输出和错误处理
4. 性能监控和统计信息

使用方法:
1. 确保数据库已初始化并有关键词数据
2. 运行此脚本: python examples/keyword_monitor_debug_example.py
"""

import asyncio
import json
import sys
from datetime import datetime
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from tortoise import Tortoise
from settings.config import settings
from tasks.core.manager import TaskManager
from tasks.core.models import TaskConfig
from tasks.monitors.keyword import KeywordMonitorTask

from log import logger


class KeywordMonitorDebugger:
    """关键词监控任务调试器"""

    def __init__(self):
        self.manager = TaskManager()
        self.manager.register_task("keyword_monitor", KeywordMonitorTask)

    async def init_database(self):
        """初始化数据库连接"""
        try:
            await Tortoise.init(config=settings.tortoise_orm)
            logger.info("✅ 数据库连接初始化成功")
        except Exception as e:
            logger.error(f"❌ 数据库连接失败: {str(e)}")
            raise

    async def close_database(self):
        """关闭数据库连接"""
        try:
            await Tortoise.close_connections()
            logger.info("✅ 数据库连接已关闭")
        except Exception as e:
            logger.warning(f"⚠️ 关闭数据库连接时出错: {str(e)}")

    async def check_keywords_status(self):
        """检查关键词状态"""
        try:
            from models.trendinsight import TrendInsightKeyword
            
            total_count = await TrendInsightKeyword.all().count()
            logger.info(f"📊 数据库中总关键词数量: {total_count}")
            
            if total_count > 0:
                # 获取前5个关键词作为示例
                sample_keywords = await TrendInsightKeyword.all().limit(5)
                logger.info(f"📝 示例关键词:")
                for i, kw in enumerate(sample_keywords, 1):
                    logger.info(f"   {i}. {kw.keyword} (ID: {kw.id}, 更新时间: {kw.updated_at})")
            else:
                logger.warning("⚠️ 数据库中没有关键词数据，请先添加关键词")
                
        except Exception as e:
            logger.error(f"❌ 检查关键词状态失败: {str(e)}")

    async def run_debug_task(self, config_dict: dict):
        """运行调试任务"""
        logger.info(f"\n🚀 开始执行关键词监控调试任务")
        logger.info(f"📋 任务配置: {json.dumps(config_dict, ensure_ascii=False, indent=2)}")
        logger.info("=" * 60)

        try:
            # 验证配置
            task_config = self.manager.validate_config(config_dict)
            logger.info(f"✅ 配置验证通过")

            # 执行任务
            start_time = datetime.now()
            result = await self.manager.execute_task(task_config)
            end_time = datetime.now()
            
            # 输出结果
            logger.info(f"\n🎉 任务执行完成!")
            logger.info(f"⏱️ 执行时间: {(end_time - start_time).total_seconds():.2f} 秒")
            logger.info(f"📊 执行结果:")
            logger.info(f"   - 状态: {result.status}")
            logger.info(f"   - 处理数量: {result.processed_count}")
            logger.info(f"   - 成功数量: {result.success_count}")
            logger.info(f"   - 失败数量: {result.failed_count}")
            
            if hasattr(result, 'errors') and result.errors:
                logger.error(f"❌ 错误信息:")
                for i, error in enumerate(result.errors[:5], 1):  # 只显示前5个错误
                    logger.error(f"   {i}. {error}")
                if len(result.errors) > 5:
                    logger.error(f"   ... 还有 {len(result.errors) - 5} 个错误")

            if hasattr(result, 'performance'):
                logger.info(f"📈 性能指标:")
                perf = result.performance
                if isinstance(perf, dict):
                    for key, value in perf.items():
                        logger.info(f"   - {key}: {value}")

            return result

        except Exception as e:
            logger.error(f"❌ 任务执行失败: {str(e)}")
            import traceback
            logger.error(f"详细错误信息:\n{traceback.format_exc()}")
            return None

    async def run_multiple_configs(self):
        """运行多种配置进行对比测试"""
        configs = [
            {
                "name": "小批量测试",
                "config": {
                    "task_type": "keyword_monitor",
                    "batch_size": 5,
                    "keyword_video_limit": 20,
                    "max_age_hours": 24,
                    "timeout": 300
                }
            },
            {
                "name": "标准配置",
                "config": {
                    "task_type": "keyword_monitor",
                    "batch_size": 20,
                    "keyword_video_limit": 100,
                    "max_age_hours": 1,
                    "timeout": 600
                }
            },
            {
                "name": "大批量测试",
                "config": {
                    "task_type": "keyword_monitor",
                    "batch_size": 50,
                    "keyword_video_limit": 200,
                    "max_age_hours": 6,
                    "timeout": 1200
                }
            }
        ]

        results = []
        for test_case in configs:
            logger.info(f"\n{'='*80}")
            logger.info(f"🧪 测试配置: {test_case['name']}")
            logger.info(f"{'='*80}")
            
            result = await self.run_debug_task(test_case['config'])
            results.append({
                "name": test_case['name'],
                "config": test_case['config'],
                "result": result
            })
            
            # 测试间隔，避免API限流
            logger.info(f"\n⏳ 等待 10 秒后进行下一个测试...")
            await asyncio.sleep(10)

        # 输出对比结果
        logger.info(f"\n{'='*80}")
        logger.info(f"📊 测试结果对比")
        logger.info(f"{'='*80}")
        
        for test_result in results:
            result = test_result['result']
            if result:
                logger.info(f"\n🔍 {test_result['name']}:")
                logger.info(f"   - 批次大小: {test_result['config']['batch_size']}")
                logger.info(f"   - 视频限制: {test_result['config']['keyword_video_limit']}")
                logger.info(f"   - 处理数量: {result.processed_count}")
                logger.info(f"   - 成功率: {result.success_count}/{result.processed_count if result.processed_count > 0 else 1}")
                logger.info(f"   - 状态: {result.status}")

    async def run_single_debug(self):
        """运行单个调试配置"""
        # 使用与 Makefile 中相同的参数
        debug_config = {
            "task_type": "keyword_monitor",
            "batch_size": 20,
            "keyword_video_limit": 100,
            "max_age_hours": 1,
            "timeout": 600
        }
        
        await self.run_debug_task(debug_config)


async def main():
    """主函数"""
    logger.info("🔧 关键词监控任务调试器")
    logger.info("=" * 60)
    
    debugger = KeywordMonitorDebugger()
    
    try:
        # 初始化数据库
        await debugger.init_database()
        
        # 检查关键词状态
        await debugger.check_keywords_status()
        
        # 询问用户选择调试模式
        logger.info(f"\n🎯 请选择调试模式:")
        logger.info(f"1. 单个配置调试 (与 Makefile 相同参数)")
        logger.info(f"2. 多配置对比测试")
        logger.info(f"3. 退出")
        
        choice = input("\n请输入选择 (1-3): ").strip()
        
        if choice == "1":
            await debugger.run_single_debug()
        elif choice == "2":
            await debugger.run_multiple_configs()
        elif choice == "3":
            logger.info("👋 退出调试器")
        else:
            logger.error("❌ 无效选择，退出")
            
    except KeyboardInterrupt:
        logger.warning(f"\n⚠️ 用户中断执行")
    except Exception as e:
        logger.error(f"❌ 调试器运行失败: {str(e)}")
        import traceback
        logger.error(f"详细错误信息:\n{traceback.format_exc()}")
    finally:
        # 关闭数据库连接
        await debugger.close_database()


if __name__ == "__main__":
    logger.info("关键词监控任务调试器")
    logger.info("=" * 60)
    logger.info("📝 注意: 请确保数据库已初始化并包含关键词数据")
    logger.info("🔧 此工具用于调试和测试关键词监控任务的执行")
    logger.info("=" * 60)
    
    asyncio.run(main())
