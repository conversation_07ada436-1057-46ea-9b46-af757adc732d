#!/usr/bin/env python3
"""
抖音视频批量更新示例

本示例演示如何使用优化后的批量更新函数来高效处理大量抖音视频数据
"""

import asyncio
import sys
import os
from datetime import datetime
from typing import List

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), ".."))

from tortoise import Tortoise
from settings.config import settings
from models.douyin.models import DouyinAweme, batch_update_douyin_aweme, batch_update_douyin_aweme_optimized
from log import logger


async def create_sample_data(count: int = 100) -> List[DouyinAweme]:
    """
    创建示例数据
    
    Args:
        count: 要创建的数据数量
        
    Returns:
        List[DouyinAweme]: 示例数据列表
    """
    sample_data = []
    
    for i in range(count):
        aweme = DouyinAweme(
            aweme_id=f"test_aweme_{i:06d}",
            user_id=f"user_{i % 10}",  # 模拟10个不同用户
            sec_uid=f"sec_uid_{i % 10}",
            nickname=f"测试用户{i % 10}",
            title=f"测试视频标题 {i}",
            desc=f"这是第 {i} 个测试视频的描述",
            aweme_type="video",
            create_time=datetime.now(),
            liked_count=str(i * 10),
            comment_count=str(i * 2),
            share_count=str(i),
            collected_count=str(i // 2),
            source_keyword="批量测试"
        )
        sample_data.append(aweme)
    
    return sample_data


async def demo_old_vs_new_batch_update():
    """演示旧版本和新版本批量更新的性能对比"""
    
    logger.info("🚀 开始批量更新性能对比演示")
    
    # 创建测试数据
    test_data_count = 1000
    logger.info(f"📝 创建 {test_data_count} 条测试数据...")
    sample_data = await create_sample_data(test_data_count)
    
    # 测试新版本批量更新
    logger.info("\n🔥 测试新版本批量更新（使用 bulk_create 和 bulk_update）")
    start_time = datetime.now()
    
    success = await batch_update_douyin_aweme(sample_data)
    
    end_time = datetime.now()
    duration = (end_time - start_time).total_seconds()
    
    if success:
        logger.info(f"✅ 新版本批量更新成功！耗时: {duration:.2f} 秒")
    else:
        logger.error("❌ 新版本批量更新失败")
    
    # 修改一些数据用于测试更新
    logger.info("\n📝 修改部分数据用于测试更新操作...")
    for i in range(0, len(sample_data), 2):  # 修改一半的数据
        sample_data[i].title = f"更新后的标题 {i}"
        sample_data[i].liked_count = str(int(sample_data[i].liked_count) + 100)
    
    # 再次测试批量更新（这次会有更新操作）
    logger.info("\n🔄 测试批量更新（包含更新操作）")
    start_time = datetime.now()
    
    success = await batch_update_douyin_aweme(sample_data)
    
    end_time = datetime.now()
    duration = (end_time - start_time).total_seconds()
    
    if success:
        logger.info(f"✅ 批量更新（含更新操作）成功！耗时: {duration:.2f} 秒")
    else:
        logger.error("❌ 批量更新（含更新操作）失败")


async def demo_optimized_batch_update():
    """演示优化版本的批量更新（支持分批处理）"""
    
    logger.info("\n🚀 开始优化版本批量更新演示")
    
    # 创建大量测试数据
    test_data_count = 5000
    logger.info(f"📝 创建 {test_data_count} 条测试数据...")
    sample_data = await create_sample_data(test_data_count)
    
    # 测试优化版本批量更新
    logger.info(f"\n🔥 测试优化版本批量更新（分批处理，每批 500 条）")
    start_time = datetime.now()
    
    success = await batch_update_douyin_aweme_optimized(sample_data, batch_size=500)
    
    end_time = datetime.now()
    duration = (end_time - start_time).total_seconds()
    
    if success:
        logger.info(f"✅ 优化版本批量更新成功！耗时: {duration:.2f} 秒")
        logger.info(f"📊 平均处理速度: {test_data_count / duration:.0f} 条/秒")
    else:
        logger.error("❌ 优化版本批量更新失败")


async def demo_query_performance():
    """演示查询性能"""
    
    logger.info("\n🔍 查询性能演示")
    
    # 查询所有测试数据
    start_time = datetime.now()
    
    test_records = await DouyinAweme.filter(source_keyword="批量测试").all()
    
    end_time = datetime.now()
    duration = (end_time - start_time).total_seconds()
    
    logger.info(f"📊 查询到 {len(test_records)} 条测试记录，耗时: {duration:.3f} 秒")
    
    # 展示一些统计信息
    if test_records:
        unique_users = len(set(record.user_id for record in test_records))
        total_likes = sum(int(record.liked_count or 0) for record in test_records)
        
        logger.info(f"📈 统计信息:")
        logger.info(f"   - 唯一用户数: {unique_users}")
        logger.info(f"   - 总点赞数: {total_likes}")
        logger.info(f"   - 平均点赞数: {total_likes / len(test_records):.1f}")


async def cleanup_test_data():
    """清理测试数据"""
    
    logger.info("\n🧹 清理测试数据...")
    
    # 删除所有测试数据
    deleted_count = await DouyinAweme.filter(source_keyword="批量测试").delete()
    
    logger.info(f"✅ 已删除 {deleted_count} 条测试数据")


async def main():
    """主函数"""
    
    # 初始化数据库连接
    await Tortoise.init(config=settings.TORTOISE_ORM)
    
    try:
        # 演示批量更新性能对比
        await demo_old_vs_new_batch_update()
        
        # 演示优化版本批量更新
        await demo_optimized_batch_update()
        
        # 演示查询性能
        await demo_query_performance()
        
        # 清理测试数据
        await cleanup_test_data()
        
        logger.info("\n🎉 所有演示完成！")
        
    except Exception as e:
        logger.error(f"❌ 演示过程中发生错误: {e}")
        
    finally:
        # 关闭数据库连接
        await Tortoise.close_connections()


if __name__ == "__main__":
    asyncio.run(main())
