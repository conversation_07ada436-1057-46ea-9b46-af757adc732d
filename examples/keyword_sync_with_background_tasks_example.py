#!/usr/bin/env python3
"""
关键词同步后台任务处理示例

这个示例展示了如何使用修改后的关键词同步功能，包括：
1. 调用关键词同步 API
2. 后台任务自动处理视频指数数据
3. 查询处理结果

使用方法:
1. 确保服务器正在运行
2. 运行此脚本: python examples/keyword_sync_with_background_tasks_example.py
"""

import asyncio
import json
from datetime import datetime

import aiohttp
from log import logger


class KeywordSyncExample:
    """关键词同步示例类"""

    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.api_base = f"{base_url}/api/v1/trendinsight"

    async def sync_keyword_videos(self, keyword: str) -> dict:
        """
        调用关键词同步 API

        Args:
            keyword: 要同步的关键词

        Returns:
            dict: API 响应结果
        """
        url = f"{self.api_base}/keyword/sync"
        payload = {"keyword": keyword}

        async with aiohttp.ClientSession() as session:
            async with session.post(url, json=payload) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    error_text = await response.text()
                    raise Exception(f"API 调用失败 (状态码: {response.status}): {error_text}")

    async def check_video_index_data(self, aweme_ids: list) -> dict:
        """
        检查视频的指数数据（模拟查询数据库）

        Args:
            aweme_ids: 视频ID列表

        Returns:
            dict: 查询结果
        """
        # 这里应该是实际的数据库查询，为了示例简化，我们返回模拟数据
        results = {}
        for aweme_id in aweme_ids[:3]:  # 只检查前3个
            results[aweme_id] = {
                "keyword_index": "85.6",  # 模拟指数值
                "trend_score": "92.3",  # 模拟趋势评分
                "processed_at": datetime.now().isoformat(),
            }
        return results

    async def run_example(self, keywords: list):
        """
        运行完整的示例流程

        Args:
            keywords: 要测试的关键词列表
        """
        logger.info("🚀 开始关键词同步后台任务处理示例")
        logger.info("=" * 60)

        for i, keyword in enumerate(keywords, 1):
            logger.info(f"\n📋 测试 {i}/{len(keywords)}: 关键词 '{keyword}'")

            try:
                # 1. 调用同步 API
                logger.info(f"   🔄 正在同步关键词视频...")
                sync_result = await self.sync_keyword_videos(keyword)

                logger.info(f"   ✅ 同步完成:")
                logger.info(f"      - 关键词操作: {sync_result.get('keyword_action', 'unknown')}")
                logger.info(f"      - 同步视频数: {sync_result.get('videos_synced', 0)}")
                logger.info(f"      - 新建关联: {sync_result.get('relations_created', 0)}")
                logger.info(f"      - 已存在关联: {sync_result.get('relations_existing', 0)}")

                # 2. 提取视频ID
                video_items = sync_result.get("video_items", [])
                aweme_ids = [item["aweme_id"] for item in video_items if "aweme_id" in item]

                if aweme_ids:
                    logger.info(f"      - 视频ID数量: {len(aweme_ids)}")
                    logger.info(f"      - 前3个视频ID: {aweme_ids[:3]}")

                    # 3. 检查指数数据处理情况
                    logger.info(f"   📊 检查指数数据处理情况...")

                    # 等待后台任务处理（实际应用中可能需要更长时间）
                    await asyncio.sleep(2)

                    # 查询处理结果
                    index_results = await self.check_video_index_data(aweme_ids)

                    logger.info(f"   ✅ 指数数据处理结果:")
                    for aweme_id, data in index_results.items():
                        logger.info(f"      - 视频 {aweme_id}:")
                        logger.info(f"        * 关键词指数: {data['keyword_index']}")
                        logger.info(f"        * 趋势评分: {data['trend_score']}")
                        logger.info(f"        * 处理时间: {data['processed_at']}")
                else:
                    logger.warning(f"   ⚠️ 没有找到视频数据")

                # 4. 检查错误信息
                errors = sync_result.get("errors", [])
                if errors:
                    logger.error(f"   ❌ 发现错误:")
                    for error in errors:
                        logger.error(f"      - {error}")

            except Exception as e:
                logger.error(f"   ❌ 处理失败: {str(e)}")

            # 添加延迟避免请求过快
            if i < len(keywords):
                logger.info(f"   ⏳ 等待 3 秒后处理下一个关键词...")
                await asyncio.sleep(3)

        logger.info(f"\n🎉 示例运行完成！")
        logger.info(f"\n💡 关键功能说明:")
        logger.info(f"   1. 关键词同步 API 现在支持后台任务处理")
        logger.info(f"   2. 视频的 index 字段会自动提取并存储到数据库")
        logger.info(f"   3. 后台任务与主同步流程并行执行，不影响响应速度")
        logger.info(f"   4. 指数数据存储在 trendinsight_video 表的 keyword_index 字段中")


async def main():
    """主函数"""
    # 测试关键词列表
    test_keywords = ["科技前沿", "人工智能", "新能源汽车"]

    # 创建示例实例
    example = KeywordSyncExample()

    try:
        # 运行示例
        await example.run_example(test_keywords)

    except Exception as e:
        logger.error(f"❌ 示例运行失败: {str(e)}")
        import traceback

        logger.error(f"详细错误信息: {traceback.format_exc()}")


if __name__ == "__main__":
    logger.info("关键词同步后台任务处理示例")
    logger.info("=" * 60)
    logger.info("📝 注意: 请确保服务器正在运行 (http://localhost:8000)")
    logger.info("🔧 如果服务器地址不同，请修改 KeywordSyncExample 的 base_url 参数")
    logger.info("=" * 60)

    asyncio.run(main())
