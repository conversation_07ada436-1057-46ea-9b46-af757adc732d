#!/usr/bin/env python3
"""
Tortoise-ORM 多数据源使用示例

本示例演示如何在多数据源环境中使用 UserInboxSourceRelated 和 UserInboxVideoRelated 模型
"""

import asyncio
import uuid
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), ".."))

from tortoise import Tortoise

# 导入配置
from settings.config import settings

# 导入奇好助手模型（来自独立数据源）
from models.qihaozhushou import UserInboxSourceRelated, UserInboxVideoRelated

# 导入其他模型（来自默认数据源）
from models.douyin.models import DouyinAweme
from log import logger


async def init_database():
    """初始化数据库连接"""
    logger.info("🔧 正在初始化数据库连接...")
    
    # 使用配置文件中的多数据源配置
    await Tortoise.init(config=settings.tortoise_orm)
    
    # 生成数据库表结构（如果不存在）
    await Tortoise.generate_schemas()
    
    logger.info("✅ 数据库连接初始化完成")

    # 获取连接信息
    try:
        connections = list(Tortoise._connections.keys()) if hasattr(Tortoise, '_connections') else []
    except:
        connections = list(settings.tortoise_orm["connections"].keys())

    logger.info(f"📊 已连接的数据库: {connections}")


async def demonstrate_multi_database_operations():
    """演示多数据源操作"""
    
    logger.info("\n🎯 开始多数据源操作演示")
    logger.info("=" * 50)
    
    # 1. 在奇好助手数据库中创建用户收件箱关联记录
    logger.info("\n📝 1. 创建用户收件箱来源关联记录（qihaozhushou 数据库）")
    
    user_uuid = str(uuid.uuid4()).replace('-', '')
    source_id = str(uuid.uuid4()).replace('-', '')
    
    from models.qihaozhushou import SourceType

    source_related = await UserInboxSourceRelated.create(
        uuid=str(uuid.uuid4()).replace('-', ''),
        user_uuid=user_uuid,
        source_id=source_id,
        source_type=SourceType.AUTHOR
    )
    
    logger.info(f"✅ 创建来源关联: {source_related}")
    
    # 2. 在奇好助手数据库中创建视频关联记录
    logger.info("\n📝 2. 创建用户收件箱视频关联记录（qihaozhushou 数据库）")

    from models.qihaozhushou.models import CrawlerType
    from datetime import datetime

    aweme_id = f"aweme_{uuid.uuid4().hex[:16]}"

    video_related = await UserInboxVideoRelated.create(
        uuid=str(uuid.uuid4()).replace('-', ''),
        user_uuid=user_uuid,
        source_id=source_id,
        source_type=CrawlerType.KEYWORD,
        aweme_id=aweme_id,
        publish_time=datetime.now()
    )
    
    logger.info(f"✅ 创建视频关联: {video_related}")
    
    # 3. 查询奇好助手数据库中的记录
    logger.info("\n🔍 3. 查询奇好助手数据库记录")
    
    # 查询用户的所有来源关联
    user_sources = await UserInboxSourceRelated.filter(
        user_uuid=user_uuid,
        is_deleted=False
    ).all()
    
    logger.info(f"📊 用户 {user_uuid[:8]}... 的来源关联数量: {len(user_sources)}")
    
    # 查询用户的所有视频关联
    user_videos = await UserInboxVideoRelated.filter(
        user_uuid=user_uuid,
        is_deleted=False
    ).all()
    
    logger.info(f"📊 用户 {user_uuid[:8]}... 的视频关联数量: {len(user_videos)}")
    
    # 4. 演示跨数据库查询（注意：这需要在应用层面进行关联）
    logger.info("\n🔗 4. 跨数据库数据关联示例")
    
    # 假设我们要关联抖音视频数据（来自默认数据库）
    # 注意：这里只是演示概念，实际使用时需要确保数据存在
    try:
        # 查询默认数据库中的抖音视频（如果存在）
        douyin_videos_count = await DouyinAweme.all().count()
        logger.info(f"📊 默认数据库中的抖音视频数量: {douyin_videos_count}")

        # 在应用层面关联数据
        logger.info("💡 跨数据库关联需要在应用层面进行，例如：")
        logger.info("   - 先从 qihaozhushou 数据库查询用户关联的 video_id")
        logger.info("   - 再从 default 数据库查询对应的视频详情")
        
    except Exception as e:
        logger.warning(f"⚠️  查询默认数据库时出错（可能表不存在）: {e}")
    
    # 5. 演示事务操作（单数据库内）
    logger.info("\n💾 5. 数据库事务操作示例")
    
    try:
        # 在奇好助手数据库中执行事务
        from tortoise.transactions import in_transaction
        
        async with in_transaction("qihaozhushou") as conn:
            # 在事务中创建多个关联记录
            new_source = await UserInboxSourceRelated.create(
                uuid=str(uuid.uuid4()).replace('-', ''),
                user_uuid=user_uuid,
                source_id=str(uuid.uuid4()).replace('-', ''),
                source_type=SourceType.KEYWORD,
                using_db=conn
            )

            new_video = await UserInboxVideoRelated.create(
                uuid=str(uuid.uuid4()).replace('-', ''),
                user_uuid=user_uuid,
                video_id=f"video_{uuid.uuid4().hex[:16]}",
                source_type=SourceType.KEYWORD,
                using_db=conn
            )
            
            logger.info(f"✅ 事务中创建记录: {new_source.uuid[:8]}..., {new_video.uuid[:8]}...")
            
    except Exception as e:
        logger.error(f"❌ 事务操作失败: {e}")


async def demonstrate_connection_usage():
    """演示如何获取和使用特定数据库连接"""
    
    logger.info("\n🔌 数据库连接使用演示")
    logger.info("=" * 30)
    
    # 获取所有连接名称
    try:
        connections = list(Tortoise._connections.keys()) if hasattr(Tortoise, '_connections') else []
    except:
        connections = list(settings.tortoise_orm["connections"].keys())

    logger.info(f"📋 可用连接: {connections}")
    
    # 获取特定连接
    default_conn = Tortoise.get_connection("default")
    qihaozhushou_conn = Tortoise.get_connection("qihaozhushou")
    
    logger.info(f"🔗 默认连接: {default_conn}")
    logger.info(f"🔗 奇好助手连接: {qihaozhushou_conn}")
    
    # 检查连接状态
    try:
        # 执行简单查询测试连接
        result = await qihaozhushou_conn.execute_query("SELECT 1 as test")
        logger.info(f"✅ 奇好助手数据库连接正常: {result}")
    except Exception as e:
        logger.error(f"❌ 奇好助手数据库连接异常: {e}")


async def cleanup_test_data():
    """清理测试数据"""
    logger.info("\n🧹 清理测试数据...")
    
    try:
        # 删除测试创建的记录
        deleted_sources = await UserInboxSourceRelated.all().delete()
        deleted_videos = await UserInboxVideoRelated.all().delete()
        
        logger.info(f"🗑️  已删除 {deleted_sources} 条来源关联记录")
        logger.info(f"🗑️  已删除 {deleted_videos} 条视频关联记录")
        
    except Exception as e:
        logger.warning(f"⚠️  清理数据时出错: {e}")


async def main():
    """主函数"""
    logger.info("🚀 Tortoise-ORM 多数据源使用示例")
    logger.info("=" * 60)
    
    try:
        # 初始化数据库
        await init_database()
        
        # 演示多数据源操作
        await demonstrate_multi_database_operations()
        
        # 演示连接使用
        await demonstrate_connection_usage()
        
        # 清理测试数据
        await cleanup_test_data()
        
        logger.info("\n🎉 多数据源操作演示完成！")
        
    except Exception as e:
        logger.error(f"❌ 执行过程中出错: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        # 关闭数据库连接
        await Tortoise.close_connections()
        logger.info("🔧 数据库连接已关闭")


if __name__ == "__main__":
    asyncio.run(main())
