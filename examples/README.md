# 任务调试示例

这个目录包含了各种任务的调试示例，用于测试和调试不同类型的监控任务。

## 📁 文件列表

### 🔍 关键词监控
- `keyword_monitor_debug_example.py` - 关键词监控任务调试示例
- `keyword_sync_with_background_tasks_example.py` - 关键词同步后台任务示例

> 💡 **新的关键词监控示例已移至 `tasks/examples/` 目录**
> 查看 [`tasks/examples/README.md`](../tasks/examples/README.md) 了解最新的关键词监控示例

### 👥 作者监控
- `author_monitor_debug_example.py` - 作者监控任务调试示例

## 🚀 使用方法

### 前置条件

1. **数据库初始化**
   ```bash
   # 确保数据库已初始化
   python -m tortoise init
   ```

2. **数据准备**
   - 关键词监控：确保 `trendinsight_keyword` 表中有关键词数据
   - 作者监控：确保 `trendinsight_author` 表中有作者数据

### 运行示例

#### 关键词监控调试
```bash
python examples/keyword_monitor_debug_example.py
```

功能包括：
- 单个配置调试（与 Makefile 相同参数）
- 多配置对比测试
- 检查关键词状态
- 性能监控和统计

#### 作者监控调试
```bash
python examples/author_monitor_debug_example.py
```

功能包括：
- 单个配置调试（与 Makefile 相同参数）
- 多配置对比测试
- 过滤条件测试
- 检查过期作者统计
- 显示作者详细信息
- 性能测试

## 🎯 调试模式说明

### 关键词监控调试模式

> 💡 **新的关键词监控示例已移至 `tasks/examples/` 目录**
> 查看 [`tasks/examples/keyword_monitor_examples_guide.md`](../tasks/examples/keyword_monitor_examples_guide.md) 了解详细的使用指南

#### 调试示例模式（当前目录）
1. **单个配置调试** - 使用标准配置运行一次任务
2. **多配置对比测试** - 运行多种不同配置进行性能对比

### 作者监控调试模式

1. **单个配置调试** - 使用标准配置运行一次任务
2. **多配置对比测试** - 运行多种不同配置进行性能对比
3. **过滤条件测试** - 测试特定作者ID和粉丝数过滤
4. **检查过期作者统计** - 查看不同时间范围的过期作者数量
5. **显示作者详细信息** - 显示按粉丝数排序的作者列表
6. **性能测试** - 运行小批量测试并显示性能指标

## 📊 配置参数说明

### 通用参数
- `task_type`: 任务类型（"keyword_monitor" 或 "author_monitor"）
- `batch_size`: 批处理大小
- `max_age_hours`: 最大过期时间（小时）
- `timeout`: 任务超时时间（秒）

### 关键词监控特有参数
- `keyword_video_limit`: 每个关键词获取的视频数量限制

### 作者监控特有参数
- `author_video_limit`: 每个作者获取的视频数量限制
- `filters`: 过滤条件
  - `author_ids`: 指定作者ID列表
  - `min_fans_count`: 最小粉丝数要求

## 🔧 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查 `settings/config.py` 中的数据库配置
   - 确保数据库服务正在运行

2. **没有数据**
   - 关键词监控：检查 `trendinsight_keyword` 表是否有数据
   - 作者监控：检查 `trendinsight_author` 表是否有数据

3. **API 调用失败**
   - 检查网络连接
   - 确认 API 配置正确
   - 注意 API 限流

### 调试技巧

1. **查看详细日志**
   - 示例会输出详细的执行日志
   - 注意错误信息和性能指标

2. **分批测试**
   - 先使用小批量配置测试
   - 确认无误后再使用大批量配置

3. **性能监控**
   - 关注内存使用情况
   - 监控 API 调用频率
   - 记录处理速度

## 📝 注意事项

1. **API 限流**
   - 避免过于频繁的 API 调用
   - 测试间隔建议至少 10 秒

2. **数据一致性**
   - 确保数据库中的数据是最新的
   - 注意时区问题

3. **资源使用**
   - 大批量测试可能消耗较多内存
   - 建议在测试环境中运行

## 🤝 贡献

如果您发现问题或有改进建议，请：
1. 创建 Issue 描述问题
2. 提交 Pull Request 包含修复
3. 更新相关文档
