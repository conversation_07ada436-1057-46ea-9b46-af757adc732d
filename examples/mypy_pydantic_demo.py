#!/usr/bin/env python3
"""
MyPy 和 Pydantic 集成演示

这个脚本展示了如何在项目中使用 mypy 和 Pydantic 插件进行类型检查。

运行 mypy 检查：
python -m mypy --explicit-package-bases --ignore-missing-imports examples/mypy_pydantic_demo.py
"""

from datetime import datetime
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field, validator


class VideoStatistics(BaseModel):
    """视频统计数据模型"""
    
    like_count: int = Field(0, ge=0, description="点赞数")
    comment_count: int = Field(0, ge=0, description="评论数")
    share_count: int = Field(0, ge=0, description="分享数")
    view_count: int = Field(0, ge=0, description="播放数")
    
    @validator('like_count', 'comment_count', 'share_count', 'view_count')
    def validate_counts(cls, v: int) -> int:
        """验证计数字段必须为非负数"""
        if v < 0:
            raise ValueError('计数不能为负数')
        return v


class VideoInfo(BaseModel):
    """视频信息模型"""
    
    video_id: str = Field(..., min_length=1, description="视频ID")
    title: str = Field(..., min_length=1, description="视频标题")
    description: Optional[str] = Field(None, description="视频描述")
    author_name: str = Field(..., min_length=1, description="作者名称")
    create_time: datetime = Field(..., description="创建时间")
    statistics: VideoStatistics = Field(..., description="统计数据")
    tags: List[str] = Field(default_factory=list, description="标签列表")
    
    model_config = {
        # Pydantic V2 配置
        "json_encoders": {
            datetime: lambda v: v.isoformat()
        }
    }


class BatchUpdateResult(BaseModel):
    """批量更新结果模型"""
    
    total_processed: int = Field(..., ge=0, description="处理总数")
    created_count: int = Field(..., ge=0, description="创建数量")
    updated_count: int = Field(..., ge=0, description="更新数量")
    failed_count: int = Field(..., ge=0, description="失败数量")
    duration_seconds: float = Field(..., ge=0, description="处理耗时（秒）")
    
    @validator('created_count', 'updated_count', 'failed_count')
    def validate_counts_sum(cls, v: int, values: Dict[str, Any]) -> int:
        """验证各项计数的合理性"""
        if 'total_processed' in values:
            total = values['total_processed']
            if v > total:
                raise ValueError(f'单项计数 {v} 不能超过总数 {total}')
        return v


def process_video_batch(videos: List[VideoInfo]) -> BatchUpdateResult:
    """
    批量处理视频数据
    
    Args:
        videos: 视频信息列表
        
    Returns:
        BatchUpdateResult: 处理结果
    """
    start_time: datetime = datetime.now()
    
    # 类型安全的数据处理
    created_count: int = 0
    updated_count: int = 0
    failed_count: int = 0
    
    for video in videos:
        try:
            # 模拟数据处理逻辑
            video_id: str = video.video_id
            title: str = video.title
            stats: VideoStatistics = video.statistics
            
            # 根据某些条件决定是创建还是更新
            if len(video_id) > 10:
                created_count += 1
            else:
                updated_count += 1
                
        except Exception:
            failed_count += 1
    
    # 计算处理时间
    end_time: datetime = datetime.now()
    duration: float = (end_time - start_time).total_seconds()
    
    # 返回类型安全的结果
    return BatchUpdateResult(
        total_processed=len(videos),
        created_count=created_count,
        updated_count=updated_count,
        failed_count=failed_count,
        duration_seconds=duration
    )


def create_sample_videos() -> List[VideoInfo]:
    """创建示例视频数据"""
    
    videos: List[VideoInfo] = []
    
    for i in range(5):
        video: VideoInfo = VideoInfo(
            video_id=f"video_{i:03d}_{datetime.now().timestamp()}",
            title=f"测试视频 {i + 1}",
            description=f"这是第 {i + 1} 个测试视频",
            author_name=f"作者_{i + 1}",
            create_time=datetime.now(),
            statistics=VideoStatistics(
                like_count=100 * (i + 1),
                comment_count=50 * (i + 1),
                share_count=10 * (i + 1),
                view_count=1000 * (i + 1)
            ),
            tags=[f"标签{j}" for j in range(1, i + 2)]
        )
        videos.append(video)
    
    return videos


def main() -> None:
    """主函数演示"""
    
    print("🚀 MyPy + Pydantic 集成演示")
    print("=" * 50)
    
    # 创建示例数据
    videos: List[VideoInfo] = create_sample_videos()
    print(f"📊 创建了 {len(videos)} 个示例视频")
    
    # 批量处理
    result: BatchUpdateResult = process_video_batch(videos)
    
    # 输出结果
    print("\n📈 处理结果:")
    print(f"  总处理数: {result.total_processed}")
    print(f"  创建数量: {result.created_count}")
    print(f"  更新数量: {result.updated_count}")
    print(f"  失败数量: {result.failed_count}")
    print(f"  处理耗时: {result.duration_seconds:.3f} 秒")
    
    # 展示 JSON 序列化（Pydantic V2 语法）
    print("\n📄 JSON 序列化示例:")
    sample_video: VideoInfo = videos[0]
    print(sample_video.model_dump_json(indent=2))


if __name__ == "__main__":
    main()
