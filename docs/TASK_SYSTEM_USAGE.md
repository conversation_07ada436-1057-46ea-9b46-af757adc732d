# 任务系统使用文档

## 概述

本任务系统是一个独立的定时任务执行框架，专门用于自动化处理TrendInsight平台的数据监控和刷新工作。系统支持多种任务类型，包括视频趋势分数刷新、作者监控和关键词监控。

## 支持的任务类型

### 1. 趋势刷新任务 (trend_refresh)

自动刷新TrendInsight视频的趋势分数。

**使用场景：**
- 定期更新过期视频的趋势数据
- 确保分析报告基于最新的趋势信息

**配置示例：**
```json
{
  "task_type": "trend_refresh",
  "batch_size": 100,
  "timeout": 3600,
  "max_age_hours": 1,
  "filters": {
    "video_ids": ["123", "456"],
    "date_range": {
      "start": "2025-01-20",
      "end": "2025-01-21"
    }
  }
}
```

### 2. 作者监控任务 (author_monitor)

监控TrendInsight作者数据，自动同步作者的新视频内容。

**使用场景：**
- 自动发现作者发布的新视频
- 维护作者与视频的关联关系
- 定期更新作者数据状态

**配置示例：**
```json
{
  "task_type": "author_monitor",
  "batch_size": 50,
  "timeout": 7200,
  "max_age_hours": 1,
  "author_video_limit": 50,
  "filters": {
    "author_ids": ["author123", "author456"],
    "min_fans_count": 10000
  }
}
```

### 3. 关键词监控任务 (keyword_monitor)

监控TrendInsight关键词，自动搜索和同步相关新视频。

**使用场景：**
- 基于关键词发现相关热门视频
- 跟踪特定话题的视频趋势
- 建立关键词与视频的关联关系

**配置示例：**
```json
{
  "task_type": "keyword_monitor",
  "batch_size": 20,
  "timeout": 7200,
  "max_age_hours": 1,
  "keyword_video_limit": 100,
  "keyword_search_days": 7,
  "filters": {
    "keywords": ["美食", "旅游"],
    "exclude_keywords": ["广告"]
  }
}
```

## 配置参数说明

### 通用参数

| 参数名 | 类型 | 默认值 | 说明 | 约束 |
|--------|------|--------|------|------|
| `task_type` | string | 必填 | 任务类型 | 必须是 `trend_refresh`、`author_monitor` 或 `keyword_monitor` |
| `batch_size` | int | 100 | 批处理大小 | 1-1000 |
| `timeout` | int | 3600 | 超时时间（秒） | 1-86400 |
| `max_age_hours` | int | 1 | 数据过期时间（小时） | 1-168 |
| `filters` | object | null | 过滤条件 | 可选 |

### 作者监控特定参数

| 参数名 | 类型 | 默认值 | 说明 | 约束 |
|--------|------|--------|------|------|
| `author_video_limit` | int | 50 | 每个作者最多获取的视频数量 | 1-200 |

### 关键词监控特定参数

| 参数名 | 类型 | 默认值 | 说明 | 约束 |
|--------|------|--------|------|------|
| `keyword_video_limit` | int | 100 | 每个关键词最多获取的视频数量 | 1-500 |
| `keyword_search_days` | int | 7 | 关键词搜索的天数范围 | 1-30 |

### 过滤条件参数

#### 趋势刷新任务过滤条件
```json
{
  "filters": {
    "video_ids": ["video1", "video2"],  // 指定视频ID列表
    "date_range": {
      "start": "2025-01-20",            // 开始日期
      "end": "2025-01-21"               // 结束日期
    }
  }
}
```

#### 作者监控任务过滤条件
```json
{
  "filters": {
    "author_ids": ["author1", "author2"], // 指定作者ID列表
    "min_fans_count": 10000               // 最小粉丝数量
  }
}
```

#### 关键词监控任务过滤条件
```json
{
  "filters": {
    "keywords": ["美食", "旅游"],          // 包含的关键词列表
    "exclude_keywords": ["广告", "推广"]   // 排除的关键词列表
  }
}
```

## 部署方式

### 1. 命令行方式 (Cron)

**安装步骤：**
```bash
# 1. 确保Python环境
python3 --version  # >= 3.11

# 2. 安装依赖
pip install -r requirements.txt

# 3. 配置数据库连接
cp settings/development.toml.example settings/production.toml
# 编辑 settings/production.toml 配置数据库连接
```

**执行任务：**
```bash
# 趋势刷新任务
python tasks/main.py '{"task_type": "trend_refresh", "batch_size": 100}'

# 作者监控任务
python tasks/main.py '{"task_type": "author_monitor", "batch_size": 50, "author_video_limit": 50}'

# 关键词监控任务
python tasks/main.py '{"task_type": "keyword_monitor", "batch_size": 20, "keyword_video_limit": 100}'
```

**配置Cron定时任务：**
```bash
# 编辑crontab
crontab -e

# 添加定时任务
# 每小时执行趋势刷新
0 */1 * * * cd /path/to/project && python tasks/main.py '{"task_type": "trend_refresh"}' >> /var/log/trend-refresh.log 2>&1

# 每小时15分执行作者监控
15 */1 * * * cd /path/to/project && python tasks/main.py '{"task_type": "author_monitor"}' >> /var/log/author-monitor.log 2>&1

# 每小时30分执行关键词监控
30 */1 * * * cd /path/to/project && python tasks/main.py '{"task_type": "keyword_monitor"}' >> /var/log/keyword-monitor.log 2>&1
```

### 2. Docker容器部署

**Dockerfile 示例：**
```dockerfile
FROM python:3.11-slim

WORKDIR /app
COPY . .

RUN pip install --no-cache-dir -r requirements.txt

# 创建日志目录
RUN mkdir -p /app/logs

# 设置环境变量
ENV PYTHONPATH=/app
ENV DYNACONF_ENV=production

# 任务执行入口
ENTRYPOINT ["python", "tasks/main.py"]
```

**构建和运行：**
```bash
# 构建镜像
docker build -t task-system:latest .

# 运行趋势刷新任务
docker run --rm \
  -v $(pwd)/logs:/app/logs \
  -e DATABASE_URL="mysql://user:pass@host:port/db" \
  task-system:latest '{"task_type": "trend_refresh"}'

# 运行作者监控任务
docker run --rm \
  -v $(pwd)/logs:/app/logs \
  -e DATABASE_URL="mysql://user:pass@host:port/db" \
  task-system:latest '{"task_type": "author_monitor", "author_video_limit": 50}'
```

**Docker Compose 示例：**
```yaml
version: '3.8'

services:
  trend-refresh:
    build: .
    volumes:
      - ./logs:/app/logs
      - ./settings:/app/settings
    environment:
      - DYNACONF_ENV=production
      - DATABASE_URL=mysql://user:pass@db:3306/dbname
    command: '{"task_type": "trend_refresh", "batch_size": 100}'
    depends_on:
      - db

  author-monitor:
    build: .
    volumes:
      - ./logs:/app/logs
      - ./settings:/app/settings
    environment:
      - DYNACONF_ENV=production
      - DATABASE_URL=mysql://user:pass@db:3306/dbname
    command: '{"task_type": "author_monitor", "batch_size": 50}'
    depends_on:
      - db

  db:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword
      MYSQL_DATABASE: dbname
    ports:
      - "3306:3306"
```

### 3. Kubernetes CronJob 部署

**趋势刷新任务 CronJob：**
```yaml
apiVersion: batch/v1
kind: CronJob
metadata:
  name: trend-refresh-task
  namespace: default
spec:
  schedule: "0 */1 * * *"  # 每小时执行
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: trend-refresh
            image: task-system:latest
            args: ['{"task_type": "trend_refresh", "batch_size": 100}']
            env:
            - name: DYNACONF_ENV
              value: "production"
            - name: DATABASE_URL
              valueFrom:
                secretKeyRef:
                  name: db-secret
                  key: database-url
            volumeMounts:
            - name: config-volume
              mountPath: /app/settings
            - name: logs-volume
              mountPath: /app/logs
          volumes:
          - name: config-volume
            configMap:
              name: app-config
          - name: logs-volume
            persistentVolumeClaim:
              claimName: logs-pvc
          restartPolicy: OnFailure
```

**作者监控任务 CronJob：**
```yaml
apiVersion: batch/v1
kind: CronJob
metadata:
  name: author-monitor-task
  namespace: default
spec:
  schedule: "15 */1 * * *"  # 每小时15分执行
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: author-monitor
            image: task-system:latest
            args: ['{"task_type": "author_monitor", "batch_size": 50, "author_video_limit": 50}']
            env:
            - name: DYNACONF_ENV
              value: "production"
            - name: DATABASE_URL
              valueFrom:
                secretKeyRef:
                  name: db-secret
                  key: database-url
          restartPolicy: OnFailure
```

**关键词监控任务 CronJob：**
```yaml
apiVersion: batch/v1
kind: CronJob
metadata:
  name: keyword-monitor-task
  namespace: default
spec:
  schedule: "30 */1 * * *"  # 每小时30分执行
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: keyword-monitor
            image: task-system:latest
            args: ['{"task_type": "keyword_monitor", "batch_size": 20, "keyword_video_limit": 100}']
            env:
            - name: DYNACONF_ENV
              value: "production"
            - name: DATABASE_URL
              valueFrom:
                secretKeyRef:
                  name: db-secret
                  key: database-url
          restartPolicy: OnFailure
```

### 4. Function Compute 部署

**s.yaml 配置文件：**
```yaml
edition: 3.0.0
name: trend-refresh-app
access: default

resources:
  trend-refresh-function:
    component: fc3
    props:
      region: cn-hangzhou
      functionName: trend-refresh-tasks
      description: TrendInsight任务系统
      runtime: python3.11
      code: ./
      handler: tasks.main.handler
      memorySize: 1024
      timeout: 900
      environmentVariables:
        BATCH_SIZE: "100"
        TIMEOUT: "3600"
        MAX_AGE_HOURS: "1"
        PYTHONPATH: "/code"
        DYNACONF_ENV: "production"
      triggers:
        # 趋势刷新任务触发器
        - triggerName: trend-refresh-trigger
          triggerType: timer
          triggerConfig:
            cronExpression: "0 0 */1 * * *"  # 每小时执行
            enable: true
            payload: |
              {
                "task_type": "trend_refresh",
                "batch_size": 100
              }
        
        # 作者监控任务触发器
        - triggerName: author-monitor-trigger
          triggerType: timer
          triggerConfig:
            cronExpression: "0 15 */1 * * *"  # 每小时15分执行
            enable: true
            payload: |
              {
                "task_type": "author_monitor",
                "batch_size": 50,
                "author_video_limit": 50
              }
        
        # 关键词监控任务触发器
        - triggerName: keyword-monitor-trigger
          triggerType: timer
          triggerConfig:
            cronExpression: "0 30 */1 * * *"  # 每小时30分执行
            enable: true
            payload: |
              {
                "task_type": "keyword_monitor",
                "batch_size": 20,
                "keyword_video_limit": 100,
                "keyword_search_days": 7
              }
```

**部署和管理：**
```bash
# 安装Serverless Devs工具
npm install -g @serverless-devs/s

# 配置访问凭证
s config add --AccessKeyID your-access-key-id --AccessKeySecret your-access-key-secret

# 部署函数
s deploy

# 查看部署状态
s info

# 手动触发函数测试
s invoke --event '{"task_type": "trend_refresh", "batch_size": 50}'

# 查看函数日志
s logs --tail

# 更新函数代码
s deploy --use-local

# 删除函数
s remove
```

## 故障排查指南

### 常见问题和解决方案

#### 1. 数据库连接问题

**问题症状：**
```
Error: database connection failed
```

**排查步骤：**
1. 检查数据库连接配置
```bash
# 检查配置文件
cat settings/production.toml | grep -A 5 "\[database\]"
```

2. 测试数据库连接
```bash
# 使用MySQL客户端测试
mysql -h host -u user -p database
```

3. 检查网络连接
```bash
# 测试端口连通性
telnet database-host 3306
```

**解决方案：**
- 确保数据库服务正在运行
- 检查网络防火墙设置
- 验证用户名密码正确性
- 确认数据库授权配置

#### 2. API调用失败

**问题症状：**
```
Error: TrendInsight API调用失败
Error: 抖音API返回错误
```

**排查步骤：**
1. 检查API端点状态
```bash
# 测试API连通性
curl -I https://api.trendinsight.com/health
```

2. 验证认证信息
```bash
# 检查API密钥配置
echo $TRENDINSIGHT_API_KEY
```

3. 查看详细错误日志
```bash
# 查看最近的错误日志
tail -f logs/application.log | grep -i error
```

**解决方案：**
- 检查API密钥有效性
- 确认API限流策略
- 验证请求参数格式
- 联系API提供方确认服务状态

#### 3. 内存不足问题

**问题症状：**
```
Error: Out of memory
Warning: Memory usage exceeds threshold
```

**排查步骤：**
1. 检查内存使用情况
```bash
# 查看系统内存
free -h

# 查看进程内存使用
ps aux | grep python | head -10
```

2. 分析任务配置
```bash
# 检查批处理大小设置
grep -r "batch_size" logs/
```

**解决方案：**
- 减少批处理大小（batch_size）
- 增加系统内存资源
- 优化数据查询逻辑
- 使用流式处理避免大量数据加载

#### 4. 任务超时问题

**问题症状：**
```
Error: Task execution timeout
```

**排查步骤：**
1. 检查超时配置
```json
{
  "timeout": 3600  // 当前超时时间（秒）
}
```

2. 分析执行时间
```bash
# 查看执行时间日志
grep "duration" logs/application.log | tail -10
```

**解决方案：**
- 增加超时时间设置
- 优化数据处理逻辑
- 减少批处理大小
- 并行处理提高效率

#### 5. Function Compute特定问题

**问题症状：**
```
Error: Function initialization failed
Error: Cold start timeout
```

**Function Compute 调试方法：**

1. **查看函数日志：**
```bash
# 使用s工具查看日志
s logs --tail

# 或在阿里云控制台查看
# 函数计算 -> 服务及函数 -> 函数详情 -> 调用日志
```

2. **本地测试函数：**
```bash
# 创建测试事件文件
cat > test-event.json << EOF
{
  "task_type": "trend_refresh",
  "batch_size": 10
}
EOF

# 本地测试
s local invoke --event-file test-event.json
```

3. **检查函数配置：**
```bash
# 查看函数信息
s info

# 检查环境变量
s describe | grep -A 10 environmentVariables
```

4. **性能优化：**
- 增加函数内存配置（建议1024MB以上）
- 优化冷启动时间（减少依赖库）
- 使用预留实例避免冷启动
- 监控函数执行指标

### 日志分析

#### 日志级别说明
- **INFO**: 正常执行信息
- **WARNING**: 警告信息，不影响执行
- **ERROR**: 错误信息，可能影响执行结果
- **DEBUG**: 调试信息，详细执行过程

#### 关键日志模式

**任务开始：**
```
INFO - 开始执行任务: trend_refresh
INFO - 任务配置: {"task_type": "trend_refresh", "batch_size": 100}
```

**进度更新：**
```
INFO - 已处理 100 个视频，成功 95，失败 5
INFO - 处理进度: 100/1000 (10.0%)
```

**错误信息：**
```
ERROR - API调用失败: video_123, 错误: Connection timeout
WARNING - 视频 video_456 未获取到有效趋势数据
```

**任务完成：**
```
INFO - 任务执行完成: trend_refresh
INFO - 执行结果: {"status": "success", "processed": 1000, "success": 950, "failed": 50}
```

### 监控和告警

#### 关键监控指标

1. **执行成功率**
   - 成功任务数 / 总任务数
   - 目标：> 95%

2. **执行时间**
   - 平均执行时间
   - 目标：< 30分钟（趋势刷新）

3. **API调用成功率**
   - 成功API调用数 / 总API调用数
   - 目标：> 98%

4. **数据处理量**
   - 每小时处理的记录数
   - 监控数据处理趋势

#### 告警设置建议

```bash
# 示例：设置基于日志的告警
# 错误率告警
grep -c "ERROR" logs/application.log > /tmp/error_count.txt
if [ $(cat /tmp/error_count.txt) -gt 50 ]; then
  echo "Error rate too high!" | mail -s "Task System Alert" <EMAIL>
fi

# 执行时间告警
if [ $(grep "duration.*[3-9][0-9][0-9][0-9]" logs/application.log | wc -l) -gt 0 ]; then
  echo "Task execution time too long!" | mail -s "Performance Alert" <EMAIL>
fi
```

## 最佳实践

### 1. 配置优化

**批处理大小选择：**
- 趋势刷新：100-200（平衡API调用效率和内存使用）
- 作者监控：30-50（单个作者处理较复杂）
- 关键词监控：10-20（搜索API调用较重）

**超时设置：**
- 趋势刷新：3600秒（1小时）
- 作者监控：7200秒（2小时）
- 关键词监控：7200秒（2小时）

### 2. 部署策略

**生产环境建议：**
- 使用Function Compute或Kubernetes CronJob
- 设置适当的重试机制
- 配置日志轮转和归档
- 监控资源使用情况

**开发环境建议：**
- 使用命令行方式快速测试
- 减少批处理大小加快测试
- 启用DEBUG日志级别
- 使用模拟数据测试

### 3. 性能优化

**数据库优化：**
- 为常用查询字段添加索引
- 使用流式查询避免内存溢出
- 定期清理过期日志数据

**API调用优化：**
- 实现指数退避重试策略
- 合理设置API调用间隔
- 使用连接池复用连接
- 监控API限流状态

**内存管理：**
- 及时释放不再使用的对象
- 使用生成器进行大数据处理
- 监控内存使用阈值
- 配置合适的垃圾收集策略

这份文档涵盖了任务系统的完整使用指南，包括配置、部署、监控和故障排查。用户可以根据实际需求选择合适的部署方式和配置参数。