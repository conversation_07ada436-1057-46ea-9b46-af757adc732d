# Tortoise-ORM 多数据源配置指南

本文档详细说明如何在项目中配置和使用 Tortoise-ORM 多数据源功能。

## 📋 概述

项目现已配置为支持多数据源：
- **default**: 默认数据库，存储抖音、系统、趋势洞察等模型
- **qihaozhushou**: 奇好助手专用数据库，存储用户收件箱相关模型

## 🏗️ 配置结构

### 1. 配置文件修改

各环境配置文件已更新为支持多数据源：

#### 开发环境 (`settings/development.toml`)
```toml
[tortoise_orm]
[tortoise_orm.connections]
default = "sqlite://db.sqlite3"
qihaozhushou = "sqlite://qihaozhushou_dev.sqlite3"

[tortoise_orm.apps]
[tortoise_orm.apps.models]
models = ["models.douyin", "models.system", "models.trendinsight", "aerich.models"]
default_connection = "default"

[tortoise_orm.apps.qihaozhushou]
models = ["models.qihaozhushou"]
default_connection = "qihaozhushou"
```

#### 预发布环境 (`settings/staging.toml`)
```toml
[tortoise_orm]
[tortoise_orm.connections]
default = "sqlite://staging.sqlite3"
qihaozhushou = "mysql://qihaozhushou:qihaozhushou2025%40@172.19.73.245:3306/qihaozhushou_staging"

[tortoise_orm.apps]
[tortoise_orm.apps.models]
models = ["models.douyin", "models.system", "models.trendinsight", "aerich.models"]
default_connection = "default"

[tortoise_orm.apps.qihaozhushou]
models = ["models.qihaozhushou"]
default_connection = "qihaozhushou"
```

#### 生产环境 (`settings/production.toml`)
```toml
qihaozhushou_database_uri = "mysql://qihaozhushou:qihaozhushou2025%40@47.101.186.209:3306/qihaozhushou_prod"

[tortoise_orm]
[tortoise_orm.connections]
default = "@format {this.database_uri}"
qihaozhushou = "@format {this.qihaozhushou_database_uri}"

[tortoise_orm.apps]
[tortoise_orm.apps.models]
models = ["models.douyin", "models.system", "models.trendinsight", "aerich.models"]
default_connection = "default"

[tortoise_orm.apps.qihaozhushou]
models = ["models.qihaozhushou"]
default_connection = "qihaozhushou"
```

### 2. 模型分离

奇好助手模型已从主模型导入中分离：

```python
# models/__init__.py - 不再导入 qihaozhushou 模型
from .douyin import *
from .system import *
from .trendinsight import *

# models/qihaozhushou/__init__.py - 独立导入
from .models import *
```

## 🚀 使用方法

### 1. 模型导入

```python
# 导入奇好助手模型（来自独立数据源）
from models.qihaozhushou import UserInboxSourceRelated, UserInboxVideoRelated

# 导入其他模型（来自默认数据源）
from models.douyin.models import DouyinVideo
from models.system.crawler import CrawlerCookiesAccount
```

### 2. 基本操作

```python
import asyncio
from tortoise import Tortoise
from settings.config import settings

async def example_usage():
    # 初始化多数据源
    await Tortoise.init(config=settings.TORTOISE_ORM)
    await Tortoise.generate_schemas()
    
    # 在奇好助手数据库中操作
    from models.qihaozhushou import SourceType
    source_related = await UserInboxSourceRelated.create(
        uuid="test_uuid",
        user_uuid="user_123",
        source_id="source_456",
        source_type=SourceType.AUTHOR
    )
    
    # 查询奇好助手数据库
    user_sources = await UserInboxSourceRelated.filter(
        user_uuid="user_123"
    ).all()
    
    # 关闭连接
    await Tortoise.close_connections()
```

### 3. 事务操作

```python
from tortoise.transactions import in_transaction

async def transaction_example():
    # 在特定数据库中执行事务
    async with in_transaction("qihaozhushou") as conn:
        source = await UserInboxSourceRelated.create(
            uuid="uuid1",
            user_uuid="user_123",
            source_id="source_789",
            source_type="keyword",
            using_db=conn
        )
        
        video = await UserInboxVideoRelated.create(
            uuid="uuid2",
            user_uuid="user_123",
            video_id="video_456",
            source_type="keyword",
            using_db=conn
        )
```

### 4. 连接管理

```python
# 获取特定连接
default_conn = Tortoise.get_connection("default")
qihaozhushou_conn = Tortoise.get_connection("qihaozhushou")

# 获取所有连接名称
connections = Tortoise.get_connection_names()
print(f"可用连接: {connections}")
```

## 🔧 数据库迁移

### 1. Aerich 配置

由于使用多数据源，需要为每个数据库单独管理迁移：

```bash
# 为默认数据库生成迁移
aerich init -t settings.TORTOISE_ORM --app models

# 为奇好助手数据库生成迁移
aerich init -t settings.TORTOISE_ORM --app qihaozhushou
```

### 2. 迁移命令

```bash
# 生成迁移文件
aerich migrate --app models
aerich migrate --app qihaozhushou

# 应用迁移
aerich upgrade --app models
aerich upgrade --app qihaozhushou
```

## ⚠️ 注意事项

### 1. 跨数据库关联

Tortoise-ORM 不支持跨数据库的外键关联，需要在应用层面处理：

```python
# ❌ 不支持跨数据库外键
class CrossDBModel(models.Model):
    # 这样的关联不会工作
    douyin_video = fields.ForeignKeyField("models.DouyinVideo")

# ✅ 正确的做法：在应用层面关联
async def get_user_videos_with_details(user_uuid: str):
    # 1. 从奇好助手数据库获取视频ID列表
    video_relations = await UserInboxVideoRelated.filter(
        user_uuid=user_uuid
    ).all()
    
    video_ids = [rel.video_id for rel in video_relations]
    
    # 2. 从默认数据库获取视频详情
    videos = await DouyinVideo.filter(
        id__in=video_ids
    ).all()
    
    return videos
```

### 2. 事务限制

事务只能在单个数据库内执行，不支持分布式事务：

```python
# ✅ 单数据库事务
async with in_transaction("qihaozhushou"):
    # 只能操作 qihaozhushou 数据库中的模型
    pass

# ❌ 不支持跨数据库事务
async with in_transaction():
    # 无法同时操作多个数据库
    pass
```

### 3. 连接池配置

对于生产环境，建议配置连接池参数：

```toml
[tortoise_orm.connections]
default = "mysql://user:pass@host:port/db?minsize=1&maxsize=20"
qihaozhushou = "mysql://user:pass@host:port/qihao_db?minsize=1&maxsize=10"
```

## 🧪 测试和验证

### 1. 配置验证

运行配置验证脚本：

```bash
python scripts/validate_multi_database_config.py
```

### 2. 使用示例

查看完整的使用示例：

```bash
python examples/multi_database_usage.py
```

## 📚 相关文档

- [Tortoise-ORM 官方文档](https://tortoise.github.io/)
- [多数据库配置](https://tortoise.github.io/databases.html)
- [Aerich 迁移工具](https://github.com/tortoise/aerich)

## 🔄 迁移清单

- [x] 更新配置文件支持多数据源
- [x] 分离奇好助手模型导入
- [x] 创建使用示例和验证脚本
- [x] 编写配置文档
- [ ] 更新现有代码中的模型导入
- [ ] 配置生产环境数据库
- [ ] 执行数据迁移（如需要）
- [ ] 更新部署脚本
