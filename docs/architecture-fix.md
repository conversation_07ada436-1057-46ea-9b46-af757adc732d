# 架构修复方案：修复 fetch_html 重复实现问题

## 🔍 问题分析

当前存在的架构问题：

1. **重复实现**: `fetch_html` 在多个地方都有实现
   - `rpc/douyin/html_handler/client.py` - ✅ 正确的实现
   - `utils/douyin/extract/jingxuan_data_extractor.py` - ❌ 错误的实现
   - `utils/douyin/extract/douyin_data_extractor.py` - ❌ 错误的实现

2. **职责混乱**: 工具函数直接做网络请求
3. **依赖倒置**: 底层工具依赖网络层

## 🎯 正确的架构设计

### 分层架构
```
🌐 网络层 (RPC)
    ↓
🎮 控制层 (Controllers) 
    ↓
🔄 映射层 (Mappers)
    ↓
🛠️ 工具层 (Utils) - 纯函数，不做网络请求
```

### 职责分离

1. **RPC层** (`rpc/douyin/html_handler/`)
   - 负责所有网络请求
   - 统一的HTML获取接口
   - 专业的错误处理和重试机制

2. **控制层** (`controllers/douyin/`)
   - 协调RPC层和映射层
   - 业务逻辑流程控制
   - 统一的接口封装

3. **映射层** (`mapper/douyin/`)
   - 纯数据转换
   - 将原始数据映射为标准格式
   - 不涉及网络请求

4. **工具层** (`utils/douyin/`)
   - 纯函数，只做数据处理
   - HTML解析、数据提取
   - 不做任何网络请求

## 🚀 修复方案

### 1. 移除工具函数中的网络请求

#### ❌ 错误的实现 (当前)
```python
# utils/douyin/extract/jingxuan_data_extractor.py
class JingxuanDataExtractor:
    def fetch_html(self, url: str) -> str:
        # 直接做网络请求 - 违反单一职责原则
        response = httpx.get(url)
        return response.text
```

#### ✅ 正确的实现 (修复后)
```python
# utils/douyin/extract/jingxuan_data_extractor.py
class JingxuanDataExtractor:
    def extract_pace_f_data(self, html_content: str, aweme_id: str) -> str:
        # 只做数据提取，不做网络请求
        # HTML内容由外层传入
        ...
    
    def parse_jingxuan_data(self, pace_f_data: str) -> Dict[str, Any]:
        # 纯数据处理函数
        ...
```

### 2. 控制层正确使用RPC层

#### ✅ 正确的流程
```python
# controllers/douyin/video_fetcher_controller.py
async def _fetch_jingxuan_data(self, aweme_id: str, options: Dict) -> Dict:
    # 1. 使用RPC层获取HTML
    from rpc.douyin.html_handler.client import html_client
    from rpc.douyin.html_handler.schemas import JingxuanRequest
    
    request = JingxuanRequest(aweme_id=aweme_id, ...)
    response = await html_client.fetch_jingxuan_page(request)
    
    # 2. 使用工具层处理HTML内容
    from utils.douyin.extract.jingxuan_data_extractor import JingxuanDataExtractor
    
    extractor = JingxuanDataExtractor()
    pace_f_data = extractor.extract_pace_f_data(response.content, aweme_id)
    parsed_data = extractor.parse_jingxuan_data(pace_f_data)
    
    return parsed_data
```

### 3. 统一网络请求入口

所有HTML获取都应该通过RPC层：

```python
# rpc/douyin/html_handler/client.py
class DouyinHTMLClient:
    async def fetch_html(self, request: HTMLRequest) -> HTMLResponse:
        # 统一的HTML获取实现
        # 包含代理、重试、错误处理等
        ...
    
    async def fetch_jingxuan_page(self, request: JingxuanRequest) -> HTMLResponse:
        # 特定页面的获取方法
        ...
    
    async def fetch_mobile_share_page(self, request: MobileShareRequest) -> HTMLResponse:
        # 移动端分享页面获取
        ...
```

## 📋 具体修复步骤

### 步骤1: 重构工具函数
- [ ] 移除 `utils/douyin/extract/jingxuan_data_extractor.py` 中的 `fetch_html` 方法
- [ ] 将其改为纯数据处理函数 `extract_pace_f_data(html_content, aweme_id)`
- [ ] 移除 `utils/douyin/extract/douyin_data_extractor.py` 中的网络请求代码

### 步骤2: 修复控制层
- [ ] 更新 `VideoFetcherController` 使用RPC层获取HTML
- [ ] 确保控制层只协调，不直接做网络请求

### 步骤3: 验证RPC层
- [ ] 确认 `rpc/douyin/html_handler/client.py` 提供所有必要的接口
- [ ] 补充缺失的页面类型支持

### 步骤4: 测试验证
- [ ] 测试新的架构是否正常工作
- [ ] 确保没有重复的网络请求实现

## 🎯 预期效果

修复后的架构将实现：

1. **单一职责**: 每层只负责自己的事情
2. **依赖正确**: 上层依赖下层，而不是相反
3. **可维护性**: 网络请求逻辑集中在RPC层
4. **可测试性**: 工具函数变成纯函数，易于测试
5. **一致性**: 所有HTML获取都通过同一套机制

## 📝 注意事项

1. 修改过程中要保持向后兼容性
2. 现有的接口不要破坏
3. 逐步重构，不要一次性改动太大
4. 每个步骤都要有测试验证

这样的架构修复将让代码更加清晰、可维护，并符合软件设计的最佳实践。