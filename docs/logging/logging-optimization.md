# Loguru + Dynaconf 日志系统优化指南

## 📋 概述

本文档详细说明如何基于 loguru 和 dynaconf 的最佳实践，对现有日志系统进行全面优化。通过配置驱动的方式，实现更灵活、可维护、高性能的日志管理系统。

## 🎯 优化目标

- **配置驱动**: 通过 TOML 配置文件管理所有日志设置
- **环境感知**: 不同环境自动应用对应的日志策略
- **结构化日志**: 支持 JSON 格式输出，便于日志分析
- **性能优化**: 异步日志处理，减少对应用性能的影响
- **统一管理**: 集中管理应用日志和 SQL 日志

## 📊 现状分析

### 当前架构问题

1. **硬编码配置**: `log/log.py` 中硬编码了日志配置逻辑
2. **分散管理**: 日志设置分散在多个文件中
3. **缺乏验证**: 没有使用 dynaconf 的验证器确保配置正确性
4. **功能局限**: 未充分利用 loguru 的高级特性

### 当前文件结构

```
log/
├── __init__.py
└── log.py                    # 当前日志配置类 (需要重构)

settings/
├── config.py                 # Dynaconf 配置
├── default.toml             # 基础配置 (包含 log_level)
├── development.toml         # 开发环境配置
├── staging.toml             # 测试环境配置  
├── production.toml          # 生产环境配置
└── sql_logging.py           # SQL 日志配置 (需要集成)
```

## 🔧 优化方案

### 1. 配置结构重构

#### 1.1 TOML 配置扩展

在 `settings/default.toml` 中添加完整的日志配置节：

```toml
[default.logging]
# 基础日志级别
level = "INFO"

# 日志格式配置
format = "{time:YYYY-MM-DD HH:mm:ss.SSS} | {level: <8} | {name}:{function}:{line} - {message}"
colorize = true

# 控制台输出配置
[default.logging.console]
enabled = true
level = "DEBUG"
colorize = true

# 文件日志配置
[default.logging.file]
enabled = true
path = "logs/application.log"
level = "INFO"
rotation = "100 MB"
retention = "30 days"
compression = "gz"
enqueue = true

# JSON 结构化日志配置
[default.logging.json]
enabled = false
path = "logs/application.json"
level = "INFO"
serialize = true

# SQL 日志配置
[default.logging.sql]
enabled = false
level = "DEBUG"
format = "<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | <level>{level: <8}</level> | <cyan>SQL</cyan> | <level>{message}</level>"
```

#### 1.2 环境特定配置

**development.toml**:
```toml
[development.logging]
level = "DEBUG"

[development.logging.console]
level = "DEBUG"
colorize = true

[development.logging.sql]
enabled = true
level = "DEBUG"
```

**production.toml**:
```toml
[production.logging]
level = "WARNING"

[production.logging.console]
enabled = false

[production.logging.json]
enabled = true
level = "INFO"

[production.logging.file]
level = "WARNING"
rotation = "500 MB"
retention = "90 days"
```

### 2. Dynaconf 验证器增强

在 `settings/config.py` 中添加日志配置验证器：

```python
from dynaconf import Dynaconf, Validator

settings = Dynaconf(
    # ... 现有配置 ...
    validators=[
        # ... 现有验证器 ...
        
        # 日志配置验证器
        Validator("logging.level", must_exist=True, is_in=["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]),
        Validator("logging.format", must_exist=True, is_type_of=str),
        Validator("logging.colorize", default=True, is_type_of=bool),
        
        # 控制台日志验证器
        Validator("logging.console.enabled", default=True, is_type_of=bool),
        Validator("logging.console.level", default="DEBUG", is_in=["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]),
        Validator("logging.console.colorize", default=True, is_type_of=bool),
        
        # 文件日志验证器
        Validator("logging.file.enabled", default=True, is_type_of=bool),
        Validator("logging.file.path", default="logs/application.log", is_type_of=str),
        Validator("logging.file.level", default="INFO", is_in=["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]),
        Validator("logging.file.rotation", default="100 MB", is_type_of=str),
        Validator("logging.file.retention", default="30 days", is_type_of=str),
        Validator("logging.file.compression", default="gz", is_in=["gz", "zip", "tar.gz"]),
        Validator("logging.file.enqueue", default=True, is_type_of=bool),
        
        # JSON 日志验证器
        Validator("logging.json.enabled", default=False, is_type_of=bool),
        Validator("logging.json.path", default="logs/application.json", is_type_of=str),
        Validator("logging.json.level", default="INFO", is_in=["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]),
        Validator("logging.json.serialize", default=True, is_type_of=bool),
        
        # SQL 日志验证器
        Validator("logging.sql.enabled", default=False, is_type_of=bool),
        Validator("logging.sql.level", default="DEBUG", is_in=["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]),
        Validator("logging.sql.format", must_exist=True, is_type_of=str),
        
        # 环境特定验证
        Validator("logging.json.enabled", eq=True, env="production"),
        Validator("logging.console.enabled", eq=False, env="production"),
        Validator("logging.sql.enabled", eq=True, env="development"),
    ]
)
```

### 3. Loguru 配置重构

重构 `log/log.py` 为配置驱动的日志管理器：

```python
"""
配置驱动的 Loguru 日志管理器

基于 dynaconf 配置自动设置 loguru 日志系统
"""
import sys
from pathlib import Path
from typing import Dict, Any, Optional
from loguru import logger as loguru_logger

from settings import settings


class LoggingManager:
    """基于配置的日志管理器"""
    
    def __init__(self):
        self._handlers_ids = []
        self._sql_handler_configured = False
    
    def setup_logger(self) -> Any:
        """根据配置设置 loguru 日志系统"""
        # 移除默认处理器
        loguru_logger.remove()
        
        # 获取日志配置
        logging_config = settings.logging
        
        # 设置控制台日志
        if logging_config.console.enabled:
            self._setup_console_logger(logging_config.console)
        
        # 设置文件日志
        if logging_config.file.enabled:
            self._setup_file_logger(logging_config.file)
        
        # 设置 JSON 日志
        if logging_config.json.enabled:
            self._setup_json_logger(logging_config.json)
        
        # 设置 SQL 日志
        if logging_config.sql.enabled:
            self._setup_sql_logger(logging_config.sql)
        
        return loguru_logger
    
    def _setup_console_logger(self, config: Any):
        """设置控制台日志处理器"""
        handler_id = loguru_logger.add(
            sink=sys.stderr,
            level=config.level,
            format=getattr(config, 'format', settings.logging.format),
            colorize=config.colorize,
            enqueue=False  # 控制台不需要异步
        )
        self._handlers_ids.append(handler_id)
    
    def _setup_file_logger(self, config: Any):
        """设置文件日志处理器"""
        # 确保日志目录存在
        log_path = Path(config.path)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        
        handler_id = loguru_logger.add(
            sink=str(log_path),
            level=config.level,
            format=getattr(config, 'format', settings.logging.format),
            rotation=config.rotation,
            retention=config.retention,
            compression=config.compression,
            enqueue=config.enqueue,
            encoding="utf-8"
        )
        self._handlers_ids.append(handler_id)
    
    def _setup_json_logger(self, config: Any):
        """设置 JSON 结构化日志处理器"""
        # 确保日志目录存在
        log_path = Path(config.path)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        
        handler_id = loguru_logger.add(
            sink=str(log_path),
            level=config.level,
            serialize=config.serialize,
            rotation=getattr(config, 'rotation', "100 MB"),
            retention=getattr(config, 'retention', "30 days"),
            compression=getattr(config, 'compression', "gz"),
            enqueue=True,  # JSON 日志使用异步
            encoding="utf-8"
        )
        self._handlers_ids.append(handler_id)
    
    def _setup_sql_logger(self, config: Any):
        """设置 SQL 日志处理器"""
        if self._sql_handler_configured:
            return
        
        # 集成现有的 SQL 日志功能
        from settings.sql_logging import enable_sql_logging
        enable_sql_logging(
            level=config.level,
            format_string=config.format
        )
        self._sql_handler_configured = True
    
    def reload_configuration(self):
        """重新加载配置并重新设置日志系统"""
        # 移除现有处理器
        for handler_id in self._handlers_ids:
            loguru_logger.remove(handler_id)
        self._handlers_ids.clear()
        
        # 重新设置
        return self.setup_logger()
    
    def bind_context(self, **context) -> Any:
        """绑定上下文信息到日志记录器"""
        return loguru_logger.bind(**context)
    
    def get_logger(self, name: Optional[str] = None) -> Any:
        """获取带名称的日志记录器"""
        if name:
            return loguru_logger.bind(name=name)
        return loguru_logger


# 全局日志管理器实例
logging_manager = LoggingManager()

# 初始化日志系统
logger = logging_manager.setup_logger()

# 导出常用接口
def get_logger(name: Optional[str] = None):
    """获取日志记录器"""
    return logging_manager.get_logger(name)

def bind_context(**context):
    """绑定上下文"""
    return logging_manager.bind_context(**context)

def reload_logging():
    """重新加载日志配置"""
    global logger
    logger = logging_manager.reload_configuration()
    return logger
```

### 4. 高级特性实现

#### 4.1 动态日志级别调整

```python
# 在 log/log.py 中添加
def set_log_level(level: str):
    """动态调整日志级别"""
    for handler_id in logging_manager._handlers_ids:
        loguru_logger.remove(handler_id)
    
    # 临时更新配置
    settings.set('logging.level', level.upper())
    
    # 重新设置日志系统
    return logging_manager.reload_configuration()
```

#### 4.2 模块级别日志过滤

```python
# 为不同模块设置不同的日志级别
douyin_logger = get_logger("douyin").bind(module="douyin")
trendinsight_logger = get_logger("trendinsight").bind(module="trendinsight")
api_logger = get_logger("api").bind(module="api")
```

#### 4.3 结构化日志记录

```python
# 使用结构化日志记录重要事件
logger.info("用户操作", extra={
    "user_id": user.id,
    "action": "login",
    "ip_address": request.client.host,
    "timestamp": datetime.now().isoformat()
})

# API 请求日志
logger.info("API 请求", extra={
    "method": request.method,
    "url": str(request.url),
    "status_code": response.status_code,
    "response_time_ms": response_time * 1000
})
```

## 📈 预期收益

### 性能提升
- **异步日志处理**: 减少 I/O 阻塞，提升应用响应速度
- **智能轮转**: 避免单个日志文件过大影响性能
- **条件日志**: 在生产环境中减少不必要的日志输出

### 开发体验改善
- **配置驱动**: 无需修改代码即可调整日志行为
- **环境感知**: 自动适配不同环境的日志需求
- **结构化输出**: 便于日志分析和监控集成

### 运维便利性
- **统一管理**: 所有日志配置集中在 TOML 文件中
- **验证机制**: 启动时自动验证配置的正确性
- **动态调整**: 支持运行时调整日志级别

## 🚀 实施步骤

1. **备份现有配置**: 保存当前的 `log/log.py` 文件
2. **扩展 TOML 配置**: 在各环境配置文件中添加日志配置节
3. **添加验证器**: 在 `settings/config.py` 中增加日志配置验证
4. **重构日志管理器**: 替换 `log/log.py` 的实现
5. **集成 SQL 日志**: 将现有 SQL 日志功能整合到新架构中
6. **测试验证**: 在各环境中测试新的日志配置
7. **文档更新**: 更新相关文档和使用指南

## ⚠️ 注意事项

- **向后兼容**: 确保现有代码中的 `from log.log import logger` 仍然可用
- **配置验证**: 启动时会进行配置验证，确保所有必需的配置项都已正确设置
- **性能监控**: 在生产环境中监控日志系统的性能影响
- **日志轮转**: 定期检查日志文件的大小和数量，避免磁盘空间不足

## 📚 相关文档

- [Loguru 官方文档](https://loguru.readthedocs.io/)
- [Dynaconf 验证器指南](https://dynaconf.readthedocs.io/en/docs_223/guides/validation.html)
- [TOML 配置格式说明](https://toml.io/)
- [FastAPI 日志集成最佳实践](https://fastapi.tiangolo.com/advanced/middleware/#middleware)

---

*本文档会随着项目需求的变化而持续更新。如有问题或建议，请在项目仓库中提交 Issue。*