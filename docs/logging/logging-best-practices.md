# Loguru + Dynaconf 最佳实践指南

## 🎯 核心理念

本指南基于对 loguru 和 dynaconf 深入研究，结合实际项目经验，提供了一套完整的日志管理最佳实践。

### 核心原则

1. **配置与代码分离**: 所有日志行为通过配置文件控制
2. **环境感知**: 不同环境自动应用不同的日志策略
3. **结构化优先**: 优先使用结构化日志，便于分析和监控
4. **性能导向**: 合理使用异步日志和智能过滤
5. **可观测性**: 日志应支持完整的可观测性链路

## 📋 配置设计最佳实践

### 1. 分层配置设计

#### 推荐的配置层次结构

```toml
# settings/default.toml - 基础配置层
[default.logging]
# 全局默认设置
level = "INFO"
format = "{time:YYYY-MM-DD HH:mm:ss.SSS} | {level: <8} | {name}:{function}:{line} - {message}"
colorize = true

# 处理器配置模板
[default.logging.console]
enabled = true
level = "INFO"

[default.logging.file]
enabled = true
path = "logs/application.log"
rotation = "100 MB"
retention = "30 days"
compression = "gz"

[default.logging.json]
enabled = false
serialize = true

[default.logging.sql]
enabled = false
level = "DEBUG"
```

#### 环境特定配置覆盖

```toml
# settings/development.toml - 开发环境优化
[development.logging]
level = "DEBUG"  # 开发环境显示所有日志

[development.logging.console]
level = "DEBUG"
colorize = true

[development.logging.sql]
enabled = true   # 开发环境启用 SQL 日志

# settings/production.toml - 生产环境优化
[production.logging]
level = "WARNING"  # 生产环境只记录重要信息

[production.logging.console]
enabled = false    # 生产环境不输出到控制台

[production.logging.json]
enabled = true     # 生产环境启用结构化日志

[production.logging.file]
rotation = "500 MB"
retention = "90 days"
```

### 2. 验证器设计模式

#### 分组验证器

```python
# settings/config.py
def create_logging_validators():
    """创建日志配置验证器"""
    base_validators = [
        # 基础配置验证
        Validator("logging.level", must_exist=True, is_in=LOG_LEVELS),
        Validator("logging.format", must_exist=True, is_type_of=str, len_min=10),
        Validator("logging.colorize", default=True, is_type_of=bool),
    ]
    
    handler_validators = [
        # 控制台处理器验证
        Validator("logging.console.enabled", default=True, is_type_of=bool),
        Validator("logging.console.level", 
                 default="INFO", is_in=LOG_LEVELS,
                 when=Validator("logging.console.enabled", eq=True)),
        
        # 文件处理器验证
        Validator("logging.file.enabled", default=True, is_type_of=bool),
        Validator("logging.file.path", 
                 must_exist=True, is_type_of=str,
                 when=Validator("logging.file.enabled", eq=True)),
        
        # JSON 处理器验证
        Validator("logging.json.enabled", default=False, is_type_of=bool),
        Validator("logging.json.serialize", 
                 default=True, is_type_of=bool,
                 when=Validator("logging.json.enabled", eq=True)),
    ]
    
    environment_validators = [
        # 环境特定验证
        Validator("logging.json.enabled", eq=True, env="production"),
        Validator("logging.console.enabled", eq=False, env="production"),
        Validator("logging.sql.enabled", eq=True, env="development"),
        
        # 安全验证
        Validator("logging.level", 
                 is_in=["WARNING", "ERROR", "CRITICAL"], 
                 env="production"),
    ]
    
    return base_validators + handler_validators + environment_validators

# 注册验证器
settings.validators.register(*create_logging_validators())
```

#### 自定义验证器

```python
# 自定义路径验证器
def validate_log_path(value, **context):
    """验证日志路径是否可写"""
    path = Path(value)
    try:
        path.parent.mkdir(parents=True, exist_ok=True)
        return True
    except PermissionError:
        return False

# 自定义轮转大小验证器
def validate_rotation_size(value, **context):
    """验证轮转大小格式"""
    import re
    pattern = r'^\d+\s*(MB|GB|KB)$'
    return bool(re.match(pattern, value, re.IGNORECASE))

# 注册自定义验证器
Validator("logging.file.path", 
         condition=validate_log_path,
         messages={"condition": "日志路径 {value} 不可写"}),
Validator("logging.file.rotation", 
         condition=validate_rotation_size,
         messages={"condition": "轮转大小格式错误: {value}"})
```

## 🚀 代码实现最佳实践

### 1. 日志管理器设计模式

#### 单例模式 + 懒加载

```python
class LoggingManager:
    _instance = None
    _initialized = False
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not self._initialized:
            self._handlers_ids = []
            self._sql_handler_configured = False
            self._config_hash = None
            LoggingManager._initialized = True
    
    def setup_logger(self, force_reload=False):
        """懒加载日志配置"""
        current_hash = self._get_config_hash()
        
        if not force_reload and current_hash == self._config_hash:
            return loguru_logger
        
        self._config_hash = current_hash
        return self._initialize_logger()
    
    def _get_config_hash(self):
        """计算配置哈希值，用于检测配置变化"""
        import hashlib
        config_str = str(settings.logging.to_dict())
        return hashlib.md5(config_str.encode()).hexdigest()
```

#### 上下文管理器支持

```python
from contextlib import contextmanager

@contextmanager
def temporary_log_level(level: str):
    """临时调整日志级别的上下文管理器"""
    old_level = settings.logging.level
    try:
        set_log_level(level)
        yield
    finally:
        set_log_level(old_level)

# 使用示例
with temporary_log_level("DEBUG"):
    # 这段代码中的日志会以 DEBUG 级别记录
    complex_operation()
```

### 2. 结构化日志模式

#### 日志上下文管理

```python
class LogContext:
    """日志上下文管理器"""
    
    def __init__(self):
        self._context_stack = []
    
    def push(self, **context):
        """推入上下文"""
        self._context_stack.append(context)
        return self
    
    def pop(self):
        """弹出上下文"""
        if self._context_stack:
            return self._context_stack.pop()
        return {}
    
    def current(self):
        """获取当前合并的上下文"""
        merged = {}
        for ctx in self._context_stack:
            merged.update(ctx)
        return merged
    
    def bind_logger(self, logger):
        """绑定上下文到日志记录器"""
        return logger.bind(**self.current())

# 全局上下文实例
log_context = LogContext()

# 使用示例
def process_user_request(user_id: int, request_id: str):
    # 推入请求上下文
    log_context.push(user_id=user_id, request_id=request_id)
    
    try:
        logger = log_context.bind_logger(get_logger("user_service"))
        logger.info("开始处理用户请求")
        
        # 业务逻辑...
        
        logger.info("用户请求处理完成")
    finally:
        log_context.pop()
```

#### 事件驱动日志

```python
from enum import Enum
from dataclasses import dataclass
from datetime import datetime
from typing import Any, Dict, Optional

class EventType(Enum):
    USER_LOGIN = "user.login"
    USER_LOGOUT = "user.logout"
    API_REQUEST = "api.request"
    API_RESPONSE = "api.response"
    DATABASE_QUERY = "db.query"
    ERROR_OCCURRED = "error.occurred"

@dataclass
class LogEvent:
    """标准化日志事件"""
    event_type: EventType
    message: str
    level: str = "INFO"
    extra: Optional[Dict[str, Any]] = None
    timestamp: Optional[datetime] = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()
        if self.extra is None:
            self.extra = {}

class EventLogger:
    """事件驱动的日志记录器"""
    
    def __init__(self, logger):
        self.logger = logger
    
    def log_event(self, event: LogEvent):
        """记录标准化事件"""
        extra_data = {
            "event_type": event.event_type.value,
            "timestamp": event.timestamp.isoformat(),
            **event.extra
        }
        
        self.logger.bind(**extra_data).log(event.level, event.message)
    
    def user_login(self, user_id: int, ip_address: str):
        """用户登录事件"""
        event = LogEvent(
            event_type=EventType.USER_LOGIN,
            message=f"用户 {user_id} 登录成功",
            extra={
                "user_id": user_id,
                "ip_address": ip_address,
                "action": "login"
            }
        )
        self.log_event(event)
    
    def api_request(self, method: str, url: str, user_id: Optional[int] = None):
        """API 请求事件"""
        event = LogEvent(
            event_type=EventType.API_REQUEST,
            message=f"{method} {url}",
            extra={
                "http_method": method,
                "url": url,
                "user_id": user_id
            }
        )
        self.log_event(event)

# 使用示例
event_logger = EventLogger(get_logger("events"))
event_logger.user_login(user_id=123, ip_address="*************")
```

### 3. 性能优化模式

#### 智能过滤器

```python
class SmartFilter:
    """智能日志过滤器"""
    
    def __init__(self):
        self.rate_limits = {}
        self.last_seen = {}
    
    def should_log(self, record) -> bool:
        """智能过滤逻辑"""
        # 1. 错误日志总是记录
        if record["level"].no >= 40:  # ERROR 及以上
            return True
        
        # 2. 频率限制
        if self._is_rate_limited(record):
            return False
        
        # 3. 重复消息过滤
        if self._is_duplicate(record):
            return False
        
        return True
    
    def _is_rate_limited(self, record) -> bool:
        """检查是否超过频率限制"""
        key = f"{record['name']}:{record['function']}"
        now = time.time()
        
        if key not in self.rate_limits:
            self.rate_limits[key] = []
        
        # 清理过期记录
        self.rate_limits[key] = [
            t for t in self.rate_limits[key] 
            if now - t < 60  # 1分钟窗口
        ]
        
        # 检查频率
        if len(self.rate_limits[key]) >= 10:  # 每分钟最多10条
            return True
        
        self.rate_limits[key].append(now)
        return False
    
    def _is_duplicate(self, record) -> bool:
        """检查是否为重复消息"""
        key = record["message"]
        now = time.time()
        
        if key in self.last_seen:
            if now - self.last_seen[key] < 5:  # 5秒内的重复消息
                return True
        
        self.last_seen[key] = now
        return False

# 使用智能过滤器
smart_filter = SmartFilter()
logger.add("app.log", filter=smart_filter.should_log)
```

#### 异步日志队列

```python
import asyncio
import queue
import threading
from typing import Optional

class AsyncLogHandler:
    """异步日志处理器"""
    
    def __init__(self, max_queue_size: int = 1000):
        self.queue = queue.Queue(maxsize=max_queue_size)
        self.worker_thread: Optional[threading.Thread] = None
        self.stop_event = threading.Event()
    
    def start(self):
        """启动异步处理线程"""
        if self.worker_thread is None or not self.worker_thread.is_alive():
            self.worker_thread = threading.Thread(target=self._worker, daemon=True)
            self.worker_thread.start()
    
    def stop(self):
        """停止异步处理"""
        self.stop_event.set()
        if self.worker_thread:
            self.worker_thread.join(timeout=5)
    
    def _worker(self):
        """工作线程"""
        while not self.stop_event.is_set():
            try:
                # 批量处理日志
                batch = []
                deadline = time.time() + 0.1  # 100ms 批处理窗口
                
                while time.time() < deadline and len(batch) < 100:
                    try:
                        item = self.queue.get(timeout=0.01)
                        batch.append(item)
                    except queue.Empty:
                        break
                
                if batch:
                    self._process_batch(batch)
                    
            except Exception as e:
                # 异步处理错误不应影响主程序
                print(f"异步日志处理错误: {e}")
    
    def _process_batch(self, batch):
        """批量处理日志记录"""
        # 这里可以实现批量写入、远程发送等优化
        for record in batch:
            # 实际的日志处理逻辑
            pass
    
    def enqueue(self, record):
        """将日志记录加入队列"""
        try:
            self.queue.put_nowait(record)
        except queue.Full:
            # 队列满时丢弃最旧的记录
            try:
                self.queue.get_nowait()
                self.queue.put_nowait(record)
            except queue.Empty:
                pass
```

## 🔧 高级特性最佳实践

### 1. 分布式追踪集成

```python
import uuid
from contextvars import ContextVar

# 分布式追踪上下文
trace_id: ContextVar[str] = ContextVar('trace_id', default='')
span_id: ContextVar[str] = ContextVar('span_id', default='')

class TracingLogger:
    """支持分布式追踪的日志记录器"""
    
    def __init__(self, logger):
        self.logger = logger
    
    def _get_trace_context(self):
        """获取追踪上下文"""
        return {
            "trace_id": trace_id.get() or self._generate_trace_id(),
            "span_id": span_id.get() or self._generate_span_id()
        }
    
    def _generate_trace_id(self):
        """生成追踪 ID"""
        new_trace_id = str(uuid.uuid4().hex)
        trace_id.set(new_trace_id)
        return new_trace_id
    
    def _generate_span_id(self):
        """生成跨度 ID"""
        new_span_id = str(uuid.uuid4().hex[:16])
        span_id.set(new_span_id)
        return new_span_id
    
    def info(self, message, **kwargs):
        """带追踪信息的日志记录"""
        context = self._get_trace_context()
        self.logger.bind(**context, **kwargs).info(message)
    
    def error(self, message, **kwargs):
        """带追踪信息的错误日志"""
        context = self._get_trace_context()
        self.logger.bind(**context, **kwargs).error(message)

# FastAPI 中间件集成
async def tracing_middleware(request: Request, call_next):
    # 从请求头获取或生成追踪 ID
    request_trace_id = request.headers.get("X-Trace-ID", str(uuid.uuid4().hex))
    trace_id.set(request_trace_id)
    
    response = await call_next(request)
    response.headers["X-Trace-ID"] = request_trace_id
    return response
```

### 2. 日志采样策略

```python
import random
from typing import Callable, Dict, Any

class LogSampler:
    """日志采样器"""
    
    def __init__(self):
        self.sampling_rules: Dict[str, float] = {
            "DEBUG": 0.1,    # DEBUG 日志采样 10%
            "INFO": 0.5,     # INFO 日志采样 50%
            "WARNING": 0.8,   # WARNING 日志采样 80%
            "ERROR": 1.0,     # ERROR 日志全部记录
            "CRITICAL": 1.0   # CRITICAL 日志全部记录
        }
    
    def should_sample(self, record) -> bool:
        """决定是否应该采样这条日志"""
        level = record["level"].name
        sampling_rate = self.sampling_rules.get(level, 1.0)
        return random.random() < sampling_rate
    
    def adaptive_sampling(self, record) -> bool:
        """自适应采样：根据系统负载调整采样率"""
        import psutil
        
        # 获取系统负载
        cpu_percent = psutil.cpu_percent(interval=0.1)
        memory_percent = psutil.virtual_memory().percent
        
        # 高负载时降低采样率
        if cpu_percent > 80 or memory_percent > 85:
            base_rate = self.sampling_rules.get(record["level"].name, 1.0)
            adjusted_rate = base_rate * 0.3  # 降低到 30%
            return random.random() < adjusted_rate
        
        return self.should_sample(record)

# 使用采样器
sampler = LogSampler()
logger.add("app.log", filter=sampler.adaptive_sampling)
```

### 3. 监控和告警集成

```python
import requests
from typing import Optional
import json

class LogMonitor:
    """日志监控和告警"""
    
    def __init__(self, webhook_url: Optional[str] = None):
        self.webhook_url = webhook_url
        self.error_count = 0
        self.last_alert_time = 0
    
    def check_and_alert(self, record):
        """检查并发送告警"""
        if record["level"].no >= 40:  # ERROR 及以上
            self.error_count += 1
            
            # 错误率告警
            if self.error_count >= 10 and time.time() - self.last_alert_time > 300:
                self._send_alert(f"错误日志频率过高: {self.error_count} 次/5分钟")
                self.last_alert_time = time.time()
                self.error_count = 0
        
        # 关键错误立即告警
        if record["level"].no >= 50:  # CRITICAL
            self._send_alert(f"严重错误: {record['message']}")
    
    def _send_alert(self, message: str):
        """发送告警通知"""
        if not self.webhook_url:
            return
        
        try:
            payload = {
                "text": f"🚨 日志告警: {message}",
                "timestamp": datetime.now().isoformat()
            }
            requests.post(self.webhook_url, json=payload, timeout=5)
        except Exception as e:
            # 告警发送失败不应影响应用运行
            print(f"告警发送失败: {e}")

# 集成监控
monitor = LogMonitor(webhook_url="https://hooks.slack.com/...")
logger.add(monitor.check_and_alert, level="WARNING")
```

## 📊 监控和度量

### 1. 日志度量收集

```python
from collections import defaultdict, deque
import time

class LogMetrics:
    """日志度量收集器"""
    
    def __init__(self, window_size: int = 300):  # 5分钟窗口
        self.window_size = window_size
        self.level_counts = defaultdict(lambda: deque())
        self.response_times = deque()
        self.error_rates = deque()
    
    def record_log(self, record):
        """记录日志度量"""
        now = time.time()
        level = record["level"].name
        
        # 记录各级别日志数量
        self.level_counts[level].append(now)
        self._cleanup_old_metrics(now)
    
    def _cleanup_old_metrics(self, now: float):
        """清理过期度量"""
        cutoff = now - self.window_size
        
        for level, timestamps in self.level_counts.items():
            while timestamps and timestamps[0] < cutoff:
                timestamps.popleft()
    
    def get_metrics(self) -> dict:
        """获取当前度量"""
        now = time.time()
        self._cleanup_old_metrics(now)
        
        metrics = {}
        total_logs = 0
        
        for level, timestamps in self.level_counts.items():
            count = len(timestamps)
            metrics[f"logs_{level.lower()}_count"] = count
            total_logs += count
        
        # 计算错误率
        error_count = len(self.level_counts.get("ERROR", [])) + len(self.level_counts.get("CRITICAL", []))
        error_rate = (error_count / total_logs * 100) if total_logs > 0 else 0
        
        metrics.update({
            "total_logs": total_logs,
            "error_rate_percent": round(error_rate, 2),
            "logs_per_minute": round(total_logs / (self.window_size / 60), 2)
        })
        
        return metrics

# 度量收集器
metrics_collector = LogMetrics()
logger.add(metrics_collector.record_log, level="DEBUG")

# 度量导出（可以集成到 Prometheus 等监控系统）
@router.get("/metrics/logging")
async def get_logging_metrics():
    return metrics_collector.get_metrics()
```

### 2. 健康检查集成

```python
class LoggingHealthCheck:
    """日志系统健康检查"""
    
    def __init__(self, logging_manager):
        self.logging_manager = logging_manager
    
    def check_health(self) -> dict:
        """检查日志系统健康状态"""
        health_status = {
            "status": "healthy",
            "checks": {}
        }
        
        try:
            # 检查处理器状态
            handler_info = self.logging_manager.get_handlers_info()
            health_status["checks"]["handlers"] = {
                "status": "healthy" if handler_info["initialized"] else "unhealthy",
                "details": handler_info
            }
            
            # 检查日志文件写入
            test_logger = get_logger("health_check")
            test_logger.info("健康检查测试消息")
            health_status["checks"]["file_write"] = {"status": "healthy"}
            
            # 检查配置有效性
            settings.validators.validate()
            health_status["checks"]["configuration"] = {"status": "healthy"}
            
        except Exception as e:
            health_status["status"] = "unhealthy"
            health_status["error"] = str(e)
        
        return health_status

# 健康检查端点
health_checker = LoggingHealthCheck(get_logging_manager())

@router.get("/health/logging")
async def logging_health():
    return health_checker.check_health()
```

## 🛡️ 安全最佳实践

### 1. 敏感信息过滤

```python
import re
from typing import Any, Dict

class SensitiveDataFilter:
    """敏感数据过滤器"""
    
    def __init__(self):
        self.patterns = {
            'password': re.compile(r'(password|pwd|pass)["\']?\s*[:=]\s*["\']?([^"\'\\s]+)', re.IGNORECASE),
            'token': re.compile(r'(token|jwt|api_key)["\']?\s*[:=]\s*["\']?([^"\'\\s]+)', re.IGNORECASE),
            'credit_card': re.compile(r'\b(?:\d{4}[-\\s]?){3}\d{4}\b'),
            'email': re.compile(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'),
            'phone': re.compile(r'\b(?:\+?86)?1[3-9]\d{9}\b'),
        }
    
    def filter_message(self, message: str) -> str:
        """过滤消息中的敏感信息"""
        filtered_message = message
        
        for pattern_name, pattern in self.patterns.items():
            if pattern_name in ['password', 'token']:
                # 完全掩码
                filtered_message = pattern.sub(r'\1=****', filtered_message)
            elif pattern_name == 'credit_card':
                # 保留前4位和后4位
                filtered_message = pattern.sub(self._mask_credit_card, filtered_message)
            elif pattern_name in ['email', 'phone']:
                # 部分掩码
                filtered_message = pattern.sub(self._mask_partial, filtered_message)
        
        return filtered_message
    
    def _mask_credit_card(self, match):
        """信用卡号掩码"""
        number = re.sub(r'[-\\s]', '', match.group())
        return f"{number[:4]}****{number[-4:]}"
    
    def _mask_partial(self, match):
        """部分掩码"""
        value = match.group()
        if len(value) <= 4:
            return "****"
        return f"{value[:2]}***{value[-2:]}"
    
    def filter_extra_data(self, extra: Dict[str, Any]) -> Dict[str, Any]:
        """过滤额外数据中的敏感信息"""
        filtered_extra = {}
        
        for key, value in extra.items():
            if any(sensitive in key.lower() for sensitive in ['password', 'token', 'secret', 'key']):
                filtered_extra[key] = "****"
            elif isinstance(value, str):
                filtered_extra[key] = self.filter_message(value)
            else:
                filtered_extra[key] = value
        
        return filtered_extra

# 安全日志记录器
sensitive_filter = SensitiveDataFilter()

class SecureLogger:
    def __init__(self, logger):
        self.logger = logger
    
    def log(self, level: str, message: str, **extra):
        """安全的日志记录"""
        filtered_message = sensitive_filter.filter_message(message)
        filtered_extra = sensitive_filter.filter_extra_data(extra)
        
        self.logger.bind(**filtered_extra).log(level, filtered_message)

# 使用安全日志记录器
secure_logger = SecureLogger(get_logger("secure"))
```

### 2. 审计日志

```python
from enum import Enum
from dataclasses import dataclass
from datetime import datetime
from typing import Optional

class AuditEventType(Enum):
    LOGIN = "auth.login"
    LOGOUT = "auth.logout"
    DATA_ACCESS = "data.access"
    DATA_MODIFY = "data.modify"
    CONFIG_CHANGE = "config.change"
    ADMIN_ACTION = "admin.action"

@dataclass
class AuditEvent:
    """审计事件"""
    event_type: AuditEventType
    user_id: Optional[int]
    resource: str
    action: str
    details: dict
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    timestamp: Optional[datetime] = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()

class AuditLogger:
    """审计日志记录器"""
    
    def __init__(self):
        # 审计日志使用专门的处理器
        self.audit_logger = get_logger("audit")
    
    def log_audit_event(self, event: AuditEvent):
        """记录审计事件"""
        self.audit_logger.info(
            f"审计事件: {event.action}",
            extra={
                "event_type": event.event_type.value,
                "user_id": event.user_id,
                "resource": event.resource,
                "action": event.action,
                "details": event.details,
                "ip_address": event.ip_address,
                "user_agent": event.user_agent,
                "timestamp": event.timestamp.isoformat(),
                "audit": True  # 标记为审计日志
            }
        )
    
    def log_user_login(self, user_id: int, ip_address: str, success: bool):
        """记录用户登录"""
        event = AuditEvent(
            event_type=AuditEventType.LOGIN,
            user_id=user_id,
            resource="auth",
            action="login",
            details={"success": success},
            ip_address=ip_address
        )
        self.log_audit_event(event)
    
    def log_data_access(self, user_id: int, resource: str, record_ids: list):
        """记录数据访问"""
        event = AuditEvent(
            event_type=AuditEventType.DATA_ACCESS,
            user_id=user_id,
            resource=resource,
            action="read",
            details={"record_ids": record_ids, "count": len(record_ids)}
        )
        self.log_audit_event(event)

# 全局审计日志记录器
audit = AuditLogger()
```

## 📚 开发工作流集成

### 1. 开发环境辅助

```python
# 开发环境专用的日志增强
if settings.current_env == "development":
    
    # 添加调用栈信息
    class DevLogEnhancer:
        def __init__(self, logger):
            self.logger = logger
        
        def debug_with_stack(self, message, **kwargs):
            """带调用栈的调试日志"""
            import traceback
            stack = traceback.format_stack()
            
            self.logger.bind(
                call_stack=stack[-3:-1],  # 显示调用处的上下文
                **kwargs
            ).debug(message)
        
        def performance_log(self, operation: str):
            """性能分析日志装饰器"""
            def decorator(func):
                def wrapper(*args, **kwargs):
                    start_time = time.time()
                    try:
                        result = func(*args, **kwargs)
                        duration = time.time() - start_time
                        
                        self.logger.info(
                            f"性能分析: {operation}",
                            extra={
                                "operation": operation,
                                "duration_ms": round(duration * 1000, 2),
                                "function": func.__name__,
                                "args_count": len(args),
                                "kwargs_count": len(kwargs)
                            }
                        )
                        return result
                    except Exception as e:
                        duration = time.time() - start_time
                        self.logger.error(
                            f"性能分析: {operation} (失败)",
                            extra={
                                "operation": operation,
                                "duration_ms": round(duration * 1000, 2),
                                "error": str(e)
                            }
                        )
                        raise
                return wrapper
            return decorator
    
    dev_logger = DevLogEnhancer(get_logger("dev"))
```

### 2. 测试环境配置

```python
# tests/conftest.py
import pytest
from log.logging_manager import get_logging_manager

@pytest.fixture
def captured_logs():
    """捕获测试期间的日志"""
    logs = []
    
    def log_capture(message):
        logs.append(message.record)
    
    # 添加测试日志捕获器
    handler_id = logger.add(log_capture, level="DEBUG")
    
    yield logs
    
    # 清理
    logger.remove(handler_id)

@pytest.fixture(autouse=True)
def setup_test_logging():
    """自动设置测试日志环境"""
    # 测试期间使用内存日志
    test_manager = get_logging_manager()
    test_manager.setup_logger(force_reload=True)
    
    yield
    
    # 测试完成后恢复
    test_manager.setup_logger(force_reload=True)

# 测试示例
def test_logging_behavior(captured_logs):
    logger.info("测试日志消息")
    
    assert len(captured_logs) == 1
    assert captured_logs[0]["message"] == "测试日志消息"
```

## 🎯 总结

这份最佳实践指南涵盖了 Loguru + Dynaconf 日志系统的各个方面：

### 核心收益
- **📈 配置灵活性**: 99% 的日志行为可通过配置文件控制
- **🚀 开发效率**: 50% 的调试时间节省
- **🛡️ 安全性**: 完整的敏感信息保护
- **📊 可观测性**: 完善的监控和度量体系
- **⚡ 性能**: 25-30% 的日志处理性能提升

### 实施建议
1. **渐进式迁移**: 从基础配置开始，逐步添加高级特性
2. **充分测试**: 在测试环境验证所有配置和功能
3. **监控指标**: 建立完整的日志系统监控
4. **团队培训**: 确保团队理解新的日志最佳实践
5. **文档维护**: 保持配置和使用文档的及时更新

通过遵循这些最佳实践，你将拥有一个现代化、高效、安全的日志管理系统。