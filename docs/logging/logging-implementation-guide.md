# Loguru + Dynaconf 实施指南与代码示例

## 🛠️ 详细实施步骤

### 步骤 1: 配置文件扩展

#### 1.1 扩展 `settings/default.toml`

```toml
# 默认配置
[default]
# ... 现有配置保持不变 ...

# 扩展日志配置
[default.logging]
# 全局日志级别
level = "INFO"
# 全局日志格式
format = "{time:YYYY-MM-DD HH:mm:ss.SSS} | {level: <8} | {name}:{function}:{line} - {message}"
# 是否启用颜色
colorize = true

# 控制台日志配置
[default.logging.console]
enabled = true
level = "INFO"
colorize = true
format = "{time:YYYY-MM-DD HH:mm:ss.SSS} | {level: <8} | {name}:{function}:{line} - {message}"

# 文件日志配置
[default.logging.file]
enabled = true
path = "logs/application.log"
level = "INFO"
rotation = "100 MB"
retention = "30 days"
compression = "gz"
enqueue = true
format = "{time:YYYY-MM-DD HH:mm:ss.SSS} | {level: <8} | {name}:{function}:{line} - {message}"

# JSON 结构化日志配置
[default.logging.json]
enabled = false
path = "logs/application.json"
level = "INFO"
serialize = true
rotation = "100 MB"
retention = "30 days"
compression = "gz"
enqueue = true

# SQL 日志配置
[default.logging.sql]
enabled = false
level = "DEBUG"
format = "<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | <level>{level: <8}</level> | <cyan>SQL</cyan> | <level>{message}</level>"
```

#### 1.2 扩展 `settings/development.toml`

```toml
[development]
# ... 现有配置保持不变 ...

# 开发环境日志配置覆盖
[development.logging]
level = "DEBUG"
colorize = true

[development.logging.console]
enabled = true
level = "DEBUG"
colorize = true

[development.logging.file]
level = "DEBUG"
rotation = "50 MB"
retention = "7 days"

[development.logging.sql]
enabled = true
level = "DEBUG"
```

#### 1.3 扩展 `settings/production.toml`

```toml
[production]
# ... 现有配置保持不变 ...

# 生产环境日志配置覆盖
[production.logging]
level = "WARNING"
colorize = false

[production.logging.console]
enabled = false  # 生产环境不输出到控制台

[production.logging.file]
level = "WARNING"
rotation = "500 MB"
retention = "90 days"

[production.logging.json]
enabled = true  # 生产环境启用结构化日志
level = "INFO"
rotation = "500 MB"
retention = "90 days"

[production.logging.sql]
enabled = false  # 生产环境默认关闭 SQL 日志
```

### 步骤 2: 验证器配置

#### 2.1 更新 `settings/config.py`

```python
import os
from dynaconf import Dynaconf, Validator

# Project root directory
PROJECT_ROOT = os.path.abspath(os.path.join(os.path.dirname(__file__), os.pardir))

settings = Dynaconf(
    envvar_prefix="APP",
    settings_files=[
        "settings/default.toml",        # 基础应用配置
        "settings/database.toml",       # 数据库应用配置（apps）
        "settings/development.toml",    # 开发环境特定配置
        "settings/staging.toml",        # 测试环境特定配置
        "settings/production.toml",     # 生产环境特定配置
        ".secrets.toml",                # 敏感信息配置
    ],
    environments=True,
    load_dotenv=True,
    merge_enabled=True,
    PROJECT_ROOT=PROJECT_ROOT,
    BASE_DIR=PROJECT_ROOT,
    LOGS_ROOT=os.path.join(PROJECT_ROOT, "logs"),
)

# 现有验证器保持不变，添加日志相关验证器
settings.validators.register(
    # ... 现有验证器 ...
    
    # 全局日志配置验证器
    Validator("logging.level", must_exist=True, is_in=["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]),
    Validator("logging.format", must_exist=True, is_type_of=str, len_min=10),
    Validator("logging.colorize", default=True, is_type_of=bool),
    
    # 控制台日志验证器
    Validator("logging.console.enabled", default=True, is_type_of=bool),
    Validator("logging.console.level", 
              default="INFO", 
              is_in=["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"],
              when=Validator("logging.console.enabled", eq=True)),
    Validator("logging.console.colorize", default=True, is_type_of=bool),
    Validator("logging.console.format", default="", is_type_of=str),
    
    # 文件日志验证器
    Validator("logging.file.enabled", default=True, is_type_of=bool),
    Validator("logging.file.path", 
              default="logs/application.log", 
              is_type_of=str,
              when=Validator("logging.file.enabled", eq=True)),
    Validator("logging.file.level", 
              default="INFO", 
              is_in=["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"],
              when=Validator("logging.file.enabled", eq=True)),
    Validator("logging.file.rotation", default="100 MB", is_type_of=str),
    Validator("logging.file.retention", default="30 days", is_type_of=str),
    Validator("logging.file.compression", default="gz", is_in=["gz", "zip", "tar.gz", ""]),
    Validator("logging.file.enqueue", default=True, is_type_of=bool),
    Validator("logging.file.format", default="", is_type_of=str),
    
    # JSON 日志验证器
    Validator("logging.json.enabled", default=False, is_type_of=bool),
    Validator("logging.json.path", 
              default="logs/application.json",
              is_type_of=str,
              when=Validator("logging.json.enabled", eq=True)),
    Validator("logging.json.level", 
              default="INFO", 
              is_in=["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"],
              when=Validator("logging.json.enabled", eq=True)),
    Validator("logging.json.serialize", default=True, is_type_of=bool),
    Validator("logging.json.rotation", default="100 MB", is_type_of=str),
    Validator("logging.json.retention", default="30 days", is_type_of=str),
    Validator("logging.json.compression", default="gz", is_in=["gz", "zip", "tar.gz", ""]),
    Validator("logging.json.enqueue", default=True, is_type_of=bool),
    
    # SQL 日志验证器
    Validator("logging.sql.enabled", default=False, is_type_of=bool),
    Validator("logging.sql.level", 
              default="DEBUG", 
              is_in=["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"],
              when=Validator("logging.sql.enabled", eq=True)),
    Validator("logging.sql.format", 
              must_exist=True, 
              is_type_of=str, 
              len_min=10,
              when=Validator("logging.sql.enabled", eq=True)),
    
    # 环境特定验证器
    Validator("logging.json.enabled", eq=True, env="production"),
    Validator("logging.console.enabled", eq=False, env="production"),
    Validator("logging.sql.enabled", eq=True, env="development"),
    Validator("logging.level", is_in=["DEBUG", "INFO"], env="development"),
    Validator("logging.level", is_in=["WARNING", "ERROR", "CRITICAL"], env="production"),
)
```

### 步骤 3: 重构日志管理器

#### 3.1 创建新的 `log/logging_manager.py`

```python
"""
配置驱动的 Loguru 日志管理器

基于 dynaconf 配置自动设置 loguru 日志系统
"""
import sys
import logging
from pathlib import Path
from typing import Dict, Any, Optional, List
from loguru import logger as loguru_logger

from settings import settings


class LoggingManager:
    """基于配置的日志管理器"""
    
    def __init__(self):
        self._handlers_ids: List[int] = []
        self._sql_handler_configured = False
        self._is_initialized = False
    
    def setup_logger(self) -> Any:
        """根据配置设置 loguru 日志系统"""
        if self._is_initialized:
            return loguru_logger
        
        try:
            # 验证配置
            settings.validators.validate()
            
            # 移除默认处理器
            loguru_logger.remove()
            
            # 获取日志配置
            logging_config = settings.logging
            
            # 设置控制台日志
            if logging_config.console.enabled:
                self._setup_console_logger(logging_config.console, logging_config)
            
            # 设置文件日志
            if logging_config.file.enabled:
                self._setup_file_logger(logging_config.file, logging_config)
            
            # 设置 JSON 日志
            if logging_config.json.enabled:
                self._setup_json_logger(logging_config.json, logging_config)
            
            # 设置 SQL 日志
            if logging_config.sql.enabled:
                self._setup_sql_logger(logging_config.sql)
            
            self._is_initialized = True
            loguru_logger.info("日志系统初始化完成", extra={
                "console_enabled": logging_config.console.enabled,
                "file_enabled": logging_config.file.enabled,
                "json_enabled": logging_config.json.enabled,
                "sql_enabled": logging_config.sql.enabled,
                "handlers_count": len(self._handlers_ids)
            })
            
        except Exception as e:
            # 如果配置失败，使用基本配置
            loguru_logger.remove()
            loguru_logger.add(sys.stderr, level="INFO")
            loguru_logger.error(f"日志配置失败，使用默认配置: {e}")
        
        return loguru_logger
    
    def _get_format(self, handler_config: Any, global_config: Any) -> str:
        """获取日志格式"""
        return getattr(handler_config, 'format', None) or global_config.format
    
    def _setup_console_logger(self, config: Any, global_config: Any):
        """设置控制台日志处理器"""
        try:
            handler_id = loguru_logger.add(
                sink=sys.stderr,
                level=config.level,
                format=self._get_format(config, global_config),
                colorize=config.colorize,
                enqueue=False,  # 控制台不需要异步
                catch=True,
                backtrace=True,
                diagnose=True
            )
            self._handlers_ids.append(handler_id)
            loguru_logger.debug(f"控制台日志处理器已添加: ID={handler_id}")
        except Exception as e:
            loguru_logger.error(f"设置控制台日志失败: {e}")
    
    def _setup_file_logger(self, config: Any, global_config: Any):
        """设置文件日志处理器"""
        try:
            # 确保日志目录存在
            log_path = Path(config.path)
            log_path.parent.mkdir(parents=True, exist_ok=True)
            
            handler_id = loguru_logger.add(
                sink=str(log_path),
                level=config.level,
                format=self._get_format(config, global_config),
                rotation=config.rotation,
                retention=config.retention,
                compression=config.compression,
                enqueue=config.enqueue,
                encoding="utf-8",
                catch=True,
                backtrace=True,
                diagnose=True
            )
            self._handlers_ids.append(handler_id)
            loguru_logger.debug(f"文件日志处理器已添加: ID={handler_id}, 路径={log_path}")
        except Exception as e:
            loguru_logger.error(f"设置文件日志失败: {e}")
    
    def _setup_json_logger(self, config: Any, global_config: Any):
        """设置 JSON 结构化日志处理器"""
        try:
            # 确保日志目录存在
            log_path = Path(config.path)
            log_path.parent.mkdir(parents=True, exist_ok=True)
            
            handler_id = loguru_logger.add(
                sink=str(log_path),
                level=config.level,
                serialize=config.serialize,
                rotation=config.rotation,
                retention=config.retention,
                compression=config.compression,
                enqueue=config.enqueue,
                encoding="utf-8",
                catch=True,
                backtrace=True,
                diagnose=True
            )
            self._handlers_ids.append(handler_id)
            loguru_logger.debug(f"JSON 日志处理器已添加: ID={handler_id}, 路径={log_path}")
        except Exception as e:
            loguru_logger.error(f"设置 JSON 日志失败: {e}")
    
    def _setup_sql_logger(self, config: Any):
        """设置 SQL 日志处理器"""
        if self._sql_handler_configured:
            return
        
        try:
            # 集成现有的 SQL 日志功能
            from settings.sql_logging import enable_sql_logging
            enable_sql_logging(
                level=config.level,
                format_string=config.format
            )
            self._sql_handler_configured = True
            loguru_logger.debug("SQL 日志处理器已配置")
        except Exception as e:
            loguru_logger.error(f"设置 SQL 日志失败: {e}")
    
    def reload_configuration(self):
        """重新加载配置并重新设置日志系统"""
        try:
            # 禁用 SQL 日志
            if self._sql_handler_configured:
                from settings.sql_logging import disable_sql_logging
                disable_sql_logging()
                self._sql_handler_configured = False
            
            # 移除现有处理器
            for handler_id in self._handlers_ids:
                loguru_logger.remove(handler_id)
            self._handlers_ids.clear()
            
            # 重置初始化状态
            self._is_initialized = False
            
            # 重新设置
            return self.setup_logger()
        except Exception as e:
            loguru_logger.error(f"重新加载日志配置失败: {e}")
            return loguru_logger
    
    def bind_context(self, **context) -> Any:
        """绑定上下文信息到日志记录器"""
        return loguru_logger.bind(**context)
    
    def get_logger(self, name: Optional[str] = None) -> Any:
        """获取带名称的日志记录器"""
        if name:
            return loguru_logger.bind(logger_name=name)
        return loguru_logger
    
    def set_level(self, level: str):
        """动态设置日志级别"""
        try:
            # 临时更新配置
            old_level = settings.logging.level
            settings.set('logging.level', level.upper())
            
            # 重新加载配置
            self.reload_configuration()
            
            loguru_logger.info(f"日志级别已从 {old_level} 调整为 {level.upper()}")
        except Exception as e:
            loguru_logger.error(f"设置日志级别失败: {e}")
    
    def get_handlers_info(self) -> Dict[str, Any]:
        """获取当前处理器信息"""
        return {
            "handlers_count": len(self._handlers_ids),
            "handlers_ids": self._handlers_ids,
            "sql_configured": self._sql_handler_configured,
            "initialized": self._is_initialized
        }


# 全局日志管理器实例
_logging_manager = LoggingManager()


def get_logging_manager() -> LoggingManager:
    """获取日志管理器实例"""
    return _logging_manager


def setup_logging() -> Any:
    """初始化日志系统"""
    return _logging_manager.setup_logger()


def get_logger(name: Optional[str] = None) -> Any:
    """获取日志记录器"""
    return _logging_manager.get_logger(name)


def bind_context(**context) -> Any:
    """绑定上下文"""
    return _logging_manager.bind_context(**context)


def reload_logging() -> Any:
    """重新加载日志配置"""
    return _logging_manager.reload_configuration()


def set_log_level(level: str):
    """动态调整日志级别"""
    _logging_manager.set_level(level)


def get_handlers_info() -> Dict[str, Any]:
    """获取处理器信息"""
    return _logging_manager.get_handlers_info()
```

#### 3.2 更新 `log/log.py`

```python
"""
日志模块 - 保持向后兼容性

这个文件保持现有的导入接口不变，内部使用新的配置驱动日志管理器
"""
from .logging_manager import (
    setup_logging,
    get_logger,
    bind_context,
    reload_logging,
    set_log_level,
    get_handlers_info,
    get_logging_manager
)

# 保持向后兼容的接口
class Loggin:
    """保持向后兼容的日志配置类"""
    
    def __init__(self):
        self.manager = get_logging_manager()
    
    def setup_logger(self):
        """设置日志记录器 - 向后兼容接口"""
        return setup_logging()


# 创建实例并初始化
loggin = Loggin()
logger = loggin.setup_logger()

# 导出常用接口 (保持向后兼容)
__all__ = [
    'loggin',
    'logger',
    'get_logger',
    'bind_context',
    'reload_logging',
    'set_log_level',
    'get_handlers_info'
]
```

### 步骤 4: 使用示例

#### 4.1 基本使用

```python
# 在任何模块中使用
from log.log import logger

# 基本日志记录
logger.info("应用启动")
logger.debug("调试信息")
logger.warning("警告信息")
logger.error("错误信息")

# 带上下文的日志记录
logger.bind(user_id=123, action="login").info("用户登录")

# 异常日志记录
try:
    result = 1 / 0
except Exception as e:
    logger.exception("计算错误")
```

#### 4.2 模块化日志记录

```python
# controllers/douyin/controller.py
from log.log import get_logger

# 创建模块专用日志记录器
logger = get_logger("douyin.controller")

class DouyinController:
    def __init__(self):
        self.logger = logger.bind(module="douyin", component="controller")
    
    def fetch_video(self, video_id: str):
        self.logger.info("开始获取视频", video_id=video_id)
        try:
            # 业务逻辑
            self.logger.debug("视频数据处理中", video_id=video_id)
            # ...
            self.logger.info("视频获取成功", video_id=video_id)
        except Exception as e:
            self.logger.error("视频获取失败", video_id=video_id, error=str(e))
            raise
```

#### 4.3 API 请求日志中间件

```python
# core/middlewares.py
from fastapi import Request, Response
from log.log import get_logger
import time

logger = get_logger("api.middleware")

async def logging_middleware(request: Request, call_next):
    start_time = time.time()
    
    # 记录请求开始
    logger.info("API 请求开始", extra={
        "method": request.method,
        "url": str(request.url),
        "client_ip": request.client.host,
        "user_agent": request.headers.get("user-agent")
    })
    
    # 处理请求
    response = await call_next(request)
    
    # 计算处理时间
    process_time = time.time() - start_time
    
    # 记录请求完成
    logger.info("API 请求完成", extra={
        "method": request.method,
        "url": str(request.url),
        "status_code": response.status_code,
        "process_time_ms": round(process_time * 1000, 2)
    })
    
    return response
```

#### 4.4 动态日志级别调整

```python
# 创建管理接口
from fastapi import APIRouter
from log.log import set_log_level, get_handlers_info

router = APIRouter(prefix="/admin/logging")

@router.post("/level/{level}")
async def update_log_level(level: str):
    """动态调整日志级别"""
    try:
        set_log_level(level)
        return {"message": f"日志级别已调整为 {level}"}
    except Exception as e:
        return {"error": str(e)}

@router.get("/info")
async def get_logging_info():
    """获取当前日志配置信息"""
    return get_handlers_info()
```

### 步骤 5: 测试验证

#### 5.1 配置验证测试

```python
# tests/test_logging_config.py
import pytest
from settings import settings
from log.logging_manager import get_logging_manager

def test_logging_config_validation():
    """测试日志配置验证"""
    # 验证配置是否正确加载
    assert hasattr(settings, 'logging')
    assert hasattr(settings.logging, 'level')
    assert settings.logging.level in ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']

def test_logging_manager_initialization():
    """测试日志管理器初始化"""
    manager = get_logging_manager()
    logger = manager.setup_logger()
    
    # 验证日志记录器是否正常工作
    logger.info("测试日志记录")
    
    # 验证处理器信息
    info = manager.get_handlers_info()
    assert info['initialized'] is True
    assert info['handlers_count'] > 0

def test_context_binding():
    """测试上下文绑定"""
    from log.log import get_logger
    
    logger = get_logger("test")
    bound_logger = logger.bind(test_id=123, component="test")
    
    # 这应该不会抛出异常
    bound_logger.info("测试上下文绑定")
```

#### 5.2 性能测试

```python
# tests/test_logging_performance.py
import time
import threading
from log.log import get_logger

def test_logging_performance():
    """测试日志记录性能"""
    logger = get_logger("performance_test")
    
    # 记录开始时间
    start_time = time.time()
    
    # 写入大量日志
    for i in range(1000):
        logger.info(f"性能测试消息 {i}", iteration=i)
    
    # 计算耗时
    elapsed_time = time.time() - start_time
    print(f"1000 条日志记录耗时: {elapsed_time:.2f} 秒")
    
    # 断言性能要求 (例如: 1000条日志应在1秒内完成)
    assert elapsed_time < 1.0

def test_concurrent_logging():
    """测试并发日志记录"""
    logger = get_logger("concurrency_test")
    
    def log_worker(worker_id: int, message_count: int):
        for i in range(message_count):
            logger.info(f"工作线程 {worker_id} 消息 {i}", worker_id=worker_id, message_id=i)
    
    # 创建多个线程同时写日志
    threads = []
    for worker_id in range(5):
        thread = threading.Thread(target=log_worker, args=(worker_id, 100))
        threads.append(thread)
        thread.start()
    
    # 等待所有线程完成
    for thread in threads:
        thread.join()
    
    logger.info("并发日志测试完成")
```

### 步骤 6: 迁移脚本

#### 6.1 创建迁移脚本 `scripts/migrate_logging.py`

```python
#!/usr/bin/env python3
"""
日志系统迁移脚本

这个脚本帮助从旧的日志配置迁移到新的配置驱动系统
"""
import os
import shutil
from pathlib import Path
from datetime import datetime

def backup_old_config():
    """备份现有的日志配置"""
    backup_dir = Path("backups") / f"logging_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    backup_dir.mkdir(parents=True, exist_ok=True)
    
    # 备份现有的 log.py
    old_log_file = Path("log/log.py")
    if old_log_file.exists():
        shutil.copy2(old_log_file, backup_dir / "log.py.backup")
        print(f"已备份 {old_log_file} 到 {backup_dir}")
    
    # 备份现有的日志文件
    logs_dir = Path("logs")
    if logs_dir.exists():
        shutil.copytree(logs_dir, backup_dir / "logs", dirs_exist_ok=True)
        print(f"已备份日志文件到 {backup_dir}")
    
    return backup_dir

def validate_new_config():
    """验证新的配置是否正确"""
    try:
        from settings import settings
        settings.validators.validate()
        print("✓ 配置验证通过")
        return True
    except Exception as e:
        print(f"✗ 配置验证失败: {e}")
        return False

def test_new_logging():
    """测试新的日志系统"""
    try:
        from log.logging_manager import setup_logging, get_logger
        
        # 初始化日志系统
        logger = setup_logging()
        print("✓ 日志系统初始化成功")
        
        # 测试日志记录
        test_logger = get_logger("migration_test")
        test_logger.info("日志系统迁移测试")
        print("✓ 日志记录测试成功")
        
        return True
    except Exception as e:
        print(f"✗ 日志系统测试失败: {e}")
        return False

def main():
    """主迁移流程"""
    print("开始日志系统迁移...")
    
    # 1. 备份现有配置
    backup_dir = backup_old_config()
    print(f"备份完成: {backup_dir}")
    
    # 2. 验证新配置
    if not validate_new_config():
        print("配置验证失败，迁移中止")
        return False
    
    # 3. 测试新日志系统
    if not test_new_logging():
        print("日志系统测试失败，迁移中止")
        return False
    
    print("✓ 日志系统迁移完成!")
    print(f"备份文件位置: {backup_dir}")
    print("请运行应用程序确认一切正常工作")
    
    return True

if __name__ == "__main__":
    main()
```

### 步骤 7: 监控和调试

#### 7.1 创建日志监控脚本 `scripts/monitor_logging.py`

```python
#!/usr/bin/env python3
"""
日志系统监控脚本

监控日志文件大小、处理器状态等
"""
import os
import json
from pathlib import Path
from datetime import datetime

def check_log_files():
    """检查日志文件状态"""
    logs_dir = Path("logs")
    if not logs_dir.exists():
        print("日志目录不存在")
        return
    
    print("日志文件状态:")
    print("-" * 50)
    
    for log_file in logs_dir.glob("*.log*"):
        size_mb = log_file.stat().st_size / (1024 * 1024)
        mtime = datetime.fromtimestamp(log_file.stat().st_mtime)
        print(f"{log_file.name:30} {size_mb:8.2f} MB  {mtime}")

def check_config_status():
    """检查配置状态"""
    try:
        from settings import settings
        from log.logging_manager import get_logging_manager
        
        print("\n配置状态:")
        print("-" * 50)
        print(f"当前环境: {settings.current_env}")
        print(f"日志级别: {settings.logging.level}")
        print(f"控制台日志: {'启用' if settings.logging.console.enabled else '禁用'}")
        print(f"文件日志: {'启用' if settings.logging.file.enabled else '禁用'}")
        print(f"JSON 日志: {'启用' if settings.logging.json.enabled else '禁用'}")
        print(f"SQL 日志: {'启用' if settings.logging.sql.enabled else '禁用'}")
        
        # 获取处理器信息
        manager = get_logging_manager()
        handler_info = manager.get_handlers_info()
        print(f"\n处理器状态:")
        print(f"已初始化: {'是' if handler_info['initialized'] else '否'}")
        print(f"处理器数量: {handler_info['handlers_count']}")
        print(f"SQL 已配置: {'是' if handler_info['sql_configured'] else '否'}")
        
    except Exception as e:
        print(f"配置检查失败: {e}")

def main():
    check_log_files()
    check_config_status()

if __name__ == "__main__":
    main()
```

## 🚨 常见问题和解决方案

### 问题 1: 配置验证失败

```bash
# 错误信息示例
dynaconf.validator.ValidationError: logging.level must be in ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'] but it is 'debug' in env DEVELOPMENT
```

**解决方案**: 确保配置文件中的日志级别使用大写格式。

### 问题 2: 日志文件权限问题

```bash
# 错误信息示例
PermissionError: [Errno 13] Permission denied: 'logs/application.log'
```

**解决方案**: 确保应用程序对日志目录有写权限，或更改日志文件路径。

### 问题 3: 日志轮转不工作

**解决方案**: 检查 rotation 配置格式，确保使用正确的格式如 "100 MB", "1 day" 等。

### 问题 4: SQL 日志重复输出

**解决方案**: 确保只调用一次 `enable_sql_logging`，或在重新加载配置时先调用 `disable_sql_logging`。

## 📊 性能基准测试

运行性能测试来验证新系统的表现：

```bash
# 运行性能测试
python -m pytest tests/test_logging_performance.py -v

# 运行迁移脚本
python scripts/migrate_logging.py

# 监控日志状态
python scripts/monitor_logging.py
```

---

这个实施指南提供了完整的代码示例和步骤说明，帮助你顺利从现有的日志系统迁移到新的配置驱动系统。