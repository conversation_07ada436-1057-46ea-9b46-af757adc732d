"""
抖音HTML数据提取性能优化模块

包含：
- 预编译正则表达式模式
- 可配置的超时和重试设置
- 内存优化的HTML处理
- 性能监控和指标收集
"""

import asyncio
import gc
import re
import time
from collections import defaultdict
from contextlib import asynccontextmanager

from threading import Lock
from typing import Any, Dict, List, Optional, Pattern, Tuple


from loguru import logger

from utils.logging_config import setup_module_logger

# 设置模块专用日志
setup_module_logger(__name__)


class CompiledRegexManager:
    """预编译正则表达式管理器"""

    def __init__(self):
        self._cache: Dict[str, Pattern] = {}
        self._cache_access_count: Dict[str, int] = defaultdict(int)
        self._lock = Lock()
        self.logger = logger.bind(component="CompiledRegexManager")

        # 预编译常用正则表达式
        self._precompile_common_patterns()

    def _precompile_common_patterns(self):
        """预编译常用的正则表达式模式"""
        common_patterns = {
            # HTML解析相关
            "pace_f_pattern": r"window\.pace_f\s*=\s*(\[.*?\]);?",
            "router_data_pattern": r"window\._ROUTER_DATA\s*=\s*(\{.*?\});?",
            "script_tag_pattern": r"<script[^>]*>(.*?)</script>",
            "json_pattern": r'\{[^}]*"[^"]*":[^}]*\}',
            # aweme_id相关
            "aweme_id_pattern": r'"aweme_id"\s*:\s*"(\d{15,20})"',
            "aweme_id_validation": r"^\d{15,20}$",
            # URI解码相关
            "uri_encoded_pattern": r"%[0-9A-Fa-f]{2}",
            "pace_f_data_pattern": r'"data"\s*:\s*"([^"]*)"',
            # 错误检测相关
            "captcha_pattern": r"(captcha|verification|blocked|forbidden)",
            "error_pattern": r"(404|not found|error|exception)",
            "redirect_pattern": r"(redirect|location\.href|window\.location)",
            # 数据提取相关
            "author_pattern": r'"author"\s*:\s*\{[^}]*\}',
            "statistics_pattern": r'"statistics"\s*:\s*\{[^}]*\}',
            "video_pattern": r'"video"\s*:\s*\{[^}]*\}',
        }

        for name, pattern in common_patterns.items():
            try:
                compiled_pattern = re.compile(pattern, re.DOTALL | re.IGNORECASE)
                self._cache[name] = compiled_pattern
                self.logger.debug(f"预编译正则表达式: {name}")
            except re.error as e:
                self.logger.error(f"预编译正则表达式失败 {name}: {e}")

    def get_pattern(self, name: str, pattern: Optional[str] = None) -> Pattern:
        """获取编译后的正则表达式模式"""
        with self._lock:
            if name in self._cache:
                self._cache_access_count[name] += 1
                return self._cache[name]

            if pattern is None:
                raise ValueError(f"Pattern '{name}' not found and no pattern provided")

            try:
                compiled_pattern = re.compile(pattern, re.DOTALL | re.IGNORECASE)

                # 缓存管理
                if len(self._cache) >= 100:  # 默认缓存大小
                    self._evict_least_used_pattern()

                self._cache[name] = compiled_pattern
                self._cache_access_count[name] = 1

                self.logger.debug(f"编译并缓存新正则表达式: {name}")
                return compiled_pattern

            except re.error as e:
                self.logger.error(f"编译正则表达式失败 {name}: {e}")
                raise

    def _evict_least_used_pattern(self):
        """驱逐最少使用的模式"""
        if not self._cache:
            return

        least_used_name = min(self._cache_access_count.keys(), key=lambda k: self._cache_access_count[k])

        del self._cache[least_used_name]
        del self._cache_access_count[least_used_name]

        self.logger.debug(f"驱逐最少使用的正则表达式: {least_used_name}")

    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        with self._lock:
            return {
                "cache_size": len(self._cache),
                "max_cache_size": 100,  # 默认缓存大小
                "access_counts": dict(self._cache_access_count),
                "total_accesses": sum(self._cache_access_count.values()),
            }


class MemoryOptimizedHTMLProcessor:
    """内存优化的HTML处理器"""

    def __init__(self):
        self._processed_count = 0
        self.logger = logger.bind(component="MemoryOptimizedHTMLProcessor")

    def process_html_content(self, html_content: str, aweme_id: str) -> Tuple[str, Dict[str, Any]]:
        """内存优化的HTML内容处理"""
        processing_start = time.time()
        original_size = len(html_content)

        try:
            # 内存检查
            memory_info = self._check_memory_usage()
            if memory_info["usage_percent"] > 0.8:  # 默认内存警告阈值
                self.logger.warning(
                    f"内存使用率高: {memory_info['usage_percent']:.1%}",
                    extra={"memory_info": memory_info, "aweme_id": aweme_id},
                )

            # 大小检查
            if original_size > 5 * 1024 * 1024:  # 默认最大HTML大小为5MB
                self.logger.warning(
                    f"HTML内容过大: {original_size} bytes (max: {5 * 1024 * 1024})",
                    extra={"aweme_id": aweme_id},
                )
                # 截断HTML内容
                html_content = html_content[: 5 * 1024 * 1024]

            # HTML压缩（默认启用）
            html_content = self._compress_html(html_content)

            # HTML清理和预处理
            html_content = self._clean_html_content(html_content)

            # 增加处理计数
            self._processed_count += 1

            # 垃圾回收检查
            if self._processed_count % 100 == 0:  # 默认垃圾回收阈值
                collected = gc.collect()
                self.logger.debug(f"触发垃圾回收: 回收了 {collected} 个对象")

            processing_time = time.time() - processing_start

            processing_stats = {
                "original_size": original_size,
                "processed_size": len(html_content),
                "compression_ratio": original_size / len(html_content) if html_content else 1,
                "processing_time": processing_time,
                "memory_info": memory_info,
            }

            return html_content, processing_stats

        except Exception as e:
            self.logger.error(f"HTML处理失败: {e}", extra={"aweme_id": aweme_id})
            raise

    def _compress_html(self, html_content: str) -> str:
        """压缩HTML内容"""
        # 移除多余空白
        html_content = re.sub(r"\s+", " ", html_content)

        # 移除HTML注释
        html_content = re.sub(r"<!--.*?-->", "", html_content, flags=re.DOTALL)

        # 移除不必要的空行
        html_content = re.sub(r"\n\s*\n", "\n", html_content)

        return html_content.strip()

    def _clean_html_content(self, html_content: str) -> str:
        """清理HTML内容"""
        # 移除潜在的危险内容
        dangerous_patterns = [
            r"<script[^>]*>.*?alert\(.*?\).*?</script>",
            r"javascript:",
            r"onclick\s*=",
            r"onerror\s*=",
        ]

        for pattern in dangerous_patterns:
            html_content = re.sub(pattern, "", html_content, flags=re.IGNORECASE | re.DOTALL)

        return html_content

    def _check_memory_usage(self) -> Dict[str, Any]:
        """检查内存使用情况"""
        try:
            process = psutil.Process()
            memory_info = process.memory_info()
            virtual_memory = psutil.virtual_memory()

            return {
                "rss_mb": memory_info.rss / 1024 / 1024,
                "vms_mb": memory_info.vms / 1024 / 1024,
                "usage_percent": virtual_memory.percent / 100,
                "available_mb": virtual_memory.available / 1024 / 1024,
                "total_mb": virtual_memory.total / 1024 / 1024,
            }
        except Exception as e:
            self.logger.warning(f"无法获取内存信息: {e}")
            return {"error": str(e)}


class PerformanceMonitor:
    """性能监控器"""

    def __init__(self):
        self._metrics: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        self._operation_counters: Dict[str, int] = defaultdict(int)
        self._error_counters: Dict[str, int] = defaultdict(int)
        self._lock = Lock()
        self.logger = logger.bind(component="PerformanceMonitor")

        # 启动后台监控任务
        self._start_background_monitoring()

    def record_operation(
        self, operation_name: str, duration: float, success: bool = True, metadata: Optional[Dict] = None
    ):
        """记录操作性能"""
        with self._lock:
            timestamp = time.time()

            # 记录性能指标
            self._metrics[f"{operation_name}_duration"].append(
                {"timestamp": timestamp, "duration": duration, "success": success, "metadata": metadata or {}}
            )

            # 更新计数器
            self._operation_counters[operation_name] += 1

            if not success:
                self._error_counters[operation_name] += 1

            # 记录慢操作日志
            if duration > 5.0:  # 默认性能日志阈值
                self.logger.warning(
                    f"慢操作检测: {operation_name} 耗时 {duration:.2f}秒",
                    extra={"operation": operation_name, "duration": duration, "success": success, "metadata": metadata},
                )

    def get_performance_stats(self, operation_name: Optional[str] = None) -> Dict[str, Any]:
        """获取性能统计信息"""
        with self._lock:
            if operation_name:
                return self._get_operation_stats(operation_name)
            else:
                return self._get_all_stats()

    def _get_operation_stats(self, operation_name: str) -> Dict[str, Any]:
        """获取单个操作的统计信息"""
        duration_key = f"{operation_name}_duration"
        durations = [m["duration"] for m in self._metrics[duration_key]]

        if not durations:
            return {"operation": operation_name, "no_data": True}

        return {
            "operation": operation_name,
            "total_count": self._operation_counters[operation_name],
            "error_count": self._error_counters[operation_name],
            "success_rate": 1 - (self._error_counters[operation_name] / self._operation_counters[operation_name]),
            "avg_duration": sum(durations) / len(durations),
            "min_duration": min(durations),
            "max_duration": max(durations),
            "p95_duration": self._calculate_percentile(durations, 0.95),
            "p99_duration": self._calculate_percentile(durations, 0.99),
        }

    def _get_all_stats(self) -> Dict[str, Any]:
        """获取所有操作的统计信息"""
        all_operations = set()
        for key in self._metrics.keys():
            if key.endswith("_duration"):
                operation_name = key[:-9]  # 移除 "_duration" 后缀
                all_operations.add(operation_name)

        stats = {}
        for operation in all_operations:
            stats[operation] = self._get_operation_stats(operation)

        return stats

    def _calculate_percentile(self, values: List[float], percentile: float) -> float:
        """计算百分位数"""
        if not values:
            return 0.0

        sorted_values = sorted(values)
        index = int(len(sorted_values) * percentile)
        return sorted_values[min(index, len(sorted_values) - 1)]

    def _start_background_monitoring(self):
        """启动后台监控任务"""

        async def monitoring_loop():
            while True:
                try:
                    await asyncio.sleep(10.0)  # 默认指标收集间隔
                    await self._collect_system_metrics()
                except Exception as e:
                    self.logger.error(f"后台监控任务错误: {e}")

        # 在事件循环中启动监控任务
        try:
            loop = asyncio.get_event_loop()
            loop.create_task(monitoring_loop())
        except RuntimeError:
            # 如果没有运行的事件循环，则创建一个
            self.logger.info("创建新的事件循环用于性能监控")


class PerformanceOptimizer:
    """性能优化器主类"""

    def __init__(self):
        # 初始化组件
        self.regex_manager = CompiledRegexManager()
        self.html_processor = MemoryOptimizedHTMLProcessor()
        self.performance_monitor = PerformanceMonitor()

        # 信号量控制并发（使用默认值）
        self._extraction_semaphore = asyncio.Semaphore(10)  # 默认最大并发提取数
        self._db_semaphore = asyncio.Semaphore(5)  # 默认最大并发数据库操作数

        self.logger = logger.bind(component="PerformanceOptimizer")
        self.logger.info("性能优化器初始化完成")

    @asynccontextmanager
    async def optimized_extraction(self, operation_name: str, aweme_id: str):
        """优化的数据提取上下文管理器"""
        start_time = time.time()
        async with self._extraction_semaphore:
            try:
                self.logger.debug(f"开始优化提取: {operation_name}", extra={"aweme_id": aweme_id})
                yield

                duration = time.time() - start_time
                self.performance_monitor.record_operation(operation_name, duration, True, {"aweme_id": aweme_id})

            except Exception as e:
                duration = time.time() - start_time
                self.performance_monitor.record_operation(
                    operation_name, duration, False, {"aweme_id": aweme_id, "error": str(e)}
                )
                raise

    @asynccontextmanager
    async def optimized_db_operation(self, operation_name: str):
        """优化的数据库操作上下文管理器"""
        start_time = time.time()
        async with self._db_semaphore:
            try:
                yield

                duration = time.time() - start_time
                self.performance_monitor.record_operation(operation_name, duration, True)

            except Exception as e:
                duration = time.time() - start_time
                self.performance_monitor.record_operation(operation_name, duration, False, {"error": str(e)})
                raise

    async def optimized_retry(self, operation_func, operation_name: str, *args, **kwargs):
        """优化的重试机制"""
        last_exception = None
        max_retries = 3  # 默认最大重试次数
        base_delay = 1.0  # 默认基础延迟
        max_delay = 10.0  # 默认最大延迟

        for attempt in range(max_retries + 1):
            try:
                start_time = time.time()
                result = await operation_func(*args, **kwargs)

                duration = time.time() - start_time
                self.performance_monitor.record_operation(
                    f"{operation_name}_retry", duration, True, {"attempt": attempt + 1}
                )

                return result

            except Exception as e:
                last_exception = e
                duration = time.time() - start_time
                self.performance_monitor.record_operation(
                    f"{operation_name}_retry", duration, False, {"attempt": attempt + 1, "error": str(e)}
                )

                if attempt < max_retries:
                    # 指数退避延迟
                    import random
                    delay = min(
                        base_delay * (2**attempt) + random.uniform(0, 1),
                        max_delay,
                    )
                    self.logger.warning(
                        f"操作失败，{delay:.1f}秒后重试 (第{attempt + 1}次): {e}",
                        extra={
                            "operation": operation_name,
                            "attempt": attempt + 1,
                            "max_retries": max_retries,
                            "delay": delay,
                        },
                    )
                    await asyncio.sleep(delay)
                else:
                    self.logger.error(
                        f"操作最终失败，已重试{max_retries}次: {e}", extra={"operation": operation_name}
                    )

        raise last_exception

    def get_optimization_report(self) -> Dict[str, Any]:
        """获取优化报告"""
        return {
            "config": self.config.__dict__,
            "regex_cache_stats": self.regex_manager.get_cache_stats(),
            "performance_stats": self.performance_monitor.get_performance_stats(),
            "system_info": {
                "extraction_semaphore": {
                    "value": self._extraction_semaphore._value,
                    "max_value": self.config.max_concurrent_extractions,
                },
                "db_semaphore": {
                    "value": self._db_semaphore._value,
                    "max_value": self.config.max_concurrent_db_operations,
                },
            },
        }
