# 抖音工具模块

## 概述

抖音工具模块提供了一系列用于处理抖音相关功能的工具类，包括URL处理、数据提取、性能优化等功能。

## 功能特性

- **双重处理策略**: 先通过移动端URL解析，失败后使用RPC接口
- **数据验证**: 验证下载链接有效性
- **自动保存**: 自动将处理结果保存到数据库
- **统一响应**: 提供统一的响应格式
- **错误处理**: 完善的异常处理机制

## 使用方法

### URL处理器使用

```python
from utils.douyin.url_processor import DouyinUrlProcessor

# 创建URL处理器实例
url_processor = DouyinUrlProcessor()

# 处理抖音URL
result = await url_processor.process_douyin_url("https://v.douyin.com/xxx")

# 提取视频ID
video_id = url_processor.extract_video_id("https://www.douyin.com/video/7123456789012345678")

# 提取用户ID
from utils.douyin import extract_douyin_user_id
user_id = extract_douyin_user_id("https://www.douyin.com/user/MS4wLjABAAAA123")
```

### 数据提取器使用

```python
from utils.douyin.extract.jingxuan_data_extractor import JingxuanDataExtractor

# 创建数据提取器实例
extractor = JingxuanDataExtractor()

# 处理精选URL
result = await extractor.process_jingxuan_url("7123456789012345678")
```

## 处理流程

1. **参数验证**: 验证视频ID格式是否正确
2. **数据库查询**: 首先检查数据库中是否已存在该视频
3. **移动端URL处理**:
   - 拼接移动端URL
   - 使用HTML解析器提取数据
   - 验证下载链接有效性
4. **RPC接口备选**: 移动端处理失败时使用RPC接口
5. **数据保存**: 将处理结果保存到数据库
6. **响应构建**: 返回统一格式的响应

## 响应格式

```json
{
    "video_id": "7123456789012345678",
    "input_type": "aweme_id",
    "original_input": "7123456789012345678",
    "processed": true,
    "data": {
        // 视频详细数据
    },
    "source": "html_extraction" // 或 "rpc_api" 或 "database"
}
```

## 数据来源说明

- `database`: 数据来自数据库缓存
- `html_extraction`: 数据来自移动端URL的HTML解析
- `rpc_api`: 数据来自RPC接口调用

## 优势

- **职责分离**: 将复杂逻辑从控制器中分离
- **易于测试**: 独立的处理器类便于单元测试
- **可维护性**: 清晰的方法分工，便于维护和扩展
- **可复用性**: 处理器可以在不同的控制器中复用