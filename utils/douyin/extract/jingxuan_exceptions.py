#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
精选数据提取器专用异常类

为精选提取失败提供特定错误类型，支持详细的错误信息和错误分类
"""

from typing import Any, Dict, List, Optional


class JingxuanExtractionError(Exception):
    """精选提取基础异常类"""

    def __init__(self, message: str, aweme_id: Optional[str] = None, error_code: Optional[str] = None):
        """
        初始化精选提取异常

        Args:
            message: 错误消息
            aweme_id: 相关的视频ID
            error_code: 错误代码
        """
        super().__init__(message)
        self.message = message
        self.aweme_id = aweme_id
        self.error_code = error_code

    def __str__(self) -> str:
        parts = [self.message]
        if self.aweme_id:
            parts.append(f"aweme_id: {self.aweme_id}")
        if self.error_code:
            parts.append(f"error_code: {self.error_code}")
        return " | ".join(parts)

    def to_dict(self) -> Dict[str, Any]:
        """
        将异常转换为字典格式，便于日志记录和API响应

        Returns:
            Dict: 异常信息字典
        """
        return {
            "message": self.message,
            "aweme_id": self.aweme_id,
            "error_code": self.error_code,
            "error_type": self.__class__.__name__,
        }


class JingxuanNetworkError(JingxuanExtractionError):
    """精选提取网络相关错误"""

    def __init__(
        self, message: str, aweme_id: Optional[str] = None, status_code: Optional[int] = None, url: Optional[str] = None
    ):
        """
        初始化网络错误

        Args:
            message: 错误消息
            aweme_id: 相关的视频ID
            status_code: HTTP状态码
            url: 请求的URL
        """
        super().__init__(message, aweme_id, "NETWORK_ERROR")
        self.status_code = status_code
        self.url = url

    def __str__(self) -> str:
        parts = [self.message]
        if self.aweme_id:
            parts.append(f"aweme_id: {self.aweme_id}")
        if self.status_code:
            parts.append(f"status_code: {self.status_code}")
        if self.url:
            parts.append(f"url: {self.url}")
        return " | ".join(parts)

    def to_dict(self) -> Dict[str, Any]:
        """
        将网络错误转换为字典格式

        Returns:
            Dict: 异常信息字典
        """
        result = super().to_dict()
        result.update({"status_code": self.status_code, "url": self.url})
        return result


class JingxuanParsingError(JingxuanExtractionError):
    """精选提取HTML解析相关错误"""

    def __init__(
        self,
        message: str,
        aweme_id: Optional[str] = None,
        parsing_stage: Optional[str] = None,
        content_length: Optional[int] = None,
        parsing_details: Optional[Dict[str, Any]] = None,
    ):
        """
        初始化解析错误

        Args:
            message: 错误消息
            aweme_id: 相关的视频ID
            parsing_stage: 解析阶段（如：html_fetch, pace_f_extraction, etc.）
            content_length: 内容长度
            parsing_details: 解析详情，包含更多调试信息
        """
        super().__init__(message, aweme_id, "PARSING_ERROR")
        self.parsing_stage = parsing_stage
        self.content_length = content_length
        self.parsing_details = parsing_details or {}

    def __str__(self) -> str:
        parts = [self.message]
        if self.aweme_id:
            parts.append(f"aweme_id: {self.aweme_id}")
        if self.parsing_stage:
            parts.append(f"stage: {self.parsing_stage}")
        if self.content_length is not None:
            parts.append(f"content_length: {self.content_length}")
        return " | ".join(parts)

    def to_dict(self) -> Dict[str, Any]:
        """
        将解析错误转换为字典格式

        Returns:
            Dict: 异常信息字典
        """
        result = super().to_dict()
        result.update(
            {
                "parsing_stage": self.parsing_stage,
                "content_length": self.content_length,
                "parsing_details": self.parsing_details,
            }
        )
        return result


class JingxuanDataError(JingxuanExtractionError):
    """精选提取数据相关错误"""

    def __init__(
        self,
        message: str,
        aweme_id: Optional[str] = None,
        data_type: Optional[str] = None,
        validation_field: Optional[str] = None,
        data_preview: Optional[str] = None,
    ):
        """
        初始化数据错误

        Args:
            message: 错误消息
            aweme_id: 相关的视频ID
            data_type: 数据类型（如：json, uri_decode, aweme_item）
            validation_field: 验证失败的字段
            data_preview: 数据预览（截断的数据内容）
        """
        super().__init__(message, aweme_id, "DATA_ERROR")
        self.data_type = data_type
        self.validation_field = validation_field
        self.data_preview = data_preview

    def __str__(self) -> str:
        parts = [self.message]
        if self.aweme_id:
            parts.append(f"aweme_id: {self.aweme_id}")
        if self.data_type:
            parts.append(f"data_type: {self.data_type}")
        if self.validation_field:
            parts.append(f"field: {self.validation_field}")
        return " | ".join(parts)

    def to_dict(self) -> Dict[str, Any]:
        """
        将数据错误转换为字典格式

        Returns:
            Dict: 异常信息字典
        """
        result = super().to_dict()
        result.update(
            {"data_type": self.data_type, "validation_field": self.validation_field, "data_preview": self.data_preview}
        )
        return result


class JingxuanConversionError(JingxuanExtractionError):
    """精选提取数据转换相关错误"""

    def __init__(
        self,
        message: str,
        aweme_id: Optional[str] = None,
        conversion_stage: Optional[str] = None,
        missing_fields: Optional[List[str]] = None,
        source_data_preview: Optional[str] = None,
    ):
        """
        初始化转换错误

        Args:
            message: 错误消息
            aweme_id: 相关的视频ID
            conversion_stage: 转换阶段
            missing_fields: 缺失的字段列表
            source_data_preview: 源数据预览
        """
        super().__init__(message, aweme_id, "CONVERSION_ERROR")
        self.conversion_stage = conversion_stage
        self.missing_fields = missing_fields or []
        self.source_data_preview = source_data_preview

    def __str__(self) -> str:
        parts = [self.message]
        if self.aweme_id:
            parts.append(f"aweme_id: {self.aweme_id}")
        if self.conversion_stage:
            parts.append(f"stage: {self.conversion_stage}")
        if self.missing_fields:
            parts.append(f"missing_fields: {', '.join(self.missing_fields)}")
        return " | ".join(parts)

    def to_dict(self) -> Dict[str, Any]:
        """
        将转换错误转换为字典格式

        Returns:
            Dict: 异常信息字典
        """
        result = super().to_dict()
        result.update(
            {
                "conversion_stage": self.conversion_stage,
                "missing_fields": self.missing_fields,
                "source_data_preview": self.source_data_preview,
            }
        )
        return result


class JingxuanValidationError(JingxuanExtractionError):
    """精选提取数据验证相关错误"""

    def __init__(
        self,
        message: str,
        aweme_id: Optional[str] = None,
        validation_type: Optional[str] = None,
        expected_value: Optional[str] = None,
        actual_value: Optional[str] = None,
        validation_context: Optional[Dict[str, Any]] = None,
    ):
        """
        初始化验证错误

        Args:
            message: 错误消息
            aweme_id: 相关的视频ID
            validation_type: 验证类型
            expected_value: 期望值
            actual_value: 实际值
            validation_context: 验证上下文，包含更多调试信息
        """
        super().__init__(message, aweme_id, "VALIDATION_ERROR")
        self.validation_type = validation_type
        self.expected_value = expected_value
        self.actual_value = actual_value
        self.validation_context = validation_context or {}

    def __str__(self) -> str:
        parts = [self.message]
        if self.aweme_id:
            parts.append(f"aweme_id: {self.aweme_id}")
        if self.validation_type:
            parts.append(f"validation: {self.validation_type}")
        if self.expected_value and self.actual_value:
            parts.append(f"expected: {self.expected_value}, actual: {self.actual_value}")
        return " | ".join(parts)

    def to_dict(self) -> Dict[str, Any]:
        """
        将验证错误转换为字典格式

        Returns:
            Dict: 异常信息字典
        """
        result = super().to_dict()
        result.update(
            {
                "validation_type": self.validation_type,
                "expected_value": self.expected_value,
                "actual_value": self.actual_value,
                "validation_context": self.validation_context,
            }
        )
        return result


class JingxuanTimeoutError(JingxuanExtractionError):
    """精选提取超时错误"""

    def __init__(
        self,
        message: str,
        aweme_id: Optional[str] = None,
        timeout_seconds: Optional[int] = None,
        operation: Optional[str] = None,
        retry_count: Optional[int] = None,
    ):
        """
        初始化超时错误

        Args:
            message: 错误消息
            aweme_id: 相关的视频ID
            timeout_seconds: 超时时间（秒）
            operation: 超时的操作
            retry_count: 重试次数
        """
        super().__init__(message, aweme_id, "TIMEOUT_ERROR")
        self.timeout_seconds = timeout_seconds
        self.operation = operation
        self.retry_count = retry_count

    def __str__(self) -> str:
        parts = [self.message]
        if self.aweme_id:
            parts.append(f"aweme_id: {self.aweme_id}")
        if self.timeout_seconds:
            parts.append(f"timeout: {self.timeout_seconds}s")
        if self.operation:
            parts.append(f"operation: {self.operation}")
        if self.retry_count is not None:
            parts.append(f"retries: {self.retry_count}")
        return " | ".join(parts)

    def to_dict(self) -> Dict[str, Any]:
        """
        将超时错误转换为字典格式

        Returns:
            Dict: 异常信息字典
        """
        result = super().to_dict()
        result.update(
            {"timeout_seconds": self.timeout_seconds, "operation": self.operation, "retry_count": self.retry_count}
        )
        return result


class JingxuanRateLimitError(JingxuanExtractionError):
    """精选提取速率限制错误"""

    def __init__(
        self,
        message: str,
        aweme_id: Optional[str] = None,
        retry_after: Optional[int] = None,
        limit_type: Optional[str] = None,
    ):
        """
        初始化速率限制错误

        Args:
            message: 错误消息
            aweme_id: 相关的视频ID
            retry_after: 建议重试时间（秒）
            limit_type: 限制类型（IP, 账户等）
        """
        super().__init__(message, aweme_id, "RATE_LIMIT_ERROR")
        self.retry_after = retry_after
        self.limit_type = limit_type

    def __str__(self) -> str:
        parts = [self.message]
        if self.aweme_id:
            parts.append(f"aweme_id: {self.aweme_id}")
        if self.retry_after:
            parts.append(f"retry_after: {self.retry_after}s")
        if self.limit_type:
            parts.append(f"limit_type: {self.limit_type}")
        return " | ".join(parts)

    def to_dict(self) -> Dict[str, Any]:
        """
        将速率限制错误转换为字典格式

        Returns:
            Dict: 异常信息字典
        """
        result = super().to_dict()
        result.update({"retry_after": self.retry_after, "limit_type": self.limit_type})
        return result


class JingxuanContentChangedError(JingxuanExtractionError):
    """精选提取内容结构变更错误"""

    def __init__(
        self,
        message: str,
        aweme_id: Optional[str] = None,
        expected_structure: Optional[str] = None,
        found_structure: Optional[str] = None,
    ):
        """
        初始化内容结构变更错误

        Args:
            message: 错误消息
            aweme_id: 相关的视频ID
            expected_structure: 期望的内容结构
            found_structure: 实际发现的内容结构
        """
        super().__init__(message, aweme_id, "CONTENT_CHANGED_ERROR")
        self.expected_structure = expected_structure
        self.found_structure = found_structure

    def __str__(self) -> str:
        parts = [self.message]
        if self.aweme_id:
            parts.append(f"aweme_id: {self.aweme_id}")
        if self.expected_structure and self.found_structure:
            parts.append(f"expected: {self.expected_structure}, found: {self.found_structure}")
        return " | ".join(parts)

    def to_dict(self) -> Dict[str, Any]:
        """
        将内容结构变更错误转换为字典格式

        Returns:
            Dict: 异常信息字典
        """
        result = super().to_dict()
        result.update({"expected_structure": self.expected_structure, "found_structure": self.found_structure})
        return result


# 错误代码映射
ERROR_CODE_MAPPING = {
    "NETWORK_ERROR": "网络请求失败",
    "PARSING_ERROR": "HTML解析失败",
    "DATA_ERROR": "数据格式错误",
    "CONVERSION_ERROR": "数据转换失败",
    "VALIDATION_ERROR": "数据验证失败",
    "TIMEOUT_ERROR": "操作超时",
    "RATE_LIMIT_ERROR": "速率限制",
    "CONTENT_CHANGED_ERROR": "内容结构变更",
}


def get_error_description(error_code: str) -> str:
    """
    获取错误代码的描述

    Args:
        error_code: 错误代码

    Returns:
        错误描述
    """
    return ERROR_CODE_MAPPING.get(error_code, "未知错误")


def create_error_context(aweme_id: str, operation: str, **kwargs) -> dict:
    """
    创建错误上下文信息

    Args:
        aweme_id: 视频ID
        operation: 操作名称
        **kwargs: 其他上下文信息

    Returns:
        错误上下文字典
    """
    context = {
        "aweme_id": aweme_id,
        "operation": operation,
        "timestamp": __import__("time").time(),
    }
    context.update(kwargs)
    return context


def format_error_for_log(error: JingxuanExtractionError) -> Dict[str, Any]:
    """
    将错误格式化为适合日志记录的格式

    Args:
        error: 精选提取错误

    Returns:
        格式化后的错误信息
    """
    if hasattr(error, "to_dict"):
        return error.to_dict()

    # 基本错误信息
    return {
        "message": str(error),
        "error_type": error.__class__.__name__,
        "error_code": getattr(error, "error_code", None),
        "aweme_id": getattr(error, "aweme_id", None),
    }


def get_recovery_suggestion(error: JingxuanExtractionError) -> str:
    """
    根据错误类型提供恢复建议

    Args:
        error: 精选提取错误

    Returns:
        恢复建议
    """
    if isinstance(error, JingxuanNetworkError):
        if getattr(error, "status_code", 0) == 429:
            return "建议等待一段时间后重试，或使用不同的IP地址"
        elif getattr(error, "status_code", 0) == 403:
            return "可能需要更新User-Agent或使用不同的请求头"
        else:
            return "检查网络连接并稍后重试"

    elif isinstance(error, JingxuanTimeoutError):
        return "增加超时时间或在网络状况良好时重试"

    elif isinstance(error, JingxuanRateLimitError):
        retry_after = getattr(error, "retry_after", 60)
        return f"建议等待{retry_after}秒后重试，或减少请求频率"

    elif isinstance(error, JingxuanParsingError):
        return "HTML结构可能已变更，需要更新解析逻辑"

    elif isinstance(error, JingxuanContentChangedError):
        return "抖音页面结构可能已更新，需要适配新的数据格式"

    return "尝试使用备用提取方法"
