#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
抖音HTML数据提取器
用于从抖音页面HTML中提取_ROUTER_DATA数据
"""

import json
import re
from typing import Any, Dict, List, Optional, TypedDict

import httpx
from bs4 import BeautifulSoup
from faker import Faker
from faker.providers import user_agent
from loguru import logger

fake = Faker()
fake.add_provider(user_agent)


# 类型定义
class UrlInfo(TypedDict):
    """URL信息类型"""

    uri: str
    url_list: List[str]


class AuthorInfo(TypedDict):
    """作者信息类型"""

    short_id: str
    nickname: str
    signature: str
    avatar_thumb: UrlInfo
    avatar_medium: UrlInfo
    follow_status: int
    following_count: int
    favoriting_count: int
    unique_id: str
    mplatform_followers_count: int
    followers_detail: Optional[Any]
    platform_sync_info: Optional[Any]
    geofencing: Optional[Any]
    policy_version: Optional[Any]
    sec_uid: str
    type_label: Optional[Any]
    card_entries: Optional[Any]
    mix_info: Optional[Any]


class MusicInfo(TypedDict):
    """音乐信息类型"""

    mid: str
    title: str
    author: str
    cover_hd: UrlInfo
    cover_large: UrlInfo
    cover_medium: UrlInfo
    cover_thumb: UrlInfo
    duration: int
    position: Optional[Any]
    status: int


class VideoInfo(TypedDict):
    """视频信息类型"""

    play_addr: UrlInfo
    cover: UrlInfo
    height: int
    width: int
    bit_rate: Optional[Any]
    big_thumbs: Optional[Any]


class StatisticsInfo(TypedDict):
    """统计信息类型"""

    aweme_id: str
    comment_count: int
    digg_count: int
    play_count: int
    share_count: int
    collect_count: int


class TextExtraInfo(TypedDict):
    """文本额外信息类型"""

    start: int
    end: int
    type: int
    hashtag_name: str
    hashtag_id: int


class RiskInfo(TypedDict):
    """风险信息类型"""

    warn: bool
    type: int
    content: str
    reflow_unplayable: int


class AwemeItem(TypedDict):
    """抖音视频项目类型"""

    aweme_id: str
    desc: str
    create_time: int
    author: AuthorInfo
    music: MusicInfo
    cha_list: Optional[Any]
    video: VideoInfo
    statistics: StatisticsInfo
    text_extra: List[TextExtraInfo]
    video_labels: Optional[Any]
    aweme_type: int
    image_infos: Optional[Any]
    risk_infos: RiskInfo
    comment_list: Optional[Any]
    geofencing: Optional[Any]
    video_text: Optional[Any]
    label_top_text: Optional[Any]
    promotions: Optional[Any]
    long_video: Optional[Any]
    images: Optional[Any]
    group_id_str: str
    chapter_list: Optional[Any]
    interaction_stickers: Optional[Any]
    img_bitrate: Optional[Any]
    chapter_bar_color: Optional[Any]


class VideoInfoRes(TypedDict):
    """视频信息响应类型"""

    extra: Dict[str, Any]
    filter_list: List[Any]
    is_oversea: int
    item_list: List[AwemeItem]
    status_code: int


class CommonContext(TypedDict):
    """通用上下文类型"""

    ua: str
    isSpider: bool
    webId: str
    query: Dict[str, Any]
    renderInSSR: int
    lastPath: str
    appName: str
    host: str
    isNotSupportWebp: bool


class VideoPageData(TypedDict):
    """视频页面数据类型"""

    ua: str
    isSpider: bool
    webId: str
    query: Dict[str, Any]
    renderInSSR: int
    lastPath: str
    appName: str
    host: str
    isNotSupportWebp: bool
    commonContext: CommonContext
    videoInfoRes: VideoInfoRes
    itemId: str
    isVideoOptimize: bool
    openDirectGroup: str
    isVideoStyleOptimize: bool
    isButtonOptimize: bool
    isAutoOpenApp: bool
    darkModeAdaptation: bool
    serverToken: str


class LoaderData(TypedDict):
    """加载器数据类型"""

    video_layout: Optional[Any]
    # 动态键，格式为 "video_(id)/page"，值为 VideoPageData


class RouterData(TypedDict):
    """路由数据类型"""

    loaderData: Dict[str, Any]  # 包含 video_layout 和动态的视频页面数据
    errors: Optional[Any]


class ProcessUrlResult:
    """处理URL结果类型"""

    def __init__(
        self,
        url: str,
        success: bool = False,
        router_data: Optional[RouterData] = None,
        video_info: Optional[AwemeItem] = None,
        error: Optional[str] = None,
    ):
        self.url = url
        self.success = success
        self.router_data = router_data
        self.video_info = video_info
        self.error = error

    def __getitem__(self, key: str):
        """支持字典式访问以保持向后兼容"""
        return getattr(self, key)

    def __setitem__(self, key: str, value):
        """支持字典式设置以保持向后兼容"""
        setattr(self, key, value)

    def __contains__(self, key: str) -> bool:
        """支持 in 操作符"""
        return hasattr(self, key)

    def get(self, key: str, default=None):
        """支持 get 方法以保持向后兼容"""
        return getattr(self, key, default)


class DouyinDataExtractor:
    """抖音数据提取器类"""

    def __init__(self, timeout: int = 10):
        """
        初始化提取器

        Args:
            timeout: 请求超时时间（秒）
        """
        self.timeout = timeout
        self.client = httpx.Client(timeout=timeout)
        # 设置请求头，模拟浏览器
        self.client.headers.update(
            {
                "User-Agent": fake.ios_platform_token(),
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
                "Accept-Language": "zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2",
            }
        )

    def __enter__(self):
        """上下文管理器入口"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器退出"""
        self.close()
        # 不抑制异常
        return False

    def close(self):
        """关闭客户端连接"""
        if hasattr(self, "client"):
            self.client.close()

    def fetch_html(self, url: str) -> str:
        """
        获取URL的HTML内容

        Args:
            url: 目标URL

        Returns:
            HTML内容字符串

        Raises:
            httpx.RequestError: 请求失败时抛出
        """
        try:
            response = self.client.get(url, timeout=self.timeout)
            response.raise_for_status()
            return response.text
        except httpx.RequestError as e:
            raise httpx.RequestError(f"获取HTML失败: {e}")

    def extract_router_data_from_html(self, html_content: str) -> Optional[RouterData]:
        """
        从HTML内容中提取_ROUTER_DATA数据

        Args:
            html_content: HTML内容字符串

        Returns:
            _ROUTER_DATA的JSON数据，如果未找到则返回None
        """
        # 使用BeautifulSoup解析HTML
        soup = BeautifulSoup(html_content, "html.parser")

        # 查找包含_ROUTER_DATA的script标签
        script_tags = soup.find_all("script")

        for script in script_tags:
            if script.string and "_ROUTER_DATA" in script.string:
                return self._parse_router_data_from_script(script.string)

        return None

    def _parse_router_data_from_script(self, script_content: str) -> Optional[RouterData]:
        """
        从script内容中解析_ROUTER_DATA

        Args:
            script_content: script标签的内容

        Returns:
            解析后的JSON数据，如果解析失败则返回None
        """
        try:
            # 使用正则表达式提取_ROUTER_DATA的JSON部分
            # 匹配到分号或字符串末尾，适应各种情况
            pattern = r"_ROUTER_DATA\s*=\s*({.*?})(?:;|$)"
            match = re.search(pattern, script_content, re.DOTALL)

            if match:
                json_str = match.group(1)
                # 解析JSON
                return json.loads(json_str)

        except (json.JSONDecodeError, AttributeError) as e:
            logger.error(f"解析JSON失败: {e}")

        return None

    def extract_video_info(self, router_data: RouterData) -> Optional[AwemeItem]:
        """
        从router_data中提取视频信息

        Args:
            router_data: _ROUTER_DATA数据

        Returns:
            提取的视频信息，返回 AwemeItem 类型
        """
        try:
            # 使用强类型访问 loaderData
            loader_data = router_data.get("loaderData")
            if not loader_data:
                return None

            # 查找视频页面数据（动态键名）
            video_page_data = None
            for key, value in loader_data.items():
                if key.startswith("video_") and isinstance(value, dict):
                    video_page_data = value
                    break

            if not video_page_data:
                return None

            # 提取视频信息响应
            video_info_res = video_page_data.get("videoInfoRes")
            if not video_info_res or not isinstance(video_info_res, dict):
                return None

            # 提取视频项目列表
            item_list = video_info_res.get("item_list")
            if not item_list or not isinstance(item_list, list) or len(item_list) == 0:
                return None

            # 返回第一个视频项目
            item = item_list[0]
            if isinstance(item, dict):
                return item

            return None

        except (KeyError, IndexError, TypeError) as e:
            logger.error(f"提取视频信息失败: {e}")
            return None

    def process_url(self, url: str) -> ProcessUrlResult:
        """
        处理URL，获取完整的数据

        Args:
            url: 抖音视频URL

        Returns:
            包含原始数据和提取信息的字典
        """
        # 初始化结果，使用强类型结构
        result = ProcessUrlResult(url=url)

        try:
            # 获取HTML内容
            html_content: str = self.fetch_html(url)

            # 提取_ROUTER_DATA
            router_data: Optional[RouterData] = self.extract_router_data_from_html(html_content)

            if router_data is not None:
                result.success = True
                result.router_data = router_data

                # 提取视频信息
                video_info: Optional[AwemeItem] = self.extract_video_info(router_data)
                if video_info is not None:
                    result.video_info = video_info
            else:
                result.error = "未找到_ROUTER_DATA数据"

        except Exception as e:
            result.error = str(e)

        return result

    @staticmethod
    def _extract_avatar_url(data: Dict[str, Any]) -> Optional[str]:
        """
        从数据字典中提取作者头像URL

        Args:
            data: 包含视频信息的字典

        Returns:
            Optional[str]: 作者头像URL，如果未找到则返回None
        """
        try:
            author_info = data.get("author", {})
            avatar_url = author_info.get("avatar_thumb", {}).get("url_list", [None])[0]
            if avatar_url:
                return avatar_url
        except Exception as e:
            logger.error(f"提取头像URL时发生错误: {e}")
        return None

    @staticmethod
    def _extract_aweme_detail_url(data: Dict[str, Any], aweme_id: str) -> Optional[str]:
        """
        从数据字典中提取视频详情页URL

        Args:
            data: 包含视频信息的字典
            aweme_id: 视频ID

        Returns:
            Optional[str]: 视频详情页URL，如果未找到则返回None
        """
        try:
            # 构造标准抖音视频详情页URL
            detail_url = f"https://www.iesdouyin.com/share/video/{aweme_id}/"
            return detail_url
        except Exception as e:
            logger.error(f"提取视频详情页URL时发生错误: {e}")
            return None

    @staticmethod
    def _extract_content_cover_url(data: Dict[str, Any]) -> Optional[str]:
        """
        从数据字典中提取视频封面URL

        Args:
            data: 包含视频信息的字典

        Returns:
            Optional[str]: 视频封面URL，如果未找到则返回None
        """
        try:
            cover_info = data.get("video", {}).get("cover", {})
            cover_url = cover_info.get("url_list", [None])[0]
            if cover_url:
                return cover_url
        except Exception as e:
            logger.error(f"提取视频封面URL时发生错误: {e}")
        return None

    @staticmethod
    def extract_video_download_url(data: Dict[str, Any]) -> Optional[str]:
        """
        从数据字典中提取视频下载URL

        Args:
            data: 包含视频信息的字典

        Returns:
            Optional[str]: 视频下载URL，如果未找到则返回None
        """
        try:
            # 尝试从bit_rate中提取下载URL
            bit_rate_list = data.get("video", {}).get("bit_rate", [])
            if bit_rate_list and isinstance(bit_rate_list, list):
                # 选择第一个bit_rate的play_addr作为下载URL
                play_addr = bit_rate_list[0].get("play_addr", {})
                download_url = play_addr.get("url_list", [None])[0]
                if download_url:
                    return download_url

            # 如果bit_rate中没有，则尝试从play_addr中提取
            play_addr = data.get("video", {}).get("play_addr", {})
            download_url = play_addr.get("url_list", [None])[0]
            if download_url:
                return download_url
        except Exception as e:
            logger.error(f"提取视频下载URL时发生错误: {e}")
        return None
