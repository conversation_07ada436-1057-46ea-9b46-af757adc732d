"""
纯HTML解析工具 - 不做网络请求，只处理HTML内容

这个模块专门用于解析HTML内容，提取其中的数据。
所有的HTML内容都应该由RPC层获取后传入。
"""

import json
import re
import urllib.parse
from typing import Any, Dict, Optional

from loguru import logger


class DouyinHTMLParser:
    """
    抖音HTML解析器 - 纯函数式，不做网络请求
    """

    @staticmethod
    def extract_pace_f_data(html_content: str, aweme_id: str) -> Optional[str]:
        """
        从HTML内容中提取pace_f数据

        Args:
            html_content: HTML内容字符串
            aweme_id: 目标视频ID

        Returns:
            Optional[str]: 提取到的pace_f数据，如果没找到返回None
        """
        if not html_content or not aweme_id:
            return None

        try:
            # 查找包含目标aweme_id的pace_f条目
            pace_f_pattern = r'<script id="pace_f"[^>]*>([^<]+)</script>'
            matches = re.findall(pace_f_pattern, html_content)

            for match in matches:
                if aweme_id in match:
                    logger.debug(f"找到包含 {aweme_id} 的pace_f数据")
                    return match

            logger.warning(f"未找到包含 {aweme_id} 的pace_f数据")
            return None

        except Exception as e:
            logger.error(f"提取pace_f数据时发生错误: {e}")
            return None

    @staticmethod
    def decode_uri_component(encoded_data: str) -> Optional[str]:
        """
        解码URI组件

        Args:
            encoded_data: 编码的数据

        Returns:
            Optional[str]: 解码后的数据
        """
        try:
            return urllib.parse.unquote(encoded_data)
        except Exception as e:
            logger.error(f"URI解码失败: {e}")
            return None

    @staticmethod
    def parse_json_data(json_str: str) -> Optional[Dict[str, Any]]:
        """
        解析JSON字符串

        Args:
            json_str: JSON字符串

        Returns:
            Optional[Dict]: 解析后的字典，失败返回None
        """
        try:
            return json.loads(json_str)
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析失败: {e}")
            return None
        except Exception as e:
            logger.error(f"数据解析时发生错误: {e}")
            return None

    @staticmethod
    def extract_mobile_data_from_html(html_content: str, aweme_id: str) -> Optional[Dict[str, Any]]:
        """
        从移动端HTML中提取视频数据

        Args:
            html_content: HTML内容
            aweme_id: 视频ID

        Returns:
            Optional[Dict]: 提取到的数据
        """
        if not html_content or not aweme_id:
            return None

        try:
            # 查找包含视频数据的script标签
            script_pattern = r"<script[^>]*>window\._SSR_HYDRATED_DATA=({.*?})</script>"
            matches = re.findall(script_pattern, html_content)

            for match in matches:
                try:
                    data = json.loads(match)
                    # 在数据中查找目标视频
                    if DouyinHTMLParser._contains_aweme_id(data, aweme_id):
                        logger.debug(f"找到移动端视频数据: {aweme_id}")
                        return data
                except json.JSONDecodeError:
                    continue

            logger.warning(f"未找到移动端视频数据: {aweme_id}")
            return None

        except Exception as e:
            logger.error(f"提取移动端数据时发生错误: {e}")
            return None

    @staticmethod
    def extract_jingxuan_data_from_html(html_content: str, aweme_id: str) -> Optional[Dict[str, Any]]:
        """
        从精选页面HTML中提取并处理视频数据

        Args:
            html_content: HTML内容
            aweme_id: 视频ID

        Returns:
            Optional[Dict]: 提取并解析后的视频数据
        """
        if not html_content or not aweme_id:
            return None

        try:
            # 1. 提取pace_f数据
            pace_f_data = DouyinHTMLParser.extract_pace_f_data(html_content, aweme_id)
            if not pace_f_data:
                return None

            # 2. 解码URI
            decoded_data = DouyinHTMLParser.decode_uri_component(pace_f_data)
            if not decoded_data:
                return None

            # 3. 解析JSON
            parsed_data = DouyinHTMLParser.parse_json_data(decoded_data)
            if not parsed_data:
                return None

            # 4. 查找目标视频数据
            video_data = DouyinHTMLParser._find_video_in_data(parsed_data, aweme_id)
            if video_data:
                logger.debug(f"成功提取精选页面视频数据: {aweme_id}")
                return video_data

            logger.warning(f"在解析后的数据中未找到视频: {aweme_id}")
            return None

        except Exception as e:
            logger.error(f"提取精选页面数据时发生错误: {e}")
            return None

    @staticmethod
    def _contains_aweme_id(data: Any, aweme_id: str) -> bool:
        """
        递归检查数据中是否包含指定的aweme_id

        Args:
            data: 要检查的数据
            aweme_id: 目标视频ID

        Returns:
            bool: 是否包含
        """
        if isinstance(data, dict):
            # 检查当前字典
            if data.get("aweme_id") == aweme_id:
                return True
            # 递归检查字典中的值
            for value in data.values():
                if DouyinHTMLParser._contains_aweme_id(value, aweme_id):
                    return True
        elif isinstance(data, list):
            # 递归检查列表中的元素
            for item in data:
                if DouyinHTMLParser._contains_aweme_id(item, aweme_id):
                    return True
        elif isinstance(data, str):
            # 检查字符串中是否包含aweme_id
            return aweme_id in data

        return False

    @staticmethod
    def _find_video_in_data(data: Any, aweme_id: str) -> Optional[Dict[str, Any]]:
        """
        在数据结构中查找指定的视频数据

        Args:
            data: 要搜索的数据
            aweme_id: 目标视频ID

        Returns:
            Optional[Dict]: 找到的视频数据
        """
        if isinstance(data, dict):
            # 检查当前字典是否是目标视频
            if data.get("aweme_id") == aweme_id:
                return data
            # 递归搜索字典中的值
            for value in data.values():
                result = DouyinHTMLParser._find_video_in_data(value, aweme_id)
                if result:
                    return result
        elif isinstance(data, list):
            # 递归搜索列表中的元素
            for item in data:
                result = DouyinHTMLParser._find_video_in_data(item, aweme_id)
                if result:
                    return result

        return None

    @staticmethod
    def validate_video_data_structure(data: Dict[str, Any]) -> bool:
        """
        验证视频数据结构的完整性

        Args:
            data: 视频数据字典

        Returns:
            bool: 数据结构是否有效
        """
        if not isinstance(data, dict):
            return False

        # 检查必需的基础字段
        required_fields = ["aweme_id", "desc"]
        for field in required_fields:
            if field not in data:
                return False

        # 检查视频相关字段
        if "video" in data:
            video = data["video"]
            if isinstance(video, dict):
                # 检查播放地址
                if "play_addr" in video or "bit_rate" in video:
                    return True

        return True


# 便捷函数
def parse_jingxuan_html(html_content: str, aweme_id: str) -> Optional[Dict[str, Any]]:
    """
    解析精选页面HTML的便捷函数

    Args:
        html_content: HTML内容
        aweme_id: 视频ID

    Returns:
        Optional[Dict]: 解析后的视频数据
    """
    return DouyinHTMLParser.extract_jingxuan_data_from_html(html_content, aweme_id)


def parse_mobile_html(html_content: str, aweme_id: str) -> Optional[Dict[str, Any]]:
    """
    解析移动端HTML的便捷函数

    Args:
        html_content: HTML内容
        aweme_id: 视频ID

    Returns:
        Optional[Dict]: 解析后的视频数据
    """
    return DouyinHTMLParser.extract_mobile_data_from_html(html_content, aweme_id)
