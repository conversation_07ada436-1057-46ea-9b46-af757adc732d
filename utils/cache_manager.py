"""
缓存管理器

提供统一的缓存机制，避免频繁的数据库查询
"""

import asyncio
import time
from threading import Lock
from typing import Any, Dict, Optional

from log import logger


class CacheManager:
    """
    缓存管理器

    提供线程安全的缓存功能，支持TTL（生存时间）
    """

    def __init__(self, default_ttl: int = 300):
        """
        初始化缓存管理器

        Args:
            default_ttl: 默认缓存时间（秒），默认5分钟
        """
        self._cache: Dict[str, Dict[str, Any]] = {}
        self._default_ttl = default_ttl
        self._lock = Lock()

    def get(self, key: str) -> Optional[Any]:
        """
        获取缓存值

        Args:
            key: 缓存键

        Returns:
            缓存值，如果不存在或已过期则返回 None
        """
        with self._lock:
            if key not in self._cache:
                return None

            cached_data = self._cache[key]
            current_time = time.time()

            # 检查是否过期
            if current_time - cached_data["timestamp"] >= cached_data["ttl"]:
                del self._cache[key]
                logger.debug(f"缓存已过期并删除: {key}")
                return None

            logger.debug(f"缓存命中: {key}")
            return cached_data["value"]

    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """
        设置缓存值

        Args:
            key: 缓存键
            value: 缓存值
            ttl: 生存时间（秒），如果为 None 则使用默认值
        """
        if ttl is None:
            ttl = self._default_ttl

        with self._lock:
            self._cache[key] = {"value": value, "timestamp": time.time(), "ttl": ttl}
            logger.debug(f"缓存已设置: {key}, TTL: {ttl}s")

    def delete(self, key: str) -> bool:
        """
        删除缓存

        Args:
            key: 缓存键

        Returns:
            是否成功删除
        """
        with self._lock:
            if key in self._cache:
                del self._cache[key]
                logger.debug(f"缓存已删除: {key}")
                return True
            return False

    def clear(self) -> None:
        """清空所有缓存"""
        with self._lock:
            cache_count = len(self._cache)
            self._cache.clear()
            logger.info(f"已清空所有缓存，共 {cache_count} 项")

    def cleanup_expired(self) -> int:
        """
        清理过期缓存

        Returns:
            清理的缓存项数量
        """
        current_time = time.time()
        expired_keys = []

        with self._lock:
            for key, cached_data in self._cache.items():
                if current_time - cached_data["timestamp"] >= cached_data["ttl"]:
                    expired_keys.append(key)

            for key in expired_keys:
                del self._cache[key]

        if expired_keys:
            logger.info(f"清理了 {len(expired_keys)} 个过期缓存项")

        return len(expired_keys)

    def get_stats(self) -> Dict[str, Any]:
        """
        获取缓存统计信息

        Returns:
            缓存统计信息
        """
        with self._lock:
            current_time = time.time()
            total_items = len(self._cache)
            expired_items = 0

            for cached_data in self._cache.values():
                if current_time - cached_data["timestamp"] >= cached_data["ttl"]:
                    expired_items += 1

            return {
                "total_items": total_items,
                "active_items": total_items - expired_items,
                "expired_items": expired_items,
                "default_ttl": self._default_ttl,
            }


class CookiesCacheManager(CacheManager):
    """
    专门用于 cookies 缓存的管理器
    """

    def __init__(self, ttl: int = 300):
        """
        初始化 cookies 缓存管理器

        Args:
            ttl: cookies 缓存时间（秒），默认5分钟
        """
        super().__init__(default_ttl=ttl)

    def get_cookies(self, platform: str) -> Optional[str]:
        """
        获取平台 cookies

        Args:
            platform: 平台名称

        Returns:
            cookies 字符串，如果不存在或已过期则返回 None
        """
        cache_key = f"cookies_{platform}"
        return self.get(cache_key)

    def set_cookies(self, platform: str, cookies: str, ttl: Optional[int] = None) -> None:
        """
        设置平台 cookies

        Args:
            platform: 平台名称
            cookies: cookies 字符串
            ttl: 生存时间（秒），如果为 None 则使用默认值
        """
        cache_key = f"cookies_{platform}"
        self.set(cache_key, cookies, ttl)

    def invalidate_cookies(self, platform: str) -> bool:
        """
        使平台 cookies 失效

        Args:
            platform: 平台名称

        Returns:
            是否成功删除
        """
        cache_key = f"cookies_{platform}"
        return self.delete(cache_key)


# 创建全局缓存管理器实例
cache_manager = CacheManager()
cookies_cache_manager = CookiesCacheManager()


# 定期清理过期缓存的任务
async def periodic_cache_cleanup():
    """定期清理过期缓存"""
    while True:
        try:
            # 每5分钟清理一次过期缓存
            await asyncio.sleep(300)

            # 清理通用缓存
            general_cleaned = cache_manager.cleanup_expired()

            # 清理 cookies 缓存
            cookies_cleaned = cookies_cache_manager.cleanup_expired()

            if general_cleaned > 0 or cookies_cleaned > 0:
                logger.info(f"定期缓存清理完成: 通用缓存 {general_cleaned} 项, cookies缓存 {cookies_cleaned} 项")

        except Exception as e:
            logger.error(f"定期缓存清理失败: {e}")


# 启动定期清理任务
def start_cache_cleanup_task():
    """启动缓存清理任务"""
    try:
        loop = asyncio.get_event_loop()
        loop.create_task(periodic_cache_cleanup())
        logger.info("缓存清理任务已启动")
    except Exception as e:
        logger.warning(f"启动缓存清理任务失败: {e}")
