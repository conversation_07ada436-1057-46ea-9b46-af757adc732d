#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一的日志配置工具

提供项目统一的日志配置，确保所有日志文件都输出到 logs 目录下
"""

from pathlib import Path
from typing import Optional

from loguru import logger


def get_logs_dir() -> Path:
    """获取日志目录路径"""
    # 获取项目根目录
    current_file = Path(__file__)
    project_root = current_file.parent.parent  # utils/logging_config.py -> project_root
    logs_dir = project_root / "logs"

    # 确保日志目录存在
    logs_dir.mkdir(exist_ok=True)

    return logs_dir


def setup_file_logger(
    log_file_name: str,
    level: str = "INFO",
    rotation: str = "10 MB",
    retention: str = "30 days",
    compression: str = "gz",
    format_string: Optional[str] = None,
) -> str:
    """
    设置文件日志记录器

    Args:
        log_file_name: 日志文件名（不包含路径）
        level: 日志级别
        rotation: 日志轮转大小
        retention: 日志保留时间
        compression: 压缩格式
        format_string: 自定义日志格式

    Returns:
        str: 完整的日志文件路径
    """
    logs_dir = get_logs_dir()
    log_file_path = logs_dir / log_file_name

    # 默认日志格式
    if format_string is None:
        format_string = (
            "<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | "
            "<level>{level: <8}</level> | "
            "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - "
            "<level>{message}</level>"
        )

    # 添加文件日志处理器
    logger.add(
        str(log_file_path),
        level=level,
        rotation=rotation,
        retention=retention,
        compression=compression,
        format=format_string,
        enqueue=True,  # 异步写入
        backtrace=True,  # 显示完整的堆栈跟踪
        diagnose=True,  # 显示变量值
    )

    return str(log_file_path)


def setup_test_logger(test_name: str, level: str = "DEBUG") -> str:
    """
    为测试设置专用的日志记录器

    Args:
        test_name: 测试名称
        level: 日志级别

    Returns:
        str: 日志文件路径
    """
    log_file_name = f"test_{test_name}.log"
    return setup_file_logger(log_file_name=log_file_name, level=level, rotation="5 MB", retention="7 days")


def setup_module_logger(module_name: str, level: str = "INFO") -> str:
    """
    为模块设置专用的日志记录器

    Args:
        module_name: 模块名称
        level: 日志级别

    Returns:
        str: 日志文件路径
    """
    log_file_name = f"{module_name}.log"
    return setup_file_logger(log_file_name=log_file_name, level=level)


def setup_api_logger(api_name: str, level: str = "INFO") -> str:
    """
    为 API 设置专用的日志记录器

    Args:
        api_name: API 名称
        level: 日志级别

    Returns:
        str: 日志文件路径
    """
    log_file_name = f"api_{api_name}.log"
    return setup_file_logger(log_file_name=log_file_name, level=level, rotation="20 MB", retention="60 days")


def setup_example_logger(example_name: str, level: str = "INFO") -> str:
    """
    为示例脚本设置专用的日志记录器

    Args:
        example_name: 示例名称
        level: 日志级别

    Returns:
        str: 日志文件路径
    """
    log_file_name = f"example_{example_name}.log"
    return setup_file_logger(log_file_name=log_file_name, level=level, rotation="5 MB", retention="14 days")


def cleanup_old_logs(days: int = 30):
    """
    清理旧的日志文件

    Args:
        days: 保留天数
    """
    logs_dir = get_logs_dir()

    import time

    current_time = time.time()
    cutoff_time = current_time - (days * 24 * 60 * 60)

    for log_file in logs_dir.glob("*.log*"):
        if log_file.stat().st_mtime < cutoff_time:
            try:
                log_file.unlink()
                logger.info(f"删除旧日志文件: {log_file}")
            except Exception as e:
                logger.error(f"删除日志文件失败 {log_file}: {e}")


# 便捷函数
def get_log_path(log_file_name: str) -> str:
    """获取日志文件的完整路径"""
    logs_dir = get_logs_dir()
    return str(logs_dir / log_file_name)


# 预定义的日志配置
class LogConfig:
    """预定义的日志配置"""

    # 测试日志
    TEST_LEVEL = "DEBUG"
    TEST_ROTATION = "5 MB"
    TEST_RETENTION = "7 days"

    # API 日志
    API_LEVEL = "INFO"
    API_ROTATION = "20 MB"
    API_RETENTION = "60 days"

    # 示例日志
    EXAMPLE_LEVEL = "INFO"
    EXAMPLE_ROTATION = "5 MB"
    EXAMPLE_RETENTION = "14 days"

    # 模块日志
    MODULE_LEVEL = "INFO"
    MODULE_ROTATION = "10 MB"
    MODULE_RETENTION = "30 days"


if __name__ == "__main__":
    # 测试日志配置
    print("测试统一日志配置...")

    # 设置测试日志
    test_log_path = setup_test_logger("logging_config_test")
    print(f"测试日志路径: {test_log_path}")

    # 记录测试日志
    logger.info("这是一条测试日志")
    logger.warning("这是一条警告日志")
    logger.error("这是一条错误日志")

    print("日志配置测试完成！")
    print(f"日志目录: {get_logs_dir()}")
