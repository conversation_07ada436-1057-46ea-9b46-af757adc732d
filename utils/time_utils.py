"""时间处理工具函数"""

import time
import logging
from datetime import datetime
from typing import Union

logger = logging.getLogger(__name__)


def safe_convert_to_timestamp(value: Union[str, int, float, None]) -> int:
    """
    安全地将各种时间格式转换为时间戳

    Args:
        value: 时间值，可以是字符串、整数、浮点数或None

    Returns:
        int: 时间戳（秒）
    """
    if value is None:
        return int(time.time())

    # 如果已经是数字，直接返回
    if isinstance(value, (int, float)):
        # 如果是毫秒时间戳，转换为秒
        if value > 1e12:  # 大于1e12的认为是毫秒时间戳
            return int(value / 1000)
        return int(value)

    # 如果是字符串，尝试解析
    if isinstance(value, str):
        value = value.strip()

        # 如果是纯数字字符串
        if value.isdigit():
            timestamp = int(value)
            # 如果是毫秒时间戳，转换为秒
            if timestamp > 1e12:
                return int(timestamp / 1000)
            return timestamp

        # 尝试解析ISO格式时间字符串
        try:
            # 处理各种ISO格式
            if value.endswith("Z"):
                # UTC时间格式：2025-03-17T08:39:52Z
                dt = datetime.fromisoformat(value.replace("Z", "+00:00"))
            elif "+" in value or value.count("-") > 2:
                # 带时区的格式：2025-03-17T08:39:52+08:00
                dt = datetime.fromisoformat(value)
            else:
                # 本地时间格式：2025-03-17T08:39:52 或 2025-03-17 08:39:52
                value = value.replace(" ", "T")  # 统一格式
                dt = datetime.fromisoformat(value)

            return int(dt.timestamp())

        except (ValueError, TypeError) as e:
            logger.warning(f"无法解析时间字符串 '{value}': {e}，使用当前时间")
            return int(time.time())

    # 其他类型，返回当前时间
    logger.warning(f"未知的时间格式 '{value}' (类型: {type(value)})，使用当前时间")
    return int(time.time())


def safe_convert_to_datetime(value: Union[str, int, float, None]) -> datetime:
    """
    安全地将各种时间格式转换为 datetime 对象

    Args:
        value: 时间值，可以是字符串、整数、浮点数或None

    Returns:
        datetime: datetime 对象
    """
    if value is None:
        return datetime.now()

    # 如果已经是 datetime 对象，直接返回
    if isinstance(value, datetime):
        return value

    # 如果已经是数字，当作时间戳处理
    if isinstance(value, (int, float)):
        # 如果是毫秒时间戳，转换为秒
        if value > 1e12:  # 大于1e12的认为是毫秒时间戳
            value = value / 1000
        try:
            return datetime.fromtimestamp(value)
        except (ValueError, OSError) as e:
            logger.warning(f"无法从时间戳 '{value}' 创建 datetime: {e}，使用当前时间")
            return datetime.now()

    # 如果是字符串，尝试解析
    if isinstance(value, str):
        value = value.strip()

        # 如果是纯数字字符串，当作时间戳处理
        if value.isdigit():
            timestamp = int(value)
            # 如果是毫秒时间戳，转换为秒
            if timestamp > 1e12:
                timestamp = timestamp / 1000
            try:
                return datetime.fromtimestamp(timestamp)
            except (ValueError, OSError) as e:
                logger.warning(f"无法从时间戳字符串 '{value}' 创建 datetime: {e}，使用当前时间")
                return datetime.now()

        # 尝试解析ISO格式时间字符串
        try:
            # 处理各种ISO格式
            if value.endswith("Z"):
                # UTC时间格式：2025-03-17T08:39:52Z
                return datetime.fromisoformat(value.replace("Z", "+00:00"))
            elif "+" in value or value.count("-") > 2:
                # 带时区的格式：2025-03-17T08:39:52+08:00
                return datetime.fromisoformat(value)
            else:
                # 本地时间格式：2025-03-17T08:39:52 或 2025-03-17 08:39:52
                value = value.replace(" ", "T")  # 统一格式
                return datetime.fromisoformat(value)

        except (ValueError, TypeError) as e:
            logger.warning(f"无法解析时间字符串 '{value}': {e}，使用当前时间")
            return datetime.now()

    # 其他类型，返回当前时间
    logger.warning(f"未知的时间格式 '{value}' (类型: {type(value)})，使用当前时间")
    return datetime.now()